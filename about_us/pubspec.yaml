name: about_us
description: 智家APP关于我们页面 package project.
version: 1.0.0
author: <EMAIL>
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3

environment:
  sdk: ">=3.3.1 <4.0.0"
  flutter: ">=3.19.3"

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.

  dio: 5.3.2
  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"
  flutter_screenutil: 5.0.0+2
  provider: 6.0.5
  url_launcher: 6.1.10
  crypto: 3.0.1

  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: ">=1.0.0"
  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.0"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.2.0"

  upsystem:
    hosted:
      name: upsystem
      url: http://**************:8083
    version: ">=1.0.0"

  plugin_upgrade:
    hosted:
      name: plugin_upgrade
      url: http://**************:8083
    version: ">=0.2.0"

  umeng:
    hosted:
      name: umeng
      url: http://**************:8083
    version: ">=0.1.1"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=1.0.0"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.2.0"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.2.0"

  flutter_common_ui:
    version: ">=8.0.0 <999.999.999"
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
