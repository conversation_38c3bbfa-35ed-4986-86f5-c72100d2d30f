library about_us;

import 'package:about_us/viewmodels/app_update_view_model.dart';
import 'package:about_us/viewmodels/switch_click_view_model.dart';
import 'package:about_us/viewmodels/check_app_status_view_model.dart';
import 'package:about_us/viewmodels/score_dialog_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:about_us/pages/home.dart';
import 'package:provider/provider.dart';

class AboutUs extends StatelessWidget {
  AboutUs({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height,
      ),
      designSize: Size(375, 667),
      orientation: Orientation.portrait,
    );
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<SwitchClickViewModel>(
            create: (BuildContext context) => SwitchClickViewModel()),
        ChangeNotifierProvider<AppUpdateViewModel>(
            create: (BuildContext context) => AppUpdateViewModel()),
        ChangeNotifierProvider<CheckAppStatusViewModel>(
            create: (BuildContext context) => CheckAppStatusViewModel()),
        ChangeNotifierProvider<ScoreDialogViewModel>(
            create: (BuildContext context) => ScoreDialogViewModel()) 
      ],
      child: HomePage(),
    );
  }
}
