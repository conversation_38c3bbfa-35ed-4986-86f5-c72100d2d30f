import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 废弃，使用flutter_common_ui中的，9.3.0 版本后删除
class ToastHelper {
  static OverlayEntry? _entry;
  static Timer? _timer;

  static void showToast(BuildContext context, String text) {
    closeToast();
    TextStyle style = TextStyle(
      color: Colors.white,
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      decoration: TextDecoration.none,
    );

    Widget widget = Center(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 15.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20.w)),
          color: Colors.black,
        ),
        child: Text(
          text,
          style: style,
        ),
      ),
    );
    _entry = OverlayEntry(
      builder: (_) => widget,
    );

    Overlay.of(context).insert(_entry!);

    _timer = Timer(const Duration(seconds: 2), () {
      _entry?.remove();
      _entry = null;
    });
  }

  static void closeToast() {
    if (_entry != null && _timer != null) {
      _entry!.remove();
      _timer!.cancel();
      _entry = null;
      _timer = null;
    }
  }
}
