import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:typed_data';

class HttpUtils {
  // 智家请求接口域名
  static const BASE_URL = 'https://zj.haier.net';
  // 版本更新(POST)
  static const UPDATE_VERSION = '/emuplus/secuag/common/appversion';
  // hash string
  static const HEX_STRING = '0123456789abcdef';

  /// 获取时间戳
  static int getTimestamp() {
    return new DateTime.now().millisecondsSinceEpoch;
  }

  /// 获取请求签名 SHA256
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getSHA256Sign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    var bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    var list = sha256.convert(bytes).bytes;
    if (list.length <= 0) {
      return '';
    }
    int length = list.length;
    Uint8List uList = new Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      int k = i + 1;
      final index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  /// 获取请求签名 MD5
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getMD5Sign(
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    var bytes = utf8.encode(paramsJson + appId + appKey + timestamp.toString());
    var list = md5.convert(bytes).bytes;
    if (list.length <= 0) {
      return '';
    }
    int length = list.length;
    Uint8List uList = new Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      int k = i + 1;
      final index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }
}
