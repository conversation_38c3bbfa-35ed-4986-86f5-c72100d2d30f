import 'dart:convert';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:dio/dio.dart';

class HeaderInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    /// 超时时间设置
    options.connectTimeout = const Duration(seconds: 30);

    handler.next(options);
  }
}

/// http 请求封装
class HttpManager {
  static Dio _dio = Dio(); // 使用默认配置

  HttpManager() {
    _dio.interceptors.add(new HeaderInterceptors());
  }

  /// 统一封装 post 请求
  static Future<dynamic> postData(
    String url, {
    dynamic params,
    required Options options,
    Map<String, dynamic>? header,
    bool query = false,
  }) async {
    final IsOnline isOnline =
        await Network.isOnline();
    if (!isOnline.isOnline) {
      return null;
    }
    Map<String, dynamic> _headers = <String, dynamic>{};
    if (header != null) {
      _headers.addAll(header);
    }
    Options _options = options;
    if (_options.headers != null) {
      _options.headers!.addAll(_headers);
    } else {
      _options.headers = _headers;
    }

    Response<dynamic>? response;
    try {
      response = await _dio.post<dynamic>(url,
          data: params,
          queryParameters: query
              ? (params is Map ? params.cast<String, dynamic>() : {})
              : null,
          options: _options);
    } catch (e) {
      // print('post data error: $e');
    }

    dynamic data = response?.data ?? <dynamic, dynamic>{};

    if (data is String) {
      data = jsonDecode(data);
    }
    // 兼容不同接口返回数据格式
    if (data['retCode'] == '00000' || data['retCode'] == '60002') {
      return data;
    } else {
      return null;
    }
  }
}

final HttpManager httpManager = new HttpManager();
