import 'dart:convert';
import 'dart:io';

import 'package:app_info/AppinfosModel.dart';
import 'package:dio/dio.dart';
import 'package:about_us/service/httpService.dart';
import 'package:app_info/Appinfos.dart';
import 'package:about_us/utils/http.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

class HTTP {
  // 更新版本
  static Future<dynamic> updateVersion() async {
    final AppInfoModel appinfo = await AppInfoPlugin.getAppInfo();
    final OauthData oauthdata = await User.getOauthData();
    final int _timestamp = HttpUtils.getTimestamp();
    final Map<String, dynamic> requestMap = <String, dynamic>{
      'channel': Platform.operatingSystem,
      'appId': appinfo.appId,
      'version': appinfo.appVersion
    };
    final String bodyJson = json.encode(requestMap);
    final String sign = HttpUtils.getMD5Sign(
      bodyJson,
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    return await HttpManager.postData(
        HttpUtils.BASE_URL + HttpUtils.UPDATE_VERSION,
        params: requestMap,
        options: Options(
          contentType: Headers.jsonContentType,
          headers: <String, dynamic>{
            'appId': appinfo.appId,
            'appKey': appinfo.appKey,
            'appVersion': appinfo.appVersion,
            'accessToken': oauthdata.uhome_access_token,
            'clientId': appinfo.clientId,
            'sign': sign,
            'sequenceId': _timestamp.toString(),
            'language': 'zh-cn',
            'timezone': 8,
            'timestamp': _timestamp.toString()
          },
        ));
  }
}
