import 'package:about_us/utils/constant.dart';
import 'package:about_us/utils/toast.dart';
import 'package:about_us/utils/vdn.dart';
import 'package:about_us/viewmodels/app_update_view_model.dart';
import 'package:about_us/viewmodels/check_app_status_view_model.dart';
import 'package:about_us/viewmodels/score_dialog_view_model.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:storage/storage.dart';
import 'package:trace/trace.dart';
import 'package:umeng/umeng.dart';
import 'package:url_launcher/url_launcher.dart';

class SwitchClickViewModel with ChangeNotifier {
  Map<String, String> eventClickList = <String, String>{
    CONSTANT.SCORE: 'MB13995',
    CONSTANT.CHECK_UPDATE: 'MB13987',
    CONSTANT.APPLET: 'MB13996',
    CONSTANT.OFFICIAL_ACCOUNT: 'MB13997',
    CONSTANT.ICP_TITLE: 'MB36079',
    CONSTANT.SERVICE_ITEMS: 'MB36080',
    CONSTANT.INFO_PROTECT_POLICY: 'MB36081',
    CONSTANT.personalInformation: 'MB36082',
    CONSTANT.sharePersonalInformation: 'MB36083'
  };

  /// 点击进行跳转判断处理
  Future<void> handleClickEvent(BuildContext context,
      {required String text}) async {
    CustomToast.cancelToast();
    if (text == CONSTANT.CUSTOMER_PHONE) {
      final Uri uri = Uri.parse('tel:************');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
      return;
    }
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      CustomToast.showToast('网络不可用');
      return;
    }
    if (eventClickList[text] != null) {
      final String tempEventId = eventClickList[text]!;
      Trace.traceEvent(eventId: tempEventId);
    }

    final Map<String, VoidCallback> actions = <String, VoidCallback>{
      CONSTANT.SCORE: () {
        Provider.of<ScoreDialogViewModel>(context, listen: false)
            .setScoreDialogStatus(true);
      },
      CONSTANT.ICP_TITLE: () => VDN.gotoPage(CONSTANT.ICP_URL),
      CONSTANT.SERVICE_ITEMS: () => VDN.gotoPage(CONSTANT.SERVICE_ITEMS_URL +
          DateTime.now().millisecondsSinceEpoch.toString()),
      CONSTANT.INFO_PROTECT_POLICY: () => VDN.gotoPage(
          CONSTANT.INFO_PROTECT_POLICY_URL,
          params: <String, dynamic>{}),
      CONSTANT.personalInformation: () =>
          VDN.gotoPage(CONSTANT.personalInformationUrl),
      CONSTANT.sharePersonalInformation: () =>
          VDN.gotoPage(CONSTANT.sharePersonalInformationUrl),
      CONSTANT.CHECK_UPDATE: () =>
          Provider.of<AppUpdateViewModel>(context, listen: false)
              .handleUpdateStatus(context),
      CONSTANT.APPLET: () async {
        try {
          // 获取是否为游客模式
          final bool isGuestMode = await Storage.getBooleanValue('isGuestMode');
          if (isGuestMode) {
            CustomToast.showToast('请在微信小程序搜索查看');
            return;
          }

          if (context.mounted &&
              Provider.of<CheckAppStatusViewModel>(context, listen: false)
                      .isInstalledWeixin ==
                  false) {
            CustomToast.showToast('未安装APP');
            return;
          }
          UmengPlugin.pullUpWxMiniProgram(CONSTANT.WX_USER_NAME, '')
              .then((String data) {})
              .catchError((dynamic onError) {
            // CustomToast.showToast(context, '未安装APP');
          });
        } catch (e) {
          CustomToast.showToast('未安装APP');
        }
      }
    };

    return actions[text]!();
  }
}
