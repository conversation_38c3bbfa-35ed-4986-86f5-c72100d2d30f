import 'dart:io';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:about_us/utils/constant.dart';
import 'package:about_us/utils/toast.dart';
import 'package:flutter/material.dart';
import 'package:plugin_upgrade/plugin_upgrade.dart';
import 'package:storage/storage.dart';

class AppUpdateViewModel with ChangeNotifier {
  String _versionLabel = '';
  bool _isShowNew = false;
  bool _isShowUpdateDialog = false;
  String _showType = 'showNothing';

  String get versionLabel => _versionLabel;
  bool get isShowNew => _isShowNew;
  bool get isShowUpdateDialog => _isShowUpdateDialog;
  String get showType => _showType;

  // 设置版本显示
  void setVersionLabel(String versionLabel) {
    _versionLabel = versionLabel;
    notifyListeners();
  }

  void setIsShowUpdateDialog(bool value) {
    _isShowUpdateDialog = value;
    notifyListeners();
  }

  void setShowType(String value) {
    _showType = value;
    notifyListeners();
  }

  // 版本更新
  Future<void> getLastestVersionInfo(BuildContext context) async {
    final AppInfoModel appinfo = await AppInfoPlugin.getAppInfo();
    final String versionTitle = CONSTANT.VERSION_NUMBER + appinfo.appVersion;
    try {
      final bool result = await UpgradePlugin.checkFullVersion(false);
      await Storage.putIntegerValue(
          CONSTANT.NEWVERSION_REDPOINT, result ? 1 : 0);
      if (result) {
        // 存在新版本
        _isShowNew = true;
        setVersionLabel(versionTitle);
      } else {
        // 是最新版本
        _isShowNew = false;
        setVersionLabel(versionTitle + CONSTANT.VERSION_LATEST);
      }
    } catch (err) {
      setVersionLabel(versionTitle);
    } finally {
      notifyListeners();
    }
  }

  // 点击版本更新
  Future<void> handleUpdateStatus(BuildContext context) async {
    try {
      final bool result = await UpgradePlugin.checkFullVersion(true);
      if (Platform.isIOS && result == false) {
        CustomToast.showToast('当前APP已经是最新版本');
      }
    } catch (err) {
      // print('_checkFullVersion error: $err');
    } finally {
      notifyListeners();
    }
  }
}
