import 'dart:io';

import 'package:about_us/utils/vdn.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:about_us/utils/constant.dart';

class ScoreDialogViewModel with ChangeNotifier {
  bool _scoreDialogStatus = false;

  bool get scoreDialogStatus => _scoreDialogStatus;

  // 设置去评分dialog状态
  void setScoreDialogStatus(bool value) async {
    _scoreDialogStatus = value;
    notifyListeners();
  }

  // 去反馈
  void scoreFeedback() async {
    setScoreDialogStatus(false);
    VDN.gotoPage(CONSTANT.SCORE_FEEDBACK);
  }

  // 点个攒
  void scoreLike() async {
    setScoreDialogStatus(false);
    if (Platform.isAndroid) {
      VDN.gotoPage(CONSTANT.SCORE_LIKE_ANDROID);
    } else {
      await _launchInBrowser(CONSTANT.SCORE_LIKE);
    }
  }

  Future<void> _launchInBrowser(String url) async {
    try {
      if (!await launch(
        url,
        forceSafariVC: false,
        forceWebView: false,
        headers: <String, String>{'my_header_key': 'my_header_value'},
      )) {
        throw 'Could not launch $url';
      }
    } catch (e) {}
  }
}
