import 'package:flutter/material.dart';
import 'package:upsystem/upsystem.dart';

class CheckAppStatusViewModel with ChangeNotifier {
  bool _isInstalledWeixin = false;

  bool get isInstalledWeixin => _isInstalledWeixin;

  // 验证手机是否安装微信
  void getAppInstallWeixinStatus() async {
    // final _appList = [
    //   {'type': AppType.wechat, 'name': '微信', 'status': ''},
    //   {'type': AppType.qq, 'name': 'QQ', 'status': ''},
    //   {'type': AppType.weibo, 'name': '微博', 'status': ''},
    // ];
    var info = {'type': AppType.wechat, 'name': '微信', 'status': ''};
    final result = await UpSystem.isAppInstalled(info['type'] as AppType);
    _isInstalledWeixin = result;
    notifyListeners();
  }
}
