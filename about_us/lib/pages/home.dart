import 'dart:io';

import 'package:about_us/utils/constant.dart';
import 'package:about_us/utils/vdn.dart';
import 'package:about_us/viewmodels/app_update_view_model.dart';
import 'package:about_us/viewmodels/check_app_status_view_model.dart';
import 'package:about_us/viewmodels/score_dialog_view_model.dart';
import 'package:about_us/viewmodels/switch_click_view_model.dart';
import 'package:about_us/widgets/appbar.dart';
import 'package:about_us/widgets/continuous_tap.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
// 适配屏幕模块
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:uplustrace/uplustrace.dart';

import '../utils/toast.dart';
import '../widgets/bold_text_style.dart';

GlobalKey aboutUsPageKey = GlobalKey(debugLabel: 'aboutUsPage');

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  // _HomePage createState() => _HomePage();
  _HomePage createState() => _HomePage();
}

class _HomePage extends State<HomePage> {

  @override
  void initState() {
    super.initState();
    UplusTrace.startTrack(CONSTANT.TRACE_ABOUT_US);
    CustomToast.init(context);
    _initData();
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      UplusTrace.finishTrack(CONSTANT.TRACE_ABOUT_US);
    });
  }

  void _initData() {
    Provider.of<AppUpdateViewModel>(context, listen: false)
        .getLastestVersionInfo(context);
    Provider.of<CheckAppStatusViewModel>(context, listen: false)
        .getAppInstallWeixinStatus();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 构建模块顶部
  Widget _buildTopContent() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 48),
      child: Column(
        children: <Widget>[
          Image.asset(
            'assets/images/logo.webp',
            width: 64,
            height: 64,
            package: 'about_us',
          ),
          const SizedBox(
            height: 12,
          ),
          ContinuousTapGestureDetector(
            child: SizedBox(
              height: 17,
              child: Text(
                Provider.of<AppUpdateViewModel>(context).versionLabel,
                style: TextStyle(
                  fontSize: 12,
                  color: AppSemanticColors.item.secondary,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.none,
                ),
              ),
            ),
            listener: ContinuousTapListener(
              numberOfTapsRequired: 10,
              onContinuousTap: () {
                VDN.gotoPage(Platform.isIOS
                    ? CONSTANT.GRAY_SCALE_URL_IOS
                    : CONSTANT.GRAY_SCALE_URL);
              },
            ),
          )
        ],
      ),
    );
  }

  // 构建中间卡片
  Widget _buildProfileList() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        child: Column(
          children: <Widget>[
            _buildProfileTextWidget(
                leftText: CONSTANT.CHECK_UPDATE,
                isShowNew: Provider.of<AppUpdateViewModel>(context).isShowNew),
            _buildProfileTextWidget(
                leftText: CONSTANT.ICP_TITLE, rightText: '鲁ICP备14026385号-16A'),
            _buildProfileTextWidget(leftText: CONSTANT.SERVICE_ITEMS),
            _buildProfileTextWidget(leftText: CONSTANT.INFO_PROTECT_POLICY),
            _buildProfileTextWidget(leftText: CONSTANT.personalInformation),
            _buildProfileTextWidget(
                leftText: CONSTANT.sharePersonalInformation),
            _buildProfileTextWidget(leftText: CONSTANT.APPLET),
            _buildProfileTextWidget(
                leftText: CONSTANT.CUSTOMER_PHONE, rightText: '************'),
          ],
        ),
      ),
    );
  }

  // 构建模块底部
  Widget _buildBottomContent() {
    return Column(
      children: <Widget>[
        _buildVersionModule(textContext: CONSTANT.COMPANY_COPYRIGHT),
        const SizedBox(height: 4),
        _buildVersionModule(textContext: CONSTANT.COPYRIGHT),
      ],
    );
  }

  /// 构建底部版权所有模块
  Widget _buildVersionModule({required String textContext}) {
    return SizedBox(
      height: 14,
      child: Text(
        textContext,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w400,
          color: AppSemanticColors.item.secWeaken,
          decoration: TextDecoration.none,
        ),
      ),
    );
  }

  /// 构建显示文字widget
  Widget _buildProfileTextWidget(
      {required String leftText,
      bool isShowNew = false,
      String rightText = ''}) {
    return PressableOverlayWithTapWidget(
        overlayClick: () {
          Provider.of<SwitchClickViewModel>(context, listen: false)
              .handleClickEvent(context, text: leftText);
        },
        child: Container(
          height: 54,
          width: double.infinity,
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Text(
                    leftText,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (isShowNew)
                    Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 6.w),
                      width: 32.w,
                      height: 16.w,
                      decoration: const BoxDecoration(
                        color: Color.fromRGBO(237, 40, 86, 1),
                        borderRadius: BorderRadius.all(Radius.circular(9.0)),
                      ),
                      child: Text(
                        'NEW',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.sp,
                        ),
                      ),
                    ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  if (rightText.isNotEmpty)
                    Text(
                      rightText,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppSemanticColors.item.secWeaken,
                      ),
                    ),
                  const SizedBox(width: 4),
                  // 右箭头
                  Image.asset(
                    'assets/images/right-arrow.webp',
                    width: 16,
                    height: 16,
                    package: 'about_us',
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  /// [去评分]对话框背景
  Widget _showScoreDialogBg() {
    return Positioned(
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
      ),
    );
  }

  /// [去评分]对话框内容
  Widget _showScoreDialog() {
    return Positioned(
        top: (MediaQuery.of(context).size.height - 246.w) / 2,
        left: (MediaQuery.of(context).size.width - 270.w) / 2,
        child: Column(
          children: <Widget>[
            Container(
              width: 270.w,
              margin: EdgeInsets.only(bottom: 16.w),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              child: Column(
                children: <Widget>[
                  Image.asset(
                    'assets/images/dialog_bg_3.png',
                    width: 270,
                    height: 124,
                    package: 'about_us',
                  ),
                  Container(
                    padding: EdgeInsets.all(16.w),
                    height: 106.w,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Column(
                      children: <Widget>[
                        Text(
                          '亲，点个赞吧！',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 17.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(0, 0, 0, 0.8),
                            decoration: TextDecoration.none,
                          ),
                        ),
                        Padding(padding: EdgeInsets.only(top: 12.w)),
                        Text(
                          '如果觉得海尔智家好用，请给我们点个赞吧，感谢您的支持',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(0, 0, 0, 0.6),
                            decoration: TextDecoration.none,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1,
                          color: Color.fromRGBO(0, 0, 0, 0.07),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    height: 46.w,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            Provider.of<ScoreDialogViewModel>(context,
                                    listen: false)
                                .scoreFeedback();
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 12.w),
                            width: 135.w,
                            decoration: const BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  width: 0.5,
                                  color: Color.fromRGBO(0, 0, 0, 0.07),
                                ),
                              ),
                            ),
                            child: Text(
                              '去反馈',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 17.sp,
                                color: const Color.fromRGBO(0, 0, 0, 0.6),
                                fontWeight: FontWeight.w400,
                                decoration: TextDecoration.none,
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Provider.of<ScoreDialogViewModel>(context,
                                    listen: false)
                                .scoreLike();
                          },
                          child: Container(
                            width: 135.w,
                            padding: EdgeInsets.symmetric(vertical: 12.w),
                            child: Text(
                              '点个赞',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 17.sp,
                                color: const Color.fromRGBO(34, 131, 226, 1),
                                fontWeight: FontWeight.w400,
                                decoration: TextDecoration.none,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Provider.of<ScoreDialogViewModel>(context, listen: false)
                    .setScoreDialogStatus(false);
              },
              child: Image.asset(
                'assets/images/close.png',
                width: 30,
                height: 30,
                package: 'about_us',
              ),
            ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    double paddingBottom = MediaQuery.of(context).padding.bottom;
    paddingBottom = paddingBottom > 0 ? paddingBottom : 12;
    return Stack(
      children: <Widget>[
        Scaffold(
          key: aboutUsPageKey,
          backgroundColor: AppSemanticColors.background.secondary,
          appBar: MyAppBar(
            title: CONSTANT.TITLE,
            handleBackEvent: () => VDN.close(),
            rightActions: const <Widget>[],
          ),
          body: DefaultTextStyle(
            style: TextStyle(
              color: AppSemanticColors.item.primary,
              fontWeight: FontWeight.w400,
              fontFamilyFallback: fontFamilyFallback(),
            ),
            child: ScrollConfiguration(
              behavior: CusBehavior(),
              child: CustomScrollView(
                physics: const ClampingScrollPhysics(),
                slivers: <Widget>[
                  SliverToBoxAdapter(child: _buildTopContent()),
                  SliverToBoxAdapter(child: _buildProfileList()),
                  SliverFillRemaining(
                    hasScrollBody: false,
                    fillOverscroll: true,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                      Padding(
                        padding:
                            EdgeInsets.only(top: 40, bottom: paddingBottom),
                        child: _buildBottomContent(),
                      )
                    ]),
                  ),
                ],
              ),
            ),
          ),
        ),
        Consumer<ScoreDialogViewModel>(
          builder: (BuildContext context, ScoreDialogViewModel provider,
                  Widget? child) =>
              provider.scoreDialogStatus ? _showScoreDialogBg() : Container(),
        ),
        Consumer<ScoreDialogViewModel>(
          builder: (BuildContext context, ScoreDialogViewModel provider,
                  Widget? child) =>
              provider.scoreDialogStatus ? _showScoreDialog() : Container(),
        )
      ],
    );
  }
}

class CusBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    if (Platform.isAndroid || Platform.isFuchsia) return child;
    return super.buildOverscrollIndicator(context, child, details);
  }
}
