import 'package:flutter/material.dart';

/**
 * demo code
 * ContinuousTapGestureDetector(
	child: Text(''),
	listener: ContinuousTapListener(
		numberOfTapsRequired: 10,
		onContinuousTap: () {
			todo...
		},
	),
),
 */

/// 连续点击手势封装
class ContinuousTapGestureDetector extends GestureDetector {
  final Widget child;
  final ContinuousTapListener listener; // 连续点击监听回调函数

  ContinuousTapGestureDetector({
    required this.child,
    required this.listener,
  }) : super(
          child: child,
          onTap: () {
            listener.onTapListening();
          },
        );
}

class ContinuousTapListener {
  /// 需要连点的次数， 默认为 1 次
  final int numberOfTapsRequired;

  /// 满足连点条件时的回调
  final void Function() onContinuousTap;

  /// 上一次点击的时间戳
  int _lastTapTime = 0;

  /// 已记录的点击次数
  int _numberOfTapRecorded = 0;

  /// 两次点击的最大时间间隔
  int _maxMillisecondsBetweenTwoTaps = 350;

  ContinuousTapListener({
    this.numberOfTapsRequired = 1,
    required this.onContinuousTap,
  });

  /// 点击事件的监听函数
  void onTapListening() {
    final now = DateTime.now().millisecondsSinceEpoch;
    if (_lastTapTime > 0 &&
        now - _lastTapTime > _maxMillisecondsBetweenTwoTaps) {
      _resetRecord(time: now, count: 1);
    } else {
      _lastTapTime = now;
      _numberOfTapRecorded += 1;
      _checkAvailable();
    }
  }

  /// 重置记录
  void _resetRecord({int time = 0, int count = 0}) {
    _lastTapTime = time;
    _numberOfTapRecorded = count;
  }

  /// 验证是否满足预设条件，如果满足则执行回调函数
  void _checkAvailable() {
    if (_numberOfTapRecorded >= numberOfTapsRequired) {
      _resetRecord();
      onContinuousTap();
    }
  }
}
