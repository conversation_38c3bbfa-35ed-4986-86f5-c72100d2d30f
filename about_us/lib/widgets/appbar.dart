import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import 'bold_text_style.dart';

class MyAppBar extends StatelessWidget implements PreferredSizeWidget {
  const MyAppBar({
    super.key,
    required this.title,
    required this.handleBackEvent,
    required this.rightActions,
  });

  /// appbar 标题栏显示的标题
  final String title;

  /// appbar 返回按钮点击事件
  final void Function() handleBackEvent;

  /// appbar 右侧操作按钮
  final List<Widget> rightActions;

  @override
  Size get preferredSize => const Size.fromHeight(44.0);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark),
      backgroundColor: AppSemanticColors.background.secondary,
      title: Text(
        title,
        style: TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w500,
          color: AppSemanticColors.item.primary,
          fontFamilyFallback: fontFamilyFallback(),
        ),
      ),
      elevation: 0,
      scrolledUnderElevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Image.asset(
          'assets/images/back.webp',
          width: 24,
          height: 24,
          package: 'about_us',
        ),
        onPressed: handleBackEvent,
      ),
      actions: rightActions,
    );
  }
}
