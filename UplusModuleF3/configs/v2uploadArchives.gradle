subprojects {
    apply plugin: 'maven'

// 这里不需要artifacts，uploadArchives命令会自动生成并上传./build/outputs/flutter-release.aar，不然出现下面错误
// A POM cannot have multiple artifacts with the same type and classifier
//artifacts {
//    archives file('./build/outputs/flutter-release.aar')
//}

    uploadArchives {
        repositories {
            mavenDeployer {
                //println "==maven url: ${artGroupId}:${artifactId}:${artVersion}"

                repository(url: 'file:///Users/<USER>/Work/maven/repository')

                pom.version = '2.0.0'

            }
        }
    }
}