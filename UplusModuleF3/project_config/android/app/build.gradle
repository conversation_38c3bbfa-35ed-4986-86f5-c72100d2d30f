def flutterPluginVersion = 'managed'

apply plugin: 'com.android.application'
apply plugin: 'com.growingio.android'

android {
    compileSdkVersion 29

    compileOptions {
        sourceCompatibility 1.8
        targetCompatibility 1.8
    }

    defaultConfig {
        applicationId "com.haier.uhome.uplusmodule"
        minSdkVersion 22
        targetSdkVersion 26
        versionCode 2020111401.toInteger()
        versionName "6.23.0"
        resValue("string", "growingio_project_id", "8153487f4c347830")
        resValue("string", "growingio_url_scheme", "growing.fcb4472d765eb940")
        ndk {
            //选择要添加的对应cpu类型的.so库。
            abiFilters 'armeabi'
        }
        manifestPlaceholders = [

                SVR_ENVIRONMENT: "SHENGCHAN",
                APP_KEY        : "f50c76fbc8271d361e1f6b5973f54585",
                HR_UC_CID      : "uplusappadrd",
                HR_UC_SEC      : "egm_r2orAAyazr",
                HR_UC_URL      : "https://account-api.haier.net",

//                SVR_ENVIRONMENT: "YANSHOU",
//                APP_KEY        : "f50c76fbc8271d361e1f6b5973f54585",
//                HR_UC_URL      : "https://testaccount.haier.com",
//                HR_UC_CID      : "uplusappadrd",
//                HR_UC_SEC      : "egm_r2orAAyazr",

                APP_ID         : "MB-UZHSH-0000",
                JPUSH_PKGNAME  : "com.haier.uhome.uplus",
                JPUSH_APPKEY   : "2e3ac5fa492b670366aaac2a",  //JPush上注册的包名对应的appkey.
                JPUSH_CHANNEL  : "developer-default",   //暂时填写默认值即可.

                MEIZU_APPKEY   : "MZ-74e2b047afd6452996b3694cfad3dd77",
                MEIZU_APPID    : "MZ-118820",
                XIAOMI_APPKEY  : "MI-*************",
                XIAOMI_APPID   : "MI-2882303761517332372",
                HUAWEI_APPID   : "*********",
                OPPO_APPKEY    : "OP-5omXqRoGaMo8K88SkK4k8cSS4",
                OPPO_APPID     : "OP-2022439",
                OPPO_APPSECRET : "OP-ebc9a0aC91fDF04CD6531F0165aDe783",
                VIVO_APPID     : "14666",
                VIVO_APPKEY    : "0975fb11-7b8f-4305-afce-6d286c05f9c6",
                qqappid        : "1104627851",
        ]
        javaCompileOptions {
            annotationProcessorOptions {
                includeCompileClasspath true
            }
        }
        lintOptions {
            disable 'InvalidPackage'
        }
    }

    signingConfigs {
        release {
            keyAlias 'haierapk-release-key.keystore'
            keyPassword '&zk7eVbTOf#$Cw8w'
            storeFile file("../sign/HaierAPK-release-key.keystore")
            storePassword '&zk7eVbTOf#$Cw8w'
        }

        debug {
            keyAlias 'haierapk-release-key.keystore'
            keyPassword '&zk7eVbTOf#$Cw8w'
            storeFile file("../sign/HaierAPK-release-key.keystore")
            storePassword '&zk7eVbTOf#$Cw8w'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }
}
buildDir = new File(rootProject.projectDir, "../build/host")
dependencies {
    implementation project(':flutter')
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation ('com.haier.uhome.uplus:UPInitKit:*******.2022030401') {
        exclude group: 'com.alipay.android.phone.thirdparty', module: 'utdid-build'
    }
    implementation ('com.haier.uhome.uplus:Upluskit:********.2022011401') {
        exclude group: 'com.alipay.android.phone.thirdparty', module: 'utdid-build'
    }
    implementation 'com.haier.uhome.uplus.flutterapp:flutter:1.17.6'
    api 'com.haier.uhome.uplus:flutterBasecore:1.0.8'
    implementation project(path: ':vdn')
    implementation project(path: ':flutter_boost')
//    implementation 'com.haier.uhome.uplus:upplugin-share:3.1.17'
    implementation 'com.haier.uhome.uplus:upcomponent:1.3.19'
    implementation 'com.haier.uhome.uplus:upplugin-core:3.1.17'
    annotationProcessor 'com.github.bumptech.glide:annotations:4.9.0'
    implementation 'com.haier.uhome.uplus:UpCrash:*******.2023013101'
    implementation 'com.haier.uhome.uplus:upCrashLogPlugin:*******.2023013101'
    implementation 'com.haier.uhome.uplus:upResourcePlugin:*******.2023020301'
    implementation 'com.haier.uhome.uplus:upresource:********.2023020301'
    implementation 'com.haier.uhome.uplus:upresource-core:********.2023020302'

}


configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    resolutionStrategy.force "com.haier.uhome.uplus:upplugin-core:3.1.17"
//    resolutionStrategy.force "com.haier.uhome.uplus:upplugin-share:3.1.17"
    resolutionStrategy.force "com.haier.uhome.uplus:upcomponent:1.3.19"
}

configurations {
    all*.exclude group:'com.tencent.tauth', module: 'qqopensdk'
    all*.exclude group:'com.sina.weibo.sdk', module: 'core'
}

