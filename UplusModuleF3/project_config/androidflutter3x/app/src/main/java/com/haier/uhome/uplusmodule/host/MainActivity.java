package com.haier.uhome.uplusmodule.host;

import android.content.Intent;

import com.umeng.socialize.UMShareAPI;

import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import static com.idlefish.flutterboost.containers.FlutterActivityLaunchConfigs.EXTRA_URL;

public class MainActivity extends FlutterBoostActivity {

    @Override
    public String getUrl() {
        String contentUrl;
        if (!getIntent().hasExtra(EXTRA_URL)) {
            contentUrl = "flutter://index.html";
        } else {
            contentUrl = getIntent().getStringExtra(EXTRA_URL);
        }
        return contentUrl;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
    }
}
