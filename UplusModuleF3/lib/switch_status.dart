import 'package:function_toggle/function_toggle.dart';

class SwitchStatusControlState {
  SwitchStatusControlState.fromJson(Map<dynamic, dynamic> json) {
    if (json.containsKey('switchStatus') && json['switchStatus'] is bool) {
      switchStatus = json['switchStatus'] as bool;
    }
    if (json.containsKey('delayedTime') && json['delayedTime'] is int) {
      delayedTime = json['delayedTime'] as int;
    }
  }
  bool switchStatus = false;
  int delayedTime = 500;

  @override
  String toString() {
    return '{switchStatus: $switchStatus, delayedTime: $delayedTime}';
  }
}

class CommonRequest {
  /// 查询配置文件开关状态，是否走新增加载默认主题逻辑，开关默认关闭
  static Future<SwitchStatusControlState> checkSwitchStatusToggle() async {
    SwitchStatusControlState switchStatusControlState =
        SwitchStatusControlState.fromJson({});
    try {
      switchStatusControlState = await FunctionToggle.instance
          .getFunctiontoggleModel<SwitchStatusControlState>('Switch_Status',
              (Map<dynamic, dynamic> json) {
        return SwitchStatusControlState.fromJson(json);
      });
    } catch (e) {}
    return switchStatusControlState;
  }
}
