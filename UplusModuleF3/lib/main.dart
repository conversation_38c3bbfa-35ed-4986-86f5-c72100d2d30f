import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_main/main_lib.dart';
import 'package:crashlog/crashlog.dart';
import 'package:crashlog/model/crashlog_model.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:multiengines/route_manager.dart';

import 'package:flutter_main/config/route_list.dart';

import 'switch_status.dart';

// 异常上报
void _reportError(Object obj, StackTrace? stack) {
  CommonRequest.checkSwitchStatusToggle().then((value) {
    print('SwitchStatusToggle-${value.switchStatus}');
    if (value.switchStatus) {
      String reason = obj.toString();
      String name = '';
      if (reason.contains('Exception')) {
        name = reason.substring(0, reason.indexOf('Exception') + 9);
      } else if (reason.contains(':')) {
        name = reason.substring(0, reason.indexOf(':'));
      } else {
        name = reason.length > 15 ? reason.substring(0, 15) : reason;
      }
      name = 'Flutter_$name';
      List<String>? stackTrace = stack == null ? [] : [stack.toString()];

      //调用API上报异常信息
      WriteExceptionModel exception =
          WriteExceptionModel(name, reason, stackTrace: stackTrace);

      Crashlog.writeException(exception);
    }
  });
}

void main(List<String>? args) {
  FlutterError.onError = (FlutterErrorDetails details) {
    print("未捕获的异常---${details.exception}-${details.stack}");
    _reportError(details.exception, details.stack);
    //mpaas日志记录
    LogModle modle =
        LogModle("error", "flutter", "${details.exception}-${details.stack}");
    Log.printLog(modle);
  };
  runZonedGuarded(() {
    print("main start with args: $args");
    WidgetsFlutterBinding.ensureInitialized();
    RouteManager.configRoutesAndGetChild(args, RouteList.routes)
        .then((Widget? childWidget) {
      runApp(MyApp(
        child: childWidget ??
            Container(
              color: Colors.red,
            ),
        args: args,
      ));
    });
  }, (Object obj, StackTrace stack) {
    print("未捕获的异常------$obj-$stack");
    _reportError(obj, stack);
    //mpaas日志记录
    LogModle modle = LogModle("error", "flutter", "$obj-$stack");
    Log.printLog(modle);
  });
}
