import 'dart:convert';

import 'package:dio/dio.dart';

import 'constant.dart';
import 'log.dart';

class HeaderInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.connectTimeout = const Duration(seconds: 30);
    handler.next(options);
  }
}

class ResponseInterceptors extends InterceptorsWrapper {
  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }
}

class LogsInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    return handler.next(options);
  }

  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '请求异常: $err');
    return handler.next(err);
  }
}

/// http 请求封装
class HttpManager {
  // 使用默认配置
  HttpManager() {
    _dio.interceptors.add(HeaderInterceptors());
    _dio.interceptors.add(LogsInterceptors());
    _dio.interceptors.add(ResponseInterceptors());
  }

  final Dio _dio = Dio();

  /// 统一封装 get 请求
  Future<Map<dynamic, dynamic>> getData(
    String url, {
    Map<String, dynamic>? params,
    Options? options,
    Map<String, dynamic>? header,
  }) async {
    final Map<String, dynamic> headers = <String, dynamic>{};
    if (header != null) {
      headers.addAll(header);
    }
    final Options option = options ?? Options();
    if (option.headers != null) {
      option.headers!.addAll(headers);
    } else {
      option.headers = headers;
    }
    Response<dynamic> response;
    try {
      response = await _dio.get(url, queryParameters: params, options: option);
      dynamic data = response.data ?? <dynamic, dynamic>{};
      if (data is String) {
        data = jsonDecode(data);
      }
      if (data is! Map<dynamic, dynamic>) {
        return <dynamic, dynamic>{};
      }
      return data;
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, dynamic>{'fn': 'dio getData', 'err': err});
      rethrow;
    }
  }

  /// 统一封装 post 请求
  Future<Map<dynamic, dynamic>> postData(
    String url, {
    Map<String, dynamic>? params,
    Options? options,
    Map<String, dynamic>? header,
    bool query = false,
  }) async {
    final Map<String, dynamic> headers = <String, dynamic>{};
    if (header != null) {
      headers.addAll(header);
    }
    final Options option = options ?? Options();
    if (option.headers != null) {
      option.headers!.addAll(headers);
    } else {
      option.headers = headers;
    }
    Response<dynamic> response;
    try {
      DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        'fn': 'dio postData',
        'start': <dynamic>{url, params}
      });
      response = await _dio.post(url,
          data: params,
          queryParameters: query ? params : null,
          options: option);

      dynamic data = response.data ?? <dynamic, dynamic>{};
      if (data is String) {
        data = jsonDecode(data);
      }
      if (data is! Map<dynamic, dynamic>) {
        return <dynamic, dynamic>{};
      }
      DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        'fn': 'dio postData',
        'success': <dynamic>{url, params, data}
      });
      return data;
    } catch (err) {
      DevLogger.error(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        'fn': 'dio postData',
        'err': <dynamic>{url, params, err}
      });
      rethrow;
    }
  }
}

final HttpManager httpManager = HttpManager();
