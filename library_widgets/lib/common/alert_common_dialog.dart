import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum InfoAlignment { start, center, end }

class AlertCommonDialog {
  static void showConfirmAlertDialog(BuildContext context,
      {required String title,
      required String info,
      InfoAlignment? infoAlignment,
      String? cancelTitle,
      required String confirmTitle,
      required void Function() confirmCallback,
      void Function()? cancelCallback}) {
    showDialog<dynamic>(
        barrierDismissible: false, //为true点击蒙层退出
        context: context,
        builder: (BuildContext context) {
          return SimpleDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(14))),
            title: title.isNotEmpty ? _alertTitle(title) : null,
            titlePadding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              top: 19.w,
            ),
            contentPadding: EdgeInsets.zero,
            children: <Widget>[
              _alertInfo(info, infoAlignment),
              Divider(
                height: 1.w,
              ),
              Row(
                children: <Widget>[
                  if (cancelTitle is String)
                    Expanded(
                      child: _alertButton(
                          cancelTitle, const Color.fromRGBO(102, 102, 102, 1),
                          () {
                        Navigator.pop(context);
                        if (cancelCallback != null) {
                          cancelCallback();
                        }
                      }),
                    ),
                  if (cancelTitle is String)
                    Container(
                      width: 0.5.w,
                      height: 43.5.w,
                      color: const Color.fromRGBO(238, 238, 238, 1),
                    ),
                  Expanded(
                    child: _alertButton(
                        confirmTitle, const Color.fromRGBO(34, 131, 226, 1),
                            () {
                      Navigator.pop(context);
                      confirmCallback();
                    }),
                  ),
                ],
              ),
            ],
          );
        });
  }

  static Widget _alertTitle(String title) {
    return Text(
      title,
      style: TextStyle(
          fontSize: 17.sp,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w400),
      textAlign: TextAlign.center,
    );
  }

  static Widget _alertInfo(String info, InfoAlignment? infoAlignment) {
    Alignment alignment = Alignment.center;
    TextAlign textAlign = TextAlign.center;
    if (infoAlignment == InfoAlignment.start) {
      alignment = Alignment.centerLeft;
      textAlign = TextAlign.start;
    } else if (infoAlignment == InfoAlignment.end) {
      alignment = Alignment.centerRight;
      textAlign = TextAlign.end;
    }

    return Container(
      padding: EdgeInsets.only(left: 16.w, top: 9.w, right: 16.w, bottom: 14.w),
      alignment: alignment,
      child: Text(
        info,
        textAlign: textAlign,
        style: TextStyle(
          fontSize: 12.sp,
          color: const Color.fromRGBO(102, 102, 102, 1),
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  static Widget _alertButton(
      String title, Color textColor, void Function() callback) {
    return TextButton(
      child: Text(
        title,
        style: TextStyle(
          fontSize: 17.sp,
          color: textColor,
          fontWeight: FontWeight.w400,
        ),
        textAlign: TextAlign.center,
      ),
      onPressed: () {
        callback();
      },
    );
  }
}
