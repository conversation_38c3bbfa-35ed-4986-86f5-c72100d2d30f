/*
 * @Author: long<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-06-17 09:44:23
 * @Description: 
 */
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:storage/storage.dart';

import 'constant.dart';

class ToastHelperLibrary {
  static OverlayEntry? _entry;
  static Timer? _timer;

  // 是否可以弹toast, 当服务tab为当前tab时，才可以弹
  static bool _canShowToast = true;

  static Future<void> getTemporyStorage() async {
    Storage.addDataListener(CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET,
        (_, __) async {
      //在场景tab下，我的和推荐时不显示toast
      final String isSceneshow = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET);
      final String changeTabtype = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);
      if (json.decode(isSceneshow)['value'] == '1' &&
          json.decode(changeTabtype)['value'] != 'recommend') {
        _canShowToast = true;
      } else {
        closeToast();
      }
    });
  }

  // 更新是否可以弹窗
  static void updateCanShow(bool value) {
    _canShowToast = value;
  }

  static void showToast(String? text, BuildContext context,
      [bool isLoading = false, int? remainTime]) {
    if (_canShowToast) {
      getTemporyStorage();
      closeToast();
      final TextStyle style = TextStyle(
        color: Colors.white,
        fontSize: 14.sp,
        fontWeight: FontWeight.normal,
        decoration: TextDecoration.none,
      );

      final Widget widget = Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 234.w),
          padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 12.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
            color: const Color.fromRGBO(0, 0, 0, .5),
          ),
          child: Text(
            text!,
            style: style,
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      );
      _entry = OverlayEntry(
        builder: (_) => widget,
      );
      Overlay.of(context).insert(_entry!);

      _timer = Timer(
          isLoading
              ? const Duration(seconds: 15)
              : remainTime != null
                  ? Duration(seconds: remainTime)
                  : const Duration(seconds: 2), () {
        _entry?.remove();
        _entry = null;
      });
    }
  }

  static void showToastWidget(Widget? text, BuildContext context,
      [bool isLoading = false, int? remainTime]) {
    if (_canShowToast) {
      getTemporyStorage();
      closeToast();

      final Widget widget = Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 234.w),
          padding: EdgeInsets.symmetric(vertical: 8.w, horizontal: 12.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
            color: const Color.fromRGBO(0, 0, 0, .5),
          ),
          child: text,
        ),
      );
      _entry = OverlayEntry(
        builder: (_) => widget,
      );
      Overlay.of(context).insert(_entry!);

      _timer = Timer(
          isLoading
              ? const Duration(seconds: 15)
              : remainTime != null
                  ? Duration(seconds: remainTime)
                  : const Duration(seconds: 2), () {
        _entry?.remove();
        _entry = null;
      });
    }
  }

  static void closeToast() {
    if (_entry != null && _timer != null) {
      _entry!.remove();
      _entry = null;
      _timer!.cancel();
      _timer = null;
    }
  }
}
