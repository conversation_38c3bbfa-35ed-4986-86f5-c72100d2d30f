// 我的列表--自动泛场景打开时-判断互斥逻辑
import 'dart:async';

import 'package:device_utils/api/api_response.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../model/mutex_scene_response.dart';
import '../model/scene_model.dart';
import 'api.dart';
import 'constant.dart';
import 'log.dart';
import 'toast_helper.dart';
import 'util.dart';

/// 打开自动场景开关时，是否打开互斥弹窗（自动泛场景打开）
/// @param {dynamic} item 操作开关的某项
/// @param {int} index 操作项在数组中的下表
/// @param {BuildContext} context
/// @param {Future} changeSceneItemIsOpen  更改操作项的isOpen属性值
Future<void> changeSceneOpentype(
  dynamic item,
  int index,
  BuildContext context,
  Future<void> Function(int, int) changeSceneItemIsOpen,
  AnimationController rotationController,
) async {
  String? mutexSceneName = '';
  if (item is! SceneModel) {
    return;
  }
  try {
    // 关闭自动场景,无需区分泛场景，直接关闭
    if (item.isOpen == 1) {
      operationSwitch(
          context, item, index, changeSceneItemIsOpen, rotationController);
      return;
    }
    // 打开自动泛场景
    // 1、判断是否为泛场景
    // 2、若是，则调用判断是否互斥的接口
    final String? familyId = item.familyId;
    final String? sceneId = item.id;
    final String? type = item.type;
    // TODO: sfj 待验证
    final Map<dynamic, dynamic> ret =
        await API.getMutexSceneList(familyId!, sceneId!, type);
    final MutexSceneResponse response = MutexSceneResponse.fromJson(ret);
    final List<String> mutexSceneNameArr = <String>[];
    final List<String> mutexSceneIdArr = <String>[];
    if (response.retCode == CONSTANT.REQUEST_SUCCESS_CODE) {
      if (response.data.isNotEmpty) {
        // 有互斥的场景，整理弹窗内容
        for (final MutexSceneData item in response.data) {
            mutexSceneNameArr.add(item.sceneName);
            mutexSceneIdArr.add(item.id);
        }

        mutexSceneName = mutexSceneNameArr.join(',');
        // 打开互斥场景弹窗
        if (context.mounted) {
          await mutuxDialog(context, '$mutexSceneName场景正在运行，是否切换', item, index,
              changeSceneItemIsOpen, rotationController);
        }
      } else {
        // 没有互斥的场景, 直接执行
        if (context.mounted) {
          operationSwitch(
              context, item, index, changeSceneItemIsOpen, rotationController);
        }
      }
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary,
        msg: <dynamic, dynamic>{'fn': 'changeSceneOpentype', 'err': err});
  }
}

/// 互斥弹窗
/// @param {BuildContext} context
/// @param {String} mutexSceneName 互斥弹窗显示内容
/// @param {dynamic} item 操作开关的某项
/// @param {int} index 操作项在数组中的下表
/// @param {Future} changeSceneItemIsOpen  更改操作项的isOpen属性值
Future<void> mutuxDialog(
    BuildContext sceneCardContext,
    String? mutexSceneName,
    dynamic item,
    int index,
    Future<void> Function(int, int) changeSceneItemIsOpen,
    AnimationController rotationController) async {
  try {
    showDialog<dynamic>(
        context: sceneCardContext,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CupertinoAlertDialog(
            title: Text(
              '温馨提示',
              style: TextStyle(fontSize: 15.0.sp, fontWeight: FontWeight.w400),
            ),
            content: Text(mutexSceneName!),
            actions: <Widget>[
              CupertinoDialogAction(
                child: const Text(
                  '取消',
                  style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.93)),
                ),
                onPressed: () {
                  Navigator.pop(context);
                  // print("取消");
                },
              ),
              CupertinoDialogAction(
                child: const Text('确定'),
                onPressed: () {
                  // print("确定");
                  operationSwitch(sceneCardContext, item, index, changeSceneItemIsOpen,
                      rotationController);
                  Navigator.pop(context);
                },
              ),
            ],
          );
        });
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary, msg: <String, Object>{'fn': 'mutuxDialog', 'err': err});
  }
}

/// 操作自动场景的switch开关，请求打开、关闭接口
/// @param {dynamic} item 操作开关的某项
/// @param {int} index 操作项在数组中的下表
/// @param {Future} changeSceneItemIsOpen  更改操作项的isOpen属性值
Future<void> operationSwitch(
    BuildContext context,
    dynamic item,
    int index,
    Future<void> Function(int, int) changeSceneItemIsOpen,
    AnimationController rotationController) async {
  if (item is! SceneModel) {
    return;
  }
  // TODO: sfj 待验证
  try {
    // if (item.sceneIds != null &&
    //     item.sceneIds.length != null &&
    //     item.sceneIds.length > 1) {
    final List<dynamic> sceneIds = item.sceneIds ?? <dynamic>[];
    if (sceneIds.isNotEmpty) {
      final List<dynamic> sceneList = <dynamic>[];
      // item.sceneIds.forEach((id) {
      //   sceneList.add({
      //     'familyId': item.familyId,
      //     'type': item.type,
      //     'sceneId': id,
      //     'status': item.isOpen == 1 ? '0' : '1'
      //   });
      // });
      for (final dynamic id in sceneIds) {
        sceneList.add(<dynamic, dynamic>{
          'familyId': item.familyId,
          'type': item.type,
          'sceneId': id,
          'status': item.isOpen == 1 ? '0' : '1'
        });
      }

      // TODO: sfj 待验证
      // final res = await API.batchManualOperation(sceneList);
      final Map<dynamic, dynamic> res =
          await API.batchManualOperation(sceneList);
      final ApiResponse response = ApiResponse.fromJson(res);
      if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS) {
        changeSceneItemIsOpen(index, item.isOpen == 0 ? 1 : 0);
        if (item.isOpen == 1) {
          // 打开
          rotationController.reset();
          rotationController.forward();
        }
      } else {
        if (response.retCode == '20410') {
          if (context.mounted) {
            ToastHelperLibrary.showToast('开启失败，缺少执行设备', context);
          }
        } else if (response.retCode == '20445') {
          if (context.mounted) {
            ToastHelperLibrary.showToast('缺少开启场景的必要设备', context);
          }
        } else {
          if (context.mounted) {
            handleRequestError(response, context);
          }
        }
      }
    } else {
      // TODO: sfj 待验证
      final Map<dynamic, dynamic> res = await API.manualOperation(
          item.familyId, item.id, item.type, item.isOpen == 1 ? '0' : '1');
      final ApiResponse response = ApiResponse.fromJson(res);
      if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS) {
        changeSceneItemIsOpen(index, item.isOpen == 0 ? 1 : 0);
        if (item.isOpen == 1) {
          // 打开
          rotationController.reset();
          rotationController.forward();
        }
      } else {
        if (response.retCode == '20410') {
          if (context.mounted) {
            ToastHelperLibrary.showToast('开启失败，缺少执行设备', context);
          }
        } else if (response.retCode == '20445') {
          if (context.mounted) {
            ToastHelperLibrary.showToast('缺少开启场景的必要设备', context);
          }
        } else {
          if (context.mounted) {
            handleRequestError(response, context);
          }
        }
      }
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary, msg: <String, Object>{'fn': 'operationSwitch', 'err': err});
  }
}
