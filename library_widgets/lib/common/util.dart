import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:crypto/crypto.dart';
import 'package:device_utils/api/api_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:trace/trace.dart';
import 'package:vdn/vdn.dart';

import 'constant.dart';
import 'log.dart';
import 'toast_helper.dart';


typedef VoidCallback = void Function();

///用户偏好设置
SharedPreferences? preferences;

enum DeviceStatus {
  Unknown,

  /// 故障
  Caution,

  /// 离线
  Offline,
}

/// vdn跳转防抖
class Debounce {
  //函数防抖初始时间, 点击间隔超过1s, 才再次执行
  static int lastTime = 0;
  static void run(VoidCallback action) {
    final int currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - lastTime > 500) {
      action();
    }
    lastTime = currentTime;
  }
}

// 获得设备的像素密度
double getDpr() {
  return ScreenUtil().pixelRatio;
}

// TODO(sfj): 待验证
// gio打点, 参数可选variable
Future<void> gioTrack(String eventId, [Map<String, dynamic>? variable]) async {
  try {
    if (variable is Map<String, dynamic>) {
      // 循环判断每一项是否为null或者空字串，是的话则修改value为'null'
      if (variable.isNotEmpty) {
        variable.forEach((String key, dynamic value) {
          if (value == null || value == '') {
            variable[key] = 'null';
          }
        });
      }
      DevLogger.debug(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        'fn': 'gioTrack',
        'data': <String, dynamic>{'eventId': eventId, 'variable': variable}
      });
      await Trace.traceEventWithVariable(eventId: eventId, variable: variable);
    } else {
      DevLogger.debug(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        'fn': 'gioTrack',
        'data': <String, dynamic>{'eventId': eventId}
      });
      await Trace.traceEvent(eventId: eventId);
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary,
        msg: <String, Object>{'fn': 'gioTrack', 'err': err});
  }
}

// vdn跳转
Future<void> goToPage(
    String? url, Function? closeSpaceDetailPage, BuildContext context) async {
  Debounce.run(() async {
    goToPageNoDebounce(url, closeSpaceDetailPage, context);
  });
}

// vdn跳转不要防抖
Future<void> goToPageNoDebounce(
    String? url, Function? closeSpaceDetailPage, BuildContext context) async {
  Future.delayed(const Duration(milliseconds: 100), () async {
    try {
      final IsOnline isOnline =
          await Network.isOnline();
      if (!isOnline.isOnline) {
        if (context.mounted) {
          ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
        }
      } else {
        DevLogger.debug(
            tag: CONSTANT.tagLibrary, msg: <String, String?>{'goToPage': url});
        if (closeSpaceDetailPage != null) {
          closeSpaceDetailPage();
        }
        await Vdn.goToPage(url!, params: <String, int>{'checkGuestMode': 1});
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary, msg: <String, Object>{'goToPage': err});
    }
  });
}

/// 函数防抖
///
/// [func]: 要执行的方法
/// [delay]: 要迟延的时长
/// [immediately]: 是否立即执行
Function debounce(Function func,
    [Duration delay = const Duration(milliseconds: 2000),
    bool immediately = true]) {
  Timer? timer;
  target() {
    if (timer?.isActive ?? false) {
      timer?.cancel();
    }
    // 立即执行
    if (immediately) {
      // 没有定时器，立即执行
      final bool callNow = timer == null;
      // 给定时器赋值
      timer = Timer(delay, () {
        timer!.cancel();
        timer = null;
      });
      if (callNow) {
        func.call();
      }
    } else {
      timer = Timer(delay, () {
        func.call();
      });
    }
  }
  return target;
}

// TODO(sfj): 待验证
Future<void> handleRequestError(dynamic error, BuildContext context) async {
  try {
    final IsOnline isOnline =
        await Network.isOnline();
    if (isOnline.isOnline) {
      final RegExp onlineReg =
          RegExp(r'(断开与互联网的连接|unable to resolve host|网络不可用)');
      if (context.mounted) {
        if (error is ApiResponse && onlineReg.hasMatch(error.retInfo)) {
          ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
        } else {
          ToastHelperLibrary.showToast(CONSTANT.REQUEST_ERROR, context);
        }
      }
    } else {
      if (context.mounted) {
        ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
      }
    }
  } catch (error) {}
}

// Http请求类
class HTTP {
  // hash string
  static const String HEX_STRING = '0123456789abcdef';

  // 获取时间戳
  static int getTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  // 设置请求头
  static Map<String, dynamic> commonHeaders() {
    return <String, String>{
      'Content-Type': 'application/json;charset=UTF-8',
      'timestamp': getTimestamp().toString(),
      'sequenceId': getTimestamp().toString(),
    };
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getSign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    final Uint8List bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    final List<int> list = sha256.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }
    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getMD5Sign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    final Uint8List bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    final List<int> list = md5.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }
    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }
}

// 拼接路径，推荐模版跳转展示类泛场景时使用
// http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?devicemac=${}&typeid=${}&fromName=changjing#/DIY --->
// http://uplus.haier.com/uplusapp/DeviceList/DetailView.html?devicemac=123123&typeid=543909&fromName=changjing#/DIY

Future<String> assembleUrl(String? deviceUI,
    Map<String, DeviceInfoModel>? devicesList, String? deviceId) async {
  String deviceUINew = '';
  DeviceInfoModel? deviceInfo;

  try {
    //  获取选中设备的全部信息
    if (devicesList != null) {
      deviceInfo = devicesList[deviceId];
    }
    if (deviceUI != null) {
      deviceUINew = deviceUI;
      // 替换${deviceId}
      if (deviceUI.indexOf(r'${deviceId}') > 0) {
        deviceUINew = deviceUI.replaceFirst(r'${deviceId}', deviceId ?? '');
      }
      // 替换${typeId}
      if (deviceUINew.indexOf(r'${typeId}') > 0) {
        if (deviceInfo != null) {
          deviceUINew =
              deviceUINew.replaceFirst(r'${typeId}', deviceInfo.wifiType);
        } else {
          deviceUINew = deviceUINew.replaceFirst(r'${typeId}', '');
        }
      }
      if (deviceUI.indexOf(r'${devicemac}') > 0) {
        deviceUINew = deviceUINew.replaceFirst(r'${devicemac}', deviceId ?? '');
      }
      return deviceUINew;
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary,
        msg: <String, Object>{'fn': 'assembleUrl', 'err': err});
  }
  return deviceUINew;
}

// 获取当前主tab的下标，判断是否在场景tab内
Future<int> getMainTabIndex() async {
  try {
    preferences = await SharedPreferences.getInstance();
    if (preferences != null) {
      if (preferences!.containsKey(CONSTANT.CURRENT_TAB_INDEX_KEY)) {
        final int mainindex =
            preferences!.getInt(CONSTANT.CURRENT_TAB_INDEX_KEY) ?? 0;
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg: ' getMainTabIndex mainindex $mainindex');
        return mainindex;
      }
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.tagLibrary,
        msg: <String, Object>{'fn': 'getMainTabIndex', 'err': err});
  }
  return 0;
}
