/*
 * @Date: 2022-07-06 10:45:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-11-08 20:36:34
 * @FilePath: /library_widgets/lib/common/constant.dart
 */
class CONSTANT {
  // 提示信息
  static const String NET_WORK_ERROR = '网络不可用';
  static const String REQUEST_ERROR = '请求服务器异常';

  static const String REQUEST_SUCCESS_CODE = '00000';
  static const String SCENE_ERR_A00004 = 'A00004';
  static const String SCENE_ERR_LI0012 = 'LI0012';
  static const String SCENE_MISS_EQUIPMENT = '开启失败，缺少执行设备';
  static const String SCENE_MISS_DEVICE = '设备被移除，场景已失效';

  // 0-默认值,1-由于设备分享导致场景被动关闭,2-由于设备分享导致场景打开
  static const String SCENE_STATUS_1 = '1';

  /// 获取当前 主tab index
  static const String CURRENT_TAB_INDEX_KEY = 'main_current_tab_index';

  // 场景tab是否显示存储临时缓存
  static const String DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET =
      'device-scene-isSceneShow-library-widget';

  // 场景tab下主tab我的、推荐切换时的缓存
  static const String DEVICE_SCENE_TABTYPE_INDEX = 'device-scene-tabtype-index';

  // 智家请求接口域名
  static const String BASE_URL = 'https://zj.haier.net';

  // 智家验收域名
  static const String BASE_URL_YS = 'https://zj-yanshou.haier.net';

  // h5详情返回flutter页携参
  static const String TO_DEVICE_SCENE_MINE =
      'https://uplus.haier.com/uplusapp/device_scene/index.html?close_current_page=true&tabType=mine';

  // log的tag
  static const String tagLibrary = '[library_widgets]:';
  static const String PACKAGE_NAME = 'library_widgets';

  // 场景执行中
  static const String SCENE_OPERATING = '场景启动，执行中';
  static const String SERVER_RET_CODE_SUCCESS = '00000';

  // 场景执行接口
  static const String SCENE_OPERATION_URL =
      '/omssceneapi/scene/v2/user/trigger/operation';

  // 场景执行接口
  static const String SCENE_INFO_URL = '/omssceneapi/scene/v1/start/info';

  // 场景图片
  static const String sceneManuallyNestingPng =
      'assets/images/scene-manually-nesting.png'; //手动执行
  static const String sceneMessagePng = 'assets/images/scene-message.png'; //消息
  static const String sceneTimingPng = 'assets/images/scene-timing.png'; //时间
  static const String sceneOuterenvPng =
      'assets/images/scene-outerenv.png'; //外部环境变化
  static const String iconDefault = 'assets/images/icon_default.png'; //默认图片
  static const String iconDefaultTopN =
      'assets/images/icon_default_topn.png'; //默认图片
  static const String iconManuallyScene =
      'assets/images/icon-manual.png'; //执行手动场景

  // 我的场景列表
  static const String MINE_SCENE_LIST_URL = '/omssceneapi/scene/v4/list';

  // 推荐场景卡片空间
  static const String LISTBYROOM_URL =
      '/omssceneapi/house/v1/recommend/listByRoom';

  // 查询泛场景模板详情
  static const String GENERAL_DETAIL_URL =
      '/omssceneapi/scene/store/v3/sceneDetail';

  // 推荐场景卡片方案
  static const String LISTBYSCHEME_URL =
      '/omssceneapi/house/v1/recommend/listByScheme';

  // 专属场景增加自动场景根据空间查询推荐场景
  static const String SPACE_RECOMMENDATION_SCENE =
      '/api-gw/zjsceneapi/house/v1/recommend/listByRoom';

  // 专属场景增加自动场景根据系统查询推荐场景
  static const String SYSTEM_RECOMMENDATION_SCENE =
      '/api-gw/zjsceneapi/house/v1/recommend/listByScheme';

  // 查询空气产业云场景接口 文档地址：https://stp.haier.net/project/790/interface/api/231418
  static const String QUERY_AIR_SCENE =
      '/api-gw/zjsceneapi/scene/v1/queryAirScene';

  //自动场景互斥列表
  static const String MUTEX_LIST_URL = '/omssceneapi/scene/v1/mutex/list';

  //自动场景开开启关闭
  static const String MINE_SCENE_LIST_MANUALOPERATION_URL =
      '/omssceneapi/scene/v2/user/manual/operation';

  //自动场景开开启关闭批量
  static const String MINE_SCENE_LIST_BATCHMANUALOPERATION_URL =
      '/omssceneapi/scene/v1/batch/manual/operation';

  // 登录url
  static const String LOGIN_URL = 'mpaas://usercenter';

  // 售后客服电话
  static const String CONTACT_HELP_TEL = '************';

  // 扫码添加设备
  static const String VDN_QRSCAN =
      'http://uplus.haier.com/uplusapp/main/qrcodescan.html?needAuthLogin=1&&selectIndex=0';

  // 在线客服
  static const String VDN_SERVICE_PHONE =
      'mpaas://CustomerService/?device=app#/zhijiaHome';

  // 存储场景filter颜色 永久缓存
  static const String SCENE_FILTER_COLOR = 'scene_filter_color';

  // ----------------------- 离线/故障弹窗打点业务相关  ------------------------------//
  // 点击故障图标 value上报房间名，如客厅 content_type上报设备品类名称，如电视
  static const String TRACE_ON_SPACE_CLICK_CAUTION = 'MB32184';

  // 点击离线图标 value上报房间名，如客厅 content_type上报设备品类名称，如电视
  static const String TRACE_ON_SPACE_CLICK_OFFLINE = 'MB32185';

  // 点击故障-一键报修
  static const String TRACE_ON_SPACE_CAUTION_REPAIR = 'MB32186';

  // 点击故障弹窗-呼叫
  static const String TRACE_ON_SPACE_CAUTION_CALL = 'MB32187';

  // 点击故障-在线客服
  static const String TRACE_ON_SPACE_CAUTION_SERVICE = 'MB32188';

  // 点击离线-重新连接
  static const String TRACE_ON_SPACE_OFFLINE_RECONNECTION = 'MB32189';

  // 点击离线-在线客服
  static const String TRACE_ON_SPACE_OFFLINE_SERVICE = 'MB32190';

  // 泛场景
  static const String SCENE_SERVICE = 'SERVICE';

  // 高阶场景
  static const String SCENE_ADV = 'ADV';

  // 场景名称
  static const String SCENE_NAME = 'scene_name';
  static const String SCENE_TYPE_MORE = 'more';
  static const String CONTENT_TYPE = 'content_type';

  // 空间名称
  static const String CHANNEL_NAME = 'channel_name';

  // 可展示类
  static const String TRIGGERTYPE_DISPLAY = 'display';

  // 专业系统-空气
  static const String SYSTEM_AIR = 'air';

  // 智家推荐
  static const String smartHomeRecommendUrl = '/omsappapi/ad/v1/rotation';
  static const String iconHaier1 = 'assets/images/default-device.png';

  // 绑定设备
  static const String bindPage =
      'http://uplus.haier.com/uplusapp/main/qrcodescan.html?needAuthLogin=1';

  // 绑定视频引导页
  static const String bingGuideVideo =
      'https://zjrs.haier.net/zjapp/bindingHelp/index.html?container_type=3#/videoGuide';
}
