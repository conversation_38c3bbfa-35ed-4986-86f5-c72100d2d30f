/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-10-19 14:55:23
 * @description: 推荐场景图片色系 蓝 粉 绿 紫 橙
 */

import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_color_models/flutter_color_models.dart';
import 'package:storage/storage.dart';

import '../constant.dart';
import '../log.dart';

enum ColorType { Blue, Pink, Green, Purple, Orange }

// 每种色系对应的颜色滤镜
class ColorFilter {

  ColorFilter({
    required this.hue,
    required this.brightness,
    required this.saturation,
    required this.colorType,
  });
  ColorType colorType;
  double hue;
  double saturation;
  double brightness;

  @override
  String toString() {
    return 'ColorFilter{colorType: $colorType,hue: $hue,saturation: $saturation,brightness: $brightness}';
  }
}

// 橙绿蓝紫粉 处理参数
List<ColorFilter> _fliters = <ColorFilter>[
  ColorFilter(
      colorType: ColorType.Orange,
      hue: -0.868,
      saturation: 1.5,
      brightness: 0.0),
  ColorFilter(
      colorType: ColorType.Green, hue: 0.5, saturation: 1.0, brightness: 0.0),
  ColorFilter(
      colorType: ColorType.Blue, hue: 0.0, saturation: 0.0, brightness: 0.0),
  ColorFilter(
      colorType: ColorType.Purple,
      hue: -0.33,
      saturation: 0.16,
      brightness: 0.2),
  ColorFilter(
      colorType: ColorType.Pink, hue: -0.72, saturation: 0.14, brightness: 0.0),
];

// 根据图片色系，返回图片处理参数
Future<ColorFilter> getSceneImageFilter(
    String? url, List<dynamic> sceneIconHueStorage) async {
  if (url == null || url == '') {
    DevLogger.error(
        tag: CONSTANT.tagLibrary,
        msg: <String, String>{'fn': 'get image hue value error ', 'value': 'url empty'});
    return _fliters[2];
  } else {
    try {
      num hue = 0.0;
      // 缓存中 去取数据
      final int index = sceneIconHueStorage.indexWhere((dynamic element) {
        if ((element is Map) && element.containsKey(url)) {
          return true;
        }
        return false;
      });
      // 如果缓存中有，则直接在缓存中取。
      if (index >= 0) {
        //TODO: sfj 待验证
        if (sceneIconHueStorage[index] is Map) {
          final Map<dynamic, dynamic> map =
              sceneIconHueStorage[index] as Map<dynamic, dynamic>;
          if (map[url] is num) {
            hue = map[url] as num;
          }
        }

        DevLogger.info(
            tag: CONSTANT.tagLibrary, msg: 'color get save data  $hue');
      } else {
        String jsonUrl = '';
        if (url.contains('?')) {
          jsonUrl = '$url&x-oss-process=image/average-hue';
        } else {
          jsonUrl = '$url?x-oss-process=image/average-hue';
        }
        final Response<dynamic> result = await Dio().get(jsonUrl);
        String? colorRgb;
        HslColor? hslColor;
        final dynamic resultData = result.data;
        if (resultData != null && resultData is Map) {
          colorRgb =
              resultData['RGB'] is String ? resultData['RGB'] as String : '';
          if (colorRgb != '') {
            final Color color = Color(int.parse(colorRgb));
            hslColor = HslColor.fromColor(color);
            hue = hslColor.hue;
            DevLogger.info(
                tag: CONSTANT.tagLibrary, msg: 'color get new data  $hue');
          }
        }
        final List<dynamic> newSceneIconHueStorage = sceneIconHueStorage;
        final String hueStr = hue.toStringAsFixed(5);
        newSceneIconHueStorage.add(<String, double>{url: double.parse(hueStr)});
        // 缓存中 最多存储70条数据，如果超过70条，则删除30条。
        if (newSceneIconHueStorage.length >= 70) {
          newSceneIconHueStorage.removeRange(0, 30);
        }
        Storage.putStringValue(
            CONSTANT.SCENE_FILTER_COLOR, jsonEncode(newSceneIconHueStorage));
      }
      if (hue >= 15 && hue <= 40) {
        // 橙色
        return _fliters[0];
      } else if ((hue >= 140 && hue <= 160) || hue == 0) {
        // hue == 0 解决 绿色的图片经过 oss提取图片主色调，一直返回为灰色的问题，灰色的hue为0。
        // 绿色
        return _fliters[1];
      } else if (hue >= 190 && hue <= 210) {
        // 蓝色
        return _fliters[2];
      } else if (hue >= 245 && hue <= 265) {
        // 紫色
        return _fliters[3];
      } else if (hue >= 330 && hue <= 350) {
        // 粉色
        return _fliters[4];
      } else {
        // 其他 默认蓝色
        return _fliters[2];
      }
    } catch (e) {
      // 出错，不处理 默认蓝色
      DevLogger.error(tag: CONSTANT.tagLibrary, msg: <String, Object>{
        'fn': 'get image hue value error',
        'value': <String, Object>{url: e}
      });
      return _fliters[2];
    }
  }
}
