// 接口请求方法
import 'dart:convert';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:dio/dio.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import 'constant.dart';
import 'log.dart';
import 'server.dart';
import 'util.dart';

class SceneTraceInfo {
  SceneTraceInfo(String this.bName,
      [this.aId,
      this.uId,
      this.ver,
      this.step,
      String? bId,
      String? code,
      String? scid,
      String? uTraceId]) {
    if (uTraceId != null && uTraceId.isNotEmpty) {
      this.uTraceId = uTraceId;
    } else {
      this.uTraceId = DateTime.now().millisecondsSinceEpoch.toString();
    }
    uSpanId = '0.1';
    sys = 'APP';
    subSys = 'scene';
    ts = DateTime.now().millisecondsSinceEpoch.toString();
    if (bId != null && bId.isNotEmpty) {
      this.bId = bId;
    }
    if (code != null && code.isNotEmpty) {
      this.code = code;
    }
    if (scid != null && scid.isNotEmpty) {
      this.scid = scid;
    }
  }

  String? uTraceId;
  String? uSpanId;
  String? sys;
  String? subSys;
  String? aId;
  String? uId;
  String? ver;
  int? step; //1 | 2
  String bId = '';
  String? ts;
  String code = '';
  String? bName;
  String scid = '';

  Map<String, dynamic> makeBody(SceneTraceInfo traceInfo) {
    return <String, dynamic>{
      'total': 1,
      'logData': <dynamic>[
        <String, dynamic>{
          'uTraceId': traceInfo.uTraceId,
          'uSpandId': traceInfo.uSpanId,
          'step': traceInfo.step,
          'bId': traceInfo.bId,
          'sys': traceInfo.sys,
          'subSys': traceInfo.subSys,
          'ts': traceInfo.ts,
          'code': traceInfo.code,
          'aId': traceInfo.aId,
          'uId': traceInfo.uId,
          'bName': traceInfo.bName,
          'ver': traceInfo.ver,
          'args': <String, dynamic>{'scid': traceInfo.scid}
        }
      ]
    };
  }
}

class API {
  /// 获取一些本地存储的请求信息
  static String _appId = '';
  static String _appKey = '';
  static String _clientId = '';
  static String _appVersion = '';
  static String _accessToken = '';
  static String _userCenterAccessToken = '';
  static String _userId = '';
  static String _env = '';
  static bool _loginStatus = false;

  // false为生产环境
  static bool _grayMode = false; // 灰度

  // 初始AppInfo，有的时候不更新，防止每次都去取
  static Future<void> initApiParams() async {
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      _loginStatus = loginStatus.isLogin;
    } catch (e) {
      _loginStatus = false;
    }

    try {
      if (_appId == '') {
        final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
        updateApiParams(appInfo: appInfo);
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, dynamic>{'fn': 'initApiParams', 'error': err});
    }
  }

  // 更新参数
  static void updateApiParams({AppInfoModel? appInfo, OauthData? oauthData}) {
    // app信息
    if (appInfo != null) {
      _appId = appInfo.appId;
      _appKey = appInfo.appKey;
      _appVersion = appInfo.appVersion;
      _clientId = appInfo.clientId;
      _env = appInfo.env;
      _grayMode = appInfo.grayMode;
    }
    // 鉴权信息
    if (oauthData != null) {
      _accessToken = oauthData.uhome_access_token;
      _userCenterAccessToken = oauthData.user_center_access_token;
      _userId = oauthData.uhome_user_id;
    }
  }

  static Future<Map<String, dynamic>> _headers(
      [Map<String, dynamic>? otherHeaderInfo, int? timestamp]) async {
    String accessToken;
    String accountToken;
    String userId;
    try {
      if (_loginStatus) {
        final OauthData oauthData = await User.getOauthData();
        final UserInfo userinfo = await User.getUserInfo();
        accessToken = oauthData.uhome_access_token;
        accountToken = oauthData.user_center_access_token;
        userId = userinfo.userId;
      } else {
        accessToken = '';
        accountToken = '';
        userId = '';
      }

      final Map<String, dynamic> headers = <String, dynamic>{
        'timestamp': timestamp.toString(),
        'appId': _appId,
        'clientId': _clientId,
        'userId': userId,
        'accessToken': accessToken,
        'accountToken': accountToken,
        'appVersion': _appVersion,
      };

      if (otherHeaderInfo != null) {
        headers.addAll(otherHeaderInfo);
      }
      return headers;
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  // 自定义-请求我的场景手动场景执行接口
  static Future<Map<dynamic, dynamic>> triggerSceneV2(
      String? familyId, String? sceneId) async {
    await initApiParams();
    final int timestamp = DateTime.now().millisecondsSinceEpoch;

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'sceneId': sceneId,
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SCENE_OPERATION_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SCENE_OPERATION_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v4',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 自定义-请求我的场景手动场景执行结果接口
  static Future<Map<dynamic, dynamic>> sceneStartInfo(
      String? familyId, String? sceneId, String? sequenceId) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'sceneId': sceneId,
      'sequenceId': sequenceId
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SCENE_INFO_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SCENE_INFO_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v4',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 请求我的场景列表接口
  // static getSceneList(
  //     String? familyId, int limit, int cursor, List? sceneTagList) async {
  //   await initApiParams();
  //   final _timestamp = HTTP.getTimestamp();
  //
  //   Map<String, dynamic> requestMap = {
  //     'familyId': familyId,
  //     'limit': limit,
  //     'cursor': cursor,
  //     'sceneTagList': sceneTagList,
  //   };
  //
  //   String bodyJson = json.encode(requestMap);
  //
  //   String sign = HTTP.getSign(
  //     CONSTANT.MINE_SCENE_LIST_URL,
  //     bodyJson,
  //     _appId,
  //     _appKey,
  //     _timestamp,
  //   );
  //
  //   return await httpManager.postData(
  //     (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
  //         CONSTANT.MINE_SCENE_LIST_URL,
  //     params: requestMap,
  //     options: Options(
  //       headers: {
  //         'timestamp': _timestamp.toString(),
  //         'appId': _appId,
  //         'clientId': _clientId,
  //         'userId': _userId,
  //         'accessToken': _accessToken,
  //         'accountToken': _userCenterAccessToken,
  //         'appVersion': _appVersion,
  //         'apiVersion': 'v4',
  //         'sequenceId': new DateTime.now().millisecondsSinceEpoch,
  //         "version": 0.6,
  //         'sign': sign,
  //       },
  //     ),
  //   );
  // }

  // 按空间查询场景卡片
  static Future<Map<dynamic, dynamic>> getListByRoom(
      String? familyId, int limit, String? roomId) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'limit': limit,
      'roomId': roomId
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_RECOMMENDATION_SCENE,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_RECOMMENDATION_SCENE,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 未登录时按空间查询场景卡片
  static Future<Map<dynamic, dynamic>> getListByRoomLoginOut(
      String? familyId, int limit, String? roomId, String? roomType) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'limit': limit,
      'roomId': roomId,
      'roomType': roomType
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_RECOMMENDATION_SCENE,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_RECOMMENDATION_SCENE,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 按方案查询场景卡片
  static Future<Map<dynamic, dynamic>> getListByScheme(
      String? familyId, String? plan, int limit, String? roomId) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'plan': plan,
      'limit': limit,
      'roomId': roomId
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SYSTEM_RECOMMENDATION_SCENE,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SYSTEM_RECOMMENDATION_SCENE,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 查询空气产业云场景接口
  static Future<Map<dynamic, dynamic>> getAirScene(
      String? familyId, String? roomId) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'roomId': roomId,
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.QUERY_AIR_SCENE,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.QUERY_AIR_SCENE,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 查询模板详情数据
  static Future<Map<dynamic, dynamic>> getGeneralDetail(
      String familyId, String templateId, String type) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'templateId': templateId,
      'type': type,
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.GENERAL_DETAIL_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.GENERAL_DETAIL_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 查询互斥场景数据
  static Future<Map<dynamic, dynamic>> getMutexSceneList(
      String familyId, String sceneId, String? type) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'sceneId': sceneId,
      'type': type,
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.MUTEX_LIST_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.MUTEX_LIST_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 请求我的场景自动场景开关接口
  static Future<Map<dynamic, dynamic>> manualOperation(
      String? familyId, String? sceneId, String? type, String status) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'familyId': familyId,
      'sceneId': sceneId,
      'type': type,
      'status': status
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.MINE_SCENE_LIST_MANUALOPERATION_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.MINE_SCENE_LIST_MANUALOPERATION_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // 请求我的场景自动场景开关接口-批量
  static Future<Map<dynamic, dynamic>> batchManualOperation(
      List<dynamic>? sceneList) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'sceneList': sceneList
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.MINE_SCENE_LIST_BATCHMANUALOPERATION_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.MINE_SCENE_LIST_BATCHMANUALOPERATION_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        },
      ),
    );
  }

  // App的发布状态
  static String _publishStatus() {
    String publishStatus;
    if (_env == '生产' && !_grayMode) {
      publishStatus = '1';
    } else if (_env == '生产' && _grayMode) {
      publishStatus = '0';
    } else {
      publishStatus = '1';
    }
    return publishStatus;
  }

  /// 获取智家推荐列表
  static Future<Map<dynamic, dynamic>> getSmartHomeIntroductions(
      String adLocation) async {
    await initApiParams();
    final Map<String, dynamic> params = <String, dynamic>{
      'adLocation': adLocation,
      'publishStatus': _publishStatus()
    };
    final int timestamp = HTTP.getTimestamp();
    final String bodyJson = json.encode(params);
    final String sign = HTTP.getSign(
        CONSTANT.smartHomeRecommendUrl, bodyJson, _appId, _appKey, timestamp);
    final Map<String, dynamic> headersInfo =
        await _headers(<String, dynamic>{'sign': sign, 'timestamp': timestamp});
    return httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.smartHomeRecommendUrl,
      params: params,
      header: headersInfo,
    );
  }
}
