/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-08-08 17:02:16
 * @description: 
 */
import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart' as userPlugin;

import '../components/scene_continue/scene_continue.dart';
import '../model/scene_model.dart';
import '../model/scene_trigger_response.dart';
import '../store/actions.dart' as Action;
import '../store/reducers/library_reducer.dart';
import '../store/state.dart';
import 'api.dart';
import 'constant.dart';
import 'log.dart';
import 'toast_helper.dart';
import 'util.dart';

class ManualOperation {
  static bool? networkStatus1;

  // static OverlayEntry? _resultModal;
  // 是否可以弹toast, 当服务tab为当前tab时，才可以弹

  static Map<dynamic, dynamic> actionResultParam = <dynamic, dynamic>{};
  static String? sceneName = '';

  // 手动执行
  static List<dynamic> result = <dynamic>[];
  static int reloadCount = 0;
  static Timer? notSuccessShutDownSearchFour; //关闭弹窗以后如果有未成功的设备继续执行4次的定时器
  // static Timer? _timer; //4次定时器有值期间重新设置的定时器来执行轮询
  static int continueSearchCount = 0;
  static int retryCount = 0; //下发指令接口接口查询失败重新查次数
  static Timer? containueTimer; //  场景日志重复执行定时器

  static String sceneGeographicalFencePng =
      'assets/images/scene-geography.png'; //地理围栏
  static String sceneDelayPng = 'assets/images/scene-delay.png'; //延时
  static String sceneSwitchNestingPng =
      'assets/images/scene-switch-nesting.png'; //设备动作
  static String sceneProgressSuccessPng =
      'assets/images/scene-progress-success.png';
  static String sceneProgressWaitingPng =
      'assets/images/scene-progress-waiting.png';
  static String sceneProgressRunningPng =
      'assets/images/scene-progress-running.png';
  static String scenePartSuccessPng = 'assets/images/scene-part-success.png';
  static String sceneProgressFailurePng =
      'assets/images/scenc-progress-failure.png';

  static final Store<LibraryWidgetState> _store = Store<LibraryWidgetState>(
      LibraryReducer,
      initialState: LibraryWidgetState.initial());

  // 场景执行中，如果切换tab或者刷新列表，停止显示toast和动画
  static Future<void> getTemporyStorage(
      String? tabType, Function? handleRunAnimation) async {
    Storage.addDataListener(CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET,
        (_, __) async {
      //在场景tab下，我的和推荐时不显示toast，
          final String isSceneshow = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET);
          final String changeTabtype = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);
          DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, String>{
        'getTemporyStorage_isSceneshow': isSceneshow,
        'getTemporyStorage_changeTabtype': changeTabtype
      });
          if (json.decode(changeTabtype)['value'] != tabType) {
        ToastHelperLibrary.closeToast();
        if (handleRunAnimation != null) {
          handleRunAnimation(0);
        }
      }
      if (json.decode(isSceneshow)['value'] == '1' &&
          json.decode(changeTabtype)['value'] != 'recommend') {
        ToastHelperLibrary.updateCanShow(true);
      } else {
        ToastHelperLibrary.closeToast();
      }
    });
  }

  // 清除所有的提示语
  static void cleanAllToast() {
    ToastHelperLibrary.closeToast();
  }

  static Future<void> getResultData(BuildContext context,
      {Function? updateCurtainWorkingAction,
      List<String>? sceneIdList,
      required dynamic Function(dynamic) dispatch,
      Function? handleRunAnimation,
      String? isModel,
      String? tabtype,
      String? sceneName}) async {
    // 如果断网
    try {
      if (networkStatus1!) {
        SceneContinue.resetInterfaceToRefresh();

        DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, Object?>{
          '_getResultData': sceneIdList,
          '_isModel': isModel,
          '_tabtype': tabtype
        });
        // 网络重连
        SceneContinue.scenePoll(context,
            updateCurtainWorkingAction: updateCurtainWorkingAction,
            actionResultParam: actionResultParam,
            continueSearchCount: 1,
            sceneIdList: sceneIdList,
            dispatch: dispatch,
            handleRunAnimation: handleRunAnimation,
            retryCount: 0,
            containueTimer: containueTimer,
            isModel: isModel,
            tabtype: tabtype,
            sceneName: sceneName);


      }
    } catch (e) {
      ToastHelperLibrary.closeToast();
      handleRequestError(e, context);
    }
  }

// 执行结果弹框
  static Future<void> _openResultModal(BuildContext context,
      {Function? updateCurtainWorkingAction,
      List<String>? sceneIdList,
      required dynamic Function(dynamic) dispatch,
      Function? handleRunAnimation,
      String? isModel,
      String? tabtype,
      String? sceneName}) async {
    reloadCount = 0;
    retryCount = 0;
    continueSearchCount = 0;

    DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, Object?>{
      '_openResultModal': sceneIdList,
      'isModel': isModel
    });
    getTemporyStorage(tabtype, handleRunAnimation);
    await getResultData(context,
        updateCurtainWorkingAction: updateCurtainWorkingAction,
        sceneIdList: sceneIdList,
        dispatch: dispatch,
        handleRunAnimation: handleRunAnimation,
        isModel: isModel,
        tabtype: tabtype,
        sceneName: sceneName);
  }

// 获取鉴权信息
  static Future<void> _getOauthData() async {
    try {
      final OauthData res = await userPlugin.User.getOauthData();
      DevLogger.info(
          tag: CONSTANT.tagLibrary,
          msg: <String, OauthData>{'_getOauthData': res});
      // 更新鉴权
      // 更新api的鉴权信息
      API.updateApiParams(oauthData: res);
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'_getOauthData err': err});
    }
  }

  // 接口刷新提示定时器和动画
  static void cleanTimerAndController(Function? handleRunAnimation) {
    ToastHelperLibrary.closeToast();
    if (handleRunAnimation != null) {
      handleRunAnimation(0);
    }
    SceneContinue.cleanAllTimer(containueTimer);
    _store.dispatch(Action.UpdateContinueSceneAction(<String>[]));
  }

// 手动执行
  static Future<void> execute(
      dynamic objData, bool networkStatus, String familyId, BuildContext context, String isModel,
      {Function? updateCurtainWorkingAction,
      Function? handleRunAnimation,
      String? tabtype}) async {
    SceneModel obj = SceneModel();
    // TODO: sfj 待验证
    // if (isModel != '') {
    if (objData is SceneModel) {
      obj = objData;
    } else if (objData is Map<dynamic, dynamic>) {
      obj = SceneModel.fromJson(objData);
    }
    try {
      networkStatus1 = networkStatus;
      // 1.如果网络断开，提示网络不可用
      if (!networkStatus) {
        ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
        return;
      } else {
        // 注释为场景日志弹窗组件处理逻辑（暂时弃用）
        // 1.如果场景已经启动，正在执行
        // if (_executing) {
        //   return;
        // }
        // _executing = true;
        // 2.toast提示场景启动中

        // 场景我的列表改版、和专属场景执行方法，点击执行后再store中存储场景id，有执行结果后删除
        final List<String> sceneList = _store.state.sceneIdList;
        DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, Object?>{
          '_execute_sceneList': sceneList,
          'obj_id': obj.id
        });
        if (obj.id != null) {
          if (!sceneList.contains(obj.id)) {
            sceneList.add(obj.id!);
          } else {
            // 重复点击同一场景提示场景执行中，
            ToastHelperLibrary.showToast('执行中', context);
            return;
          }
          _store.dispatch(Action.UpdateContinueSceneAction(sceneList));
          if (handleRunAnimation != null) {
            handleRunAnimation(1);
          }
        }
        getTemporyStorage(tabtype, handleRunAnimation);
        // ToastHelperLibrary.showToast(CONSTANT.SCENE_OPERATING, context, true);
        // 3.执行场景指令
        try {
          gioTrack('MB19112', <String, dynamic>{
            'scene': obj.sceneName,
            'content_site': obj.tagList != null ? json.encode(obj.tagList) : '',
            'status': '执行'
          });
        } catch (e) {
          ToastHelperLibrary.closeToast();
          DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '执行打点报错');
        }
        try {
          await _getOauthData();
          // 场景执行下发指令接口
          // TODO: sfj 待验证
          final Map<dynamic, dynamic> ret =
              await API.triggerSceneV2(familyId, obj.id);
          final SceneTriggerResponse response =
              SceneTriggerResponse.fromJson(ret);
          if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS &&
              response.data.isNotEmpty) {
            // 我的列表执行，下发命令后需要传递参数用来计算执行中的数量
            if (handleRunAnimation != null && isModel == '') {
              handleRunAnimation(2);
            }
            // 显示modal
            actionResultParam = <String, dynamic>{
              'familyId': obj.familyId,
              'sceneId': obj.id,
              'sequenceId': response.data
            };
            DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, String?>{
              '_execute': obj.sceneName,
              'tabtype': tabtype
            });
            if (context.mounted) {
              _openResultModal(context,
                  updateCurtainWorkingAction: updateCurtainWorkingAction,
                  sceneIdList: sceneList,
                  dispatch: _store.dispatch,
                  handleRunAnimation: handleRunAnimation,
                  isModel: isModel,
                  tabtype: tabtype,
                  sceneName: obj.sceneName); //查询执行结果
            }
          } else {
            ToastHelperLibrary.closeToast();
            if (context.mounted) {
              if (response.retCode == CONSTANT.SCENE_ERR_LI0012) {
                ToastHelperLibrary.showToast(
                    CONSTANT.SCENE_MISS_EQUIPMENT, context);
              } else if (response.retCode == 'LS0012') {
                ToastHelperLibrary.showToast('场景异常，请下拉刷新后重试', context);
              } else if (response.retCode == 'LC0002') {
                ToastHelperLibrary.showToast('设备不支持该功能', context);
              } else if (response.retCode == 'LD0001') {
                ToastHelperLibrary.showToast('场景执行失败，网关离线', context);
              } else if (response.retCode == 'LI0009') {
                ToastHelperLibrary.showToast('场景执行失败', context);
              } else if (response.retCode == 'LS0022') {
                ToastHelperLibrary.showToast('场景规则信息不存在', context);
              } else {
                handleRequestError(response, context);
              }
            }
            // 执行失败，从执行场景store数据中删除场景Id数据
            if (obj.id != '' && obj.id != null) {
              if (sceneList.contains(obj.id)) {
                sceneList.removeAt(sceneList.indexOf(obj.id!));
              }
              _store.dispatch(Action.UpdateContinueSceneAction(sceneList));
            }
            if (handleRunAnimation != null) {
              handleRunAnimation(0);
            }
          }
        } catch (e) {
          ToastHelperLibrary.closeToast();
          if (context.mounted) {
            handleRequestError(e, context);
          }
        } finally {}
      }
    } catch (e) {
      if (context.mounted) {
        handleRequestError(e, context);
      }
    }
  }
}
