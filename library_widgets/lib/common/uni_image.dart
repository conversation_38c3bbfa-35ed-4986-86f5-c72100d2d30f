/*
 * @describe: 图片组件-来源网络or项目
 */

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'constant.dart';

class UniNetworkImage extends StatelessWidget {
  const UniNetworkImage({
    super.key,
    this.src,
    this.defaultSrc,
    this.width,
    this.height,
    this.boxFit,
    this.assetImgHeight,
  });

  final String? src;
  final String? defaultSrc;
  final double? width;
  final double? height;
  final BoxFit? boxFit;
  final double? assetImgHeight;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
        imageUrl: src!,
        width: width,
        height: height,
        fit: BoxFit.cover,
        placeholder: (BuildContext context, String url) => UniAssetImage(
              src: defaultSrc,
              width: width,
              height: assetImgHeight,
              boxFit: BoxFit.cover,
            ),
        errorWidget: (BuildContext context, String url, Object error) => UniAssetImage(
              src: defaultSrc,
              width: width,
              height: assetImgHeight,
              boxFit: BoxFit.cover,
            ));
  }
}

class UniAssetImage extends StatelessWidget {
  const UniAssetImage({
    super.key,
    this.src,
    this.width,
    this.height,
    this.boxFit,
  });

  final String? src;
  final double? width;
  final double? height;
  final BoxFit? boxFit;

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      src!,
      package: CONSTANT.PACKAGE_NAME,
      width: width,
      height: height,
      fit: boxFit,
    );
  }
}
