/// 判断变量或变量的某项是否为空
///
/// 参数：
/// obj：必填项。需要判断是否非空的数据。
/// params：选填项。需要判断的obj的子项列表。
///
/// 返回值：
/// bool类型，true表示不为空，false表示为空。
///
/// 详细说明：https://ihaier.feishu.cn/docs/doccn0FCUQ6QQHNTuNFahnlIo0f#
///
/// [示例代码]
/// ```dart
///   isNotEmptyFn(null);  // false
///   isNotEmptyFn("");  // false
///
///   Map _m = {"a":{"b":{"c":1,"d":""}}};
///   isNotEmptyFn(_m, params: ["a","b","c"])； // true,值为1
///   isNotEmptyFn(_m, params: ["a","a","c"])； // false，取不到
///   isNotEmptyFn(_m, params: ["a","b","d"])； // false，值为""
/// ```
bool isNotEmptyFn(dynamic obj, {List<dynamic>? params}) {
  if (obj == null) {
    // 如果obj为空，则直接设置为空
    return false;
  } else if (params != null && params.isNotEmpty) {
    if (obj is List || obj is Map || obj is Set || obj is String) {
      // 如果有需要校验的参数，并且数据是list或者map或者set类型，循环判断
      try {
        dynamic o = obj;

        for (int i = 0; i < params.length; i++) {
          if (o is Set) {
            if (o.contains(params[i])) {
              o = params[i];
            } else {
              return false;
            }
          } else {
            // 用[]取校验的参数，如果报错则为空
            o = o[params[i]];
          }
        }
        // 如果可以正常取到最后一项校验，需要调用自身判断最后的值是否为空
        return isNotEmptyFn(o);
      } catch (e) {
        // 报错，为空
        return false;
      }
    } else {
      // 如果有需要校验的参数，并且数据不是list或者map类型，直接为空
      return false;
    }
  } else {
    // 没有需要校验的参数，直接判断obj
    if (obj is String && obj == '') {
      // 如果是String，判断是否为空字符串
      return false;
    } else if ((obj is List || obj is Map || obj is Set) && obj.length == 0) {
      // 如果是list或map，判断长度是否为0
      return false;
    }
  }
  // 其余的情况都为非空
  return true;
}
