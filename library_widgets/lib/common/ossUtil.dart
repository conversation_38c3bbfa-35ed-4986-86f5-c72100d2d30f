// oss处理图片文件
// 阿里云文档链接：https://help.aliyun.com/document_detail/44688.html

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'util.dart';

class OssUtil {
  // 视频截取第一帧
  static const String OSS_VIDEO_SNAP =
      'x-oss-process=video/snapshot,t_1,f_jpg,h_0,w_320,ar_auto';
  //分享到微信的缩略图
  static const String OSS_WXSHARE =
      'x-oss-process=image/resize,m_lfit,w_300,limit_0/auto-orient,1';

  // 获取width * height图片的oss处理字符串（超过居中裁剪）
  // 参数单位是px,设计图给出得尺寸
  static String getOssTypeBySize(num width, num height) {
    return 'x-oss-process=image/resize,m_fill,w_${(width.w * getDpr()).ceil()},h_${(height.w * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 获取width,高度自适应图片的oss处理字符串
  // 参数单位是px,设计图给出得尺寸
  static String getOssTypeByWidth(num width) {
    return 'x-oss-process=image/resize,m_lfit,w_${(width.w * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 获取height,宽度自适应图片的oss处理字符串
  // 参数单位是px,设计图给出得尺寸
  static String getOssTypeByHeight(num height) {
    return 'x-oss-process=image/resize,m_lfit,h_${(height.w * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 添加图片oss处理
  static final RegExp _domin =
      RegExp('/accountstatic.haier.com|zjrs.haier.net|synrs.haier.net/gi');
  static String addOssProcess(String? originUrl, String processType) {
    if (originUrl != null &&
        originUrl != '' &&
        _domin.hasMatch(originUrl) &&
        !originUrl.contains('oss-process')) {
      // 图片在iOS 14及以上才支持webp, 所以分享这里去掉webp的转换
      if (processType != OssUtil.OSS_VIDEO_SNAP &&
          processType != OssUtil.OSS_WXSHARE) {
        processType += '/format,webp';
      }
      if (originUrl.contains('.gif') && !processType.contains('format')) {
        processType = '$processType/format,gif';
      }
      if (originUrl.contains('?')) {
        return '${originUrl.split('?')[0]}?$processType&${originUrl.split('?')[1]}';
      }
      // 无其他参数，进行oss处理
      return '$originUrl?$processType';
    }
    return originUrl ?? '';
  }
}
