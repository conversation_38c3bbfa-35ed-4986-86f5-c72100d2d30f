import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/components/custom_card/painers.dart';

import 'painers.dart';

/// 带边框和阴影的卡片
/// 边框宽度1.5 白色透明度0.8 从上至下渐变
/// 阴影 黑色透明度0.08
class CustomCard extends StatelessWidget {
  const CustomCard({
    super.key,

    /// 卡片宽度
    this.width,

    /// 卡片高度
    this.height,

    /// 卡片背景、边框、阴影的透明度 （用于未启用卡片）
    this.opacity,

    /// 卡片倒角
    /// 默认4个角统一为这个值
    required this.redius,

    /// 子元素
    required this.child,

    /// 右上倒角
    /// 如果设置了会覆盖右上角的倒角，但不影响其他角
    this.topRightRedius,

    /// 左上倒角
    /// 如果设置了会覆盖左上角的倒角，但不影响其他角
    this.topLeftRedius,

    /// 右下倒角
    /// 如果设置了会覆盖右下角的倒角，但不影响其他角
    this.bottomRightRedius,

    /// 左下圆角
    /// 如果设置了会覆盖左下角的倒角，但不影响其他角
    this.bottomLeftRedius,

    /// 卡片是否有毛玻璃
    this.hasBlur,
  });

  final double? width;
  final double? height;
  final double redius;
  final Widget child;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  final bool? hasBlur;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: hasBlur == false
          ? BoxDecoration(
              boxShadow: <BoxShadow>[
                  BoxShadow(
                      color: Colors.black.withOpacity(0.02),
                      blurRadius: 4.w,
                      offset: Offset(0, 4.w))
                ],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(topLeftRedius ?? redius),
                topRight: Radius.circular(topRightRedius ?? redius),
                bottomLeft: Radius.circular(bottomLeftRedius ?? redius),
                bottomRight: Radius.circular(bottomRightRedius ?? redius),
              ))
          : null,
      width: width,
      height: height,
      child: CustomPaint(
        painter: hasBlur == false
            ? null
            : ShadowPainter(
                redius: redius,
                topRightRedius: topRightRedius,
                topLeftRedius: topLeftRedius,
                bottomRightRedius: bottomRightRedius,
                bottomLeftRedius: bottomLeftRedius,
                opacity: opacity,
              ),
        foregroundPainter: BorderPainer(
          redius: redius,
          topRightRedius: topRightRedius,
          topLeftRedius: topLeftRedius,
          bottomRightRedius: bottomRightRedius,
          bottomLeftRedius: bottomLeftRedius,
          opacity: opacity,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(topLeftRedius ?? redius),
            topRight: Radius.circular(topRightRedius ?? redius),
            bottomLeft: Radius.circular(bottomLeftRedius ?? redius),
            bottomRight: Radius.circular(bottomRightRedius ?? redius),
          ),
          child: hasBlur == false
              ? Container(
                  color: Colors.white.withOpacity(0.7 * (opacity ?? 1)),
                  child: child,
                )
              : BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 14.0, sigmaY: 14.0),
                  child: Container(
                    color: Colors.white.withOpacity(0.64 * (opacity ?? 1)),
                    child: child,
                  ),
                ),
        ),
      ),
    );
  }
}
