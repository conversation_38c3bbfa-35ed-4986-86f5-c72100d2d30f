import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 卡片阴影Painer
/// 黑色透明度0.08
/// 模糊 x = 2 y = 4
/// 向下偏移4px
class ShadowPainter extends CustomPainter {
  ShadowPainter({
    required this.redius,
    this.topRightRedius,
    this.topLeftRedius,
    this.bottomRightRedius,
    this.bottomLeftRedius,
    this.opacity,
  });

  final double redius;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  @override
  void paint(Canvas canvas, Size size) {
    // 阴影
    //创建画笔
    final Paint shadow = Paint()
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.25.w
      ..color = Colors.black.withOpacity(0.02 * (opacity ?? 1))
      ..imageFilter = ImageFilter.blur(sigmaX: 2, sigmaY: 4);

    final RRect rRect = RRect.fromLTRBAndCorners(
      1,
      4.25.w,
      size.width - 1.4.w,
      size.height + 1,
      topLeft: Radius.circular(topLeftRedius ?? redius),
      topRight: Radius.circular(topRightRedius ?? redius),
      bottomLeft: Radius.circular(bottomLeftRedius ?? redius),
      bottomRight: Radius.circular(bottomRightRedius ?? redius),
    );

    canvas.drawRRect(rRect, shadow);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// 边框Painer
/// 白色 透明度0.08
/// 宽度1.5
/// 从卡片上到下渐变

class BorderPainer extends CustomPainter {
  BorderPainer({
    required this.redius,
    this.topRightRedius,
    this.topLeftRedius,
    this.bottomRightRedius,
    this.bottomLeftRedius,
    this.opacity,
  });

  final double redius;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  @override
  void paint(Canvas canvas, Size size) {
    // 高光
    //创建画笔
    final Paint highlight = Paint()
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5.w
      ..color = Colors.white.withOpacity(0.5 * (opacity ?? 1));

    final Rect rect = Rect.fromLTRB(
      0.5.w,
      0.5.w,
      size.width - 0.8.w,
      size.height,
    );
    final RRect rRect = RRect.fromLTRBAndCorners(
      0.5.w,
      0.5.w,
      size.width - 0.8.w,
      size.height,
      topLeft: Radius.circular(topLeftRedius ?? redius),
      topRight: Radius.circular(topRightRedius ?? redius),
      bottomLeft: Radius.circular(bottomLeftRedius ?? redius),
      bottomRight: Radius.circular(bottomRightRedius ?? redius),
    );

    highlight.shader = LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      colors: <Color>[
        Colors.white.withOpacity(0),
        Colors.white.withOpacity(0.5 * (opacity ?? 1))
      ],
    ).createShader(
      rect,
    );
    canvas.drawRRect(rRect, highlight);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
