/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2022-08-08 17:02:16
 * @description: 重写listview 去掉安卓底部水波纹效果
 */
import 'dart:io';

import 'package:flutter/material.dart';

class HideWaterRippleListView extends StatelessWidget {
  const HideWaterRippleListView({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: OverScrollBehavior(),
      child: child,
    );
  }
}

class OverScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    if (Platform.isAndroid || Platform.isFuchsia) {
      return GlowingOverscrollIndicator(
        showLeading: false,
        //不显示尾部水波纹
        showTrailing: false,
        axisDirection: details.direction,
        color: Theme.of(context).colorScheme.secondary,
        child: child,
      );
    } else {
      return child;
    }
  }
}
