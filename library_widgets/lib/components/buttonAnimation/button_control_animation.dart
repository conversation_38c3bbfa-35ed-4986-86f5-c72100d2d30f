/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-18 14:15:21
 * @description: 按钮动效
 */
import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../common/constant.dart';
import '../../common/log.dart';
import '../../common/util.dart';

final SpringSimulation quickControlSimulation = SpringSimulation(
    const SpringDescription(
        mass: 12, //质量
        stiffness: 1, //硬度
        damping: 1.5 // 阻尼
        ),
    0,
    1,
    40 // 速度
    );

class ButtonControlAnimation extends StatefulWidget {
  const ButtonControlAnimation(
      {super.key,
      this.clickType,
      required this.quickControl,
      this.text,
      required this.attr,
      this.imageAttr,
      this.child});

  final Map<dynamic, dynamic>? clickType; // 回调函数需传的值
  final void Function(Map<dynamic, dynamic>?) quickControl; // 点击以后的回调函数
  final String? text; // 按钮的文字
  final Map<dynamic, dynamic> attr; // 按钮的宽高 圆角等属性，按钮里面文字的属性：文字颜色 字体大小等属性。
  final Map<dynamic, dynamic>? imageAttr; // 按钮里面有图片的属性
  final Widget? child; // 按钮里面的子元素 通过父级传过来

  @override
  State<ButtonControlAnimation> createState() => _ButtonControlAnimationState();
}

class _ButtonControlAnimationState extends State<ButtonControlAnimation>
    with TickerProviderStateMixin {
  // 动效 改变大小
  double scale = 1.0;
  AnimationController? _quickController1;
  Animation<double>? _quickAnimation1;
  AnimationController? _quickController2;
  Animation<double>? _quickAnimation2;

  // 默认文字的属性
  TextStyle textStyle = TextStyle(
    fontSize: 15.sp,
    color: const Color.fromRGBO(0, 0, 0, 0.8),
    fontFamily: 'PingFangSC-Regular',
    fontWeight: FontWeight.w400,
  );

  void handleQuick() {
    _quickController2?.stop();
    _quickController1?.reset();
    _quickController1?.forward();
  }

  @override
  void initState() {
    // 一键开启和关闭 动效 初始化
    initQuickControlAnimation();
    super.initState();
  }

  // 回弹弹簧动效
  // TODO(sfj): 待验证 Tween<double>
  void startQuickControlAnimation() {
    _quickAnimation2 =
        _quickController2?.drive(Tween<double>(begin: 0.95, end: 1.0));
    _quickController2?.animateWith(quickControlSimulation);
  }

  // 一键开启和关闭 动效
  // TODO(sfj): 待验证 Tween<double>
  void initQuickControlAnimation() {
    try {
      _quickController1 = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 170));
      if (_quickController1 != null) {
        _quickAnimation1 = Tween<double>(begin: 1.0, end: 0.95).animate(
            CurvedAnimation(
                parent: _quickController1!, curve: Curves.easeInOutCubic));
        // 回弹效果
        _quickController2 = AnimationController.unbounded(vsync: this);
        _quickController2?.addListener(() {
          if (_quickAnimation2 != null) {
            if (_quickAnimation2!.value > 1.0) {
              scale = 1.0;
            } else {
              scale = _quickAnimation2!.value;
            }
          }
          setState(() {});
        });
        _quickController1?.addListener(() {
          scale = _quickAnimation1?.value ?? 1;
          setState(() {});
        });
        _quickController1?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {
            startQuickControlAnimation();
            Future.delayed(const Duration(milliseconds: 300), () {
              _quickController2?.stop();
              if (mounted) {
                widget.clickType != null
                    ? widget.quickControl(widget.clickType)
                    : widget.quickControl(<dynamic, dynamic>{});
              }
            });
          }
        });
        _quickController2?.addStatusListener((AnimationStatus status) {
          if (status == AnimationStatus.completed) {}
        });
      }
    } catch (e) {
      DevLogger.info(
          tag: CONSTANT.tagLibrary, msg: 'initQuickControlAnimation err:$e');
    }
  }

  @override
  void dispose() {
    _quickController1?.dispose();
    _quickController2?.dispose();
    super.dispose();
  }

  //

  // TODO(sfj): 待验证
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: debounce(() {
        handleQuick();
      }, const Duration(milliseconds: 1000)) as void Function()?,
      child: Transform.scale(
        scale: scale,
        child: widget.child ??
            Container(
              alignment: Alignment.center,
              // height: widget.attr['height'],
              height: widget.attr['height'] is double
                  ? widget.attr['height'] as double
                  : null,
              // width: widget.attr['width'],
              width: widget.attr['width'] is double
                  ? widget.attr['width'] as double
                  : null,
              decoration: BoxDecoration(
                boxShadow: <BoxShadow>[
                  BoxShadow(
                      color: Colors.black.withOpacity(0.06),
                      blurRadius: 2.w,
                      offset: Offset(0, 2.w))
                ],
                // color: widget.attr['deColor'],
                color: widget.attr['deColor'] is Color
                    ? widget.attr['deColor'] as Color
                    : null,
                // borderRadius: BorderRadius.circular(widget.attr['radius']),
                borderRadius: BorderRadius.circular(
                    widget.attr['radius'] is double
                        ? widget.attr['radius'] as double
                        : 0.0),
              ),
              // child: widget.imageAttr != null && widget.imageAttr?['url'] != ''
              child: widget.imageAttr is Map && widget.imageAttr!['url'] != ''
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Image.asset(
                          widget.imageAttr!['url'] is String
                              ? widget.imageAttr!['url'] as String
                              : '',
                          width: widget.imageAttr!['width'] is Double
                              ? widget.imageAttr!['width'] as double
                              : null,
                          height: widget.imageAttr!['height'] is double
                              ? widget.imageAttr!['height'] as double
                              : null,
                          cacheWidth: widget.imageAttr!['width'] is int
                              ? ((widget.imageAttr!['width'] as int) * getDpr())
                                  .ceil()
                              : null,
                          cacheHeight: widget.imageAttr!['height'] is int
                              ? ((widget.imageAttr!['height'] as int) *
                                      getDpr())
                                  .ceil()
                              : null,
                          scale: getDpr(),
                          fit: BoxFit.fitWidth,
                          package: widget.imageAttr!['package'] is String
                              ? widget.imageAttr!['package'] as String
                              : CONSTANT.PACKAGE_NAME,
                        ),
                        SizedBox(
                          width: 4.w,
                        ),
                        Flexible(
                            child: Text(
                              widget.text ?? '',
                              style: widget.attr['textStyle'] is TextStyle
                                  ? widget.attr['textStyle'] as TextStyle
                                  : textStyle,
                              overflow: TextOverflow.ellipsis,
                            ))
                      ],
                    )
                  : Align(
                      child: Text(
                        widget.text ?? '',
                        style: widget.attr['textStyle'] is TextStyle
                            ? widget.attr['textStyle'] as TextStyle
                            : textStyle,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
            ),
      ),
      // onTap: () {
      //   handleQuick();
      // },
    );
  }
}
