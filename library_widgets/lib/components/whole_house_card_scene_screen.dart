import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:storage/storage.dart';

import '../common/api.dart';
import '../common/color_filter/color_filter_generator.dart';
import '../common/constant.dart';
import '../common/log.dart';
import '../common/manual_operation_overlay.dart';
import '../common/mutex.dart';
import '../common/toast_helper.dart';
import '../common/util.dart';
import '../model/general_scene_model.dart';
import '../model/scene_detail_response.dart';
import '../model/scene_model.dart';
import 'buttonAnimation/button_control_animation.dart';
import 'custom_card/custom_card.dart';
import 'general_scene_popover.dart';

const ColorFilter sceneImagefilter = ColorFilter.matrix(<double>[
  1.0,
  0.0,
  0.0,
  0.0,
  200.0,
  0.0,
  1.0,
  0.0,
  0.0,
  200.0,
  0.0,
  0.0,
  1.0,
  0.0,
  200.0,
  0.0,
  0.0,
  0.0,
  1.0,
  0.0
]);

class WholeHouseCardSceneScreen extends StatefulWidget {
  const WholeHouseCardSceneScreen(
      {super.key,
      required this.listDataObj,
      required this.listIndex,
      required this.familyId,
      required this.type,
      this.roomId,
      this.roomName,
      required this.loginStatus,
      required this.doRefresh,
      this.updateCurtainWorkingAction,
      this.nowTime,
      this.demoAnimation,
      required this.tipToast,
      this.deviceNum,
      this.closeSpaceDetailPage});

  final SceneModel listDataObj;
  final int listIndex;
  final String familyId;
  final String type;
  final String? roomId;
  final String? roomName;
  final String loginStatus;
  final Future<void> Function() doRefresh;
  final Function? updateCurtainWorkingAction;
  final int? nowTime;
  final Function? demoAnimation;
  final Function? tipToast;
  final int? deviceNum;
  final Function? closeSpaceDetailPage;

  @override
  State<WholeHouseCardSceneScreen> createState() =>
      _WholeHouseCardSceneScreenState();
}

class _WholeHouseCardSceneScreenState extends State<WholeHouseCardSceneScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  @override
  bool get wantKeepAlive => true;
  bool _executingout = false;
  bool initNetwork = true;
  AnimationController? _rotationController; //旋转动画
  Animation<double>? _rotationAnimation;
  double ringOpacity = 0; //旋转圈的透明度
  // 蓝色水波纹
  AnimationController? _runController;
  Animation<int>? _animationRun;

  // 背景大小颜色变化
  double backWidth = 48.0;
  double backHeight = 48.0;
  double backRadius = 24.0;
  double backOpacity = 1.0;
  AnimationController? _sizeController;
  Animation<double>? _sizeAnimation;

  // 背景大小颜色变化完以后，显示一张图片
  bool isAnimationImageShow = false;

  // 执行场景结束
  bool isRunFinish = true;

  // 更新网络状态
  // init时 获取网络状态，用来判断是否是断网第一次打开场景tab，是否需要展示断网页面
  Future<void> updateNetworkStatus({String type = 'update'}) async {
    try {
      final IsOnline isOnline =
          await Network.isOnline();
      setState(() {
        initNetwork =
            !isOnline.isOnline ? false : true;
      });
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': 'updateNetworkStatus', 'err': err});
    }
  }

  // 是否可以弹toast, 当服务tab为当前tab时，才可以弹
  Future<void> doexecute(dynamic obj, Function? updateCurtainWorkingAction,
      Function? handleRunAnimation) async {
    if (!_executingout) {
      _executingout = true;
      try {
        await updateNetworkStatus(type: 'init');

        final String tabTypeJsonString = await Storage.getTemporaryStorage(
            CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);

        final dynamic tabTypeMap = json.decode(tabTypeJsonString);

        String tabType = '';
        if (tabTypeMap is Map<dynamic, dynamic> &&
            tabTypeMap['value'] is String) {
          tabType = tabTypeMap['value'] as String;
        }

        if (mounted) {
          await ManualOperation.execute(
              obj, initNetwork, widget.familyId, context, 'ismodel',
              updateCurtainWorkingAction: updateCurtainWorkingAction,
              handleRunAnimation: handleRunAnimation,
              tabtype: tabType);
        }

        _executingout = false;
      } catch (e) {
        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '手动执行报错 $e');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // 初始化水波纹动效
    _initWaterRipple();
    // 初始化背景颜色大小变化
    _initSizeAnimation();
    // 初始化旋转动画
    _initRotateAnimation();
  }

  @override
  void didUpdateWidget(covariant WholeHouseCardSceneScreen oldWidget) {
    // H5开启场景结束后返回flutter时不会调用initState，只会调用didUpdateWidget，所以需要在此方法中调用执行动画
    super.didUpdateWidget(oldWidget);
    if (widget.listDataObj.listType == 'scene' &&
        widget.listDataObj.triggerType != 'manually' &&
        widget.listDataObj.isOpen == 1 &&
        oldWidget.listDataObj.isOpen != 1) {
      // 自动场景且开启状态，执行动画
      _rotationController?.reset();
      _rotationController?.forward();
    }
    // 下拉刷新、设备变化、家庭变化、房间变化都会导致接口刷新、所以这些时候都会停止当前执行的场景
    if (widget.nowTime != oldWidget.nowTime) {
      ManualOperation.cleanTimerAndController(_handleRunAnimation);
    }
  }

  @override
  void dispose() {
    _rotationController?.dispose();
    _runController?.dispose();
    _sizeController?.dispose();
    super.dispose();
  }

  // 初始化旋转动画
  void _initRotateAnimation() {
    try {
      _rotationController = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 2000));
      if (_rotationController != null) {
        _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * pi).animate(
            CurvedAnimation(
                parent: _rotationController!, curve: Curves.linear));

        _rotationController!.addListener(() {
          setState(() {});
          // 自动场景
          if (widget.listDataObj.listType == 'scene' &&
              widget.listDataObj.triggerType == 'manually') {
            // 自动
            // if (_rotationAnimation!.value < pi / 2) {
            //   setState(() {
            //     ringOpacity = 0 + 100 * (_rotationAnimation!.value / (pi / 2));
            //   });
            // }
            // if (_rotationAnimation!.value > pi * 1.5) {
            //   setState(() {
            //     ringOpacity = 100 -
            //         100 * ((_rotationAnimation!.value - 1.5 * pi) / (pi / 2));
            //   });
            // }
            ringOpacity = 100;
          }
        });
      }
    } catch (e) {
      DevLogger.error(tag: CONSTANT.tagLibrary, msg: <String, Object>{
        'fn': '_initRotateAnimation error ',
        'data': e
      });
    }
  }

  Future<void> stopShowSceneAnimation() async {
    _handleRunAnimation(0);
  }

  // 动效开始执行与结束执行  1 为开始动画 0 停止动画
  void _handleRunAnimation(int type) {
    DevLogger.info(
        tag: CONSTANT.tagLibrary, msg: '_handleRunAnimation type--$type');
    // 停止动画
    try {
      if (type == 0) {
        _rotationController?.stop();
        _runController?.stop();
        // _sizeController?.stop();
        setState(() {
          isAnimationImageShow = false;
          ringOpacity = 0;
          isRunFinish = true;
        });
      }
      //  开始动画
      if (type == 1) {
        // 背景大小颜色变化
        setState(() {
          ringOpacity = 100;
          isAnimationImageShow = true;
          isRunFinish = false;
        });
        _rotationController?.reset();
        _rotationController?.repeat();
        _runController?.reset();
        _runController?.repeat();
        // _initSizeAnimation();
        // _sizeController!.reset();
        // _sizeController!.forward();
      }
    } catch (e) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': '_handleRunAnimation error ', 'data': e});
    }
  }

  // 水波纹动效初始化
  // 水波纹动效 实际执行的时间为1秒，中间停顿2秒。
  void _initWaterRipple() {
    try {
      _runController = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 3000));
      if (_runController != null) {
        _animationRun = IntTween(begin: 0, end: 72).animate(_runController!);
      }
    } catch (e) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': '_initWaterRipple error ', 'data': e});
    }
  }

  // 背景大小变化
  void _initSizeAnimation() {
    try {
      _sizeController?.dispose();
      _sizeController = AnimationController(
          vsync: this, duration: const Duration(milliseconds: 250));
      if (_sizeController != null) {
        _sizeAnimation = Tween<double>(begin: 0.0, end: 10.0).animate(
            CurvedAnimation(parent: _sizeController!, curve: Curves.easeIn));
        _sizeController!.addListener(() {
          if (_sizeAnimation?.value != null) {
            setState(() {
              backWidth = 48 + _sizeAnimation!.value * 3.2;
              backHeight = 48 + _sizeAnimation!.value * 4.8;
              backRadius = 24 - _sizeAnimation!.value * 1.2;
              backOpacity = 1 - (_sizeAnimation!.value / 10);
            });
            // 背景蓝色的图片 等背景大小变化到少一半的时候 开始显示
            if (_sizeAnimation!.value >= 5) {
              setState(() {
                isAnimationImageShow = true;
              });
            }
          }
        });
      }
    } catch (e) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': '_initSizeAnimation error ', 'data': e});
    }
  }

// 自动场景勾选
  void onChangeSwitch(dynamic item) {
    if (item is! SceneModel) {
      return;
    }
    Debounce.run(() async {
      try {
        await updateNetworkStatus(type: 'init');
        if (!initNetwork) {
          if (mounted) {
            ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
          }

          return;
        }
        if (item.isOpen == 0) {
          // 开启场景，查询互斥有数据弹框，同意开启则进行开启接口，否则return
          // 更改数组中操作项的isOpen值
        }
        Future<void> changeSceneItemIsOpen(int index, int openType) async {
          setState(() => item.isOpen = openType);
          if (openType == 1) {
            widget.doRefresh();
          }
        }

        // 打开时：区分自动泛场景，是否打开互斥弹窗，提示互斥后是否继续执行
        // 关闭时：无需区分，直接关闭
        if (mounted) {
          await changeSceneOpentype(item, widget.listIndex, context,
              changeSceneItemIsOpen, _rotationController!);
        }
      } catch (e) {
        if (mounted) {
          handleRequestError(e, context);
        }

        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '开关场景报错 $e');
      }
    });
  }

  // 公共打点位
  void LibraryGIO() {
    switch (widget.type) {
      case 'air':
        if (widget.roomId == '') {
          gioTrack('MB30316', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        } else {
          gioTrack('MB30325', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        }
      case 'kitchen':
        gioTrack('MB30253', <String, String>{
          'value': widget.listDataObj.sceneName ?? '',
        });
      case 'balcony':
        // gioTrack('MB31011');
        break;
      case 'bedroom':
        // gioTrack('MB31089', {
        //   'scene_name': widget.listDataObj.sceneName,
        // });
        break;
      case 'ic':
        // gioTrack('MB31268', {
        //   'scene': widget.listDataObj.sceneName,
        // });
        break;
      default:
        // 客厅和浴室点击单个场景点位相同
        gioTrack('MB30253', <String, String>{
          'value': widget.listDataObj.sceneName ?? '',
        });
        break;
    }
  }

  // 返回渠道来源名称
  String channelNameReturn() {
    String channelName = '';
    switch (widget.type) {
      case 'parlor':
        channelName = '客厅一级';
      case 'kitchen':
        channelName = '厨房一级';
      case 'bedroom':
        channelName = '卧室一级';
      case 'bathroom':
        channelName = '浴室一级';
      case 'balcony':
        channelName = '阳台一级';
      case 'ic':
        channelName = '智控一级';
      case 'air':
        channelName = '空气一级';
      case 'water':
        channelName = '用水一级';
      case 'heating':
        channelName = '采暖一级';
    }
    return channelName;
  }

// 获取泛场景模板详情数据并打开设备弹框
  Future<void> showBottomSheet(BuildContext context) async {
    try {
      showNotNetwork();
      final List<GeneralSceneModel> sceneData = <GeneralSceneModel>[];
      final Map<dynamic, dynamic> ret = await API.getGeneralDetail(
          widget.familyId,
          widget.listDataObj.templateId ?? '',
          widget.listDataObj.type ?? '');
      final SceneDetailResponse response = SceneDetailResponse.fromJson(ret);
      final Map<String, DeviceInfoModel> deviceList =
          await UpDevicePlugin.getDeviceInfoMap(widget.familyId);

      if (response.retCode == CONSTANT.REQUEST_SUCCESS_CODE) {
        sceneData.addAll(response.data.base.serviceSceneConfig.deviceInfoList);

        final String deviceUI = response.data.base.serviceSceneConfig.deviceUI;

        // 如果设备数大于1 ，打开弹窗,
        if (sceneData.length > 1) {
          //用于在底部打开弹框的效果
          if (context.mounted) {
            showModalBottomSheet<dynamic>(
                isDismissible: false,
                isScrollControlled: true,
                backgroundColor: const Color.fromRGBO(255, 255, 255, 0.93),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(10.w),
                    topRight: Radius.circular(10.w),
                  ),
                ),
                context: context,
                builder: (BuildContext context) {
                  return GeneralScenePopover(
                      sceneData: sceneData,
                      deviceList: deviceList,
                      deviceUI: deviceUI);
                });
          }
        } else if (sceneData.length == 1) {
          try {
            final String url =
                await assembleUrl(deviceUI, deviceList, sceneData[0].deviceId);
            if (context.mounted) {
              goToPageNoDebounce(url, widget.closeSpaceDetailPage, context);
            }
          } catch (err) {
            DevLogger.info(
                tag: CONSTANT.tagLibrary, msg: '展示类泛场景一个设备默认选中并跳转 err:$err');
          }
        } else if (sceneData.isEmpty) {
          // 没有可供选择设备，则需要toast提示,一下拼接toast内容
          // 如果品类数组不为空，则获取第0项的prodtypeName，拼接提示语
          if (response.data.base.serviceSceneConfig.sceneEnableAdaptation
              .serviceSceneProdTypes is List) {
            final List<ServiceSceneProdType> serviceSceneProdTypes = response
                .data
                .base
                .serviceSceneConfig
                .sceneEnableAdaptation
                .serviceSceneProdTypes!;
            final String roomId = widget.roomId ?? '';
            // 获取渠道来源名称，传递给详情页面
            String channelName = '';
            channelName = Uri.encodeComponent(channelNameReturn());
            if (serviceSceneProdTypes.isNotEmpty) {
              final String name = serviceSceneProdTypes[0].prodTypeName;
              // 没有设备，但必选设备有值时跳转通用详情页
              if (name.isNotEmpty && context.mounted) {
                goToPageNoDebounce(
                    'mpaas://scene?needAuthLogin=1&familyId=${widget.familyId}&sceneId=${widget.listDataObj.templateId}&type=${response.data.base.type}&roomId=$roomId&isDisplay=1&channelName=$channelName&sceneName=${widget.listDataObj.sceneName}&version=${widget.listDataObj.version}#generaldetail',
                    widget.closeSpaceDetailPage,
                    context);
              }
              return;
            } else if (serviceSceneProdTypes.isEmpty) {
              // 没有设备，必选设备无值时跳转通用详情页
              if (context.mounted) {
                goToPageNoDebounce(
                    'mpaas://scene?needAuthLogin=1&familyId=${widget.familyId}&sceneId=${widget.listDataObj.templateId}&type=${response.data.base.type}&roomId=$roomId&isDisplay=1&channelName=$channelName&sceneName=${widget.listDataObj.sceneName}&version=${widget.listDataObj.version}#generaldetail',
                    widget.closeSpaceDetailPage,
                    context);
              }
              return;
            }
          } else {
            if (context.mounted) {
              goToPageNoDebounce(deviceUI, widget.closeSpaceDetailPage, context);
            }
          }
        }
      }
    } catch (err) {
      DevLogger.info(
          tag: CONSTANT.tagLibrary, msg: '_getExclusiveData err:$err');
    }
  }

  void clickSceneOption() {
    String entrance = '';
    switch (widget.type) {
      case 'air':
        entrance = '3+5-全屋空气-推荐场景';
      case 'kitchen':
        entrance = '3+5-智慧厨房-推荐场景';
      case 'water':
        entrance = '3+5-全屋用水-推荐场景';
      case 'bathroom':
        entrance = '3+5-智慧浴室-推荐场景';
      case 'balcony':
        entrance = '3+5-智慧阳台-推荐场景';
      default:
        entrance = '3+5-智慧客厅-推荐场景';
        break;
    }
    gioTrack('MB31335', <String, String>{
      'scene_name': widget.listDataObj.sceneName ?? '',
      'entrance': entrance
    });
  }

  Future<void> showNotNetwork() async {
    final IsOnline isOnline =
        await Network.isOnline();
    if (!isOnline.isOnline && mounted) {
      ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
      return;
    }
  }

  // 手动或者推荐场景点击埋点
  void enabledOrRecommendScenarioClick() {
    String modelType = '';
    if (widget.listDataObj.listType == 'scene') {
      modelType = '手动';
    } else if (widget.listDataObj.listType == 'template') {
      modelType = '推荐';
    }
    switch (widget.type) {
      case 'bedroom':
        gioTrack('MB31089', <String, String>{
          'model_type': modelType,
          'scene_name': widget.listDataObj.sceneName ?? ''
        });
      case 'balcony':
        gioTrack('MB31011', <String, String>{
          'model_type': modelType,
          'scene_name': widget.listDataObj.sceneName ?? ''
        });
      case 'parlor':
        gioTrack('MB32478', <String, String>{
          'model_type': modelType,
          'scene_name': widget.listDataObj.sceneName ?? ''
        });
      case 'kitchen':
        gioTrack('MB32479', <String, String>{
          'model_type': modelType,
          'scene_name': widget.listDataObj.sceneName ?? ''
        });
      case 'bathroom':
        gioTrack('MB32480', <String, String>{
          'model_type': modelType,
          'scene_name': widget.listDataObj.sceneName ?? ''
        });
      case 'ic':
        gioTrack('MB31268', <String, String>{
          'model_type': modelType,
          'scene': widget.listDataObj.sceneName ?? ''
        });
    }
  }

  // 泛场景和高阶场景点击埋点，处理channel_name
  String getChannelName() {
    // 处理channelName，专业系统-全屋空气-空间专属-具体空间这种情况下需要特殊处理
    String channelName = channelNameReturn();
    if (widget.type == CONSTANT.SYSTEM_AIR &&
        widget.roomName != '' &&
        widget.roomName != null) {
      channelName = '$channelName-${widget.roomName}';
    }
    channelName = Uri.decodeComponent(Uri.encodeComponent(channelName));
    return channelName;
  }

  // TODO(sfj): 待验证
  Future<void> _sceneItemClick(Map<dynamic, dynamic>? param) async {
    await updateNetworkStatus(type: 'init');
    if (!initNetwork && mounted) {
      ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
      return;
    }
    // 830改版专属场景点击埋点
    if (widget.listDataObj.listType == CONSTANT.SCENE_TYPE_MORE) {
      gioTrack('MB34535',
          <String, String>{CONSTANT.CHANNEL_NAME: widget.roomName ?? '全屋'});
    } else {
      gioTrack('MB34534', <String, String>{
        CONSTANT.SCENE_NAME: widget.listDataObj.sceneName ?? '',
        CONSTANT.CHANNEL_NAME: widget.roomName ?? '全屋',
        CONSTANT.CONTENT_TYPE: widget.listDataObj.type ?? ''
      });
    }
    // 包含已启用的泛场景、模板场景、可以直接执行开启关闭：scene
    clickSceneOption();
    // 泛场景SERVICE埋点
    if (widget.listDataObj.type == CONSTANT.SCENE_SERVICE) {
      if (widget.listDataObj.triggerType == CONSTANT.TRIGGERTYPE_DISPLAY) {
        // 展示类泛场景埋点
        gioTrack('MB32501', <String, String>{
          CONSTANT.SCENE_NAME: widget.listDataObj.sceneName ?? '',
          CONSTANT.CHANNEL_NAME: getChannelName()
        });
      } else {
        // 非展示类泛场景埋点
        gioTrack('MB34049', <String, String>{
          CONSTANT.SCENE_NAME: widget.listDataObj.sceneName ?? '',
          CONSTANT.CHANNEL_NAME: getChannelName()
        });
      }
    }
    if (widget.listDataObj.type == CONSTANT.SCENE_ADV) {
      // 高阶场景埋点单独拎出来了，不跟以前一样作为非展示类泛场景埋点了
      gioTrack('MB34356', <String, String>{
        CONSTANT.SCENE_NAME: widget.listDataObj.sceneName ?? '',
        CONSTANT.CHANNEL_NAME: getChannelName()
      });
    }
    if (widget.listDataObj.listType == 'scene') {
      try {
        if (widget.type == 'water') {
          gioTrack('MB30361', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        } else if (widget.type == 'heating') {
          gioTrack('MB30990', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        } else {
          LibraryGIO();
        }
      } catch (e) {
        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '执行打点报错 $e');
      }

      if (widget.listDataObj.status != null &&
          widget.listDataObj.status == CONSTANT.SCENE_STATUS_1 && mounted) {
        ToastHelperLibrary.showToast(CONSTANT.SCENE_MISS_DEVICE, context);
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg: 'scene expired(status==1), click item show toast, return.');
        return;
      }

      // 手动执行  动画：
      if (widget.listDataObj.triggerType == 'manually') {
        // 专属场景新埋点，根据空间和场景是启用还是推荐区分
        // 已启用手动场景
        enabledOrRecommendScenarioClick();
        doexecute(widget.listDataObj, widget.updateCurtainWorkingAction,
            _handleRunAnimation);
      } else {
        // 调用开启关闭接口，开启前做互斥判断
        onChangeSwitch(widget.listDataObj);
      }
    } else if (widget.listDataObj.listType == 'template') {
      //包含模板场景、泛场景未下载或者未启用的场景：template
      try {
        if (widget.type == 'water') {
          gioTrack('MB30360', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        } else if (widget.type == 'heating') {
          gioTrack('MB30989', <String, String>{
            'value': widget.listDataObj.sceneName ?? '',
          });
        } else {
          LibraryGIO();
        }
        // 专属场景新埋点，根据空间和场景是启用还是推荐区分
        // 推荐场景点击埋点
        enabledOrRecommendScenarioClick();
      } catch (e) {
        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '执行打点报错 $e');
      }
      // 专属场景   IFTTT,泛场景-SERVICE
      if (widget.listDataObj.type == 'IFTTT' && mounted) {
        String channelName = '';
        channelName = Uri.encodeComponent(channelNameReturn());
        goToPage(
            'mpaas://scene?needAuthLogin=1&scene_id=${widget.listDataObj.templateId}&testdata=0&target_url=${Uri.encodeComponent(CONSTANT.TO_DEVICE_SCENE_MINE)}&channelName=$channelName&sceneName=${widget.listDataObj.sceneName}#templatedetail',
            widget.closeSpaceDetailPage,
            context);
      } else {
        //  泛场景
        serviceSceneToDetail();
      }
    } else if (widget.listDataObj.listType == 'showScene') {
      // 点击演示场景打点
      try {
        switch (widget.type) {
          case 'parlor':
            gioTrack('MB32124', <String, String>{'value': '0'});
          case 'kitchen':
            gioTrack('MB32125', <String, String>{'value': '0'});
          case 'balcony':
            gioTrack('MB32127', <String, String>{'value': '0'});
          case 'bedroom':
            gioTrack('MB32128', <String, String>{'value': '0'});
          case 'bathroom':
            gioTrack('MB32126', <String, String>{'value': '0'});
        }
        _handleRunAnimation(1);
        if (widget.demoAnimation != null) {
          widget.demoAnimation!(widget.tipToast);
        }
      } catch (e) {
        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '执行打点报错 $e');
      }
    } else {
      // 点击更多打点
      try {
        switch (widget.type) {
          case 'parlor':
            gioTrack('MB31919', <String, String>{'name': '客厅'});
          case 'water':
            gioTrack('MB30362');
          case 'air':
            gioTrack('MB30459');
          case 'kitchen':
            gioTrack('MB31920', <String, String>{'name': '厨房'});
          case 'balcony':
            gioTrack('MB31012');
          case 'bedroom':
            gioTrack('MB31090');
          case 'ic':
            gioTrack('MB31269');
          case 'bathroom':
            gioTrack('MB31921', <String, String>{'name': '浴室'});
          default:
            break;
        }
      } catch (e) {
        DevLogger.debug(tag: CONSTANT.tagLibrary, msg: '执行打点报错 $e');
      }
      final String roomId = widget.roomId ?? '';
      if (mounted) {
        goToPage(
            'mpaas://scene?needAuthLogin=1&type=${widget.type}&roomid=$roomId&familyid=${widget.familyId}&deviceNum=${widget.deviceNum}&version=${widget.listDataObj.version}#wholehousescenelist',
            widget.closeSpaceDetailPage,
            context);
      }
    }
  }

// 泛场景跳转详情
  void serviceSceneToDetail() {
    if (widget.listDataObj.triggerType == 'display') {
      Debounce.run(() async {
        // 展示类泛场景需要查询泛场景模板详情,拿到跳转链接
        if (widget.loginStatus == '已登录') {
          showBottomSheet(context);
        } else {
          goToPage(CONSTANT.LOGIN_URL, widget.closeSpaceDetailPage, context);
        }
      });
    } else {
      final String roomId = widget.roomId ?? '';
      String channelName = '';
      channelName = Uri.encodeComponent(channelNameReturn());
      goToPage(
          'mpaas://scene?needAuthLogin=1&familyId=${widget.familyId}&sceneId=${widget.listDataObj.templateId}&type=${widget.listDataObj.type}&spaceType=${widget.listDataObj.spaceType ?? ''}&roomId=$roomId&channelName=$channelName&sceneName=${widget.listDataObj.sceneName}&version=${widget.listDataObj.version}#generaldetail',
          widget.closeSpaceDetailPage,
          context);
    }
  }

  // 长按进入详情
  Future<void> longPressEnterDetail() async {
    await updateNetworkStatus(type: 'init');
    if (!initNetwork && mounted) {
      ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
      return;
    }
    // 启用过的场景
    if (widget.listDataObj.listType == 'scene') {
      if (widget.listDataObj.type == 'SERVICE' ||
          widget.listDataObj.type == 'ADV') {
        // 泛场景
        if (widget.listDataObj.triggerType != 'display') {
          String channelName = '';
          channelName = Uri.encodeComponent(channelNameReturn());
          //  启用后的非展示类泛场景
          if (mounted) {
            goToPage(
                'mpaas://scene?needAuthLogin=1&sceneId=${widget.listDataObj.id}&type=${widget.listDataObj.type}&spaceType=${widget.listDataObj.spaceType ?? ''}&channelName=$channelName&isOpen=${widget.listDataObj.isOpen == 1 ? 1 : 0}#MyGeneralSceneDetail',
                widget.closeSpaceDetailPage,
                context);
          }
        }
      } else {
        // 启用过的手动场景进入场景详情
        if (mounted) {
          goToPage(
              'mpaas://scene?sceneId=${widget.listDataObj.id}&from=list#scenedetail',
              widget.closeSpaceDetailPage,
              context);
        }
      }
    } else if (widget.listDataObj.listType == 'template') {
      //（推荐场景）
      if (widget.listDataObj.type == 'SERVICE' ||
          widget.listDataObj.type == 'ADV') {
        // 泛场景
        serviceSceneToDetail();
      } else {
        // 普通场景（模板场景）
        String channelName = '';
        channelName = Uri.encodeComponent(channelNameReturn());
        if (mounted) {
          goToPage(
              'mpaas://scene?needAuthLogin=1&scene_id=${widget.listDataObj.templateId}&testdata=0&target_url=${Uri.encodeComponent(CONSTANT.TO_DEVICE_SCENE_MINE)}&channelName=$channelName&sceneName=${widget.listDataObj.sceneName}#templatedetail',
              widget.closeSpaceDetailPage,
              context);
        }
      }
    }
  }

  Widget isSmartControlSpace() {
    return CustomCard(
      width: 80.w,
      height: 96.w,
      redius: 12,
      hasBlur: false,
      child: sceneItemCard(),
    );
  }

  Widget sceneItemCard() {
    return SizedBox(
        width: 80.w,
        height: 96.w,
        child: Stack(
          children: <Widget>[
            Container(
              width: 80.w,
              height: 96.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
              ),
            ),
            // 背景大小位置的变化
            Positioned(
                left: ((80 - backWidth) / 2).w,
                top: ((88 - backHeight) / 4).w,
                child: Opacity(
                  opacity: backOpacity,
                  child: Container(
                    width: backWidth.w,
                    height: backHeight.w,
                    decoration: BoxDecoration(
                      color: const Color(0xFFCDE7FD),
                      borderRadius: BorderRadius.circular(backRadius.w),
                    ),
                  ),
                )),
            if (isRunFinish) Container() else Positioned(
                    left: 2.w,
                    top: 2.w,
                    child: AnimatedOpacity(
                      duration: const Duration(microseconds: 250),
                      opacity: isAnimationImageShow ? 1 : 0,
                      child: Image.asset(
                        'assets/images/picture_back.png',
                        package: CONSTANT.PACKAGE_NAME,
                        width: 76.w,
                        height: 92.w,
                        fit: BoxFit.cover,
                      ),
                    )),

            SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Column(children: <Widget>[
                Container(
                  margin: EdgeInsets.only(top: 10.w),
                  height: 48.w,
                  width: 48.w,
                  child: Stack(
                    clipBehavior: Clipfalse,
                    children: <Widget>[
                      AnimatedOpacity(
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut,
                        // 自动场景并且关闭状态0.5透明度，其他场景或状态1透明度
                        opacity: widget.listDataObj.listType == 'scene' &&
                                widget.listDataObj.triggerType != 'manually' &&
                                (widget.listDataObj.isOpen == 0)
                            ? 0.5
                            : 1,
                        child: Stack(
                          children: <Widget>[
                        // 图标后面 蓝色的背景
                        if (isRunFinish)
                          Container(
                            width: 48.w,
                            height: 48.w,
                            decoration: BoxDecoration(
                              color: const Color(0xFFCDE7FD),
                              borderRadius: BorderRadius.circular(48.w),
                            ),
                          )
                        else
                          Container(
                            width: 48.w,
                            height: 48.w,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(48.w),
                            ),
                          ),
                        if (widget.listDataObj.listType == 'scene' &&
                            widget.listDataObj.triggerType == 'manually' &&
                            (widget.listDataObj.icon == null ||
                                widget.listDataObj.icon == ''))
                          Image.asset(
                            'assets/images/scene-hand.png',
                            width: 48.w,
                            height: 48.w,
                            package: CONSTANT.PACKAGE_NAME,
                          )
                        else
                          widget.listDataObj.listType != 'more' &&
                                  widget.listDataObj.listType != 'showScene'
                              ? ColorFiltered(
                            colorFilter: ColorFilter.matrix(
                                      ColorFilterGenerator
                                          .brightnessAdjustMatrix(
                                    value: widget.listDataObj.colorFilter
                                            ?.brightness ??
                                        0.0,
                                  )),
                                  child: ColorFiltered(
                                    colorFilter: ColorFilter.matrix(
                                        ColorFilterGenerator
                                            .saturationAdjustMatrix(
                                      value: widget.listDataObj.colorFilter
                                              ?.saturation ??
                                          0.0,
                                    )),
                                    child: ColorFiltered(
                                      colorFilter: ColorFilter.matrix(
                                          ColorFilterGenerator
                                              .hueAdjustMatrix(
                                        value: widget.listDataObj
                                                .colorFilter?.hue ??
                                            0.0,
                                      )),
                                      child: Image.network(
                                        widget.listDataObj.icon ?? '',
                                        fit: BoxFit.cover,
                                        width: 48.w,
                                        height: 48.w,
                                        loadingBuilder:
                                            (BuildContext context,
                                                Widget child,
                                                ImageChunkEvent?
                                                    loadingProgress) {
                                          if (loadingProgress == null) {
                                            return child;
                                          }
                                          return Image.asset(
                                            CONSTANT.iconDefaultTopN,
                                            package: CONSTANT.PACKAGE_NAME,
                                            width: 48.w,
                                            height: 48.w,
                                            fit: BoxFit.cover,
                                          );
                                        },
                                        errorBuilder: (BuildContext context,
                                            Object error,
                                            StackTrace? stackTrace) {
                                          return Image.asset(
                                            CONSTANT.iconDefaultTopN,
                                            package: CONSTANT.PACKAGE_NAME,
                                            width: 48.w,
                                            height: 48.w,
                                            fit: BoxFit.cover,
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                )
                              : widget.listDataObj.listType == 'more'
                                    ? Image.asset(
                                        'assets/images/scene-more.png',
                                        width: 48.w,
                                        height: 48.w,
                                        fit: BoxFit.cover,
                                        package: CONSTANT.PACKAGE_NAME,
                                      )
                                    : Image.asset(
                                        'assets/images/icon_yanshi.png',
                                        width: 48.w,
                                        height: 48.w,
                                        fit: BoxFit.cover,
                                        package: CONSTANT.PACKAGE_NAME,
                                      ),
                                                  ],
                                                ),
                      ),
                      // 图片上面覆盖一个图片 解决边缘模糊的情况
                      Positioned(
                          left: 0,
                          top: 0,
                          child: Opacity(
                              opacity: isRunFinish ? 1 : 0,
                              child: Image.asset(
                                'assets/images/icon_up_img.png',
                                width: 48.w,
                                height: 48.w,
                                fit: BoxFit.cover,
                                package: CONSTANT.PACKAGE_NAME,
                              ))),
                      // 蓝色的旋转圈
                      Positioned(
                          left: -4.w,
                          top: -4.w,
                          child: _rotationAnimation != null
                              ? Transform.rotate(
                                  angle: _rotationAnimation!.value,
                                  child: Opacity(
                                    opacity: ringOpacity / 100,
                                    child: SizedBox(
                                      width: 56.w,
                                      height: 56.w,
                                      child: Image.asset(
                                        'assets/images/running_ring_blue.png',
                                        package: CONSTANT.PACKAGE_NAME,
                                      ),
                                    ),
                                  ),
                                )
                              : Container()),
                    ],
                  ),
                ),
                Container(
                  width: 66.w,
                  height: 18.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 8.w),
                  child: Text(
                    widget.listDataObj.sceneName ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: const Color.fromRGBO(0, 0, 0, 0.80),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                )
              ]),
            ),
            // 蓝色的水波纹
            if (_animationRun != null && ringOpacity != 0)
              Positioned(
                  left: 0,
                  top: -6.w,
                  child: SizedBox(
                    width: 80.w,
                    height: 80.w,
                    child: AnimatedBuilder(
                        animation: _animationRun!,
                        builder: (BuildContext context, Widget? child) {
                          return Image.asset(
                            'assets/images/water_ripple/circle_${_animationRun!.value > 24 ? 24 : _animationRun!.value}.png',
                            package: CONSTANT.PACKAGE_NAME,
                            width: 80.w,
                            height: 80.w,
                            cacheWidth: (80.w * getDpr()).ceil(),
                            cacheHeight: (80.w * getDpr()).ceil(),
                            gaplessPlayback: true,
                            fit: BoxFit.fitWidth,
                            scale: getDpr(),
                          );
                        }),
                )) else
              Container(),
            if (widget.listDataObj.listType == 'template') Positioned(
              right: 1.5.w,
              top: 1.5.w,
              child: Image.asset(
                'assets/images/icon_suggest.png',
                width: 24.w,
                height: 14.w,
                package: CONSTANT.PACKAGE_NAME,
              ),
            ) else
              Container(),
            if (widget.listDataObj.listType == 'scene' &&
                widget.listDataObj.triggerType == 'platform') Positioned(
              top: 2.w,
              right: 2.w,
              child: Image.asset(
                widget.listDataObj.isOpen == 1
                    ? 'assets/images/icon_auto.png'
                    : 'assets/images/icon_auto_off.png',
                width: 24.w,
                height: 14.w,
                package: CONSTANT.PACKAGE_NAME,
              ),
            ) else
              Container(),
            if (widget.listDataObj.listType == 'showScene') Positioned(
              right: 1.5.w,
              top: 1.5.w,
              child: Image.asset(
                'assets/images/icon_experience.png',
                width: 24.w,
                height: 14.w,
                package: CONSTANT.PACKAGE_NAME,
              ),
            ) else
              Container(),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return GestureDetector(
        onTap: () {
          if (!(widget.listDataObj.triggerType == 'manually' &&
              widget.listDataObj.listType == 'scene')) {
            _sceneItemClick(null);
          }
        },
        onLongPress: () {
          // 长按进入详情页
          longPressEnterDetail();
        },
        child: Container(
            margin: widget.listIndex == 3
                ? EdgeInsets.zero
                : EdgeInsets.only(right: 8.w),
            child: (widget.listDataObj.triggerType == 'manually' &&
                        widget.listDataObj.listType == 'scene') ||
                    widget.listDataObj.listType == 'showScene'
                ? ButtonControlAnimation(
                attr: const <dynamic, dynamic>{},
                    quickControl: _sceneItemClick,
                    child: isSmartControlSpace())
                : isSmartControlSpace()));
  }
}
