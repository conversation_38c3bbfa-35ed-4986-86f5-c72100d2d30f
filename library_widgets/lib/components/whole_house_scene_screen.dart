import 'dart:async';
import 'dart:convert';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart' as userPlugin;
import 'package:user/user.dart';

import '../common/api.dart';
import '../common/color_filter/color_filter_util.dart';
import '../common/constant.dart';
import '../common/log.dart';
import '../common/toast_helper.dart';
import '../common/util.dart';
import '../model/AirSceneRecommendResponse.dart';
import '../model/Air_scene_model.dart';
import '../model/scene_model.dart';
import '../model/scene_recommend_response.dart';
import '../store/actions.dart' as Action;
import '../store/reducers/library_reducer.dart';
import '../store/state.dart';
import 'common_scene_item.dart';
import 'whole_house_card_scene_screen.dart';

class WholeHouseSceneScreen extends StatefulWidget {
  const WholeHouseSceneScreen({
    super.key,
    required this.type,
    this.roomId,
    this.roomName,
    this.familyId,
    this.isShowRecommendDataBase,
    this.updateCurtainWorkingAction,
    this.deviceNum,
    this.demoAnimation,
    this.closeSpaceDetailPage,
  });
  final String type;
  final String? roomId;
  final String? roomName;
  final String? familyId;
  final Function? isShowRecommendDataBase;
  final Function? updateCurtainWorkingAction;
  final int? deviceNum;
  final Function? demoAnimation;
  final Function? closeSpaceDetailPage;

  @override
  State<WholeHouseSceneScreen> createState() => _WholeHouseSceneScreenState();
}

class _WholeHouseSceneScreenState extends State<WholeHouseSceneScreen>
    with AutomaticKeepAliveClientMixin {
  final Store<LibraryWidgetState> store = Store<LibraryWidgetState>(
      LibraryReducer,
      initialState: LibraryWidgetState.initial());

  @override
  bool get wantKeepAlive => true;
  final GlobalKey appKey = GlobalKey<State>(debugLabel: CONSTANT.PACKAGE_NAME);

  GlobalKey showSceneGlobalKey = GlobalKey();

// 卡片要展示的最终数组
  List<SceneModel> showListData = <SceneModel>[];

  // 空气专属场景
  List<AirSceneModel> airListData = <AirSceneModel>[];
  String loginStatus = '未登录';

  /// init时是否断网
  bool initNetwork = true;
  int? nowTime = DateTime.now().millisecondsSinceEpoch;

  @override
  void initState() {
    super.initState();
    doRefreshData();
    getTemporyStorage();
    cleanRunDemoTime();
  }

  @override
  void didUpdateWidget(WholeHouseSceneScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 设备数发生变化，专属场景未能及时更新，导致数据没有及时更新
    if ((oldWidget.deviceNum != widget.deviceNum) &&
        (oldWidget.deviceNum == 0 || widget.deviceNum == 0)) {
      doRefreshData();
      DevLogger.info(
          tag: CONSTANT.tagLibrary,
          msg:
              'library_widget didUpdateWidget_deviceNum oldWidget-${oldWidget.deviceNum}-widget--${widget.deviceNum}');
    }
  }

  //二级页设定的缓存是永久的，所以在初始化时清空该缓存
  Future<bool> cleanRunDemoTime() async {
    return Storage.putStringValue('demo_scene_run_time', '');
  }

  Future<void> getTemporyStorage() async {
    Storage.addDataListener(CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET,
        (_, __) async {
          final String isSceneshow = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET);
          final String changeTabtype = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);
      if (json.decode(isSceneshow)['value'] == '1' &&
          json.decode(changeTabtype)['value'] == 'recommend') {
        ToastHelperLibrary.updateCanShow(true);
      } else {
        ToastHelperLibrary.closeToast();
      }
    });
  }

  // 拿登录信息
  Future<void> _getLoginStatus() async {
    try {
      final LoginStatus res = await userPlugin.User.getLoginStatus();
      DevLogger.info(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': '_getLoginStatus', 'data': res});
      setState(() {
        loginStatus = res.isLogining ? '登录中' : (res.isLogin ? '已登录' : '未登录');
      });
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'_getLoginStatus err': err});
    }
  }

  // 更新网络状态
  // init时 获取网络状态，用来判断是否是断网第一次打开场景tab，是否需要展示断网页面
  Future<void> updateNetworkStatus({String type = 'update'}) async {
    try {
      final IsOnline isOnline =
          await Network.isOnline();
      setState(() {
        initNetwork =
            !isOnline.isOnline ? false : true;
      });
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'fn': 'updateNetworkStatus', 'err': err});
    }
  }

  // 获取鉴权信息
  static Future<void> _getOauthData() async {
    try {
      final OauthData res = await userPlugin.User.getOauthData();
      DevLogger.info(
          tag: CONSTANT.tagLibrary,
          msg: <String, OauthData>{'_getOauthData': res});
      // 更新鉴权
      // 更新api的鉴权信息
      API.updateApiParams(oauthData: res);
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'_getOauthData err': err});
    }
  }

  // 刷新卡片数据
  Future<void> doRefreshData() async {
    await updateNetworkStatus(type: 'init');
    await _getLoginStatus();
    DevLogger.info(
        tag: CONSTANT.tagLibrary,
        msg: 'library_widget doRefreshData deviceNum- ${widget.deviceNum}');
    if (initNetwork) {
      _getSceneListData(3, '');
    }
    ToastHelperLibrary.closeToast();
    nowTime = DateTime.now().millisecondsSinceEpoch;
    getRunDemoSceneTime();
  }

  // 获取演示场景执行的时间缓存
  Future<void> getRunDemoSceneTime() async {
    try {
      // 获取store
      final Map<dynamic, dynamic>? runDemoScene = store.state.runDemoScene;
      // 获取storage
      final String demoSceneRunTimeString =
          await Storage.getStringValue('demo_scene_run_time');
      if (demoSceneRunTimeString != '') {
        final dynamic decodeValue =
            const JsonDecoder().convert(demoSceneRunTimeString);
        final Map<dynamic, dynamic> demoSceneRunTime =
            decodeValue is Map<dynamic, dynamic>
                ? decodeValue
                : <dynamic, dynamic>{};
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg:
                'library_widget getRunDemoSceneTime _demoSceneRunTime- $demoSceneRunTime  _runDemoScene-$runDemoScene');
        if (demoSceneRunTime.containsKey(widget.type)) {
          if (runDemoScene == null) {
            judgeDeviceNum(demoSceneRunTime);
          } else {
            // 如果不包含该type
            if (!runDemoScene.containsKey(widget.type)) {
              judgeDeviceNum(demoSceneRunTime);
            } else if (runDemoScene.containsKey(widget.type) &&
                runDemoScene[widget.type] != demoSceneRunTime[widget.type]) {
              judgeDeviceNum(demoSceneRunTime);
            }
          }
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: 'library_widget getRunDemoSceneTime - $err');
    }
  }

  // 判断空间下是否有设备，更改store中的值
  void judgeDeviceNum(Map<dynamic, dynamic> demoSceneRunTime) {
    try {
      if (widget.deviceNum != null && widget.deviceNum == 0) {
        if (widget.demoAnimation != null) {
          widget.demoAnimation!(tipToast);
        }
      } else if (widget.deviceNum != null && widget.deviceNum! > 0) {
        WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
          ToastHelperLibrary.showToast('当前空间已有设备，快去体验吧', context);
        });
      }
      store.dispatch(Action.UpdateRunDemoSceneAction(demoSceneRunTime));
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: 'library_widget judgeDeviceNum - $err');
    }
  }

  // Future<void> tipToastPlus(String type) async {
  //   if (type == '1') {
  //     ToastHelperLibrary.showToast('场景执行中', context);
  //   } else {
  //     ToastHelperLibrary.showToast('执行完了', context);
  //   }
  // }

  // 给父组件返回一个回调，获得当前获得的数组长度
  void myCallBack(int onPress) {
    if (widget.isShowRecommendDataBase != null) {
      widget.isShowRecommendDataBase!(onPress);
    }
  }

  // TODO(sfj): 待验证
  Future<void> _getSceneListData(int topN, String type) async {
    try {
      await _getOauthData();
      Map<dynamic, dynamic> res = <dynamic, dynamic>{};
      dynamic result;
      if (widget.type == 'parlor' ||
          widget.type == 'kitchen' ||
          widget.type == 'balcony' ||
          widget.type == 'bathroom' ||
          widget.type == 'bedroom') {
        //全屋空间查询(已登录、家庭ID为空，不调用接口（已和李春乐确认）)
        if (loginStatus == '已登录' &&
            widget.familyId != '' &&
            widget.familyId != null &&
            widget.roomId != '' &&
            widget.roomId != null) {
          result =
              await API.getListByRoom(widget.familyId, topN, widget.roomId);
        } else {
          // 未登录、已登录familyId不为空，roomId为空，执行此接口、familyId传空（已和田敏、李春乐确认）
          if (loginStatus == '未登录' ||
              (loginStatus == '已登录' &&
                  widget.familyId != '' &&
                  widget.familyId != null &&
                  widget.roomId == '')) {
            String roomType = '';
            if (widget.type == 'parlor') {
              roomType = 'LIVING_ROOM';
            } else if (widget.type == 'kitchen') {
              roomType = 'KITCHEN';
            } else if (widget.type == 'balcony') {
              roomType = 'BALCONY';
            } else if (widget.type == 'bedroom') {
              roomType = 'BEDROOM';
            } else {
              roomType = 'BATHROOM';
            }
            result = await API.getListByRoomLoginOut('', topN, '', roomType);
          }
        }
        DevLogger.info(tag: CONSTANT.tagLibrary, msg: '空间接口返回：$res');
      } else if ((widget.type == 'water' || widget.type == 'heating') &&
          loginStatus == '已登录' &&
          widget.familyId != '' &&
          widget.familyId != null) {
        // 用水和采暖
        result =
            await API.getListByScheme(widget.familyId, widget.type, topN, null);

        DevLogger.info(tag: CONSTANT.tagLibrary, msg: '用水接口返回：$res');
      } else if (widget.type == 'air') {
        // 全屋空气
        if (loginStatus == '已登录' &&
            widget.familyId != '' &&
            widget.familyId != null) {
          DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg: '空气产业云场景接口入参：familyId: ${widget.familyId},'
                'roomId: ${widget.roomId}');
            result = await API.getAirScene(
                widget.familyId, widget.roomId);
            DevLogger.info(tag: CONSTANT.tagLibrary, msg: '空气产业云场景接口返回：$result');
            _assembleAirData(result);
            return;
        } else {
          return;
        }
      } else {
        // 包含空气air和智控ic
        if (loginStatus == '已登录' &&
            widget.familyId != '' &&
            widget.familyId != null) {
          if (widget.roomId != '') {
            result = await API.getListByScheme(
                widget.familyId, widget.type, topN, widget.roomId);
          } else {
            result = await API.getListByScheme(
                widget.familyId, widget.type, topN, null);
          }
          DevLogger.info(tag: CONSTANT.tagLibrary, msg: '空气接口返回：$res');
        } else {
          return;
        }
      }

      if (result is Map<dynamic, dynamic>) {
        res = result;
      }
      final SceneRecommendResponse response =
          SceneRecommendResponse.fromJson(res);
      final List<SceneModel> sceneList = <SceneModel>[];
      final List<SceneModel> templateList = <SceneModel>[];
      if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS) {
        List<dynamic> sceneIconHueStorage = <dynamic>[];
        try {
          // 获取图片hue值缓存
          final String sceneIconHueStorageStr =
              await Storage.getStringValue(CONSTANT.SCENE_FILTER_COLOR);
          final dynamic decodeValue = jsonDecode(sceneIconHueStorageStr);
          DevLogger.info(
              tag: CONSTANT.tagLibrary,
              msg: 'sceneIconHueStorageStr data $sceneIconHueStorageStr');
          sceneIconHueStorage = decodeValue is List ? decodeValue : <dynamic>[];
        } catch (e) {
          DevLogger.error(
              tag: CONSTANT.tagLibrary,
              msg: 'getStringValue sceneIconHueStorageStr error $e');
        }
        if (response.data.templateList.isNotEmpty) {
          for (final SceneModel item in response.data.templateList) {
            item.listType = 'template';
          }
          for (int i = 0; i < response.data.templateList.length; i++) {
            final SceneModel item = response.data.templateList[i];
            // 获取每个场景图片的滤镜处理
            final ColorFilter filter =
                await getSceneImageFilter(item.icon, sceneIconHueStorage);
            item.colorFilter = filter;
            templateList.add(item);
          }
        }

        if (response.data.sceneList.isNotEmpty) {
          for (final SceneModel item in response.data.sceneList) {
            item.listType = 'scene';
          }
          for (int i = 0; i < response.data.sceneList.length; i++) {
            final SceneModel item = response.data.sceneList[i];
            // 获取每个场景图片的滤镜处理
            final ColorFilter filter =
                await getSceneImageFilter(item.icon, sceneIconHueStorage);
            item.colorFilter = filter;
            sceneList.add(item);
          }
        }
        // List showSceneData = [];
        List<SceneModel> allListData = <SceneModel>[];
        // SceneModel showScene =
        //     SceneModel.fromJson({'sceneName': '演示场景', 'listType': 'showScene'});
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg:
                'library_widget _getSceneListData deviceNum- ${widget.deviceNum}--roomId-${widget.roomId}');
        // if (widget.deviceNum == 0 &&
        //     loginStatus == '已登录' &&
        //     (widget.type == 'parlor' ||
        //         widget.type == 'kitchen' ||
        //         widget.type == 'balcony' ||
        //         widget.type == 'bathroom' ||
        //         widget.type == 'bedroom')) {
        //   showSceneData.add(showScene);
        //   allListData = [...sceneList, ...templateList];
        //   if (allListData.length == 3) {
        //     allListData.removeAt(allListData.length - 1);
        //   }
        //   allListData = [...showSceneData, ...allListData];
        // } else {
        //   allListData = [...sceneList, ...templateList];
        // }
        allListData = <SceneModel>[...sceneList, ...templateList];
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg: 'library_widget _getSceneListData allListData-$allListData');
        setState(() {
          final SceneModel more = SceneModel.fromJson(
              <String, String>{'sceneName': '更多',
                'listType': CONSTANT.SCENE_TYPE_MORE});
          showListData = allListData.take(3).toList();
          if (showListData.isNotEmpty) {
            showListData.add(more);
          }

          // if (allListData.length == 3) {
          //   showListData = allListData.take(3).toList();
          //   showListData.add(more);
          // } else {
          //   showListData = allListData;
          // }
        });
        DevLogger.info(
            tag: CONSTANT.tagLibrary,
            msg:
                'library_widget _getSceneListData showListData-$showListData');
        myCallBack(showListData.length);
      }
    } catch (e) {
      DevLogger.debug(tag: CONSTANT.tagLibrary, msg: 'topN查询错误 $e');
    }
  }

  void _assembleAirData(dynamic result) {
    Map<dynamic, dynamic> res = <dynamic, dynamic>{};
    final List<AirSceneModel> _airListData = <AirSceneModel>[];
    if (result is Map<dynamic, dynamic>) {
      res = result;
    }
    final AirSceneRecommendResponse response =
      AirSceneRecommendResponse.fromJson(res);
    if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS) {
      if (response.data.isNotEmpty) {
        for (int i = 0; i < response.data.length; i++) {
          final AirSceneModel item = response.data[i];
          item.type = '云场景';
          _airListData.add(item);
        }

        setState(() {
          final AirSceneModel more = AirSceneModel.fromJson(
              <String, String>{'name': '更多', 'listType': CONSTANT.SCENE_TYPE_MORE});
          airListData = _airListData.take(3).toList();
          if (_airListData.length >= 3) {
            airListData.add(more);
          }
        });
        myCallBack(airListData.length);
      }
    }
  }

  void tipToast(String type) {
    if (type == '1') {
      WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
        ToastHelperLibrary.showToast('场景执行中', context);
      });
      // Future.delayed(Duration(milliseconds: 10), () {
      //   ToastHelperLibrary.showToast('场景执行中', context);
      // });
    } else {
      // 运行结束后清除水波纹动效
      if (showSceneGlobalKey.currentState != null) {
        final dynamic showSceneState = showSceneGlobalKey.currentState;
        showSceneState.stopShowSceneAnimation();
      }
      WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
        ToastHelperLibrary.showToast('演示场景执行成功，添加设备试试吧', context, false, 3);
      });
    }
  }

  bool isDataListIsNotEmpty() {
    if (widget.type == 'air') {
      return airListData.isNotEmpty;
    } else {
      return showListData.isNotEmpty;
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return isDataListIsNotEmpty() &&
            (widget.type == 'kitchen' ||
                widget.type == 'parlor' ||
                widget.type == 'balcony' ||
                widget.type == 'bathroom' ||
                widget.type == 'bedroom' ||
                ((widget.type == 'air' ||
                        widget.type == 'water' ||
                        widget.type == 'ic' ||
                        widget.type == 'heating') &&
                    loginStatus == '已登录'))
        ? Container(
            key: appKey,
            margin: EdgeInsets.only(
              top: widget.type == 'air' ? 0 : 16.w,
              left: 16.w,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Container(
                  margin: EdgeInsets.only(bottom: 12.w),
                  child: Text(
                    '专属场景',
                    style: TextStyle(
                      fontSize: 17.sp,
                      height: 1.4,
                      color: const Color.fromRGBO(0, 0, 0, 0.80),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(
                    height: 96.w,
                    width: double.infinity,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widget.type == 'air' ? airListData.length :
                        showListData.length,
                      itemBuilder: (BuildContext context, int index) {
                        return widget.type == 'air' ? CommonSceneItem(
                          sceneName: airListData[index].name ?? '',
                          runStatus: airListData[index].runStatus ?? '',
                          listIndex: index,
                          icon: airListData[index].familyIcon ?? '',
                          linkUrl: airListData[index].linkUrl ?? '',
                          listType: airListData[index].listType ?? '',
                          sceneItemClick: () {
                            _goDetailPage(context, airListData[index]);
                          },
                          longPressEnterDetail: () {
                            _goDetailPage(context, airListData[index]);
                          }
                        ) : WholeHouseCardSceneScreen(
                            // 此处key值是为了在演示场景卡片存在时获取演示场景用来关闭动画
                            key: index == 0 &&
                                    widget.deviceNum == 0 &&
                                    loginStatus == '已登录' &&
                                    (widget.type == 'parlor' ||
                                        widget.type == 'kitchen' ||
                                        widget.type == 'balcony' ||
                                        widget.type == 'bathroom' ||
                                        widget.type == 'bedroom')
                                ? showSceneGlobalKey
                                : null,
                            loginStatus: loginStatus,
                            listDataObj: showListData[index],
                            listIndex: index,
                            familyId: widget.familyId ?? '',
                            type: widget.type,
                            roomId: widget.roomId,
                            roomName: widget.roomName,
                            doRefresh: doRefreshData,
                            updateCurtainWorkingAction:
                                widget.updateCurtainWorkingAction,
                            nowTime: nowTime,
                            demoAnimation: widget.demoAnimation,
                            tipToast: tipToast,
                            deviceNum: widget.deviceNum,
                            closeSpaceDetailPage: widget.closeSpaceDetailPage);
                      },
                    )),
              ],
            ))
        : const SizedBox(
            height: 0,
          );
  }

  Future<void> _goDetailPage(BuildContext context,AirSceneModel scene) async {
    _clickGio(scene);
    await updateNetworkStatus(type: 'init');
    if (context.mounted) {
      if (!initNetwork) {
        ToastHelperLibrary.showToast(CONSTANT.NET_WORK_ERROR, context);
        return;
      }
      if (scene.listType == CONSTANT.SCENE_TYPE_MORE) {
        final String roomId = widget.roomId ?? '';
        goToPage(
          'https://acbigdata.haier.net/static/ac-scene-list/?underneathStatusBar=1${roomId.isEmpty ? '' : '&roomId=$roomId'}',
          null,
          context,
        );
      } else {
        goToPage(
          scene.linkUrl,
          null,
          context,
        );
      }
    }
  }

  void _clickGio(AirSceneModel scene) {
    if (scene.listType == CONSTANT.SCENE_TYPE_MORE) {
      gioTrack('MB34535',
          <String, String>{CONSTANT.CHANNEL_NAME: widget.roomName ?? '全屋'});
    } else {
      gioTrack('MB34534', <String, String>{
        CONSTANT.SCENE_NAME: scene.name ?? '',
        CONSTANT.CHANNEL_NAME: widget.roomName ?? '全屋',
        CONSTANT.CONTENT_TYPE: scene.type
      });
    }
  }
}
