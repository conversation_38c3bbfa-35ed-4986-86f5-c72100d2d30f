/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/1/19
 */

import 'scene_operate_response.dart';

class SceneOperateResultModel {
  SceneOperateResultModel(
      {required this.id,
      required this.code,
      required this.param,
      required this.info});

  String id = '';
  String code = '';
  SceneOperateResultParam param = SceneOperateResultParam();
  List<SceneOperateResultInfo> info = <SceneOperateResultInfo>[];

  @override
  String toString() {
    return 'SceneOperateResultModel{id: $id, code: $code, param: $param, info: $info}';
  }
}
