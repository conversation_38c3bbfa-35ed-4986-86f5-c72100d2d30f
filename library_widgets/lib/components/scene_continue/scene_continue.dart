/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-09-27 10:45:33
 * @description: 
 */

import 'dart:async';
import 'dart:convert';

import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:storage/storage.dart';

import '../../common/api.dart';
import '../../common/constant.dart';
import '../../common/log.dart';
import '../../common/toast_helper.dart';
import '../../store/actions.dart' as Action;
import 'scene_operate_response.dart';
import 'scene_operate_result.dart';

class SceneContinue {
  static int sceneExecuteCount = 0;
  static List<SceneOperateResultModel> result = <SceneOperateResultModel>[];
  static Map<dynamic, dynamic> continueSearchCountMap = <dynamic, dynamic>{};

  static bool interfaceToRefresh = true;

  static String interface = '';
  static bool interfaceable = true;
  static bool showApiIsChanged = false;

  static void cleanAllTimer(Timer? containueTimer) {
    ToastHelperLibrary.closeToast();
    if (containueTimer != null) {
      containueTimer.cancel();
    }
    interfaceToRefresh = false;
  }

  static void resetInterfaceToRefresh() {
    interfaceToRefresh = true;
  }

  static Future<bool> judgeStopAnimationAndCancelToast(
      String? tabtype,
      Function? handleRunAnimation,
      List<String>? sceneIdList,
      String sceneId,
      dynamic Function(dynamic) dispatch) async {
    final String changeTabtype =
        await Storage.getTemporaryStorage(CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);
    DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, Object?>{
      'judgeStopAnimationAndCancelToast_changeTabtype': changeTabtype,
      '_judgeStopAnimationAndCancelToast_sceneIdList': sceneIdList,
    });
    if (json.decode(changeTabtype)['value'] != tabtype) {
      ToastHelperLibrary.closeToast();
      if (handleRunAnimation != null) {
        handleRunAnimation(0);
      }
      if (sceneIdList != null) {
        final List<String> sceneList = sceneIdList;
        if (sceneList.contains(sceneId)) {
          sceneList.removeAt(sceneList.indexOf(sceneId));
        }
        dispatch(Action.UpdateContinueSceneAction(sceneList));
      }
      return false;
    }
    return true;
  }

  //TODO(sfj): updateCurtainWorkingAction 该参数不再需要(3+5时参数)
  static void setCurtainAction(
      {required List<SceneOperateResultModel> result,
      Function? updateCurtainWorkingAction}) {
    try {
      if (result.isNotEmpty) {
        final List<SceneOperateResultModel> deviceArr =
            <SceneOperateResultModel>[];
        for (final SceneOperateResultModel element in result) {
          if (element.param.applicationCode == 'A008') {
            deviceArr.add(element);
          }
        }
        if (deviceArr.isEmpty) {
          return;
        }
        // 执行关的情况：所有设备都是关，所有设备执行成功；执行关动效:
        // 判断设备执行成功的条件：code=00000或者多设备的code=00001并且info中每个设备的execResult为true
        final bool allDevicesIsShutDown = deviceArr.every(
            (SceneOperateResultModel element) =>
                element.param.settings.value.value == 'false' &&
                ((element.code == '00000') ||
                    (element.code == '00001' &&
                        element.info.every((SceneOperateResultInfo info) =>
                            info.execResult))));

        // 有一个打开执行成功、执行开动效
        final bool anyOneOpenDeviceSuccess = deviceArr.any(
            (SceneOperateResultModel element) =>
                element.code == '00000' &&
                element.param.settings.value.value == 'true');

        // 多设备执行打开的情况有部分执行成功、执行开动效
        final bool someoneOpenDeviceSuccess = deviceArr.any(
            (SceneOperateResultModel element) =>
                element.code == '00001' &&
                element.param.settings.value.value == 'true' &&
                element.info
                    .any((SceneOperateResultInfo info) => info.execResult));
        if (sceneExecuteCount < 1) {
          if (anyOneOpenDeviceSuccess || someoneOpenDeviceSuccess) {
            if (updateCurtainWorkingAction != null) {
              updateCurtainWorkingAction(1);
              sceneExecuteCount++;
            }
          } else if (allDevicesIsShutDown) {
            if (updateCurtainWorkingAction != null) {
              updateCurtainWorkingAction(0);
              sceneExecuteCount++;
            }
          }
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'_setCurtainAction err': err});
    }
  }

  static Widget _buildTextWidget(String sceneName, String toastName) {
    final TextStyle style = TextStyle(
      color: Colors.white,
      fontSize: 14.sp,
      fontWeight: FontWeight.normal,
      decoration: TextDecoration.none,
    );
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: <Widget>[
        Text(
          toastName,
          style: style,
          textAlign: TextAlign.center,
          maxLines: 1,
        )
      ],
    );
  }

  static Future<void> showToastResult(BuildContext context,
      {required SceneOperateResponse response,
      Function? updateCurtainWorkingAction,
      required Map<dynamic, dynamic> actionResultParam,
      required int continueSearchCount,
      List<String>? sceneIdList,
      required dynamic Function(dynamic) dispatch,
      Function? handleRunAnimation,
      required int retryCount,
      Timer? continueTimer,
      String? isModel,
      String? tabType,
      String? sceneName}) async {
    try {
      // 场景执行状态(0-执行失败,1-执行成功,2-执行中.3-部分成功)、
      if (response.data.status != 2) {
        // 非执行中
        String toastName = '';
        if (response.data.status == 0) {
          toastName = '执行失败';
        } else if (response.data.status == 1) {
          toastName = '执行成功';
        } else {
          //动作执行结果code状态码 00000:执行成功  00001:执行失败   00002:执行中   00003:未执行
          int successActionNum = 0;
          int failActionNum = 0;
          for (final SceneOperateResult item
              in response.data.actionResultList) {
            if (item.code == '00000') {
              successActionNum++;
            }
            if (item.code == '00001') {
              failActionNum++;
            }
          }
          toastName = '执行成功';
        }
        // 关闭动画
        if (handleRunAnimation != null) {
          handleRunAnimation(0);
        }
        // 如果store中存在次场景，执行完成后进行删除、此处的sceneIdList和dispatch是传递过来的store中的数据
        if (sceneIdList != null) {
          final List<String> sceneList = sceneIdList;
          if (sceneList.contains(response.data.sceneId)) {
            sceneList.remove(response.data.sceneId);
          }
          dispatch(Action.UpdateContinueSceneAction(sceneList));
        }
        // 关闭执行中toast
        ToastHelperLibrary.closeToast();
        // 如果在场景tab，提示执行结果
        final String isSceneshow = await Storage.getTemporaryStorage(
            CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET);
        final String changeTabtype = await Storage.getTemporaryStorage(
            CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);

        DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, String>{
          '_showToastResult isSceneshow': isSceneshow,
          '_showToastResult changeTabtype': changeTabtype
        });

        // 如果是空间点击，只能在空间下进行提示；我的列表只能在我的列表提示toast结果
        // isModel为空代表是我的列表、不为空则是各个空间下进行了点击
        if (isModel != '') {
          // 在空间/系统
          if (json.decode(isSceneshow)['value'] == '1' &&
              json.decode(changeTabtype)['value'] != 'recommend' &&
              json.decode(changeTabtype)['value'] != 'mine' &&
              json.decode(changeTabtype)['value'] == tabType) {
            ToastHelperLibrary.closeToast();
            if (context.mounted) {
              ToastHelperLibrary.showToastWidget(
                  _buildTextWidget(sceneName ?? '', toastName), context);
            }
          }
        } else {
          // 在我的tab
          if (json.decode(isSceneshow)['value'] == '1' &&
              json.decode(changeTabtype)['value'] != 'recommend' &&
              json.decode(changeTabtype)['value'] != 'majorSystem' &&
              json.decode(changeTabtype)['value'] != 'smartSpace' &&
              json.decode(changeTabtype)['value'] == tabType) {
            ToastHelperLibrary.closeToast();
            if (context.mounted) {
              ToastHelperLibrary.showToastWidget(
                  _buildTextWidget(sceneName ?? '', toastName), context);
            }
          }
        }
        return;
      } else {
        // 执行中
        if (continueSearchCount <= 3) {
          // 正在执行3次轮询中
          if (judgeStopAnimationAndCancelToast(tabType, handleRunAnimation,
                  sceneIdList, response.data.sceneId, dispatch) ==
              false) {
            // 非当前tab，直接结束，不查结果了
            return;
          }
          // 在当前tab
          continueTimer = Timer(const Duration(milliseconds: 1500), () async {
            // 执行定时器的过程中。下拉刷新或者出发了专属场景列表变化、所有之后的定时器定时器不在执行
            if (interfaceToRefresh) {
              await scenePoll(context,
                  updateCurtainWorkingAction: updateCurtainWorkingAction,
                  actionResultParam: actionResultParam,
                  continueSearchCount: continueSearchCount,
                  sceneIdList: sceneIdList,
                  dispatch: dispatch,
                  handleRunAnimation: handleRunAnimation,
                  retryCount: retryCount,
                  containueTimer: continueTimer,
                  isModel: isModel,
                  tabtype: tabType,
                  sceneName: sceneName);
            } else {
              return;
            }
          });
        } else {
          // 轮询超过3次
          if (continueTimer != null) {
            continueTimer.cancel();
          }
          ToastHelperLibrary.closeToast();
          if (judgeStopAnimationAndCancelToast(tabType, handleRunAnimation,
                  sceneIdList, response.data.sceneId, dispatch) ==
              false) {
            // 非当前tab
            return;
          }
          // 当前tab
          ToastHelperLibrary.showToastWidget(
              _buildTextWidget(sceneName ?? '', '执行中'), context);
          // 接口超时取消执行中的场景，删除动效
          if (handleRunAnimation != null) {
            handleRunAnimation(0);
          }
          if (sceneIdList != null) {
            final List<String> sceneList = sceneIdList;

            if (sceneList.contains(response.data.sceneId)) {
              sceneList.remove(response.data.sceneId);
            }
            dispatch(Action.UpdateContinueSceneAction(sceneList));
          }
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.tagLibrary,
          msg: <String, Object>{'_showToastResult err': err});
    }
  }

  // TODO(sfj): 待验证
  // 场景执行
  static Future<void> scenePoll(BuildContext context,
      {Function? updateCurtainWorkingAction,
      required Map<dynamic, dynamic> actionResultParam,
      required int continueSearchCount,
      List<String>? sceneIdList,
      required dynamic Function(dynamic) dispatch,
      Function? handleRunAnimation,
      required int retryCount,
      required Timer? containueTimer,
      String? isModel,
      String? tabtype,
      String? sceneName}) async {
    try {
      //在场景tab下，我的和推荐时不显示toast，并清除动画
      final String isSceneshow = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_ISSCENESHOW_LIBRARY_WIDGET);
      final String changeTabtype = await Storage.getTemporaryStorage(
          CONSTANT.DEVICE_SCENE_TABTYPE_INDEX);
      DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, Object?>{
        '_scenePoll_isSceneshow': isSceneshow,
        '_scenePoll_changeTabtype': changeTabtype,
        '_scenePoll_continueSearchCount': continueSearchCount,
        '_scenePoll_sceneIdList': sceneIdList,
      });
      if (json.decode(isSceneshow)['value'] == '1' &&
          json.decode(changeTabtype)['value'] != 'recommend') {
        // 在场景-我的/空间/系统
        ToastHelperLibrary.updateCanShow(true);
      } else {
        // 不在场景/在场景的推荐tab
        ToastHelperLibrary.closeToast();
        if (handleRunAnimation != null) {
          handleRunAnimation(0);
        }
        interfaceToRefresh = false;
      }
      if (judgeStopAnimationAndCancelToast(
          tabtype,
              handleRunAnimation,
              sceneIdList,
              actionResultParam.stringValueForKey('sceneId', ''),
              dispatch) ==
          false) {
        //非当前tab
        return;
      }
      // 如果接口刷新
      if (!interfaceToRefresh) {
        return;
      }
      // 在当前tab，且接口未刷新，调用场景执行接口
      final String familyId =
          actionResultParam.stringValueForKey('familyId', '');
      final String sceneId = actionResultParam.stringValueForKey('sceneId', '');
      final String sequenceId =
          actionResultParam.stringValueForKey('sequenceId', '');
      final dynamic res =
          await API.sceneStartInfo(familyId, sceneId, sequenceId);
      final Map<dynamic, dynamic> ret = res is Map ? res : <dynamic, dynamic>{};
      final SceneOperateResponse response = SceneOperateResponse.fromJson(ret);
      DevLogger.info(tag: CONSTANT.tagLibrary, msg: <String, dynamic>{
        '_scenePoll_res': res,
      });
      if (judgeStopAnimationAndCancelToast(
              tabtype, handleRunAnimation, sceneIdList, sceneId, dispatch) ==
          false) {
        // 非当前tab
        return;
      }
      continueSearchCount++;
      if (response.retCode == CONSTANT.SERVER_RET_CODE_SUCCESS) {
        // 我的列表执行，下发命令后需要传递参数用来计算执行中的数量
        if (handleRunAnimation != null && isModel == '') {
          handleRunAnimation(2);
        }
        result = <SceneOperateResultModel>[];

        if (response.data.actionResultList.isNotEmpty) {
          for (final SceneOperateResult item in response.data.actionResultList) {
            // 判断执行状态
            result.add(SceneOperateResultModel(
                id: item.param.id,
                code: item.code,
                param: item.param,
                info: item.info));
          }
        }

        if (context.mounted) {
          showToastResult(context,
              response: response,
              updateCurtainWorkingAction: updateCurtainWorkingAction,
              actionResultParam: actionResultParam,
              continueSearchCount: continueSearchCount,
              sceneIdList: sceneIdList,
              dispatch: dispatch,
              handleRunAnimation: handleRunAnimation,
              retryCount: retryCount,
              continueTimer: containueTimer,
              isModel: isModel,
              tabType: tabtype,
              sceneName: sceneName);
        }

        if (response.data.status != 0) {
          setCurtainAction(
              result: result,
              updateCurtainWorkingAction: updateCurtainWorkingAction);
        }
      } else {
        // 调用场景执行接口失败
        Future<void>.delayed(const Duration(milliseconds: 1000), () {
          if (retryCount > 4) {
            // 查询失败
            // 关闭动画
            if (handleRunAnimation != null) {
              handleRunAnimation(0);
            }
            // 清除sceneList
            if (sceneIdList != null) {
              final List<String> sceneList = sceneIdList;
              // if (sceneList.indexOf(actionResultParam['sceneId']) != -1) {
              // sceneList.removeAt(sceneList.indexOf(actionResultParam['sceneId']));
              if (sceneList.contains(actionResultParam['sceneId'])) {
                sceneList.remove(actionResultParam['sceneId']);
              }
              dispatch(Action.UpdateContinueSceneAction(sceneList));
            }
            // 如果在当前tab toast，如果不在当前tab，不toast
            if (json.decode(changeTabtype)['value'] == tabtype) {
              ToastHelperLibrary.showToastWidget(
                  _buildTextWidget(sceneName ?? '', '执行失败'), context);
            }
            // 我的列表执行，下发命令后需要传递参数用来计算执行中的数量
            if (handleRunAnimation != null && isModel == '') {
              handleRunAnimation(2);
            }
            return;
          }
          retryCount++;
          scenePoll(context,
              updateCurtainWorkingAction: updateCurtainWorkingAction,
              actionResultParam: actionResultParam,
              continueSearchCount: continueSearchCount,
              sceneIdList: sceneIdList,
              dispatch: dispatch,
              handleRunAnimation: handleRunAnimation,
              retryCount: retryCount,
              containueTimer: containueTimer,
              isModel: isModel,
              tabtype: tabtype,
              sceneName: sceneName);
        });
      }
    } catch (err) {
      DevLogger.error(tag: CONSTANT.tagLibrary, msg: <Map<String, dynamic>>{
        <String, dynamic>{'_scenePoll err': err}
      });
    }
  }
}
