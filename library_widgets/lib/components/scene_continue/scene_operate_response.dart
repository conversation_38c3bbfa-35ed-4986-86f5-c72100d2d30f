/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2024/1/18
 */

import 'package:device_utils/api/api_response.dart';
import 'package:device_utils/typeId_parse/template_map.dart';

class SceneOperateResponse extends ApiResponse {
  SceneOperateResponse.fromJson(Map<dynamic, dynamic> json)
      : super.fromJson(json) {
    data = SceneOperateData.fromJson(
        json.mapValueForKey('data', <dynamic, dynamic>{}));
  }

  SceneOperateData data = SceneOperateData();

  @override
  String toString() {
    return 'SceneOperateResponse{retCode: $retCode, retInfo: $retInfo, data: $data}';
  }
}

class SceneOperateData {
  SceneOperateData();

  SceneOperateData.fromJson(Map<dynamic, dynamic> json) {
    sceneName = json.stringValueForKey('sceneName', '');
    status = json.intValueForKey('status', 0);
    sceneId = json.stringValueForKey('sceneId', '');
    final List<dynamic> list =
        json.listValueForKey('actionResultList', <dynamic>[]);
    for (final dynamic element in list) {
      if (element is Map) {
        actionResultList.add(SceneOperateResult.fromJson(element));
      }
    }
  }

  String sceneName = '';
  int status = 0;
  String sceneId = '';
  List<SceneOperateResult> actionResultList = <SceneOperateResult>[];

  @override
  String toString() {
    return 'SceneOperateData{sceneName: $sceneName, status: $status, sceneId: $sceneId, actionResultList: $actionResultList}';
  }
}

class SceneOperateResult {
  SceneOperateResult();

  SceneOperateResult.fromJson(Map<dynamic, dynamic> json) {
    code = json.stringValueForKey('code', '');
    final List<dynamic> list = json.listValueForKey('info', <dynamic>[]);

    for (final dynamic element in list) {
      if (element is Map<dynamic, dynamic>) {
        info.add(SceneOperateResultInfo.fromJson(element));
      }
    }

    param = SceneOperateResultParam.fromJson(
        json.mapValueForKey('param', <dynamic, dynamic>{}));
  }

  String code = '';
  List<SceneOperateResultInfo> info = <SceneOperateResultInfo>[];

  SceneOperateResultParam param = SceneOperateResultParam();

  @override
  String toString() {
    return 'SceneOperateResult{code: $code, info: $info, param: $param}';
  }
}

class SceneOperateResultParam {
  SceneOperateResultParam();

  SceneOperateResultParam.fromJson(Map<dynamic, dynamic> json) {
    id = json.stringValueForKey('id', '');
    applicationCode = json.stringValueForKey('applicationCode', '');
    settings = SceneOperateResultParamSetting.fromJson(
        json.mapValueForKey('settings', <dynamic, dynamic>{}));
  }

  String id = '';
  String applicationCode = '';

  SceneOperateResultParamSetting settings = SceneOperateResultParamSetting();

  @override
  String toString() {
    return 'SceneOperateResultParam{id: $id, applicationCode: $applicationCode, settings: $settings}';
  }
}

class SceneOperateResultParamSetting {
  SceneOperateResultParamSetting();

  SceneOperateResultParamSetting.fromJson(Map<dynamic, dynamic> json) {
    if (json['value'] is Map) {
      value = SceneOperateResultParamSettingValue.fromJson(
          json.mapValueForKey('value', <dynamic, dynamic>{}));
    }
  }

  SceneOperateResultParamSettingValue value =
      SceneOperateResultParamSettingValue();

  @override
  String toString() {
    return 'SceneOperateResultParamSetting{value: $value}';
  }
}

class SceneOperateResultParamSettingValue {
  SceneOperateResultParamSettingValue();

  SceneOperateResultParamSettingValue.fromJson(Map<dynamic, dynamic> json) {
    value = json.stringValueForKey('value', '');
    desc = json.stringValueForKey('desc', '');
  }

  String value = '';
  String desc = '';

  @override
  String toString() {
    return 'SceneOperateResultParamSettingValue{value: $value, desc: $desc}';
  }
}

class SceneOperateResultInfo {
  SceneOperateResultInfo();

  SceneOperateResultInfo.fromJson(Map<dynamic, dynamic> json) {
    execResult = json.boolValueForKey('execResult', false);
  }

  bool execResult = false;

  @override
  String toString() {
    return 'SceneOperateResultInfo{execResult: $execResult}';
  }
}
