// Created by <PERSON><PERSON><PERSON><PERSON> on 2/11/22.

import '../../common/api.dart';
import 'smart_home_recommend_models.dart';

typedef Callback = void Function(List<SmartHomeRecommendViewModel> list);

class SmartFamilyIntroductionPresenter {
  static const String _idCode = 'B0117';

  static void getSmartHomeRecommend(Callback callback) {
    API.getSmartHomeIntroductions(_idCode).then((Map<dynamic, dynamic> value) {
      final SmartHomeRecommendServerModel serverModel =
          SmartHomeRecommendServerModel.fromJson(value);
      callback(serverModel.data.slideList);
    });
  }
}
