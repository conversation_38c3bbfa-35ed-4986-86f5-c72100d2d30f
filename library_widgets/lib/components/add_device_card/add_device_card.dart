/// @Package : library_widgets
/// @Description : 添加设备卡片
/// <AUTHOR> tsj
/// @CreateTime : 2023/8/2 16:06
///
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../common/constant.dart';
import '../../common/util.dart';

class AddDeviceCard extends StatelessWidget {
  const AddDeviceCard({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        gioTrack('MB10427');
        goToPage(CONSTANT.bindPage, null, context);
      },
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          Align(
            child: Image.asset(
              'assets/images/add_device_bg.png',
              package: CONSTANT.PACKAGE_NAME,
              fit: BoxFit.contain,
            ),
          ),
          Positioned(
            left: 12.w,
            top: 12.w,
            child: Text(
              '添加设备',
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 15.sp,
                height: 16 / 15,
                color: const Color(0x00000000).withOpacity(0.93),
              ),
            ),
          ),
          Positioned(
            right: 12.w,
            top: 12.w,
            child: GestureDetector(
              onTap: () {
                gioTrack('MB10428');
                goToPage(CONSTANT.bingGuideVideo, null, context);
              },
              child: Container(
                child: Image.asset(
                  'assets/images/question_mark.png',
                  package: CONSTANT.PACKAGE_NAME,
                  width: 16.w,
                  height: 16.w,
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: FractionallySizedBox(
              widthFactor: 3 / 5,
              child: GestureDetector(
                onTap: () {
                  gioTrack('MB10427');
                  goToPage(CONSTANT.bindPage, null, context);
                },
                child: Opacity(
                  opacity: 0.88,
                  child: Container(
                    margin: EdgeInsets.only(bottom: 16.w),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(18.w),
                      boxShadow: <BoxShadow>[
                        BoxShadow(
                          offset: Offset(0, 6.w),
                          blurRadius: 10.w,
                          color: const Color(0x004798e5).withOpacity(0.3),
                        ),
                      ],
                      gradient: LinearGradient(
                        begin: Alignment(-1, tan(112 / 180 * pi)),
                        end: Alignment(1, -tan(112 / 180 * pi)),
                        colors: <Color>[
                          const Color(0x003591ed).withOpacity(1),
                          const Color(0x0096cbff).withOpacity(1)
                        ],
                        stops: const <double>[0, 1],
                      ),
                    ),
                    height: 36.w,
                    child: Text(
                      '去添加',
                      style: TextStyle(
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w400,
                        color: const Color(0x00ffffff).withOpacity(1.0),
                        height: 16 / 15,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
