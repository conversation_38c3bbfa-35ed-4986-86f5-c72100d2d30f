name: library_widgets
description: 智家APP场景 package project.
version: 0.0.1
author: <PERSON><PERSON><PERSON><PERSON>@haier.com
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3
environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter

  fluttertoast: 8.2.1

  flutter_screenutil: 5.0.0+2

  cached_network_image: 3.3.1

  dio: 5.3.2

  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"

  redux: 5.0.0

  flutter_redux: 0.8.2

  shared_preferences: 2.0.20

  flutter_color_models: 1.3.3+2

  url_launcher: 6.1.10

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.2"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.2.0"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.2.2"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=1.0.3"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.2.0"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=1.0.2"

  plugin_device:
    hosted:
      name: plugin_device
      url: http://**************:8083
    version: ">=0.0.29"

  device_utils:
    hosted:
      name: device_utils
      url: http://**************:8083
    version: ">=2.0.0"

dev_dependencies:
  flutter_test:
    sdk: flutter
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/water_ripple/
    # - assets/data/lottie.json
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
