import 'dart:async';
import 'dart:collection';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:eshop_widgets/eshop_widgets.dart';
import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/components/listView/hide_water_ripple_list_view.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:redux/redux.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_filter/device_filter_model.dart';
import 'package:smart_home/device/device_presenter.dart';
import 'package:smart_home/device/device_view_model/camera/camera_msg_presenter.dart';
import 'package:smart_home/device/device_view_model/camera_view_model.dart';
import 'package:smart_home/device/resize_device_card/resize_overlay.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/device/store/device_state.dart';
import 'package:smart_home/discover/discover_presenter.dart';
import 'package:smart_home/edit/edit_widget_overlay.dart';
import 'package:smart_home/navigator/bottom_bar.dart';
import 'package:smart_home/navigator/family/store/family_action.dart';
import 'package:smart_home/navigator/family/store/family_selectors.dart';
import 'package:smart_home/navigator/family/widget/family_pop_dialog.dart';
import 'package:smart_home/navigator/navigator.dart';
import 'package:smart_home/response_time_tracker/response_time_tracker.dart';
import 'package:smart_home/scene/switch/switch_presenter.dart';
import 'package:smart_home/smart_home/widget/bg_widget.dart';
import 'package:smart_home/smart_home/widget/login_no_device_widget.dart';
import 'package:smart_home/smart_home_presenter.dart';
import 'package:smart_home/store/smart_home_state.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/user/user_action.dart';
import 'package:smart_home/whole_house/whole_house_debounce.dart';
import 'package:smart_home/whole_house/whole_house_presenter.dart';
import 'package:smart_home/widget_common/single_touch_recognizer_widget.dart';
import 'package:upsystem/upsystem.dart';
import 'package:user/user.dart';

import 'common/constant_gio.dart';
import 'common/smart_home_util.dart';
import 'common/switch_family_bubble.dart';
import 'device/device_list_widget.dart';
import 'edit/edit_presenter/edit_presenter_manager.dart';
import 'navigator/family/store/family_state.dart';
import 'offline_gio/offline_gio_track.dart';
import 'pack_gift/giftpack_presenter.dart';
import 'smart_home/widget/device_tab_content_widget.dart';
import 'smart_home/widget/login_default_widget.dart';
import 'smart_home/widget/unlogin_widget.dart';
import 'store/smart_home_action.dart';
import 'time_cost_analysis/analysis_presenter.dart';

GlobalKey smartHomeKey = GlobalKey(debugLabel: SmartHomeConstant.package);

class SmartHome extends StatefulWidget {
  Map<String, String> mainParamsMap = <String, String>{};
  bool isFirstScreen = true;
  bool isYearTheme = false;

  SmartHome(
      {super.key, required this.mainParamsMap, required this.isFirstScreen});

  @override
  State<SmartHome> createState() => _SmartHomeState();
}

class _SmartHomeState extends State<SmartHome>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  final SmartHomePresenter _smartHomePresenter = SmartHomePresenter();

  final ItemScrollController _itemScrollController = ItemScrollController();
  PageController? _pageController;

  final EasyRefreshController _easyRefreshController = EasyRefreshController(
      controlFinishLoad: true, controlFinishRefresh: true);

  bool _isInit = true;

  @override
  void initState() {
    super.initState();
    ToastHelper.init(context);
    _isInit = true;
    gioTrack(GioConst.smartHomePageExpose);

    pageIn();

    DevLogger.info(
        tag: 'SmartHome',
        msg:
            '__seq__ ----SmartHome initState----- begin, mainParamsMap:${widget.mainParamsMap}');

    TimeConsumeStatisticTracker.init(
        widget.mainParamsMap['is_logined'] == 'true');

    _smartHomePresenter.subscribeDeviceList();

    _pageController = PageController();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _smartHomePresenter.initSmartHomeScrollController(
          PrimaryScrollController.of(context),
          _pageController,
          _itemScrollController);
    });

    _smartHomePresenter.initAppData(
      mainParamsMap: widget.mainParamsMap,
      isFirstScreen: widget.isFirstScreen,
    );

    _smartHomePresenter.addPluginListeners();

    CameraMsgPresenter.instance.addListenerForCameraMsg();

    ResponseTimeTracker.instance.startTracker();
  }

  void didAppear([Map<dynamic, dynamic>? args]) {
    DevLogger.info(
        tag: 'SmartHome', msg: '----SmartHome didAppear-----: $args');

    ToastHelper.init(context);

    pageIn();

    setStatusBarStyle();

    // 处理跳转参数
    if (args != null && args['tabType'] != null) {
      _smartHomePresenter.handleJumpToUrl(args);
    }
    DiscoverPresenter.getInstance().addDiscoverDeviceListener();
    DiscoverPresenter.getInstance().setContextAndSmartHomeTab(context, true);
    _smartHomePresenter.checkGuideStatus(context);
    if (_isInit) {
      _isInit = false;
      return;
    }
    gioTrack(GioConst.smartHomePageExpose);

    ResponseTimeTracker.instance.startTracker();

    if (!smartHomeStore.state.familyState.netAvailable) {
      Network.isOnline().then((IsOnline onValue) {
        DevLogger.info(
            tag: 'SmartHome',
            msg:
                '----SmartHome didAppear state.net=false, call checkConnectivity, net:$onValue, dispatch(UpdateNetworkStateAction)');

        smartHomeStore.dispatch(UpdateNetworkStateAction(onValue.isOnline));
      });
    }

    final bool isLogin = User.getLoginStatusSync()?.isLogin ?? false;
    if (isLogin) {
      _smartHomePresenter.fetchWholeHouseAndSceneDataInCurFamily(
          smartHomeStore.state.familyState.familyId);
    }
    DevicePresenter.getInstance().subscribeDeviceAttribute();
    DevicePresenter.getInstance().updateTimeOnOff();
    DevicePresenter.getInstance().updatePurifiedConsumableNameAndLevel();

    DevLogger.info(
        tag: 'SmartHome',
        msg:
            'guide-check SmartHome didAppear isExistDevice-Status:${smartHomeStore.state.deviceState.deviceStatus}');
  }

  void didDisappear() {
    DevLogger.info(tag: 'SmartHome', msg: '----SmartHome didDisappear-----');

    pageLeave(title: '智家');

    DiscoverPresenter.getInstance().removeDiscoverDeviceListener();
    DevicePresenter.getInstance().unSubscribeDeviceAttribute();
    ResponseTimeTracker.instance.stopTracker();
  }

  /// 设置状态栏样式
  void setStatusBarStyle() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark));
  }

  Future<void> _pullToRefreshCompleted() async {
    DevLogger.info(tag: 'SmartHome', msg: '----SmartHome pullToRefresh-----');
    CameraLiveCoordinator.instance.stopCurrentPlayer();

    final IsOnline isOnline = await Network.isOnline();

    smartHomeStore.dispatch(UpdateNetworkStateAction(isOnline.isOnline));

    if (!isOnline.isOnline) {
      ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      DevLogger.info(
          tag: 'SmartHome',
          msg: 'pullToRefresh no network, call finishRefresh() return');
      _easyRefreshController.finishRefresh();
      return;
    }

    if (smartHomeStore.state.isEditState) {
      DevLogger.info(
          tag: 'SmartHome',
          msg: 'pullToRefresh isEditState, call finishRefresh() return');
      _easyRefreshController.finishRefresh();
      return;
    }

    gioTrack(GioConst.smartHomePullToRefresh, <String, String>{
      'value': '智家',
    });

    final bool isLogin = User.getLoginStatusSync()?.isLogin ?? false;
    smartHomeStore.dispatch(UpdateImageRefreshCountAction());

    smartHomeStore.dispatch(UpdateLoginStatusAction(isLogin: isLogin));

    await getCurrentFamily(isLogin);
    if (!isLogin) {
      DevLogger.info(
          tag: 'SmartHome',
          msg:
              'pullToRefresh logout, cannot refreshUser() call finishRefresh() return.');

      NewUserGiftPackPresenter.getNewUserPackListService(
          SmartHomeConstant.unLoginNewUserPackLocation);
      _easyRefreshController.finishRefresh();
      return;
    }

    // 1 更新用户信息；2 更新家庭信息；3 更新设备信息；
    User.refreshUser().then((void value) {
      _smartHomePresenter.fetchWholeHouseAndSceneDataInCurFamily(
          smartHomeStore.state.familyState.familyId);
      _smartHomePresenter.fetchWholeHouseDataInCurFamily(
        triggerType: TriggerType.userRefresh,
      );
      UpDevicePlugin.updateDeviceList(immediate: true).then((_) {
        DevicePresenter.getInstance().getDeviceList(DeviceListRequestModel(
          familyId: smartHomeStore.state.familyState.familyId,
          triggerType: DeviceListFetchTriggerType.pullToRefresh,
        ));
        DevLogger.info(
            tag: 'SmartHome',
            msg:
                'pullToRefresh UpDevicePlugin.updateDeviceList() end, call finishRefresh() return.');
        _easyRefreshController.finishRefresh();
      }).catchError((dynamic e) {
        DevLogger.info(
            tag: 'SmartHome',
            msg:
                'pullToRefresh UpDevicePlugin.updateDeviceList() err, call finishRefresh(), e:$e');
        _easyRefreshController.finishRefresh();
      });
      // _easyRefreshController.finishRefresh();
      SwitchPresenter.querySwitchStatus(
          smartHomeStore.state.familyState.familyId);
    }).catchError((dynamic e) {
      DevLogger.error(
          tag: 'SmartHome',
          msg:
              'pullToRefresh User.refreshUser() err, call finishRefresh(), e:$e');
      _smartHomePresenter.fetchWholeHouseAndSceneDataInCurFamily(
          smartHomeStore.state.familyState.familyId);
      _smartHomePresenter.fetchWholeHouseDataInCurFamily(
        triggerType: TriggerType.pullToRefresh,
      );
      _easyRefreshController.finishRefresh();
    });
  }

  Future<void> getCurrentFamily(bool isLogin) async {
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();
      final FamilyActionModel familyActionModel = FamilyActionModel(
          familyId: familyModel.familyId,
          familyName: familyModel.info.familyName,
          memberType: familyModel.memberType);
      smartHomeStore
          .dispatch(UpdateAppCurrentFamilyInfoAction(familyActionModel));
      OfflineGioTrack().updateFamilyId(familyModel.familyId);
    } catch (err) {
      DevLogger.info(
          tag: SmartHomeConstant.package,
          msg: 'pullToRefresh getCurrentFamily error:$err');
    }
  }

  void onTabBarTap(int index) {
    _pageController?.animateToPage(index,
        duration: const Duration(milliseconds: 350), curve: Curves.easeInOut);
    gioTrack(index == 0
        ? SmartHomeConstant.quickListClickGio
        : SmartHomeConstant.deviceListClickGio);
  }

  void onPageChanged(int index) {
    _smartHomePresenter.sendHideBackTopMessage();
    CameraLiveCoordinator.instance.stopCurrentPlayer();
    _itemScrollController.scrollTo(
        index: index >= 1 ? index - 1 : 0,
        duration: const Duration(milliseconds: 350),
        curve: Curves.ease);
    smartHomeStore.dispatch(UpdateDeviceTabIndexAction(index));

    gioTrack(index == 0
        ? SmartHomeConstant.quickListExposureGio
        : SmartHomeConstant.deviceListExposureGio);

    if (index < smartHomeStore.state.deviceState.deviceFilterMap.length) {
      final String space =
          smartHomeStore.state.deviceState.deviceFilterMap.keys.toList()[index];
      gioTrack('MB38334', <String, dynamic>{'RoomName': space});
    }
  }

  int currentPage = 0;

  Widget _buildDeviceTabs(ScrollPhysics? physics, bool scrollPhysics) {
    return StoreConnector<SmartHomeState, DevicePageViewModel>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return DevicePageViewModel(store.state.deviceState.deviceFilterMap,
            store.state.deviceState.tabFilterMap, isFamilyMemberRole());
      },
      builder: (BuildContext context, DevicePageViewModel viewModel) {
        hiddenIds.clear();
        return NotificationListener<ScrollNotification>(
          onNotification: (ScrollNotification notification) {
            if (notification.metrics.axis == Axis.vertical) {
              _smartHomePresenter
                  .handleBackTopButtonVisibility(notification.metrics.pixels);
            }
            if (notification is ScrollStartNotification) {
              if (notification.metrics.axisDirection == AxisDirection.right ||
                  notification.metrics.axisDirection == AxisDirection.left) {
                CameraLiveCoordinator.instance.stopCurrentPlayer();
              }
            }
            if (notification.depth == 0 &&
                notification is ScrollEndNotification) {
              onPageChanged(currentPage);
            }
            return false;
          },
          child: PageView(
            physics: scrollPhysics
                ? const AlwaysScrollableScrollPhysics()
                : const NeverScrollableScrollPhysics(),
            controller: _pageController,
            onPageChanged: (int page) {
              currentPage = page;
            },
            children: viewModel.deviceFilterMap.values
                .toList()
                .map(
                  (DeviceFilterModel e) => ScrollConfiguration(
                      behavior: OverScrollBehavior(),
                      child: DeviceTabContentWidget(
                        physics: physics,
                        cardSortIdList: viewModel.tabFilterMap[
                                TabFilterModel(e.floorName, e.roomName)] ??
                            <String>[],
                        dragEnable: e.floorName ==
                                SmartHomeConstant.deviceFilterSelectAll &&
                            e.roomName ==
                                SmartHomeConstant.deviceFilterSelectAll &&
                            !viewModel.isFamilyMember,
                        roomId: e.roomId,
                        roomName: e.roomName,
                        filterAll: e.floorName ==
                                SmartHomeConstant.deviceFilterSelectAll &&
                            e.roomName ==
                                SmartHomeConstant.deviceFilterSelectAll,
                      )),
                )
                .toList(),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      builder: () => StoreProvider<SmartHomeState>(
        store: smartHomeStore,
        child: Stack(
          children: <Widget>[
            const BackgroundWidget(),
            EasyRefresh.builder(
                controller: _easyRefreshController,
                clipBehavior: Clip.none,
                fit: StackFit.expand,
                //修改阻尼系数
                spring: SpringDescription.withDampingRatio(
                    ratio: 0.8, mass: 1.0, stiffness: 500),
                header: LottieHeader(
                    triggerOffset: 56,
                    safeArea: false,
                    position: IndicatorPosition.locator,
                    clamping: true,
                    textColor: Colors.black.withOpacity(0.93)),
                onRefresh: _pullToRefreshCompleted,
                childBuilder: (BuildContext context, ScrollPhysics physics) {
                  return Padding(
                      padding: EdgeInsets.only(
                          top: MediaQuery.of(context).padding.top,
                          bottom: MediaQuery.of(context).padding.bottom),
                      child: ExtendedNestedScrollView(
                        controller: PrimaryScrollController.of(context),
                        physics: physics,
                        headerSliverBuilder:
                            (BuildContext context, bool innerBoxIsScrolled) {
                          return <Widget>[
                            const SliverToBoxAdapter(
                              child: NavigatorWidget(),
                            ),
                            SliverToBoxAdapter(
                                child: StoreConnector<SmartHomeState, bool>(
                              distinct: true,
                              converter: (Store<SmartHomeState> store) {
                                return store.state.isEditState;
                              },
                              builder: (BuildContext context, bool inEdit) {
                                return inEdit
                                    ? Container()
                                    : const HeaderLocator(clearExtent: false);
                              },
                            )),
                            StoreConnector<SmartHomeState, bool>(
                              distinct: true,
                              converter: (Store<SmartHomeState> store) {
                                return store.state.deviceState.deviceTabIndex !=
                                        null &&
                                    store.state.isLogin &&
                                    store.state.deviceState.deviceStatus ==
                                        DeviceStatus.hasDevice;
                              },
                              builder: (BuildContext context, bool isShowTab) {
                                return isShowTab
                                    ? DeviceBarWidget(
                                        onTap: onTabBarTap,
                                        scrollController: _itemScrollController,
                                      )
                                    : const SliverToBoxAdapter(
                                        child: SizedBox());
                              },
                            ),
                          ];
                        },
                        pinnedHeaderSliverHeightBuilder: () {
                          if (smartHomeStore.state.isLogin) {
                            return 60;
                          }
                          return 0;
                        },
                        onlyOneScrollInBody: true,
                        body: StoreConnector<SmartHomeState, bool>(
                          distinct: true,
                          converter: (Store<SmartHomeState> store) {
                            return store.state.isLogin;
                          },
                          builder: (BuildContext context, bool login) {
                            if (!login) {
                              // unLogin page
                              return StoreConnector<SmartHomeState, bool>(
                                distinct: true,
                                converter: (Store<SmartHomeState> store) {
                                  return store.state.isLogin;
                                },
                                builder: (BuildContext context, bool login) {
                                  return UnLoginPage(physics: physics);
                                },
                              );
                            }
                            return StoreConnector<SmartHomeState,
                                LoginPageShowModel>(
                              distinct: true,
                              ignoreChange: (SmartHomeState store) =>
                                  dragging || dragResizing,
                              converter: (Store<SmartHomeState> store) {
                                return LoginPageShowModel(
                                    scrollPhysics:
                                        store.state.isScrollableScrollPhysics,
                                    deviceStatus:
                                        store.state.deviceState.deviceStatus,
                                    hasDeviceTabIndex: store
                                            .state.deviceState.deviceTabIndex !=
                                        null);
                              },
                              builder: (BuildContext context,
                                  LoginPageShowModel loginPageShow) {
                                if (loginPageShow.deviceStatus ==
                                    DeviceStatus.noDevice) {
                                  return LoginNoDeviceWidget(physics: physics);
                                }
                                if (loginPageShow.deviceStatus ==
                                        DeviceStatus.hasDevice &&
                                    loginPageShow.hasDeviceTabIndex) {
                                  return SingleTouchRecognizerWidget(
                                      child: _buildDeviceTabs(
                                          physics, loginPageShow.scrollPhysics),
                                      callback: () {
                                        smartHomeStore.dispatch(
                                            UpdateScrollableScrollPhysicsAction());
                                      });
                                }
                                return LoginDefaultWidget(physics: physics);
                              },
                            );
                          },
                        ),
                      ));
                }),
            const EditWidgetOverlay(),
            StoreConnector<SmartHomeState, SwitchFamilyViewModel>(
                distinct: true,
                converter: (Store<SmartHomeState> store) {
                  return FamilySelectors.switchFamilySelector(store.state);
                },
                builder:
                    (BuildContext context, SwitchFamilyViewModel viewModel) {
                  final double statusBarH = MediaQuery.of(context).padding.top;
                  final String currentFamilyName =
                      smartHomeStore.state.familyState.familyName;
                  return viewModel.bubbleType != BubbleType.invisible
                      ? Positioned(
                          top: statusBarH + 44,
                          left: 16,
                          child: SwitchFamilyBubble(
                              text: viewModel.bubbleText,
                              onSwitch: () {
                                UpSystem.impactFeedBack();
                                if (viewModel.bubbleType == BubbleType.single) {
                                  Family.setCurrentFamily(viewModel.familyId);
                                } else {
                                  showFamilyPopDialog(context);
                                }
                                smartHomeStore.dispatch(
                                    UpdateFamilyBubbleInvisibleAction());
                                gioTrack(
                                    GioConst.autoSwitchFamily, <String, String>{
                                  'current_home_name': currentFamilyName,
                                  'target_home_name': viewModel.familyName
                                });
                              }))
                      : const SizedBox.shrink();
                })
          ],
        ),
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    DevLogger.info(tag: 'SmartHome', msg: '----SmartHome dispose-----');
    _smartHomePresenter.removePluginListeners();
    smartHomeStore.dispatch(ClearDeviceDataAction());
    _easyRefreshController.dispose();
    CameraMsgPresenter.instance.removeListenerForCameraMsg();
    WholeHouseDebounce().dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}

class LoginPageShowModel {
  bool scrollPhysics = true;
  DeviceStatus deviceStatus;
  bool hasDeviceTabIndex;

  LoginPageShowModel({
    required this.scrollPhysics,
    required this.deviceStatus,
    required this.hasDeviceTabIndex,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginPageShowModel &&
          runtimeType == other.runtimeType &&
          scrollPhysics == other.scrollPhysics &&
          deviceStatus == other.deviceStatus &&
          hasDeviceTabIndex == other.hasDeviceTabIndex;

  @override
  int get hashCode =>
      scrollPhysics.hashCode ^
      deviceStatus.hashCode ^
      hasDeviceTabIndex.hashCode;

  @override
  String toString() {
    return 'LoginPageShowModel{'
        'scrollPhysics: $scrollPhysics, '
        'isDeviceEmpty: $deviceStatus, '
        'hasDeviceTabIndex: $hasDeviceTabIndex, '
        '}';
  }
}

class DevicePageViewModel {
  final LinkedHashMap<String, DeviceFilterModel> deviceFilterMap;
  final Map<TabFilterModel, List<String>> tabFilterMap;
  final bool isFamilyMember;

  DevicePageViewModel(
      this.deviceFilterMap, this.tabFilterMap, this.isFamilyMember);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DevicePageViewModel &&
          runtimeType == other.runtimeType &&
          deviceFilterMap == other.deviceFilterMap &&
          tabFilterMap == other.tabFilterMap &&
          isFamilyMember == other.isFamilyMember;

  @override
  int get hashCode =>
      deviceFilterMap.hashCode ^
      tabFilterMap.hashCode ^
      isFamilyMember.hashCode;

  @override
  String toString() {
    return 'DevicePageViewModel{deviceFilterMap: $deviceFilterMap, tabFilterMap: $tabFilterMap, isFamilyMember: $isFamilyMember}';
  }
}

class SwitchFamilyViewModel {
  final BubbleType bubbleType;
  final String bubbleText;
  final String familyId;
  final String familyName;

  SwitchFamilyViewModel(
      this.bubbleType, this.bubbleText, this.familyId, this.familyName);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SwitchFamilyViewModel &&
          runtimeType == other.runtimeType &&
          bubbleType == other.bubbleType &&
          bubbleText == other.bubbleText &&
          familyId == other.familyId &&
          familyName == other.familyName;

  @override
  int get hashCode =>
      bubbleType.hashCode ^
      bubbleText.hashCode ^
      familyId.hashCode ^
      familyName.hashCode;
}
