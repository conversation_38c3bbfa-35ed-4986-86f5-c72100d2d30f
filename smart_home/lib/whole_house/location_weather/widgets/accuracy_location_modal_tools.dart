import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_card_model.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:smart_home/whole_house/location_weather/actions/location_weather_actions.dart';
import 'package:smart_home/whole_house/location_weather/models/query_permission_result.dart';
import 'package:smart_home/whole_house/location_weather/widgets/accuracy_location_modal.dart';
import 'package:uppermission/requestresult.dart';
import 'package:uppermission/uppermission.dart';
import 'package:upservice/map_analysis_extension/map_analysis_extension.dart';

class LocationWeatherUtils {
  static String appPermissionUrl =
      'http://uplus.haier.com/uplusapp/main/appPermission.html';
  static String editLocationUrl =
      'https://uplus.haier.com/uplusapp/scene/index.html?tab=editGeofenceCondition';
  static String privacyPolicyUrl =
      'https://zjrs.haier.net/haierActivitys/appVisualized/index.html#/view/index?mouldId=48&businessSource=5&tenantId=22000016&title=%E7%B2%BE%E7%A1%AE%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF%E5%A4%84%E7%90%86%E5%8D%95%E7%8B%AC%E6%8E%88%E6%9D%83%E5%90%8C%E6%84%8F%E4%B9%A6&isBack=fasle&backgroundImageUrl=';

  static void handleLocationClicked(BuildContext context) {
    Network.isOnline().then((IsOnline onValue) {
      if (result == NetworkStatusfalse) {
        ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
        return;
      }
      _queryLocationPermission(context);
    });
  }

  static void _queryLocationPermission(BuildContext context) {
    gioTrack(GioConst.weatherCardClickLocation);
    Uppermission.queryPermission(<String>['location'])
        .then((List<dynamic> res) {
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherUtils: _handleLocationClicked queryPermission res: $res',
      );
      final Map<String, dynamic> resultMap =
          convertType<Map<dynamic, dynamic>>(res.first, <dynamic, dynamic>{})
              .cast<String, dynamic>();
      final QueryPermissionResult permissionResult =
          QueryPermissionResult.fromJson(resultMap);

      if (permissionResult.granted) {
        Uppermission.queryAccuracyLocationPermission()
            .then((AccuracyResult accuracyResult) {
          DevLogger.debug(
            tag: SmartHomeConstant.package,
            msg:
                'LocationWeatherUtils: _handleLocationClicked queryAccuracyLocationPermission value: $accuracyResult',
          );
          if (!accuracyResult.granted) {
            AccuracyLocationModal.show(context);
          } else {
            gotoEditLocation();
          }
        }).catchError((dynamic error, dynamic stackTrace) {
          DevLogger.error(
            tag: SmartHomeConstant.package,
            msg:
                'LocationWeatherUtils: _handleLocationClicked queryAccuracyLocationPermission error: $error',
          );
          gotoEditLocation();
        });
      } else {
        gotoEditLocation();
      }
    }).catchError((dynamic error, dynamic stackTrace) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherUtils: _handleLocationClicked queryPermission error: $error',
      );
      gotoEditLocation();
    });
  }

  static void gotoEditLocation() {
    SmartHomeUtil.gotoPageWithDebounceCallback(editLocationUrl)
        .then((Map<dynamic, dynamic>? value) {
      if (value != null) {
        _updateFamilyLocationInfo(value);
      } else {
        DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'LocationWeatherUtils: _gotoEditLocation request was debounced and ignored',
        );
      }
    }).catchError((dynamic error) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'LocationWeatherUtils: _gotoEditLocation navigation error: $error',
      );
    });
  }

  static Future<void> _updateFamilyLocationInfo(
      Map<dynamic, dynamic> locationData) async {
    try {
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg:
            'LocationWeatherUtils: _updateFamilyLocationInfo  locationData: $locationData',
      );
      final String familyId = smartHomeStore.state.familyState.familyId;
      final String familyName = smartHomeStore.state.familyState.familyName;
      final String longitude = locationData.stringValueForKey('longitude', '');
      final String latitude = locationData.stringValueForKey('latitude', '');
      final String locationDesc =
          locationData.stringValueForKey('locationDesc', '');

      final FamilyLocation familyLocation = FamilyLocation(
        longitude: double.tryParse(longitude) ?? 0,
        latitude: double.tryParse(latitude) ?? 0,
      );
      final UpdateFamilyInfoModel model = UpdateFamilyInfoModel(
        familyId: familyId,
        familyName: familyName,
        familyLocation: familyLocation,
        familyPosition: locationDesc,
      );
      final DeviceCardResult result = await Family.updateFamilyInfo(model);
      DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'LocationWeatherUtils: _updateFamilyLocationInfo  result: $result',
      );
      if (result.retCode == SmartHomeConstant.logicEngineOperateSuccessCode) {
        await Family.queryFamilyInfo(familyId);
        DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'LocationWeatherUtils: queryFamilyInfo  result success',
        );
      } else {
        ToastHelper.showToast(SmartHomeConstant.editFamilyLocationFailed);
      }
    } catch (e) {
      DevLogger.error(
        tag: SmartHomeConstant.package,
        msg: 'LocationWeatherUtils: _updateFamilyLocationInfo  error: $e',
      );
      ToastHelper.showToast(SmartHomeConstant.editFamilyLocationFailed);
    } finally {
      smartHomeStore.dispatch(
        const LocationAndWeatherDataRequestAction(),
      );
    }
  }
}
