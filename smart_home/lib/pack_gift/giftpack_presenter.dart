import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';

import '../service/http_service.dart';
import '../store/smart_home_store.dart';
import 'model/user_pack_model.dart';
import 'store/giftpack_action.dart';

class NewUserGiftPackPresenter {
  static Future<void> getNewUserPackListService(String location) async {
    HttpService.getNewUserPackService(adLocation: location)
        .then((NewUserPackResponseModel? response) {
      DevLogger.info(
          tag: 'SmartHome',
          msg:
              'GiftPackPresenter, getNewUserPackService(id:$location}), response:$response');
      if (response != null &&
          response.data != null &&
          response.data!.slideList.isNotEmpty) {
        smartHomeStore.dispatch(
            UpdateNewUserPackListAction(location, response.data!.slideList));
      } else {
        smartHomeStore.dispatch(
            UpdateNewUserPackListAction(location, <NewUserPackItem>[]));
      }
    }).catchError((dynamic e) {
      Network.isOnline().then((IsOnline onValue) {
        if (result == NetworkStatusfalse) {
          return;
        }
      });
      smartHomeStore
          .dispatch(UpdateNewUserPackListAction(location, <NewUserPackItem>[]));
      DevLogger.error(
          tag: 'SmartHome',
          msg: 'GiftPackPresenter, getNewUserPackService(id:$location}), e:$e');
    });
  }
}
