/*
 * 描述：快捷/设备 切换tab
 * 作者：songFJ
 * 创建时间：2024/7/2
 */
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/navigator/sliver_header_delegate.dart';
import 'package:smart_home/store/smart_home_state.dart';

import '../device/device_filter/filter_sheet_widget.dart';
import '../device/store/device_action.dart';
import '../store/smart_home_store.dart';
import '../widget_common/card_text_style.dart';

class DeviceBarWidget extends StatelessWidget {
  DeviceBarWidget({super.key, this.onTap, required this.scrollController});

  final Dialogs _dialogs = Dialogs();

  final void Function(int)? onTap;
  final ItemScrollController scrollController;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, bool>(
      distinct: true,
      converter: (Store<SmartHomeState> store) {
        return store.state.isLogin &&
            store.state.deviceState.smartHomeDeviceMap.isNotEmpty;
      },
      builder: (BuildContext? context, bool isShowTab) {
        if (!isShowTab) {
          return SliverToBoxAdapter(child: Container());
        }

        return SliverPersistentHeader(
          delegate: SliverHeaderDelegate.builder(
            maxHeight: 60,
            builder: (BuildContext a, double b, bool c) {
              const Color color = Color(0x00d8d8d8);
              return Container(
                decoration: BoxDecoration(
                  color: color,
                  border: Border.all(color: color, width: 0),
                ),
                child: StoreConnector<SmartHomeState, BottomBarModel>(
                  distinct: true,
                  converter: (Store<SmartHomeState> store) {
                    int tabIndex = store.state.deviceState.deviceTabIndex ?? 0;
                    if (tabIndex >
                        (store.state.deviceState.deviceFilterMap.length - 1)) {
                      tabIndex = 0;
                    }
                    return BottomBarModel(
                        inEdit: store.state.isEditState,
                        categorySelectAll:
                            store.state.deviceState.selectedDeviceCategory ==
                                SmartHomeConstant.deviceFilterSelectAll,
                        isExpanded: store.state.deviceState.isFilterExpanded,
                        tabIndex: tabIndex,
                        barTitles: store.state.deviceState.deviceFilterMap.keys
                            .toList());
                  },
                  builder:
                      (BuildContext context, BottomBarModel bottomBarModel) {
                    return Opacity(
                      opacity: bottomBarModel.inEdit ? 0.39 : 1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          Expanded(
                            child: Container(
                              margin: const EdgeInsets.only(left: 18),
                              child: ScrollablePositionedList.builder(
                                itemScrollController: scrollController,
                                padding: EdgeInsets.zero,
                                physics: const ClampingScrollPhysics(),
                                itemCount: bottomBarModel.barTitles.length,
                                scrollDirection: Axis.horizontal,
                                itemBuilder: (BuildContext context, int index) {
                                  return _buildTabWidget(
                                      bottomBarModel.barTitles[index],
                                      bottomBarModel.tabIndex,
                                      index,
                                      bottomBarModel.inEdit);
                                },
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              showFilterDialogFunction(bottomBarModel, context);
                            },
                            child: Container(
                              height: 32,
                              margin: const EdgeInsets.only(left: 3, right: 16),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 12),
                              decoration: BoxDecoration(
                                  color: bottomBarModel.categorySelectAll
                                      ? const Color(0xff000000)
                                          .withOpacity(0.05)
                                      : const Color(0xff2283E2)
                                          .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(22)),
                              child: ColorFiltered(
                                colorFilter: ColorFilter.mode(
                                  bottomBarModel.categorySelectAll
                                      ? const Color(0xff666666)
                                      : const Color(0xff2283E2),
                                  BlendMode.srcIn,
                                ),
                                child: Image.asset(
                                  'assets/icons/icon_filter.webp',
                                  package: SmartHomeConstant.package,
                                  height: 16,
                                  width: 16,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    );
                  },
                ),
              );
            },
          ),
          pinned: true,
        );
      },
    );
  }

  void showFilterDialogFunction(
      BottomBarModel bottomBarModel, BuildContext context) {
    Network.isOnline().then((IsOnline onValue) {
      if (result == NetworkStatusfalse) {
        ToastHelper.showToast('网络不可用');
        return;
      }
      gioTrack(SmartHomeConstant.deviceFilterGio);

      if (smartHomeStore.state.isEditState ||
          smartHomeStore.state.deviceState.isFilterExpanded) {
        return;
      }

      smartHomeStore.dispatch(UpdateDeviceFilterToInitialAction());
      _dialogs.showSmartHomeModalBottomSheet(
        context: context,
        headerConfig: HeaderConfig.titleWithClose(
            title: '筛选',
            callback: () {
              _dialogs.closeSmartHomeModalBottomSheet();
            },
            enableDrag: true),
        child: (BuildContext context) {
          smartHomeStore.dispatch(UpdatePopupContextAction(context));
          return const FilterSheetWidget();
        },
        afterClose: () {
          smartHomeStore.dispatch(UpdatePopupContextAction(null));
        },
      );
    });
  }

  GestureDetector _buildTabWidget(
      String text, int selectTabIndex, int currentTabIndex, bool inEdit) {
    final bool isSelect = selectTabIndex == currentTabIndex;
    if (text == SmartHomeConstant.shareDeviceFlag) {
      text = SmartHomeConstant.shareDeviceTabText;
    }
    if (text.length > 6) {
      text = '${text.substring(0, 6)}...';
    }
    return GestureDetector(
      onTap: () {
        if (onTap != null && !inEdit) {
          onTap!(currentTabIndex);
        }
      },
      child: Container(
        height: 44,
        margin: EdgeInsets.only(left: currentTabIndex == 0 ? 0 : 12, right: 12),
        child: Center(
            child: Text(
          text,
          style: TextStyle(
              fontSize: isSelect ? 18 : 16,
              fontWeight: isSelect ? FontWeight.w500 : FontWeight.w400,
              color: isSelect
                  ? AppSemanticColors.item.primary
                  : AppSemanticColors.item.secWeaken,
              fontFamilyFallback: isSelect ? fontFamilyFallback() : null),
        )),
      ),
    );
  }
}

class BottomBarModel {
  bool inEdit = false;
  bool categorySelectAll = false;
  bool isExpanded = false;
  int tabIndex = 1;
  List<String> barTitles = <String>[];

  BottomBarModel({
    required this.inEdit,
    required this.categorySelectAll,
    required this.isExpanded,
    required this.tabIndex,
    required this.barTitles,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BottomBarModel &&
          runtimeType == other.runtimeType &&
          inEdit == other.inEdit &&
          categorySelectAll == other.categorySelectAll &&
          isExpanded == other.isExpanded &&
          isListEqual(barTitles, other.barTitles) &&
          tabIndex == other.tabIndex;

  @override
  int get hashCode =>
      inEdit.hashCode ^
      categorySelectAll.hashCode ^
      isExpanded.hashCode ^
      listHashCode(barTitles) ^
      tabIndex.hashCode;
}
