import 'dart:core';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:device_utils/log/log.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/device/device_alarm_info.dart';
import 'package:smart_home/device/device_info_model/card_sorted_info_response_model.dart';
import 'package:smart_home/device/fridge_foodnums/models/fridge_foodnum_model.dart';
import 'package:smart_home/device/purified_consumables_model.dart';
import 'package:smart_home/pack_gift/model/user_pack_model.dart';
import 'package:smart_home/scene/scene_model/scene_query_server_model.dart';
import 'package:smart_home/scene/scene_model/scene_setting_server_model.dart';
import 'package:smart_home/scene/scene_model/scene_sort_server_model.dart';
import 'package:smart_home/scene/scene_server_model.dart';
import 'package:smart_home/scene/switch/switch_status_query_model.dart';
import 'package:smart_home/service/rest_client.dart';
import 'package:smart_home/whole_house/device_consumables/consumables_server_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/clear_reset_consumables_model.dart';
import 'package:smart_home/whole_house/device_consumables/models/single_consumable_model.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

import '../device/aggregation/agg_camera/model/agg_sort_response_model.dart';
import '../device/aggregation/aggregation_detail/model/supportDeviceModel.dart';
import '../device/aggregation/aggregation_setting/service/aggregation_switch_request_model.dart';
import '../device/aggregation/aggregation_setting/service/switch_status_response_model.dart';
import '../device/aggregation/utils/agg_utils.dart';
import '../device/device_guide/model/guide_aggregation_model.dart';
import '../device/device_view_model/camera/service/camera_msg_response_model.dart';
import '../device/e_heat_time_of_off_model.dart';
import '../edit/model/change_family_check_model.dart';
import '../navigator/family/model/geofencing_response_model.dart';
import '../whole_house/device_env/services/env_devices_response_model.dart';
import '../whole_house/device_env/services/preference_request_model.dart';
import '../whole_house/device_fault_alarm/models/device_fault_alarm_response_model.dart';
import '../whole_house/location_weather/services/weather/weather_response_model.dart';
import '../whole_house/preference_setting/models/preference_setting_response_model.dart';

// import 'dart:convert';
// import 'dart:typed_data';
// import 'package:dio/dio.dart';
// import 'package:upservice/dio/uhome_dio/uhome_request_options.dart';
// import 'package:crypto/crypto.dart';

class HttpService {
  static String _appVersion = '';

  static Future<String> getAppVersion() async {
    if (_appVersion.isEmpty) {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _appVersion = appInfo.appVersion;
    }
    return _appVersion;
  }

  static Future<DeviceFaultAlarmResponseModel?>
      getWholeHouseFaultAlarmResponseModel(String familyId) async {
    try {
      final DeviceFaultAlarmResponseModel deviceFaultAlarmResponseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .getWholeHouseFaultAlarms(familyId);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'getWholeHouseFaultAlarmResponseModel : familyId: $familyId, result: $deviceFaultAlarmResponseModel');
      return deviceFaultAlarmResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'getWholeHouseFaultAlarmResponseModel err: $err');
      return null;
    }
  }

  /// 仪表盘-偏好设置查询
  static Future<PreferenceSettingResponseModel?> getWholeHousePreferenceSetting(
      String familyId) async {
    try {
      final PreferenceSettingResponseModel preferenceSettingModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .getWholeHousePreferenceSetting(familyId);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'getWholeHousePreferenceSetting : familyId: $familyId, result: $preferenceSettingModel');
      return preferenceSettingModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'getWholeHousePreferenceSetting err: $err');
      return null;
    }
  }

  /// 仪表盘-偏好设置
  static Future<UhomeResponseModel?> setWholeHousePreferenceSetting(
      {required String familyId,
      required String preferType,
      required String switchStatus}) async {
    try {
      final Map<String, String> params = <String, String>{
        'familyId': familyId,
        'preferType': preferType,
        'switchStatus': switchStatus
      };
      final UhomeResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .setWholeHousePreferenceSetting(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'setWholeHousePreferenceSetting : params: $params, result: $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'setWholeHousePreferenceSetting err: $err');
      return null;
    }
  }

  static Future<EHeatTimeOnOffResponseModel?> timeOnOffStatus(
      String deviceId) async {
    try {
      final EHeatTimeOnOffResponseModel responseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .eHeatTimeOnOffInfo(deviceId);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'timeOnOffStatus : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'timeOnOffStatus err: $err');
      return null;
    }
  }

  /// 快捷操控 查询厨下净水机耗材信息
  static Future<PurifiedConsumablesResponseModel?> purifiedConsumables(
      String prodNo) async {
    try {
      final PurifiedConsumablesResponseModel responseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .purifiedConsumables(prodNo);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'purifiedConsumables : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'purifiedConsumables err: $err');
      return null;
    }
  }

  static Future<AlarmInfoModel?> alarmInfo(String codes, String typeId) async {
    try {
      final Map<String, String> params = <String, String>{
        'codes': codes,
        'typeId': typeId,
      };
      final AlarmInfoModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .alarmsInfo(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package, msg: 'alarmInfo : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'alarmInfo err: $err');
      return null;
    }
  }

  static Future<ConsumableServerResponseModel?> getDeviceConsumables(
      String familyId) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'familyId': familyId,
      };
      final ConsumableServerResponseModel consumableServerResponseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .queryDeviceConsumables(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getDeviceConsumables : $consumableServerResponseModel');
      return consumableServerResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'getDeviceConsumables err: $err');

      return null;
    }
  }

  /// 获取单个耗材信息
  static Future<ConsumableInfoResponseModel?> queryConsumableInfo(
      ConsumableInfoRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'queryConsumableInfo req: $reqModel');
    try {
      final ConsumableInfoResponseModel consumableResponseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .queryConsumableInfo(reqModel.toJson());
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryConsumableInfo response: $consumableResponseModel');
      return consumableResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'queryConsumableInfo err: $err');
      return null;
    }
  }

  static Future<OperateCheckResponseModel?> operateCheck(
      List<Map<String, String>> deviceIds) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'action': 2,
        'deviceIds': deviceIds,
      };
      final OperateCheckResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .operateCheck(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package, msg: 'operateCheck : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'operateCheck err: $err');

      return null;
    }
  }

  /// 获取环境设备数据
  static Future<EnvDevicesResponseModel?> getEnvDevices(String familyId) async {
    try {
      final Map<String, String> params = <String, String>{
        'familyId': familyId,
      };
      final String appVersion = await getAppVersion();

      final EnvDevicesResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .getEnvDevices(params, appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getEnvDevices : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'getEnvDevices err: $err');
      return null;
    }
  }

  /// 获取室外天气
  /// [areaId] "outdoorWeather"时，如果没有传经纬度，此参数必传
  /// [longitude] 经度，"outdoorWeather"时，如果没有传areaId，此参数必传
  /// [latitude] 纬度，"outdoorWeather"时，如果没有传areaId，此参数必传
  static Future<WeatherResponseModel?> getWholeHouseWeather(
      {String? areaId, String? longitude, String? latitude}) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'service': <String>['outdoorWeather'], // 室外天气
      };
      // 添加可选参数
      if (areaId != null) {
        params['areaId'] = areaId;
      }
      if (longitude != null) {
        params['longitude'] = longitude;
      }
      if (latitude != null) {
        params['latitude'] = latitude;
      }

      final WeatherResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .getWholeHouseWeather(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getWholeHouseWeather : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'getWholeHouseWeather err: $err');
      return null;
    }
  }

  static Future<bool> cleanResetConsumables(
      ClearResetConsumablesRequestModel consumableRequestModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'cleanResetConsumables req: $consumableRequestModel');
    try {
      final UhomeResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .cleanResetConsumables(consumableRequestModel.toJson());
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'cleanResetConsumables : $responseModel');
      if (responseModel.retCode == '00000') {
        return true;
      }
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'cleanResetConsumables err: $err');
    }
    return false;
  }

  static Future<SceneResponseModel?> getSceneData(String familyId) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'familyId': familyId,
      };
      final OauthData? oauthData = User.getOauthDataSync();

      if ((oauthData?.uhome_access_token ?? '').isEmpty) {
        return null;
      }

      final String appVersion = await getAppVersion();
      final SceneResponseModel sceneResponseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .getQuickScene(params, 'v1', appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getSceneData : $sceneResponseModel');
      return sceneResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'getSceneData err: $err');
      return null;
    }
  }

  static Future<UhomeResponseModel?> editSceneData(
      String familyId, List<Map<String, dynamic>> sceneList) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'familyId': familyId,
        'sceneQuickBeanList': sceneList,
      };
      final UhomeResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .editQuickScene(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'editSceneData : $responseModel');
      return responseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'editSceneData err: $err');
      return null;
    }
  }

  static Future<UhomeResponseModel?> delSceneTemplate(
      String templateIds) async {
    try {
      final Map<String, String> params = <String, String>{
        'templateIds': templateIds,
      };
      final UhomeResponseModel deleteSceneResponse = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .delSceneTemplate(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'delSceneTemplate: $deleteSceneResponse');
      return deleteSceneResponse;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'delSceneTemplate err: $err');
      return null;
    }
  }

  static Future<CardSortedInfoResponseModel?> getCardSortedInfoResponseModel(
      String familyId) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'familyId': familyId
      };
      final CardSortedInfoResponseModel cardSortedInfoResponseModel =
          await SmartHomeRestClient(UhomeDio().dio,
                  baseUrl: SmartHomeConstant.baseUrl)
              .getCardSortedInfo(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'cardSortedInfoResponseModel : $cardSortedInfoResponseModel');
      return cardSortedInfoResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'cardSortedInfoResponseModel err: $err');
      return null;
    }
  }

  // 获取新手礼包
  /// 接口文档：https://stp.haier.net/project/191/interface/api/98653
  /// 请求参数：adLocation--  已登录：B0333；未登录：B0332
  /// 返回参数：详见接口文档
  static Future<NewUserPackResponseModel?> getNewUserPackService(
      {required String adLocation}) async {
    try {
      final Map<String, String> requestBody = <String, String>{};
      requestBody['adLocation'] = adLocation;
      final NewUserPackResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .getNewUserPackList(requestBody);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'getNewUserPackService : $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'getNewUserPackService err: $err');
      return null;
    }
  }

  static Future<GuideAggregationSwitchModel?> queryGuideAggregationService(
      String familyId) async {
    try {
      final String appVersion = await getAppVersion();
      final GuideAggregationResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryGuideAggregation(familyId, appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryClusterSwitchService:$response');
      return response.data;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryClusterSwitchService err: $err');
      return null;
    }
  }

  // 设置聚合卡片开关
  static Future<void> setClusterSwitchService(
      AggregationSwitchRequestModel requestParams) async {
    try {
      final dynamic response = await SmartHomeRestClient(UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .setClusterSwitch(requestParams.toJson());
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'setClusterSwitchService:$response');
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'setClusterSwitchService err: $err');
    }
  }

  // 查询聚合卡片开关状态
  static Future<SwitchStatusResponseModel?> queryAggregationSwitchStatus(
      String familyId) async {
    try {
      final Map<String, dynamic> params = <String, dynamic>{
        'familyId': familyId,
      };
      final SwitchStatusResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryAggregationSwitch(params);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus err: $err');
      return null;
    }
  }

  /// 设置环境设备偏好
  static Future<bool> setEnvDevicePreference(
      EnvDevicePreferenceRequestModel request) async {
    try {
      final String appVersion = await getAppVersion();
      final Map<String, String> params = <String, String>{};
      request.toJson().forEach((String key, dynamic value) {
        if (value != null) {
          params[key] = value.toString();
        }
      });

      final UhomeResponseModel responseModel = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .setEnvDevicePreference(params, appVersion);

      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg:
              'setEnvDevicePreference : request: $request, result: $responseModel');
      return responseModel.retCode == '00000';
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'setEnvDevicePreference err: $err');
      return false;
    }
  }

  static Future<AggDetailSortModel?> queryAggDetailSort(
      String familyId, AggTypeEnum aggType) async {
    try {
      final AggSortResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryAggDetailSort(familyId, getAggTypeEnum(aggType).toString());
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus response: $response');
      return response.data;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus err: $err');
      return null;
    }
  }

  // 查询家庭下某类未聚合设备
  static Future<SupportDeviceResModel?> querySupportedAggDevices(
      SupportDeviceReqModel params) async {
    try {
      final String appVersion = await getAppVersion();
      final SupportDeviceResModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .querySupportedAggDevices(params.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'querySupportedAggDevices response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'querySupportedAggDevices err: $err');
      return null;
    }
  }

  static Future<CameraMsgResponseModel?> getCamerasMsg(
      Set<String> params, String familyId) async {
    try {
      final List<String> pams = <String>[];
      params.forEach((String element) {
        pams.add(element);
      });
      final Map<String, dynamic> requestBody = <String, dynamic>{
        'deviceIds': pams,
        'familyId': familyId,
      };
      final String appVersion = await getAppVersion();
      final CameraMsgResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryCameraMsgInfo(requestBody, appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus response: $response');
      return response;
    } catch (err, t) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryAggregationSwitchStatus err: $err');
      return null;
    }
  }

//   static Future<CameraMsgResponseModel?> mock(Map<String, dynamic> requestBody) async{
//     final int timestamp = DateTime.now().millisecondsSinceEpoch;
//     final String paramsString = _formatPramsString('POST', '/api-gw/wisdomdevice/device/v1/queryLatestHomeSecurityInfo', requestBody);
//     final String signString =
//     _sign('/api-gw/wisdomdevice/device/v1/queryLatestHomeSecurityInfo',
//         paramsString,'MB-UZHSH-0000', 'f50c76fbc8271d361e1f6b5973f54585', timestamp);
//     final Map<String, dynamic> headers = <String, dynamic>{
//       'timestamp': timestamp,
//       'appId': 'MB-UZHSH-0000',
//       'clientId': '5889F15009A3CDDCC55D01E887D87B3C',
//       'accessToken': 'e6928fdd77d44cd5b6886b3f558264c0',
//       'accountToken': 'e6928fdd77d44cd5b6886b3f558264c0',
//       'sign': signString,
//       'appVersion':'9.3.0'
//     };
//     final Response<Map<String, dynamic>> res = await  Dio().fetch<Map<String, dynamic>>(
//         _setStreamType<CameraMsgResponseModel>(Options(
//           method: 'POST',
//           headers: headers,
//           extra: null,
//         )
//             .compose(
//           Dio().options,
//           '/api-gw/wisdomdevice/device/v1/queryLatestHomeSecurityInfo',
//           queryParameters: null,
//           data: requestBody,
//         )
//             .copyWith(
//             baseUrl: 'https://zj-yanshou.haier.net')));
//     CameraMsgResponseModel? value = CameraMsgResponseModel.fromJson(res.data!);
//     return value;
//   }
//
//  static RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
//     if (T != dynamic &&
//         !(requestOptions.responseType == ResponseType.bytes ||
//             requestOptions.responseType == ResponseType.stream)) {
//       if (T == String) {
//         requestOptions.responseType = ResponseType.plain;
//       } else {
//         requestOptions.responseType = ResponseType.json;
//       }
//     }
//     return requestOptions;
//   }
//
//   static String _sign(
//       String path,
//       String paramsString,
//       String appId,
//       String appKey,
//       int timestamp,
//       ) {
//     final List<int> bytes = utf8
//         .encode(path + paramsString + appId + appKey + timestamp.toString());
//     final List<int> list = sha256.convert(bytes).bytes;
//     if (list.isEmpty) {
//       return '';
//     }
//
//     final int length = list.length;
//     final Uint8List uList = Uint8List(length << 1);
//     const String HEX_STRING = '0123456789abcdef';
//     for (int j = 0, i = 0; j < list.length; j++) {
//       final int k = i + 1;
//       final int index = (list[j] >> 4) & 0xF;
//       uList[i] = HEX_STRING[index].codeUnitAt(0);
//       uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
//       i = k + 1;
//     }
//     return String.fromCharCodes(uList);
//   }
//
// static   String _formatPramsString(String method, String path, dynamic params) {
//     if (params is! Map) {
//       return '';
//     }
//
//     if (params.isEmpty) {
//       return '';
//     }
//
//     if (method == 'POST') {
//       return _replaceSpecialChars(jsonEncode(params));
//     }
//
//     String paramsString = '';
//     if (!path.contains('?')) {
//       paramsString = '?';
//     }
//     final Map<dynamic, dynamic> paramsMap =
//     params is Map<dynamic, dynamic> ? params : <dynamic, dynamic>{};
//
//     final List<String> paramPairs = <String>[];
//     for (final dynamic key in paramsMap.keys) {
//       paramPairs.add('$key=${paramsMap[key]}');
//     }
//
//     paramsString = paramsString + paramPairs.join('&');
//     return _replaceSpecialChars(paramsString);
//   }
//
//  static  String _replaceSpecialChars(String str) {
//     return str
//         .replaceAll(' ', '')
//         .replaceAll('\t', '')
//         .replaceAll('\r', '')
//         .replaceAll('\n', '');
//   }

  // 查询手动场景列表
  static Future<ManualSceneResponseModel?> queryManualSceneList(
      ManualSceneRequestModel reqModel) async {
    DevLogger.info(
        tag: SmartHomeConstant.package,
        msg: 'queryManualSceneList req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();
      final ManualSceneResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryManualSceneList(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryManualSceneList response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryManualSceneList err: $err');
      return null;
    }
  }

  // 批量设置手动场景
  static Future<UhomeResponseModel?> sceneBatchSetting(
      ManualSceneSettingRequestModel reqModel) async {
    try {
      final String appVersion = await getAppVersion();
      final UhomeResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .sceneBatchSetting(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'sceneBatchSetting response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'sceneBatchSetting err: $err');
      return null;
    }
  }

  // 场景列表排序
  static Future<UhomeResponseModel?> saveSceneListSort(
      ManualSceneSortRequestModel reqModel) async {
    DevLogger.info(
        tag: SmartHomeConstant.package, msg: 'sceneListSort req: $reqModel');
    try {
      final String appVersion = await getAppVersion();
      final UhomeResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .updateSceneSort(reqModel.toJson(), appVersion, 'v1');
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'sceneListSort response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'sceneListSort err: $err');
      return null;
    }
  }

  // 查询开关状态
  static Future<SceneSwitchStatusResponseModel?> switchQuery(
      SwitchStatusRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package, msg: 'switchQuery req: $reqModel');
    try {
      final String appVersion = await getAppVersion();
      final SceneSwitchStatusResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .switchQuery(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'switchQuery response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'switchQuery err: $err');
      return null;
    }
  }

  // 查询地址围栏
  static Future<GeofencingResponseModel?> queryFamilyGeofencing(
      GeofencingRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'queryFamilyGeofencing req: $reqModel');
    try {
      final GeofencingResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryFamilyGeofencing(reqModel.toJson());
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'switchQuery response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryFamilyGeofencing err: $err');
      return null;
    }
  }

  /// 查询单房间的场景列表
  static Future<ManualSceneResponseModel?> querySingleRoomSceneList(
      ManualSceneRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'querySingleRoomSceneList req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();
      final ManualSceneResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .querySingleRoomSceneList(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'querySingleRoomSceneList response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'querySingleRoomSceneList err: $err');
      return null;
    }
  }

  /// 查询所有房间的场景列表
  static Future<RoomScenesResponseModel?> queryAllRoomSceneList(
      ManualSceneRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'queryAllRoomSceneList req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();

      final RoomScenesResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryAllRoomsSceneList(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryAllRoomSceneList response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryAllRoomSceneList err: $err');
      return null;
    }
  }

  /// 批量设置单房间场景
  static Future<UhomeResponseModel?> roomSceneBatchSetting(
      ManualSceneSettingRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'roomSceneBatchSetting req: $reqModel');
    try {
      final String appVersion = await getAppVersion();
      final UhomeResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .roomSceneBatchSetting(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'roomSceneBatchSetting response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'roomSceneBatchSetting err: $err');
      return null;
    }
  }

  /// 查询冰箱食材数
  static Future<FridgeFoodNumResponseModel?> queryFridgesFoodNums(
      FridgeFoodNumRequestModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'queryFridgesFoodNums req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();
      final FridgeFoodNumResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryFridgeFoodNums(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryFridgesFoodNums response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package,
          msg: 'queryFridgesFoodNums err: $err');
      return null;
    }
  }

  /// 查询灯光|窗帘|环境聚合卡片内部单空间设备排序
  static Future<AggSortResModel?> queryAggSort(AggSortReqModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'queryAggSort req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();
      final AggSortResModel response = await SmartHomeRestClient(UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .queryAggSort(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'queryAggSort response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'queryAggSort err: $err');
      return null;
    }
  }

  /// 保存灯光|窗帘|环境聚合卡片内部单空间设备排序
  static Future<UhomeResponseModel?> saveAggSort(
      AggSortSaveReqModel reqModel) async {
    DevLogger.debug(
        tag: SmartHomeConstant.package,
        msg: 'saveAggSort req:${reqModel.toJson()}');
    try {
      final String appVersion = await getAppVersion();
      final UhomeResponseModel response = await SmartHomeRestClient(
              UhomeDio().dio,
              baseUrl: SmartHomeConstant.baseUrl)
          .saveAggSort(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: SmartHomeConstant.package,
          msg: 'saveAggSort response: $response');
      return response;
    } catch (err) {
      DevLogger.error(
          tag: SmartHomeConstant.package, msg: 'saveAggSort err: $err');
      return null;
    }
  }
}
