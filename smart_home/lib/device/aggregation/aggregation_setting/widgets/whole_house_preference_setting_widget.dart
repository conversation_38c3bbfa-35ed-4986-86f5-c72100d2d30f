import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/store/smart_home_store.dart';

import '../../../../common/constant.dart';
import '../../../../common/constant_gio.dart';
import '../../../../store/smart_home_state.dart';
import '../../../../whole_house/preference_setting/store/preference_setting_action.dart';
import 'setting_item_switch_widget.dart';

/// 仪表盘偏好设置组件
class WholeHousePreferenceSettingWidget extends StatelessWidget {
  const WholeHousePreferenceSettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<SmartHomeState, bool>(
        distinct: true,
        converter: (Store<SmartHomeState> store) {
          return store.state.wholeHouseState.preferenceSettingState.homeSwitch;
        },
        builder: (BuildContext context, bool switchValue) {
          return SettingItemSwitchWidget(
            name: '全屋信息',
            desc: '启用后，首页全屋信息将集中展示设备故障告警、耗材余量监测及运行状态提示信息',
            value: switchValue,
            onChanged: (bool value) {
              Network
                  .checkNetwork
                  .then((IsOnline onValue) {
                gioTrack(GioConst.wholeHouseSwitch, <String, String>{
                  'value': value ? '开' : '关',
                });
                if (result == NetworkStatusfalse) {
                  ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
                  return;
                }
                // 派发设置action
                smartHomeStore
                    .dispatch(SetWholeHousePreferenceSettingAction(value));
              });
            },
          );
        });
  }
}
