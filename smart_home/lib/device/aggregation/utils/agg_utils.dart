import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:vdn/vdn.dart';

import '../../../common/constant.dart';
import '../../../common/constant_gio.dart';
import '../../../widget_common/card_text_style.dart';
import '../agg_store/aggregation_device_reducer_support.dart';
import '../aggregation_card/util/aggregation_device_util.dart';
import '../aggregation_setting/util/aggregation_setting_constant.dart';
import '../aggregation_setting/util/aggregation_setting_manager.dart';

enum AggTypeEnum {
  lightAgg,
  curtainAgg,
  envAgg,
  offlineAgg,
  nonnetAgg,
  cameraAgg,
  none
}

int getAggTypeEnum(AggTypeEnum aggType) {
  if (aggType == AggTypeEnum.lightAgg) {
    return 0;
  } else if (aggType == AggTypeEnum.curtainAgg) {
    return 1;
  } else if (aggType == AggTypeEnum.envAgg) {
    return 2;
  } else if (aggType == AggTypeEnum.nonnetAgg) {
    return 3;
  } else if (aggType == AggTypeEnum.offlineAgg) {
    return 4;
  } else if (aggType == AggTypeEnum.cameraAgg) {
    return 5;
  }
  return -1;
}

String gioValueSwitch(String id) {
  if (isDeviceLightAggregation(id)) {
    return '灯光';
  } else if (isDeviceCurtainAggregation(id)) {
    return '窗帘';
  } else if (isEnvAgg(id)) {
    return '环境';
  } else if (isNonNetAgg(id)) {
    return '不支持联网';
  } else if (isCameraAgg(id)) {
    return '摄像头';
  } else if (isOfflineAgg(id)) {
    return '长期离线';
  }
  return '';
}

// 获取聚合卡片真实id
String assembleAggId(String deviceId) {
  final String familyId = Family.getCurrentFamilySync()?.familyId ?? '';
  if (isDeviceLightAggregation(deviceId)) {
    return '${AggregationSettingConstant.agg_light_id}$familyId';
  } else if (isDeviceCurtainAggregation(deviceId)) {
    return '${AggregationSettingConstant.agg_curtain_id}$familyId';
  } else if (isEnvAgg(deviceId)) {
    return '${AggregationSettingConstant.env_id}$familyId';
  } else if (isNonNetAgg(deviceId)) {
    return '${AggregationSettingConstant.non_net_id}$familyId';
  } else if (isCameraAgg(deviceId)) {
    return '${AggregationSettingConstant.camera_id}$familyId';
  } else if (isOfflineAgg(deviceId)) {
    return '${AggregationSettingConstant.offline_id}$familyId';
  } else {
    return '';
  }
}

String switchAggrParentId(String deviceId) {
  if (isDeviceLightAggregation(deviceId)) {
    return '0';
  } else if (isDeviceCurtainAggregation(deviceId)) {
    return '1';
  } else if (isEnvAgg(deviceId)) {
    return '2';
  } else if (isNonNetAgg(deviceId)) {
    return '3';
  } else if (isCameraAgg(deviceId)) {
    return '4';
  } else if (isOfflineAgg(deviceId)) {
    return '5';
  } else {
    return '';
  }
}

// 获取聚合卡片前缀
String getAggrPrefix(String deviceId) {
  if (isDeviceLightAggregation(deviceId)) {
    return AggregationSettingConstant.agg_light_id;
  } else if (isDeviceCurtainAggregation(deviceId)) {
    return AggregationSettingConstant.agg_curtain_id;
  } else if (isEnvAgg(deviceId)) {
    return AggregationSettingConstant.env_id;
  } else if (isNonNetAgg(deviceId)) {
    return AggregationSettingConstant.non_net_id;
  } else if (isCameraAgg(deviceId)) {
    return AggregationSettingConstant.camera_id;
  } else if (isOfflineAgg(deviceId)) {
    return AggregationSettingConstant.offline_id;
  } else {
    return '';
  }
}

// 埋点
void gioAggClick(String eventId, String deviceId) {
  gioTrack(eventId, <String, dynamic>{
    'sourceName': AggregationSettingManager.getSourceNameById(deviceId),
  });
}

void gioAggDetailBtnClick(String eventId, String deviceId, String value) {
  gioTrack(eventId, <String, dynamic>{
    'sourceName': AggregationSettingManager.getSourceNameById(deviceId),
    'value': value,
  });
}

AppBar buildAggCommonVdnHeader({
  required BuildContext context,
  String title = '',
  void Function()? backCallback,
}) {
  return _buildAggCommonHeaderInternal(
    context: context,
    title: title,
    backCallback: backCallback,
    closeAction: () => Vdn.close(),
  );
}

AppBar buildAggCommonNavigatorHeader({
  required BuildContext context,
  String title = '',
  void Function()? backCallback,
}) {
  return _buildAggCommonHeaderInternal(
    context: context,
    title: title,
    backCallback: backCallback,
    closeAction: () => Navigator.pop(context),
  );
}

AppBar _buildAggCommonHeaderInternal({
  required BuildContext context,
  String title = '',
  void Function()? backCallback,
  required void Function() closeAction,
}) {
  return AppBar(
    elevation: 0,
    titleSpacing: 0,
    backgroundColor: AppSemanticColors.background.secondary,
    systemOverlayStyle: const SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ),
    toolbarHeight: 44,
    centerTitle: true,
    title: Text(
      title,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 17.0,
        fontFamilyFallback: fontFamilyFallback(),
        fontWeight: FontWeight.w500,
        color: AppSemanticColors.item.primary,
      ),
    ),
    leading: IconButton(
        icon: Image.asset(
          'assets/images/icon_aggregation_back.webp',
          package: SmartHomeConstant.package,
          width: 24,
          height: 24,
        ),
        onPressed: () {
          closeAction();
          if (backCallback != null) {
            backCallback();
          }
        }),
  );
}

// 检查网络连接
Future<bool> checkNetworkConnection() async {
  final IsOnline isOnline = await Network.isOnline();
  if (result == NetworkStatusfalse) {
    ToastHelper.showToast(AggregationSettingConstant.networkUnavailable);
    return false;
  }
  return true;
}
