import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../common/common_network_image_widget.dart';
import '../../../../store/smart_home_store.dart';
import '../../../../widget_common/card_text_style.dart';
import '../../../../widget_common/over_scroll_behavior.dart';
import '../../../device_view_model/device_card_view_model.dart';
import '../../../device_widget/device_card.dart';
import '../../aggregation_card/view_model/aggregation_base_view_model.dart';
import '../../aggregation_setting/util/aggregation_setting_constant.dart';
import '../view_model/manage_dialog_room.dart';
import 'aggregation_manage_dialog.dart';

class AggregationManageContentWidget extends StatefulWidget {
  final AggregationBaseViewModel vm;
  final List<FreeDevicesRoomModel> freeDevicesByRoom;
  final void Function(List<FreeDevicesRoomModel>)? callback;

  const AggregationManageContentWidget(
      {super.key,
      required this.vm,
      required this.freeDevicesByRoom,
      required this.callback});

  @override
  State<StatefulWidget> createState() {
    return AggregationManageContentState();
  }
}

class AggregationManageContentState
    extends State<AggregationManageContentWidget> {
  @override
  Widget build(BuildContext context) {
    final double screenHeight =
        MediaQueryData.fromView(View.of(context)).size.height;
    return ScrollConfiguration(
      behavior: NoneOverScrollBehavior(),
      child: CustomScrollView(
          shrinkWrap: true,
          slivers: <Widget>[..._buildManageContent(widget.vm)]),
    );
  }

  // 构建聚合管理列表
  List<Widget> _buildManageContent(AggregationBaseViewModel viewModel) {
    return widget.freeDevicesByRoom.isNotEmpty
        ? <Widget>[
            SliverList.separated(
              itemCount: widget.freeDevicesByRoom.length,
              itemBuilder: (BuildContext context, int index) {
                return _buildRoomList(widget.freeDevicesByRoom[index], context,
                    index, widget.freeDevicesByRoom.length);
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(
                  height: 0,
                );
              },
            )
          ]
        : <Widget>[];
  }

  String getRoomName(FreeDevicesRoomModel roomInfo) {
    final bool showFloor = smartHomeStore.state.deviceState.cardShowFloor;
    String floorName = '', roomName = roomInfo.roomName;
    if (showFloor) {
      floorName = roomInfo.floorName;
    }
    return '$floorName$roomName';
  }

  // 构建聚合管理Widget
  Widget _buildRoomList(
      FreeDevicesRoomModel room, BuildContext context, int index, int listLen) {
    final List<ManageDialogRoom> devices = room.deviceList;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Text(
            getRoomName(room),
            style: TextStyle(
                fontSize: 12,
                fontFamilyFallback: fontFamilyFallback(),
                color: AppSemanticColors.item.secondary),
          ),
        ),
        Container(
          padding: const EdgeInsets.only(top: 4),
          child: CustomScrollView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            slivers: <Widget>[
              SliverList.builder(
                itemCount: devices.length,
                itemBuilder: (BuildContext context, int deviceIndex) {
                  final ManageDialogRoom deviceItem = devices[deviceIndex];
                  final DeviceCardViewModel? deviceCardViewModel =
                      smartHomeStore.state.deviceState
                              .allCardViewModelMap[deviceItem.deviceId]
                          as DeviceCardViewModel?;
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        widget.freeDevicesByRoom[index].deviceList[deviceIndex]
                            .isSelected = !deviceItem.isSelected;
                        if (widget.callback != null) {
                          widget.callback!(widget.freeDevicesByRoom);
                        }
                      });
                    },
                    child: Container(
                      height: 72,
                      padding: const EdgeInsets.only(left: 16, right: 16),
                      margin: const EdgeInsets.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: AppSemanticColors.background.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: <Widget>[
                          SizedBox(
                              width: 40,
                              height: 40,
                              child: CommonNetworkRefreshImg(
                                imageUrl: getSmallCardImgModel(
                                        deviceCardViewModel?.deviceIcon ?? '')
                                    .deviceIcon,
                                alignment: Alignment.center,
                                errorWidget: Image.asset(
                                  'assets/icons/default_device_img.webp',
                                  package: 'smart_home',
                                  height: double.infinity,
                                ),
                              )),
                          const SizedBox(
                            width: 12,
                          ),
                          Expanded(
                            child: Text(
                              deviceCardViewModel?.deviceName ?? '',
                              style: TextStyle(
                                  fontSize: 16,
                                  fontFamilyFallback: fontFamilyFallback(),
                                  fontWeight: FontWeight.w500,
                                  color: AppSemanticColors.item.primary),
                              maxLines: 1,
                              softWrap: false,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 12),
                          CustomCheckbox(
                            value: deviceItem.isSelected,
                            onChanged: (bool newValue) {
                              setState(() {
                                widget
                                    .freeDevicesByRoom[index]
                                    .deviceList[deviceIndex]
                                    .isSelected = newValue;
                                if (widget.callback != null) {
                                  widget.callback!(widget.freeDevicesByRoom);
                                }
                              });
                            },
                          )
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        )
      ],
    );
  }
}

class CustomCheckbox extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;

  const CustomCheckbox({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  CustomCheckboxState createState() => CustomCheckboxState();
}

class CustomCheckboxState extends State<CustomCheckbox> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Network.isOnline().then((IsOnline onValue) {
          if (result == NetworkStatusfalse) {
            ToastHelper.showToast(
                AggregationSettingConstant.networkUnavailable);
          } else {
            widget.onChanged(!widget.value);
          }
        });
      },
      child: SizedBox(
        width: 24,
        height: 24,
        child: widget.value
            ? Image.asset(
                'assets/images/icon_aggregation_selected.webp',
                package: 'smart_home',
              )
            : Image.asset(
                'assets/images/icon_aggregation_unselected.webp',
                package: 'smart_home',
              ),
      ),
    );
  }
}
