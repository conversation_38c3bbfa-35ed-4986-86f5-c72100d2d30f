import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:auto_orientation/auto_orientation.dart';
import 'package:cx_player/cx_player.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/common/util.dart';
import 'package:lottie/lottie.dart';
import 'package:message/message.dart';
import 'package:plugin_device/model/common_models.dart' as plugin_common;
import 'package:plugin_device/model/device_info_model.dart' as plugin_device;
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:smart_home/common/smart_home_util.dart';
import 'package:smart_home/device/device_view_model/camera/presenter/camera_player_interface.dart';
import 'package:smart_home/device/device_widget/PtzWidget.dart';
import 'package:smart_home/device/device_widget/camera_ui_support.dart';
import 'package:smart_home/device/store/camera/camera_support.dart';

import '../../common/constant.dart';
import '../device_view_model/camera_view_model.dart';

double _switchStreamButtonMarginRight = 96;

void _turnToLandscape() {
  if (Platform.isAndroid) {
    AutoOrientation.landscapeAutoMode();
  } else if (Platform.isIOS) {
    Message.send(
        "Flutter_Support_Landscape_Notification"); //让iOS容器支持横屏 —— 王栋、郑连乐
    AutoOrientation.landscapeRightMode();
  }
}

/// 设置竖屏
void _turnToPortraitUp() {
  if (Platform.isIOS) {
    Message.send("Flutter_Not_Support_Landscape_Notification");
  }
  AutoOrientation.portraitUpMode();
}

Future<CameraStreamType?> showCameraLandPlay(
    BuildContext context,
    String deviceId,
    CameraStreamCount cameraStreamCount,
    ILivePlayer player,
    CameraStreamType streamType) {
  _track(deviceId, StatId.MB38531);
  _turnToLandscape();
  return Navigator.push<CameraStreamType>(context,
      PageRouteBuilder<CameraStreamType>(pageBuilder: (BuildContext context,
          Animation<double> animation, Animation<double> secondaryAnimation) {
    return CameraLandPlayWidget(
      player,
      deviceId,
      cameraStreamCount,
      streamType: streamType,
    );
  }));
}

class CameraLandPlayWidget extends StatefulWidget {
  final ILivePlayer _player;
  final String deviceId;

  final CameraStreamCount cameraStreamCount;

  CameraStreamType streamType = CameraStreamType.main;

  CameraLandPlayWidget(this._player, this.deviceId, this.cameraStreamCount,
      {super.key, this.streamType = CameraStreamType.main});

  @override
  State<StatefulWidget> createState() {
    return _CameraLandPlayWidgetState();
  }
}

class _CameraLandPlayWidgetState extends State<CameraLandPlayWidget>
     {
  final double rightMargin = 80;
  static const int hideAnimDuration = 300;
  double bgOpacity = 0.0;
  late CameraPlayModel _playModel;
  bool init = false;
  Timer? _hideTimer;
  Timer? _removeButtonsTimer;
  bool _showButtons = true;
  bool _buildButtons = true;

  final String _stateDesc = '_CameraLandPlayWidgetState';

  @override
  void initState() {
    //拦截系统返回
    InterceptSystemBackUtil.interceptSystemBack(
        pageName: _stateDesc,
        callback: () async {
          setState(() {
            _exitPage();
          });
        });
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      if (!init) {
        init = true;
        _resetHideTimer();
      }
    });
    super.initState();
    Future<void>.delayed(Duration.zero, () {
      setState(() {
        bgOpacity = 1.0;
      });
    });
  }

  @override
  void didChangeDependencies() {
    _playModel = CameraPlayModel(widget._player, widget.deviceId);
    _playModel.addListener(_setState);
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    InterceptSystemBackUtil.cancelInterceptSystemBack(_stateDesc);
    _playModel.removeListener(_setState);
    _hideTimer?.cancel();
    _removeButtonsTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.hidden) {
      _playModel._player.stopPlay();
    } else if (state == AppLifecycleState.resumed) {
      _turnToLandscape();
    }
  }

  void _setState() {
    if (mounted) {
      setState(() {});
    }
  }

  void _exitPage() {
    Navigator.pop(context, widget.streamType);
    _turnToPortraitUp();
  }

  void _resetHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 3), () {
      if (_showButtons) {
        setState(() {
          _showButtons = false;
          _delayRemoveButtons();
        });
      }
    });
  }

  void _delayRemoveButtons() {
    _removeButtonsTimer?.cancel();
    _removeButtonsTimer =
        Timer(const Duration(milliseconds: hideAnimDuration), () {
      if (!_showButtons) {
        setState(() {
          _buildButtons = false;
        });
      }
    });
  }

  void _onTouch() {
    if (!_showButtons) {
      setState(() {
        _showButtons = true;
        _buildButtons = true;
        _removeButtonsTimer?.cancel();
      });
    }
    _resetHideTimer();
  }

  void _changeStreamType() {
    if (widget.streamType == CameraStreamType.main) {
      widget.streamType = CameraStreamType.secondary;
    } else {
      widget.streamType = CameraStreamType.main;
    }
  }

  void _switchCameraStream() {
    _changeStreamType();
    if (_playModel.shouldShowPlayBtn) {
      widget._player.startPlay();
      return;
    }
    if (mounted) {
      setState(() {});
    }
  }

  Widget _getVideoView() {
    if (widget.streamType == CameraStreamType.main) {
      return _playModel._player.getVideoView();
    }
    return _playModel._player.getExtraVideoView() ?? const SizedBox.shrink();
  }

  bool get isSupportMultiCameraStream =>
      widget.cameraStreamCount == CameraStreamCount.two;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Listener(
        behavior: HitTestBehavior.opaque,
        onPointerDown: (_) {
          _onTouch();
        },
        child: Stack(
          children: <Widget>[
            AnimatedOpacity(
              opacity: bgOpacity,
              duration: const Duration(milliseconds: 300),
              child: Container(
                color: Colors.black,
              ),
            ),
            Center(child: _getVideoView()),
            if (_buildButtons)
              AnimatedOpacity(
                opacity: _showButtons ? 1.0 : 0.0,
                duration:
                    Duration(milliseconds: _showButtons ? 0 : hideAnimDuration),
                child: Stack(
                  children: <Widget>[
                    //禁音、暂停
                    Positioned(
                      right: rightMargin,
                      bottom: 50,
                      child: Column(
                        children: <Widget>[
                          IconButton(
                              //禁音
                              onPressed: () {
                                _playModel.toggleMute();
                              },
                              icon: Image.asset(
                                _playModel.isMute
                                    ? CameraConstant.muteImageUrlOn
                                    : CameraConstant.muteImageUrlOff,
                                package: SmartHomeConstant.package,
                                fit: BoxFit.cover,
                                width: 50,
                                height: 50,
                              )),
                          const SizedBox(
                            height: 30,
                          ),
                          IconButton(
                              //暂停
                              onPressed: () {
                                widget._player.stopPlay();
                                _track(widget.deviceId, StatId.MB38530,
                                    <String, dynamic>{'value': '暂停'});
                              },
                              icon: Image.asset(
                                CameraConstant.pauseUrl,
                                fit: BoxFit.cover,
                                package: SmartHomeConstant.package,
                                width: 50,
                                height: 50,
                              )),
                        ],
                      ),
                    ),

                    //云台
                    if (widget.streamType == CameraStreamType.main)
                      Positioned(
                          left: 40,
                          bottom: 35,
                          child: SizedBox(
                            width: 100,
                            height: 100,
                            child: PtzRockerView(
                                onDirectionChanged: (Direction d) {
                              _playModel.drivePtz(d);
                            }),
                          )),
                  ],
                ),
              ),
            //background
            if (_playModel.shouldShowBg)
              Image.asset(
                width: double.infinity,
                height: double.infinity,
                'assets/images/bg_blur_big_new_rect.webp',
                package: SmartHomeConstant.package,
                fit: BoxFit.cover,
              ),
            //loading
            if (_playModel.shouldShowLoading)
              Center(
                  child: Lottie.asset(
                CameraConstant.loadImageUrl,
                height: 24,
                width: 24,
                package: SmartHomeConstant.package,
                key: const Key('camera_loading_card_image'),
              )),
            if (_playModel.shouldShowPlayBtn)
              Center(
                child: IconButton(
                    onPressed: () {
                      widget._player.startPlay();
                      _track(widget.deviceId, StatId.MB38530,
                          <String, dynamic>{'value': '播放'});
                    },
                    icon: Image.asset(
                      CameraConstant.playIcon,
                      package: SmartHomeConstant.package,
                    )),
              ),
            if (_buildButtons)
              AnimatedOpacity(
                opacity: _showButtons ? 1.0 : 0.0,
                duration:
                    Duration(milliseconds: _showButtons ? 0 : hideAnimDuration),
                child: Stack(
                  children: <Widget>[
                    Positioned(
                      left: 40,
                      top: 30,
                      child: IconButton(
                          //返回按钮
                          onPressed: () {
                            _exitPage();
                          },
                          icon: Transform.rotate(
                            angle: pi,
                            child: Image.asset(
                              CameraConstant.rightArrImage,
                              package: SmartHomeConstant.package,
                              width: 20,
                              height: 20,
                            ),
                          )),
                    ),
                    if (isSupportMultiCameraStream)
                      Positioned(
                        top: 30,
                        right: rightMargin + _switchStreamButtonMarginRight,
                        child: SizedBox(
                            height: 32,
                            child: Center(
                              child: GestureDetector(
                                onTap: () {
                                  _switchCameraStream();
                                  _track(widget.deviceId, StatId.MB76590);
                                },
                                child: CameraStreamTypeButton(
                                  isSupportMultiCameraStream
                                      ? widget.streamType
                                      : null,
                                  marginRight: 0,
                                ),
                              ),
                            )),
                      ),
                    Positioned(
                      top: 30,
                      right: rightMargin,
                      child: GestureDetector(
                        ////跳转详情页
                        onTap: () {
                          goToPageWithDebounce(
                              CameraConstant.getCameraDetailPageUrl(
                                  widget.deviceId));
                          _exitPage();
                          _track(widget.deviceId, StatId.MB38532);
                        },
                        child: Container(
                          width: 60,
                          height: 32,
                          decoration: BoxDecoration(
                              color: const Color(0x66000000),
                              borderRadius: BorderRadius.circular(16)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: <Widget>[
                              const Text(
                                '进入',
                                style: TextStyle(
                                    color: Colors.white, fontSize: 14),
                              ),
                              Image.asset(
                                CameraConstant.rightArrImage,
                                package: SmartHomeConstant.package,
                                width: 16,
                                height: 16,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
          ],
        ),
      ),
    );
  }
}

class CameraPlayModel extends ChangeNotifier {
  bool shouldShowBg = true;
  bool shouldShowLoading = false;
  bool shouldShowPlayBtn = true;
  bool isMute = true;

  final ILivePlayer _player;
  final String _deviceId;

  void _fitStream(Widget? videoView) {
    if (videoView != null && videoView is IVideoView) {
      (videoView as IVideoView).setFit(BoxFit.contain);
    }
  }

  CameraPlayModel(this._player, this._deviceId) {
    isMute = _player.isMute();

    _updatePlayState(_player.getCurrentPlayState());

    _fitStream(_player.getVideoView());
    _fitStream(_player.getExtraVideoView());

    _player.observePlayState().listen((PlayState state) {
      _updatePlayState(state);
    });
  }

  void _updatePlayState(PlayState state) {
    shouldShowBg = state != PlayState.playing;
    shouldShowLoading = state == PlayState.loading;
    shouldShowPlayBtn = state == PlayState.idle || state == PlayState.error;
    isMute = _player.isMute();
    notifyListeners();
  }

  void toggleMute() {
    if (isMute) {
      _player.unMute();
    } else {
      _player.mute();
    }
    isMute = _player.isMute();
    notifyListeners();
    _track(_deviceId, StatId.MB38534,
        <String, dynamic>{'value': isMute ? "非静音" : '静音'});
  }

  // 定义常量
  static const String PTZ_UP = '5';
  static const String PTZ_DOWN = '6';
  static const String PTZ_LEFT = '7';
  static const String PTZ_RIGHT = '8';
  static const String PTZ_STOP = '9';

  Future<void> drivePtz(Direction direction) async {
    String d = PTZ_STOP;
    switch (direction) {
      case Direction.Up:
        d = PTZ_UP;
        break;
      case Direction.Down:
        d = PTZ_DOWN;
        break;
      case Direction.Left:
        d = PTZ_LEFT;
        break;
      case Direction.Right:
        d = PTZ_RIGHT;
        break;
      default:
        d = PTZ_STOP;
        break;
    }
    final List<plugin_common.Command> commands = <plugin_common.Command>[
      plugin_common.Command.fromMap(
          <String, String>{"name": "ptzControl", "value": d})
    ];
    try {
      await UpDevicePlugin.executeOperation(_deviceId, commands);
    } catch (e, s) {
      DevLogger.error(tag: "land_play", msg: 'drivePtz error:$e $s');
    }
    _track(_deviceId, StatId.MB38533);
  }
}

class StatId {
  static const String MB38531 = 'MB38531'; //音视频-卡片横屏-进入卡片横屏
  static const String MB38530 = 'MB38530'; //音视频-卡片横屏-播放暂停
  static const String MB38533 = 'MB38533'; //音视频-卡片横屏-云台操控
  static const String MB38534 = 'MB38534'; //音视频-卡片横屏-视频静音按钮
  static const String MB38532 = 'MB38532'; //音视频-卡片横屏-进入详情页
  static const String MB76590 = 'MB76590'; //切换主副摄
}

void _track(String deviceId, String eventId, [Map<String, dynamic>? variable]) {
  UpDevicePlugin.getDeviceInfoById(deviceId)
      .then((plugin_device.DeviceInfoModel info) {
    final Map<String, dynamic> param = <String, dynamic>{
      'typeid': info.wifiType
    };
    if (variable != null) {
      param.addAll(variable);
    }
    gioTrack(eventId, param);
  });
}
