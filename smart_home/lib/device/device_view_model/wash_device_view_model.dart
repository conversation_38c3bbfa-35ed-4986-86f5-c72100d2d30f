/*
 * 描述：洗衣机
 * 作者：songFJ
 * 创建时间：2024/7/15
 */

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:plugin_device/model/common_models.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:smart_home/common/constant.dart';
import 'package:smart_home/common/constant_gio_wash.dart';
import 'package:smart_home/device/component/view_model/expand_popup_text_text_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_popup_wash_program_view_model.dart';
import 'package:smart_home/device/component/view_model/expand_wash_state_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_popup_text_view_model.dart';
import 'package:smart_home/device/component/view_model/fixed_switch_icon_text_view_model.dart';
import 'package:smart_home/device/component_view_model/grid_view_model.dart';
import 'package:smart_home/device/component_view_model/popup_component_view_model.dart';
import 'package:smart_home/device/component_view_model/power_on_off_btn_model/power_button_animation_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_bucket_switch_component_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_program_more_select_view_model.dart';
import 'package:smart_home/device/component_view_model/wash_program_select_component_view_model.dart';
import 'package:smart_home/device/device_info_model/smart_home_device_state.dart';
import 'package:smart_home/device/device_view_model/device_card_view_model.dart';
import 'package:smart_home/device/store/device_action.dart';
import 'package:smart_home/store/smart_home_store.dart';
import 'package:user/user.dart';
import 'package:wash_device_manager/wash_device_manager.dart';
import 'package:wash_device_manager/wash_device_model/user_prefer/wash_device_user_prefer_program.dart';
import 'package:wash_device_manager/wash_device_model/wash_device_attribute.dart';
import 'package:wash_device_manager/wash_device_model/wash_device_model_v3.dart';
import 'package:wash_device_manager/wash_device_model/wash_device_operate_command.dart';
import 'package:wash_device_manager/wash_device_model/wash_device_origin_attribute.dart';
import 'package:wash_device_manager/wash_device_model/wash_device_program_model_v3.dart';

import '../../common/smart_home_util.dart';
import '../component_view_model/component_view_model.dart';
import '../component_view_model/wash_roller_component_view_model.dart';
import '../device_info_model/device_card_attribute.dart';
import '../device_info_model/smart_home_device_attribute.dart';
import '../device_info_model/smart_home_device_basic_info.dart';
import '../resize_device_card/resize_base_model.dart';

enum OperateType {
  start,
  suspend,
  stop,
}

class WashDeviceViewModel extends DeviceCardViewModel {
  WashDeviceViewModel({required super.device}) {
    _modelV3 = WashDeviceManager.getInstance().washDeviceModelV3_2(
        device.basicInfo.deviceName,
        device.basicInfo.typeId,
        device.basicInfo.deviceId, <String, WashDeviceOriginAttribute>{});
  }

  final List<String> _notPowerOutWashDeviceTypeIds = <String>[
    '111c120024000810040100318000960000000000000000000000000000000000',
    '101c12002400081005010021800067000000f300000000000000000000000000',
    '111c120024000810040100318001130000000002000000000000000000000000',
    '101c12002400081005010021800067430000eb00000000000000000000000000',
    '101c12002400081005010021800036420000b600000000000000000000000000',
    '101c120024000810040100318000690000000000000000000000000000000000',
    '111c120024000810040100318000754200000001000000000000000000000000',
    '111c120024000810040100318001010000000000000000000000000000000000',
    '111c120024000810040100318001014700000001000000000000000000000000',
    '111c120024000810040100318001014300000000000000000000000000000000',
    '111c120024000810040100318001014400000000000000000000000000000000',
    '111c120024000810050100218000880000000000000000000000000000000000',
    '111c12002400081005010021800088410000d500000000000000000000000000',
    '111c120024000810040100318001014700000000000000000000000000000000',
    '101c120024000810040100318000690000000100000000000000000000000000',
    '111c120024000810040100318001014300000001000000000000000000000000',
    '111c120024000810040100318001014400000001000000000000000000000000',
    '111c120024000810040100318001014300000003000000000000000000000000',
    '111c120024000810040100318001014300000002000000000000000000000000',
    '111c120024000810040100318001014100000001000000000000000000000000',
    '111c120024000810040100318001014100000002000000000000000000000000',
    '111c120024000810040100318001010000000001000000000000000000000000',
    '111c120024000810040100318000754400000001000000000000000000000000',
    '111c120024000810040100318000964100000000000000000000000000000000',
    '111c120024000810040100318001010000000002000000000000000000000000',
    '111c120024000810040100318001010000000003000000000000000000000000',
    '111c120024000810040100318001010000000004000000000000000000000000',
    '111c120024000810040100318001014100000000000000000000000000000000',
    '111c120024000810040100318001014c00000201000000000000000000000000',
    '111c120024000810040100318000990000000000000000000000000000000000',
    '201c12002400081005020021800088414100c900000000000000000000000040',
    '111c120024000810050100218000884100000000000000000000000000000000',
    '201c51890c31c308050200218000884100000000000000000000000000000040',
    '201c51890c31c30831010021800586474200000000000000000000000000004'
  ];

  bool upUnitRunning = false;
  bool downUnitRunning = false;

  final int upUnitIndex = 0;
  final int downUnitIndex = 1;

  final String min = '分钟';

  final String standByValue = '1';
  final String standBy = '待机';

  final String supportCancel = 'supportCancel';

  final String value_0 = '0';
  final String value_1 = '1';

  final String _upRoller = '上筒';
  final String _downRoller = '下筒';

  final String _largePulsator = '大桶';
  final String _smallPulsator = '小桶';

  final String _startName = '启动';
  final String _suspendName = '暂停';
  final String _stopName = '停止';

  final Set<String> _shakeValueSet = <String>{'31', '19', '32', '36'};

  Map<String, WashDeviceUserPreferProgram> selectedProgram =
      <String, WashDeviceUserPreferProgram>{};
  Map<String, int> selectedUnitIndexMap = <String, int>{};

  WashDeviceModelV3 _modelV3 = WashDeviceModelV3();

  void updateWashDeviceModelV3() {
    final Map<String, WashDeviceOriginAttribute> attributeMap =
        <String, WashDeviceOriginAttribute>{};
    device.attributeMap.forEach((_, SmartHomeDeviceAttribute value) {
      attributeMap[value.name] = WashDeviceOriginAttribute(
          name: value.name, value: value.value, writable: value.writable);
    });

    _modelV3 = WashDeviceManager.getInstance().washDeviceModelV3_2(
        device.basicInfo.deviceName,
        device.basicInfo.typeId,
        device.basicInfo.deviceId,
        attributeMap);
  }

  WashDeviceModelV3 get modelV3 {
    return _modelV3;
  }

  WashDeviceProgramV3 get programV3 {
    return WashDeviceManager.getInstance().washDeviceProgramV3(
        User.getOauthDataSync()?.uhome_user_id ?? '',
        device.basicInfo.deviceName,
        device.basicInfo.typeId,
        device.basicInfo.deviceId);
  }

  /// 开机且有剩余时间
  @override
  bool get runningMode {
    if (multiRollerDevice) {
      /// 多筒设备
      if (washDeviceCommonStateViewModel.runningState) {
        return true;
      }

      // 检查子桶是否运行中
      if (childViewModelMap != null) {
        for (final DeviceCardViewModel childVM in childViewModelMap!.values) {
          if (childVM is WashDeviceViewModel) {
            if (childVM.washDeviceCommonStateViewModel.runningState) {
              return true;
            }
          }
        }
      }
    } else {
      if (washDeviceCommonStateViewModel.runningState) {
        return true;
      }
    }
    return false;
  }

  @override
  bool get powerOff {
    return modelV3.offStatus6.value == value_1 ||
        modelV3.onStatus5.value == value_0;
  }

  @override
  bool get alarm {
    if (multiRollerDevice) {
      /// 多筒设备告警
      return multiRollerCautionsList.isNotEmpty;
    } else {
      if (modelV3.alarmCancelKey.isEmpty) {
        return false;
      }
      return super.alarm;
    }
  }

  @override
  void alarmClick(BuildContext context) {
    if (multiRollerDevice) {
      gioTrack(SmartHomeConstant.alarmGio,
          <String, String>{'cardType': _cardTypeName});

      /// 逻辑引擎设备
      final String alarmStrings =
          engineDeviceAlarmStrings(multiRollerCautionsList);
      if (alarmStrings.isNotEmpty) {
        showAlarms(context, alarmStrings);
      }
      DevLogger.info(
          tag: SmartHomeConstant.package,
          msg: 'engineCautions - ${device.engineCautions}');
    } else {
      super.alarmClick(context);
    }
  }

  @override
  String provideAlarmCancelKey() {
    return modelV3.alarmCancelKey;
  }

  @override
  String? get otherDeviceOfflineInfo {
    if (!_notPowerOutWashDeviceTypeIds.contains(device.basicInfo.typeId)) {
      if (deviceCardType == DeviceCardType.smallCard) {
        return '';
      }
      return '已关机或未联网';
    }
    return super.otherDeviceOfflineInfo;
  }

  @override
  String get devicePowerOffInfo {
    if (powerOff) {
      if (!modelV3.supportRemoteOnOff) {
        return '已关机';
      }
    }
    return '';
  }

  @override
  bool get childLockOn => modelV3.childLockStatus7.value == value_1;

  bool get multiRollerDevice => childViewModelMap != null;

  bool get allRollerDataLoadFinish => attachDeviceList?.isNotEmpty ?? false;

  // TODO(sfj): 待调整
  /// 所有设备聚合告警状态
  List<Caution> multiRollerCautionsList = <Caution>[];

  /// 所有从属设备
  List<WashDeviceViewModel>? attachDeviceList;

  List<ComponentBaseViewModel> washRollerItemList = <ComponentBaseViewModel>[];

  /// 卡片点击弹出框数据VM
  PopupComponentViewModel? cardClickPopupComponent;

  DeviceCardAttribute multiRollerAttributeUnit =
      DeviceCardAttribute(label: '', value: '', unit: '');

  /// 洗衣机接口数据更新完成 && 属性上报时需调用该方法
  void updateMultiRollerData() {
    /// 更新子设备列表
    final Map<String, DeviceCardViewModel>? childViewModelMap =
        this.childViewModelMap;
    final int mapLength = (childViewModelMap?.keys.length ?? 0) + 1;

    if (childViewModelMap != null && (mapLength != attachDeviceList?.length)) {
      final List<WashDeviceViewModel> list = <WashDeviceViewModel>[];

      /// 洗衣机特殊，主设备算作其中一个设备
      if (modelV3.rollerName.isNotEmpty) {
        list.add(this);
      }
      childViewModelMap.forEach((_, DeviceCardViewModel value) {
        /// 只添加洗衣机属性解析数据请求成功的设备，未请求成功设备无法正常展示筒名称&无法进行排序
        if (value is WashDeviceViewModel &&
            value.modelV3.rollerName.isNotEmpty) {
          list.add(value);
        }
      });

      /// 按接口字段进行排序
      list.sort((WashDeviceViewModel a, WashDeviceViewModel b) {
        return a.modelV3.orderNum - b.modelV3.orderNum;
      });

      if (list.length == mapLength) {
        /// 更新子设备列表
        attachDeviceList = list;
      }
    }

    final List<WashDeviceViewModel>? list = attachDeviceList;

    if (list != null && list.isNotEmpty) {
      /// 组装弹出框四筒状态
      final List<ComponentBaseViewModel> componentList =
          <ComponentBaseViewModel>[];

      /// 大卡片数据
      final List<ComponentBaseViewModel> cardItemList =
          <ComponentBaseViewModel>[];

      bool allEnded = true;
      bool allStandBy = true;
      final List<String> runningRollerList = <String>[];

      /// 清理告警数据
      multiRollerCautionsList.clear();
      list.forEach((WashDeviceViewModel element) {
        if (device.basicInfo.configState ==
                SmartHomeDeviceConfigState.supported &&
            modelV3.alarmCancelKey.isNotEmpty) {
          /// 重新添加告警数据
          multiRollerCautionsList.addAll(element.device.engineCautions);
        }

        final MultiRollerDeviceStateModel? model =
            element.multiRollerDeviceState(element.deviceRole3 ? this : null);
        // element.multiRollerDeviceStatus;
        if (model?.runningState == MultiRollerRunningState.running) {
          runningRollerList.add(element.modelV3.rollerName);
        }
        if (model?.runningState != MultiRollerRunningState.washEnd) {
          allEnded = false;
        }
        if (model?.runningState != MultiRollerRunningState.standBy) {
          allStandBy = false;
        }

        final WashRollerComponentViewModel viewModel =
            WashRollerComponentViewModel(
                rollerName: element.modelV3.rollerName,
                rollerState: model?.state ?? '--',
                rollerDesc: model?.timeDesc ?? '',
                enable: true,
                rollerClickCallback: (BuildContext? context) {
                  Network
                      .checkNetwork
                      .then((IsOnline onValue) {
                    if (result == NetworkStatusfalse) {
                      ToastHelper.showToast(
                          SmartHomeConstant.NETWORK_ERROR_TIP);
                      return;
                    }
                    DevLogger.info(
                        tag: SmartHomeConstant.package,
                        msg: 'multiRoller goToPage <DetailPage>, '
                            'deviceId: ${element.deviceId}, '
                            'typeId: ${element.device.basicInfo.typeId}, '
                            'configState: ${element.device.basicInfo.configState}, '
                            'model:${element.device.basicInfo.model}');

                    hidePopup(context);

                    gioTrack(
                        GioWash.gioWashMultiClickTongSelectDialogItem,
                        <String, dynamic>{
                          'value': element.modelV3.rollerName,
                          'devicetype': _drumCount
                        });

                    /// 跳转详情页
                    goToPageWithDebounce(SmartHomeConstant.vdnDeviceDetail +
                        element.device.basicInfo.deviceId);
                  });
                });
        componentList.add(viewModel);

        /// 多筒 功能区item
        final ExpandPopupTextTextViewModel itemViewModel =
            ExpandPopupTextTextViewModel(
                text: element.modelV3.rollerName,
                desc: model?.state ?? '--',
                enable: !deviceOffline,
                clickCallback: (BuildContext? context) {
                  Network
                      .checkNetwork
                      .then((IsOnline onValue) {
                    if (result == NetworkStatusfalse) {
                      ToastHelper.showToast(
                          SmartHomeConstant.NETWORK_ERROR_TIP);
                      return;
                    }
                    DevLogger.info(
                        tag: SmartHomeConstant.package,
                        msg: 'multiRoller goToPage <DetailPage>, '
                            'deviceId: ${element.deviceId}, '
                            'typeId: ${element.device.basicInfo.typeId}, '
                            'configState: ${element.device.basicInfo.configState}, '
                            'model:${element.device.basicInfo.model}');
                    gioTrack(GioWash.gioWashMultiTongClick, <String, dynamic>{
                      'value': element.modelV3.rollerName,
                      'devicetype': _drumCount
                    });

                    /// 跳转详情页
                    goToPageWithDebounce(SmartHomeConstant.vdnDeviceDetail +
                        element.device.basicInfo.deviceId);
                  });
                });
        cardItemList.add(itemViewModel);
      });

      washRollerItemList = cardItemList;

      final GridViewModel viewModel =
          GridViewModel(componentList: componentList, mainAxisExtent: 71);

      final PopupComponentViewModel popupComponentViewModel =
          PopupComponentViewModel(
              title: '请选择筒',
              deviceId: deviceId,
              attributeKey: 'allRollerState',
              componentList: <ComponentBaseViewModel>[viewModel]);

      popupFuncSet[popupComponentViewModel.identification] =
          popupComponentViewModel;

      cardClickPopupComponent = popupComponentViewModel;
      if (runningRollerList.isNotEmpty) {
        multiRollerAttributeUnit =
            DeviceCardAttribute(label: '', value: '运行中', unit: '');
      } else if (allEnded) {
        multiRollerAttributeUnit =
            DeviceCardAttribute(label: '', value: '已结束', unit: '');
      } else if (allStandBy) {
        multiRollerAttributeUnit =
            DeviceCardAttribute(label: '', value: '待机', unit: '');
      } else {
        multiRollerAttributeUnit =
            DeviceCardAttribute(label: '', value: '', unit: '');
      }
    } else {
      multiRollerAttributeUnit =
          DeviceCardAttribute(label: '', value: '', unit: '');
    }
  }

  bool get isSingleDoubleWashNetDataNotPrepared {
    return !(smartHomeStore.state.deviceState.washInfoPreparedTypeIdSet
            .contains(device.basicInfo.typeId) &&
        smartHomeStore.state.deviceState.washPreferPreparedDeviceIdSet
            .contains(device.basicInfo.deviceId));
  }

  /// 多筒卡片通用状态
  String get _masterWashDeviceStatus {
    if (multiRollerDevice) {
      if (deviceOffline) {
        return '离线';
      }
      if (powerOff) {
        return '关';
      }

      return '在线';
    }
    return '';
  }

  /// 多筒状态获取
  MultiRollerDeviceStateModel? multiRollerDeviceState(
      WashDeviceViewModel? parentViewModel) {
    final int unitIndex = selectedUnitIndexMap[selectedFunctionSetKey] ?? 0;

    if (parentViewModel != null) {
      if (parentViewModel.lowPower) {
        return MultiRollerDeviceStateModel(
            runningState: MultiRollerRunningState.offline,
            state: '已关机',
            timeDesc: '');
      }
      if (parentViewModel.deviceOffline) {
        return MultiRollerDeviceStateModel(
            runningState: MultiRollerRunningState.offline,
            state: '离线',
            timeDesc: '');
      }
      if (parentViewModel.onlineNotReady) {
        return MultiRollerDeviceStateModel(
            runningState: MultiRollerRunningState.powerOff,
            state: '--',
            timeDesc: '');
      }
    }

    final MultiRollerDeviceStateModel? state =
        multiRollerStateModelForUnitIndex(unitIndex);

    return state;
  }

  /// 多筒设备当前筒运行状态
  MultiRollerDeviceStateModel? get multiRollerDeviceStatus {
    final int unitIndex = selectedUnitIndexMap[selectedFunctionSetKey] ?? 0;
    final MultiRollerDeviceStateModel? state =
        multiRollerStateModelForUnitIndex(unitIndex);

    return state;
  }

  // 大卡片状态展示
  @override
  String get largeCardStatus {
    return _washDeviceCardState;
  }

  /// 中卡片状态展示
  @override
  String get middleCardStatus {
    return _washDeviceCardState;
  }

  // 小卡片状态展示
  @override
  String get smallCardStatus {
    return _washDeviceCardState;
  }

  String get _washDeviceCardState {
    if (multiRollerDevice) {
      /// 只展示多筒卡片通用状态
      return _masterWashDeviceStatus;
    }

    return washDeviceCommonStateViewModel.state;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeOne() {
    String status = '';
    String label = '';

    if (modelV3.isDouble) {
      if (modelV3.isRoller) {
        label = _upRoller;
      } else {
        label = _largePulsator;
      }
    }

    if (modelV3.upStandBy) {
      status = standBy;
    } else if (modelV3.upEnd) {
      return null;
    } else if (modelV3.upCanTakeout) {
      status = '可取衣';
    } else if (modelV3.upSuspend) {
      status = '暂停中';
    } else if (modelV3.upPreDryAUTO) {
      status = 'AUTO';
    } else if (modelV3.upAuto) {
      status = 'AUTO';
    } else if (modelV3.upResn && modelV3.upRemainingResnTime > 0) {
      status = '剩${modelV3.upRemainingResnTime}$min';
      label = '预约中';
      if (modelV3.isDouble) {
        if (modelV3.isRoller) {
          label = '$_upRoller预约中';
        } else {
          label = '$_largePulsator预约中';
        }
      }
    } else if (modelV3.cyclePhaseUP35.value.isNotEmpty &&
        modelV3.upRemainingTime > 0) {
      status = '剩${modelV3.upRemainingTime}$min';
    }
    return status.isNotEmpty
        ? DeviceCardAttribute(label: label, value: status, unit: '')
        : null;
  }

  @override
  DeviceCardAttribute? deviceCardAttributeTwo() {
    String status = '';
    String label = '';
    if (modelV3.isDouble) {
      if (modelV3.isRoller) {
        label = _downRoller;
      } else {
        label = _smallPulsator;
      }
    }
    if (modelV3.downStandBy) {
      status = standBy;
    } else if (modelV3.downEnd) {
      return null;
    } else if (modelV3.downCanTakeout) {
      status = '可取衣';
    } else if (modelV3.downSuspend) {
      status = '暂停中';
    } else if (modelV3.downAuto) {
      status = 'AUTO';
    } else if (modelV3.downResn && modelV3.downRemainingResnTime > 0) {
      status = '剩${modelV3.downRemainingResnTime}$min';
      label = '预约中';
      if (modelV3.isDouble) {
        if (modelV3.isRoller) {
          label = '$_downRoller预约中';
        } else {
          label = '$_smallPulsator预约中';
        }
      }
    } else if (modelV3.cyclePhaseDN35.value.isNotEmpty &&
        modelV3.downRemainingTime > 0) {
      status = '剩${modelV3.downRemainingTime}$min';
    }
    return status.isNotEmpty
        ? DeviceCardAttribute(label: label, value: status, unit: '')
        : null;
  }

  @override
  SmartHomeDeviceAttribute? get onOffAttribute {
    final String onStatusKey = modelV3.onCommand?.name ?? '';
    return device.attributeMap[onStatusKey];
  }

  @override
  ComponentBaseViewModel? get topRightComponentViewModel {
    if (multiRollerDevice) {
      return null;
    }
    if (device.basicInfo.model.isEmpty) {
      return null;
    }
    if (deviceOffline) {
      return null;
    }

    bool haveAttribute = false;
    if (modelV3.onCommand is WashDeviceOperateCommand &&
        modelV3.offCommand is WashDeviceOperateCommand) {
      haveAttribute = true;
    }
    // 远程开关机功能开启 且 支持开关机（上报开关机属性）
    final bool showPower = modelV3.supportRemoteOnOff && haveAttribute;
    final bool isControllable = onOffAttribute?.writable ?? false;

    if (!showPower || !isControllable) {
      return null;
    }

    final PowerButtonAnimationViewModel viewModel =
        PowerButtonAnimationViewModel(
            isOn: !powerOff,
            writable: onOffAttribute?.writable ?? false,
            checkContinue: powerButtonCheckContinue,
            btnClick: (BuildContext? context) {
              powerButtonCheckContinue().then((bool pass) {
                if (pass) {
                  _gioPowerClick();
                  executePowerOnOff(!powerOff, true);
                }
              }).catchError((dynamic err) {
                DevLogger.error(
                    tag: SmartHomeConstant.package,
                    msg: 'powerButtonCheckContinue err:$err');
              });
            });

    return viewModel;
  }

  void _gioPowerClick() {
    if (deviceCardType == DeviceCardType.largeCard) {
      _gioPowerWith(SmartHomeConstant.deviceCardOnOffGio);
    } else if (deviceCardType == DeviceCardType.middleCard) {
      _gioPowerWith(SmartHomeConstant.middleCardOnOffGio);
    }
  }

  void _gioPowerWith(String gioEvent) {
    gioTrack(gioEvent, <String, dynamic>{
      'deviceid': deviceId,
      'devicetype': appTypeName,
      'status': onOffAttribute?.value == trueValue ? '关机' : '开机',
      'devno': prodNo,
    });
  }

  @override
  Future<bool> powerButtonCheckContinue() {
    return checkDeviceState(
        checkPowerOff: false,
        writable: onOffAttribute?.writable ?? false,
        checkChildLock: false);
  }

  @override
  String get additionalOfflineToast {
    if ((offlineInfo == otherDeviceOfflineInfo) && offlineInfo.isNotEmpty) {
      return SmartHomeConstant.toastDevicePowerOffOrOffline;
    }
    return super.additionalOfflineToast;
  }

  @override
  Future<void> executePowerOnOff(bool powerOn, bool writable) async {
    final IsOnline isOnline =
        await Network.isOnline();
    if (!isOnline.isOnline) {
      ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
      return;
    }
    if (device.onlineState == SmartHomeDeviceOnlineState.onlineNotReady) {
      ToastHelper.showToast(SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
      return;
    }
    if (alarm && !writable) {
      ToastHelper.showToast(SmartHomeConstant.toastDeviceAlarmInfo);
      return;
    }
    if (!writable) {
      ToastHelper.showToast(SmartHomeConstant.toastFailure);
      return;
    }
    final Map<String, String> commands = <String, String>{};
    if (powerOff) {
      final String onCommandKey = modelV3.onCommand?.name ?? '';
      final String onCommandValue = modelV3.onCommand?.value ?? '';
      commands[onCommandKey] = onCommandValue;
    } else {
      final String offCommandKey = modelV3.offCommand?.name ?? '';
      final String offCommandValue = modelV3.offCommand?.value ?? '';
      commands[offCommandKey] = offCommandValue;
    }
    washCommand(device.basicInfo.deviceId, <Map<String, String>>[commands]);
  }

  @override
  Map<String, WashDeviceLargeDeviceCardFunctionSet>? get largeCardFuncMap {
    if (multiRollerDevice) {
      /// 拿到所有筒数据时，同时显示，有一个未拿到数据，则都不显示
      if (allRollerDataLoadFinish) {
        return <String, WashDeviceLargeDeviceCardFunctionSet>{
          '多筒': WashDeviceLargeDeviceCardFunctionSet(
              unitIndex: 0,
              name: '多筒',
              componentViewModelList: <ComponentBaseViewModel>[
                ...washRollerItemList,
              ]),
        };
      }

      /// 组装卡片多个筒默认状态
      final List<ComponentBaseViewModel> defaultComponentVmList =
          List<ComponentBaseViewModel>.filled(
              (childViewModelMap?.length ?? 0) + 1, _buildMultDefaultBtn());

      const String name = '默认多筒';
      return <String, WashDeviceLargeDeviceCardFunctionSet>{
        name: WashDeviceLargeDeviceCardFunctionSet(
            unitIndex: 0,
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
              ...defaultComponentVmList,
            ]),
      };
    }

    final Map<String, WashDeviceLargeDeviceCardFunctionSet>? map;

    if (isSingleDoubleWashNetDataNotPrepared) {
      const String name = '单筒|双筒默认';
      map = <String, WashDeviceLargeDeviceCardFunctionSet>{
        name: WashDeviceLargeDeviceCardFunctionSet(
            unitIndex: upUnitIndex,
            name: name,
            componentViewModelList: <ComponentBaseViewModel>[
              defaultProgramVm,
              defaultStartVm,
            ])
      };
    } else {
      map = zoneAndUserPreferNames(modelV3, programV3);
    }

    if (!manualSelectedFuncSet) {
      /// 默认选中运行中的筒
      String functionSetKey = '';

      final List<String> keys =
          (map ?? <String, WashDeviceLargeDeviceCardFunctionSet>{})
              .keys
              .toList();
      for (final String key in keys) {
        final WashDeviceLargeDeviceCardFunctionSet? functionSet = map?[key];
        if (functionSet != null) {
          if (functionSet.unitIndex == downUnitIndex && downUnitRunning) {
            functionSetKey = key;
            break;
          } else if (functionSet.unitIndex == upUnitIndex && upUnitRunning) {
            functionSetKey = key;
            break;
          }
        }
        break;
      }

      if (functionSetKey.isNotEmpty) {
        selectedFunctionSetKey = functionSetKey;
      }
    }

    return map;
  }

  // 构建单双筒默认状态
  ExpandWashStateViewModel get defaultProgramVm {
    return ExpandWashStateViewModel(
        name: '',
        label: '',
        value: '--',
        unit: '',
        enable: false,
        paddingLeft: 8);
  }

  // 构建单双筒默认按钮
  FixedSwitchIconTextViewModel get defaultStartVm {
    const String icon = 'assets/components/wash_start.webp';
    final FixedSwitchIconTextViewModel defaultStartVm =
        FixedSwitchIconTextViewModel(
            text: _startName,
            icon: icon,
            enable: false,
            isOn: false,
            clickCallback: (BuildContext? context) {
              checkDeviceState();
            });
    return defaultStartVm;
  }

  // 构建多筒默认按钮
  ExpandPopupTextTextViewModel _buildMultDefaultBtn() {
    final ExpandPopupTextTextViewModel itemViewModel =
        ExpandPopupTextTextViewModel(
            text: '--',
            desc: '--',
            enable: false,
            clickCallback: (BuildContext? context) {
              Network
                  .checkNetwork
                  .then((IsOnline onValue) {
                if (result == NetworkStatusfalse) {
                  ToastHelper.showToast(SmartHomeConstant.NETWORK_ERROR_TIP);
                  return;
                }
                ToastHelper.showToast(
                    SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
              });
            });
    return itemViewModel;
  }

  @override
  bool cardClickRedirect(BuildContext? context) {
    if (multiRollerDevice) {
      gioTrack(GioWash.gioWashMultiCardClick,
          <String, dynamic>{'value': _drumCount, 'cardType': _cardTypeName});
      if (cardClickPopupComponent != null) {
        showPopup(context, cardClickPopupComponent);
      } else {
        ToastHelper.showToast(SmartHomeConstant.toastDeviceOnlineNotReadyInfo);
      }
      return true;
    } else {
      return super.cardClickRedirect(context);
    }
  }

  String get _cardTypeName {
    String cardTypeName = '';
    if (deviceCardType == DeviceCardType.largeCard) {
      cardTypeName = '大卡片';
    } else if (deviceCardType == DeviceCardType.middleCard) {
      cardTypeName = '中卡片';
    } else if (deviceCardType == DeviceCardType.smallCard) {
      cardTypeName = '小卡片';
    }
    return cardTypeName;
  }

  String get _drumCount {
    final int drumCount = attachDeviceList?.length ?? 4;
    String value = '四筒';
    if (drumCount == 3) {
      value = '三筒';
    } else if (drumCount == 2) {
      value = '双筒双电源';
    }
    return value;
  }

  void washDeviceCheck(String currentSelectedKey) {
    final WashDeviceLargeDeviceCardFunctionSet? prevFunctionSet =
        largeCardFuncMap?[selectedFunctionSetKey];
    final WashDeviceLargeDeviceCardFunctionSet? curFunctionSet =
        largeCardFuncMap?[currentSelectedKey];

    if (prevFunctionSet?.name != curFunctionSet?.name) {
      if ((curFunctionSet?.unitIndex == upUnitIndex && upUnitRunning) ||
          (curFunctionSet?.unitIndex == downUnitIndex && downUnitRunning)) {
        ToastHelper.showToast(
          _runningToastWhenChangeFunctionSet(
              curFunctionSet!.unitIndex, modelV3.isDouble, modelV3.isRoller),
        );
      }
    }
  }

  /// 获取 运行中的文案
  String _runningToastWhenChangeFunctionSet(
      int unitIndex, bool isDouble, bool isRoller) {
    const String prompt = '正在运行中';
    if (!isDouble) {
      return prompt;
    }
    if (isRoller && unitIndex == 0) {
      return '$_upRoller$prompt';
    }
    if (isRoller && unitIndex == 1) {
      return '$_downRoller$prompt';
    }
    if (unitIndex == 0) {
      return '$_largePulsator$prompt';
    }
    return '$_smallPulsator$prompt';
  }

  bool get compEnable =>
      !(loading || onlineNotReady || deviceOfflineOrPowerOff);

  // 筒功能集
  Map<String, WashDeviceLargeDeviceCardFunctionSet>? zoneAndUserPreferNames(
      WashDeviceModelV3 model, WashDeviceProgramV3 program) {
    final Map<String, WashDeviceLargeDeviceCardFunctionSet> allFunctions =
        <String, WashDeviceLargeDeviceCardFunctionSet>{};

    final List<WashDeviceZoneProgram> zonesUp = program.zonesUp;
    final List<WashDeviceZoneProgram> zonesDown = program.zonesDown;
    if (zonesUp.isEmpty) {
      return null;
    }
    bool showZoneSelect = false;
    if (<WashDeviceZoneProgram>[...zonesUp, ...zonesDown].length > 1) {
      showZoneSelect = true;
    }

    /// 下筒
    for (final WashDeviceZoneProgram zoneProgram in zonesDown) {
      final bool showProgram = zoneDownShowProgram();

      // 功能分组key
      String zoneName = zoneProgram.zone.zoneName;
      if (model.isRoller) {
        if (zoneName == _downRoller) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _downRoller : '') + zoneName;
      } else {
        if (zoneName == _smallPulsator) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _smallPulsator : '') + zoneName;
      }

      final FixedPopupTextViewModel switchPopupComponentModel =
          FixedPopupTextViewModel(
              text: deviceOffline ? '--' : _displayZoneName(zoneName),
              enable: compEnable,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: '切换筒');
                checkDeviceState(checkOnlineNotReady: true).then((bool pass) {
                  if (pass) {
                    popUpZoneSelect(context);
                  }
                });
              });

      /// 按照上次保留的记录，设置被选中的程序
      WashDeviceUserPreferProgram? program = selectedProgram[zoneName];
      program ??= zoneProgram.programs[0];
      selectedProgram[zoneName] = program;

      selectedUnitIndexMap[zoneName] = downUnitIndex;

      final String washTime = program.programWashingTime < 0
          ? 'AUTO'
          : '${program.programWashingTime}';

      /// 筒程序
      final ExpandPopupWashProgramViewModel programComponentViewModel =
          ExpandPopupWashProgramViewModel(
              name: deviceOffline ? '--' : program.programName,
              value: deviceOffline ? '' : washTime,
              unit:
                  (deviceOffline || program.programWashingTime < 0) ? '' : min,
              enable: compEnable,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: '程序选择');
                checkDeviceState(checkOnlineNotReady: true).then((bool pass) {
                  if (pass) {
                    /// 程序被点击，弹出程序选择
                    showProgramSelect(program, zoneProgram, zoneName, context);
                  }
                });
              });

      /// 运行状态-下筒
      final WashDeviceStateModel downStateVm =
          washDeviceStateViewModelForUnitIndexWithoutUnit(downUnitIndex);
      final ExpandWashStateViewModel stateComponentViewModel = deviceOffline
          ? ExpandWashStateViewModel(
              name: '--',
              label: '',
              value: '',
              unit: '',
              enable: false,
              paddingLeft: showZoneSelect ? 0 : 8)
          : ExpandWashStateViewModel(
              name: downStateVm.cyclePhase,
              label: downStateVm.isTimeState ? '剩' : '',
              value: downStateVm.state,
              unit: downStateVm.isTimeState ? '分钟' : '',
              paddingLeft: showZoneSelect ? 0 : 8,
              enable: compEnable);

      /// 功能集合
      final WashDeviceLargeDeviceCardFunctionSet functionSet =
          WashDeviceLargeDeviceCardFunctionSet(
              unitIndex: downUnitIndex,
              name: zoneName,
              componentViewModelList: <ComponentBaseViewModel>[
            if (showZoneSelect) switchPopupComponentModel,
            if (showProgram)
              programComponentViewModel
            else
              stateComponentViewModel,
            ...washDeviceFuncViewModel(downUnitIndex, showProgram),
          ]);
      allFunctions[zoneName] = functionSet;
    }

    /// 上筒
    for (final WashDeviceZoneProgram zoneProgram in zonesUp) {
      final bool showProgram = zoneUpShowProgram();
      String zoneName = zoneProgram.zone.zoneName;
      if (model.isRoller) {
        if (zoneName == _upRoller) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _upRoller : '') + zoneName;
      } else {
        if (zoneName == _largePulsator) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _largePulsator : '') + zoneName;
      }

      final FixedPopupTextViewModel switchPopupComponentModel =
          FixedPopupTextViewModel(
              text: deviceOffline ? '--' : _displayZoneName(zoneName),
              // desc: '切换',
              enable: compEnable,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: '切换筒');
                checkDeviceState(checkOnlineNotReady: true).then((bool pass) {
                  if (pass) {
                    popUpZoneSelect(context);
                  }
                });
              });

      /// 按照上次保留的记录，设置被选中的程序
      WashDeviceUserPreferProgram? program = selectedProgram[zoneName];
      program ??= zoneProgram.programs[0];
      selectedProgram[zoneName] = program;

      selectedUnitIndexMap[zoneName] = upUnitIndex;

      final String washTime = program.programWashingTime < 0
          ? 'AUTO'
          : '${program.programWashingTime}';

      /// 筒程序
      final ExpandPopupWashProgramViewModel programComponentViewModel =
          ExpandPopupWashProgramViewModel(
              name: deviceOffline ? '--' : program.programName,
              value: deviceOffline ? '' : washTime,
              unit:
                  (deviceOffline || program.programWashingTime < 0) ? '' : min,
              enable: compEnable,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: '程序选择');
                checkDeviceState(checkOnlineNotReady: true).then((bool pass) {
                  if (pass) {
                    /// 程序被点击，弹出程序选择
                    showProgramSelect(program, zoneProgram, zoneName, context);
                  }
                });
              });

      /// 运行状态-上筒
      final WashDeviceStateModel upStateVm =
          washDeviceStateViewModelForUnitIndexWithoutUnit(upUnitIndex);
      final ExpandWashStateViewModel stateComponentViewModel = deviceOffline
          ? ExpandWashStateViewModel(
              name: '',
              label: '',
              value: '--',
              unit: '',
              enable: false,
              paddingLeft: showZoneSelect ? 0 : 8)
          : ExpandWashStateViewModel(
              name: upStateVm.cyclePhase,
              label: upStateVm.isTimeState ? '剩' : '',
              value: upStateVm.state,
              unit: upStateVm.isTimeState ? '分钟' : '',
              paddingLeft: showZoneSelect ? 0 : 8,
              enable: compEnable);

      /// 功能集合
      final WashDeviceLargeDeviceCardFunctionSet functionSet =
          WashDeviceLargeDeviceCardFunctionSet(
              unitIndex: upUnitIndex,
              name: zoneName,
              componentViewModelList: <ComponentBaseViewModel>[
            if (showZoneSelect) switchPopupComponentModel,
            if (showProgram)
              programComponentViewModel
            else
              stateComponentViewModel,
            ...washDeviceFuncViewModel(upUnitIndex, showProgram),
          ]);
      allFunctions[zoneName] = functionSet;
    }

    return allFunctions;
  }

  String _displayZoneName(String zoneName) {
    if (zoneName.contains(_largePulsator) && zoneName != _largePulsator) {
      return zoneName.replaceAll(_largePulsator, '$_largePulsator\n');
    }
    if (zoneName.contains(_smallPulsator) && zoneName != _smallPulsator) {
      return zoneName.replaceAll(_smallPulsator, '$_smallPulsator\n');
    }
    if (zoneName.contains(_upRoller) && zoneName != _upRoller) {
      return zoneName.replaceAll(_upRoller, '$_upRoller\n');
    }
    if (zoneName.contains(_downRoller) && zoneName != _downRoller) {
      return zoneName.replaceAll(_downRoller, '$_downRoller\n');
    }
    return zoneName;
  }

  /// 筒/分区选择
  void popUpZoneSelect(BuildContext? context) {
    final WashDeviceModelV3 model = modelV3;
    final WashDeviceProgramV3 program = programV3;

    final List<WashDeviceZoneProgram> zonesUp = program.zonesUp;
    final List<WashDeviceZoneProgram> zonesDown = program.zonesDown;

    final List<String> zoneNameList = <String>[];

    /// 下筒
    for (final WashDeviceZoneProgram zoneProgram in zonesDown) {
      // 功能分组key
      String zoneName = zoneProgram.zone.zoneName;
      if (model.isRoller) {
        if (zoneName == _downRoller) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _downRoller : '') + zoneName;
      } else {
        if (zoneName == _smallPulsator) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _smallPulsator : '') + zoneName;
      }
      zoneNameList.add(zoneName);
    }

    /// 上筒
    for (final WashDeviceZoneProgram zoneProgram in zonesUp) {
      String zoneName = zoneProgram.zone.zoneName;
      if (model.isRoller) {
        if (zoneName == _upRoller) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _upRoller : '') + zoneName;
      } else {
        if (zoneName == _largePulsator) {
          zoneName = '';
        }
        zoneName = (model.isDouble ? _largePulsator : '') + zoneName;
      }
      zoneNameList.add(zoneName);
    }

    final List<ComponentBaseViewModel> componentList =
        <ComponentBaseViewModel>[];
    for (final String zoneName in zoneNameList) {
      componentList.add(WashBucketSwitchComponentViewModel(
          title: zoneName,
          showBorder: true,
          selected: zoneName == selectedFunctionSetKey,
          bucketSelect: (BuildContext? context) {
            washDeviceCheck(zoneName);
            selectedFunctionSetKey = zoneName;
            hidePopup(context);
          }));
    }
    final GridViewModel viewModel = GridViewModel(componentList: componentList);

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '切换',
            deviceId: deviceId,
            attributeKey: 'zone',
            componentList: <ComponentBaseViewModel>[viewModel]);
    manualSelectedFuncSet = true;
    showPopup(context, popupComponentViewModel);
  }

  /// 弹出程序选择列表
  void showProgramSelect(
      WashDeviceUserPreferProgram? currentPreferProgram,
      WashDeviceZoneProgram zoneProgram,
      String zoneName,
      BuildContext? context) {
    final List<ComponentBaseViewModel> componentList =
        <ComponentBaseViewModel>[];

    for (final WashDeviceUserPreferProgram element in zoneProgram.programs) {
      final int washTime = element.programWashingTime;

      componentList.add(WashProgramSelectComponentViewModel(
          title: element.programName,
          showBorder: true,
          desc: washTime <= 0 ? 'AUTO' : '${element.programWashingTime}$min',
          selected: element.programName == currentPreferProgram?.programName,
          programSelect: (BuildContext? context) {
            selectedProgram[zoneName] = element;
            smartHomeStore
                .dispatch(UpdateWashProgram(device.basicInfo.deviceId));
            hidePopup(context);
          }));
    }
    componentList.add(
        WashProgramMoreSelectViewModel(programSelect: (BuildContext? context) {
      hidePopup(context);
      cardClick(context);
    }));
    final GridViewModel viewModel =
        GridViewModel(componentList: componentList, mainAxisExtent: 65);

    final PopupComponentViewModel popupComponentViewModel =
        PopupComponentViewModel(
            title: '设置模式',
            deviceId: deviceId,
            attributeKey: 'program',
            componentList: <ComponentBaseViewModel>[viewModel]);
    showPopup(context, popupComponentViewModel);
  }

  /// 多筒洗衣机状态获取
  MultiRollerDeviceStateModel? multiRollerStateModelForUnitIndex(
      int unitIndex) {
    MultiRollerRunningState runningState = MultiRollerRunningState.running;
    String state = '';
    String timeDesc = '';

    if (lowPower) {
      return MultiRollerDeviceStateModel(
          runningState: MultiRollerRunningState.offline,
          state: '已关机',
          timeDesc: '');
    }

    if (deviceOffline) {
      return MultiRollerDeviceStateModel(
          runningState: MultiRollerRunningState.offline,
          state: deviceRole3 ? '--' : '离线',
          timeDesc: '');
    }

    if (onlineNotReady) {
      return MultiRollerDeviceStateModel(
          runningState: MultiRollerRunningState.powerOff,
          state: '--',
          timeDesc: '');
    }

    if (powerOff) {
      return MultiRollerDeviceStateModel(
          runningState: MultiRollerRunningState.powerOff,
          state: '已关机',
          timeDesc: '');
    }

    /// 理论上只有上筒
    if (unitIndex == upUnitIndex) {
      state = modelV3.cyclePhaseUP35.valueDesc;
      const String cyclePhaseShakeOut = '19';
      const String laundryCycleRefreshAir = '35';
      if (modelV3.supportFreshAir &&
          modelV3.cyclePhaseUP35.value == cyclePhaseShakeOut &&
          modelV3.laundryCycleUP34.value == laundryCycleRefreshAir) {
        state = '筒干燥中';
      } else if (modelV3.upStandBy) {
        runningState = MultiRollerRunningState.standBy;
        state = '待机';
      } else if (modelV3.upEnd) {
        runningState = MultiRollerRunningState.washEnd;
        state = '已结束';
      } else if (modelV3.upCanTakeout) {
        state = '可取衣';
      } else if (modelV3.upSuspend) {
        state = '暂停中';
      } else if (modelV3.upPreDryAUTO) {
        state = 'AUTO';
      } else if (modelV3.upAuto) {
        state = 'AUTO';
      } else if (modelV3.upAutoJudge) {
        state = '智能判干';
      } else if (modelV3.upResn && modelV3.upRemainingResnTime > 0) {
        timeDesc = '剩${modelV3.upRemainingResnTime}$min';
      } else if (modelV3.upRemainingTime > 0) {
        timeDesc = '剩${modelV3.upRemainingTime}$min';
      }
    } else {
      state = modelV3.cyclePhaseDN35.valueDesc;
      const String cyclePhaseShakeOut = '19';
      const String laundryCycleRefreshAir = '35';
      if (modelV3.supportFreshAir &&
          modelV3.cyclePhaseDN35.value == cyclePhaseShakeOut &&
          modelV3.laundryCycleDN34.value == laundryCycleRefreshAir) {
        state = '筒干燥中';
      } else if (modelV3.downStandBy) {
        runningState = MultiRollerRunningState.standBy;
        state = '待机';
      } else if (modelV3.downEnd) {
        runningState = MultiRollerRunningState.washEnd;
        state = '已结束';
      } else if (modelV3.downCanTakeout) {
        state = '可取衣';
      } else if (modelV3.downSuspend) {
        state = '暂停中';
      } else if (modelV3.downAuto) {
        state = 'AUTO';
      } else if (modelV3.downResn && modelV3.downRemainingResnTime > 0) {
        timeDesc = '剩${modelV3.downRemainingResnTime}$min';
      } else if (modelV3.downRemainingTime > 0) {
        timeDesc = '剩${modelV3.downRemainingTime}$min';
      }
    }

    if (state.isEmpty) {
      return null;
    }

    return MultiRollerDeviceStateModel(
        runningState: runningState, state: state, timeDesc: timeDesc);
  }

  // 洗衣机卡片状态数据-待机、运行中、空白； 适配大中小卡片
  WashDeviceStateModel get washDeviceCommonStateViewModel {
    String state = '';
    bool isUpStandby = false;
    bool isUpRunning = false;
    bool isDnStandby = false;
    bool isDnRunning = false;
    bool runningState = false;

    if (modelV3.upStandBy || modelV3.upEnd) {
      isUpStandby = true;
    } else if (modelV3.upCanTakeout) {
      // isUpRunning = true;
    } else if (modelV3.upSuspend) {
    } else if (modelV3.upPreDryAUTO) {
      isUpRunning = true;
    } else if (modelV3.upAuto) {
      isUpRunning = true;
    } else if (modelV3.upAutoJudge) {
      isUpRunning = true;
    } else if (modelV3.upResn && modelV3.upRemainingResnTime > 0) {
      isUpRunning = true;
    } else if (modelV3.upRemainingTime > 0) {
      isUpRunning = true;
    }

    if (modelV3.downStandBy || modelV3.downEnd) {
      isDnStandby = true;
    } else if (modelV3.downCanTakeout) {
    } else if (modelV3.downSuspend) {
    } else if (modelV3.downAuto) {
      isDnRunning = true;
    } else if (modelV3.downResn && modelV3.downRemainingResnTime > 0) {
      isDnRunning = true;
    } else if (modelV3.downRemainingTime > 0) {
      isDnRunning = true;
    }

    if (powerOff) {
      state = '关';
    } else if (modelV3.isDouble) {
      if (isUpStandby && isDnStandby) {
        state = '待机';
      } else if (isUpRunning || isDnRunning) {
        state = '运行中';
        runningState = true;
      } else {
        state = '';
      }
    } else {
      if (isUpStandby || isDnStandby) {
        state = '待机';
      } else if (isUpRunning || isDnRunning) {
        state = '运行中';
        runningState = true;
      } else {
        state = '';
      }
    }

    return WashDeviceStateModel(state, '', runningState: runningState);
  }

  /// 筒运行状态--原始数据(运行阶段、设备状态) 不带单位
  WashDeviceStateModel washDeviceStateViewModelForUnitIndexWithoutUnit(
      int unitIndex) {
    String state = ''; // 设备状态
    String cyclePhase = ''; // 运行阶段
    bool isTimeState = false; // 是否是时间状态，外部用于判断是否带前缀和单位

    if (unitIndex == upUnitIndex) {
      cyclePhase = modelV3.cyclePhaseUP35.valueDesc;
      const String cyclePhaseShakeOut = '19';
      const String laundryCycleRefreshAir = '35';
      if (modelV3.supportFreshAir &&
          modelV3.cyclePhaseUP35.value == cyclePhaseShakeOut &&
          modelV3.laundryCycleUP34.value == laundryCycleRefreshAir) {
        cyclePhase = '筒干燥中';
      }
      if (modelV3.upStandBy || modelV3.upEnd) {
        state = '待机';
        cyclePhase = '';
      } else if (modelV3.upCanTakeout) {
        state = '可取衣';
      } else if (modelV3.upSuspend) {
        state = '暂停中';
      } else if (modelV3.upPreDryAUTO) {
        state = 'AUTO';
      } else if (modelV3.upAuto) {
        state = 'AUTO';
      } else if (modelV3.upAutoJudge) {
        state = '智能判干';
      } else if (modelV3.upResn && modelV3.upRemainingResnTime > 0) {
        state = '${modelV3.upRemainingResnTime}';
        isTimeState = true;
      } else if (modelV3.upRemainingTime > 0) {
        state = '${modelV3.upRemainingTime}';
        isTimeState = true;
      }
    } else {
      cyclePhase = modelV3.cyclePhaseDN35.valueDesc;
      const String cyclePhaseShakeOut = '19';
      const String laundryCycleRefreshAir = '35';
      if (modelV3.supportFreshAir &&
          modelV3.cyclePhaseDN35.value == cyclePhaseShakeOut &&
          modelV3.laundryCycleDN34.value == laundryCycleRefreshAir) {
        cyclePhase = '筒干燥中';
      }
      if (modelV3.downStandBy || modelV3.downEnd) {
        state = '待机';
        cyclePhase = '';
      } else if (modelV3.downCanTakeout) {
        state = '可取衣';
      } else if (modelV3.downSuspend) {
        state = '暂停中';
      } else if (modelV3.downAuto) {
        state = 'AUTO';
      } else if (modelV3.downResn && modelV3.downRemainingResnTime > 0) {
        state = '${modelV3.downRemainingResnTime}';
        isTimeState = true;
      } else if (modelV3.downRemainingTime > 0) {
        state = '${modelV3.downRemainingTime}';
        isTimeState = true;
      }
    }

    return WashDeviceStateModel(state, cyclePhase, isTimeState: isTimeState);
  }

  // 启动、暂停、停止按钮
  List<ComponentBaseViewModel> washDeviceFuncViewModel(
      int unitIndex, bool showProgram) {
    final List<ComponentBaseViewModel> componentList =
        <ComponentBaseViewModel>[];

    WashDeviceAttribute startStatus = modelV3.startStatusUP;
    bool supportCancel = modelV3.supportCancelUP;
    if (unitIndex == downUnitIndex) {
      startStatus = modelV3.startStatusDN;
      supportCancel = modelV3.supportCancelDN;
    }

    if (showProgram) {
      // 未启动
      const String icon = 'assets/components/wash_start.webp';
      final FixedSwitchIconTextViewModel viewModel =
          FixedSwitchIconTextViewModel(
              text: _startName,
              icon: icon,
              enable: compEnable && startStatus.writable,
              isOn: false,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: _startName);
                final Map<String, String> commands = <String, String>{};
                executeCommandsWithOperateType(
                    showProgram, OperateType.start, commands, unitIndex, true);
              });
      componentList.add(viewModel);
    } else {
      /// 运行中
      /// 1 从attribute中获取的属性无writable，因此所有的都为true
      /// 2 与产业沟通，命令下发不处理writable,抖散防皱状态下，启动/暂停命令不让点击
      bool writable = !_shakeValueSet.contains(modelV3.cyclePhaseUP35.value);
      if (unitIndex == downUnitIndex) {
        writable = !_shakeValueSet.contains(modelV3.cyclePhaseDN35.value);
      }
      // 启动开启状态值
      const String startStatusOn = '1';
      // 启动关闭状态值
      const String startStatusOff = '0';

      const String startIcon = 'assets/components/wash_start.webp';
      const String suspendIcon = 'assets/components/wash_suspend.webp';
      const String stopIcon = 'assets/components/wash_stop.webp';
      if (startStatus.value == startStatusOn) {
        // 运行中
        final FixedSwitchIconTextViewModel viewModel =
            FixedSwitchIconTextViewModel(
                text: _suspendName,
                icon: suspendIcon,
                enable: compEnable && writable,
                isOn: false,
                clickCallback: (BuildContext? context) {
                  gio(cloudProgramName: _suspendName);

                  /// 下发暂停命令
                  executeCommandsWithOperateType(
                      showProgram,
                      OperateType.suspend,
                      suspendCommands(unitIndex),
                      unitIndex,
                      startStatus.writable);
                });
        componentList.add(viewModel);
      }
      if (startStatus.value == startStatusOff) {
        // 暂停中
        final FixedSwitchIconTextViewModel viewModel =
            FixedSwitchIconTextViewModel(
                icon: startIcon,
                text: _startName,
                enable: compEnable && writable,
                isOn: false,
                clickCallback: (BuildContext? context) {
                  gio(cloudProgramName: _startName);

                  /// 下发启动命令
                  executeCommandsWithOperateType(
                      showProgram,
                      OperateType.start,
                      resumeCommands(unitIndex),
                      unitIndex,
                      startStatus.writable);
                });
        componentList.add(viewModel);
      }

      //  停止按钮
      final FixedSwitchIconTextViewModel viewModel =
          FixedSwitchIconTextViewModel(
              text: _stopName,
              icon: stopIcon,
              enable: compEnable && supportCancel,
              isOn: false,
              clickCallback: (BuildContext? context) {
                gio(cloudProgramName: _stopName);

                /// 下发停止命令
                executeCommandsWithOperateType(showProgram, OperateType.stop,
                    stopCommands(unitIndex), unitIndex, true);
              });
      componentList.add(viewModel);
    }

    return componentList;
  }

  // 下筒运行状态
  bool zoneDownShowProgram() {
    bool showProgram = modelV3.cyclePhaseDN35.value.isEmpty ||
        modelV3.downStandBy ||
        modelV3.downEnd;
    if (modelV3.washDeviceCategory ==
        WashDeviceCategory.washerAndDryerMiddleClass_5) {
      const String powerStatusDN300Off = '0';
      const String remoteCtrValidDN302Off = '0';
      showProgram = modelV3.powerStatusDN300.value == powerStatusDN300Off ||
          modelV3.remoteCtrValidDN302.value == remoteCtrValidDN302Off ||
          showProgram;
    }
    downUnitRunning = !showProgram;
    return showProgram;
  }

  // 上筒运行状态
  bool zoneUpShowProgram() {
    bool showProgram = modelV3.cyclePhaseUP35.value.isEmpty ||
        modelV3.upStandBy ||
        modelV3.upEnd;
    if (modelV3.washDeviceCategory ==
        WashDeviceCategory.washerAndDryerMiddleClass_5) {
      const String powerStatusUP300Off = '0';
      const String remoteCtrValidUP302Off = '0';
      showProgram = modelV3.powerStatusUP300.value == powerStatusUP300Off ||
          modelV3.remoteCtrValidUP302.value == remoteCtrValidUP302Off ||
          showProgram;
    }
    upUnitRunning = !showProgram;
    return showProgram;
  }

  /// 程序处于结束状态，而非待机状态
  bool isProgramEnded(int unitIndex) {
    if (unitIndex == upUnitIndex) {
      return modelV3.upEnd;
    }
    return modelV3.downEnd;
  }

  bool upUnitPowerOn() {
    return true;
  }

  bool downUnitPowerOn() {
    return true;
  }

  bool upUnitRemoteControlOn() {
    return true;
  }

  bool downUnitRemoteControlOn() {
    return true;
  }

  /// 获取远程控制校验授权提示, 授权校验都通过，返回空字符串
  String remoteControlAuthorizationCheckMessage(int unitIndex) {
    const String remoteCtrValidStatus261Off = '0';
    if (modelV3.supportRemoteControl &&
        modelV3.remoteCtrValidStatus261.value == remoteCtrValidStatus261Off) {
      return SmartHomeConstant.toastWashAuthorizeAtMachine;
    }
    const String longTimeAuthorizationStatus375Off = '0';
    const String remoteCtrValid302Off = '0';

    WashDeviceAttribute longTimeAuthorizationStatus375 =
        modelV3.longTimeAuthorizationStatusUP375;
    WashDeviceAttribute remoteCtrValid302 = modelV3.remoteCtrValidUP302;
    if (unitIndex == downUnitIndex) {
      longTimeAuthorizationStatus375 = modelV3.longTimeAuthorizationStatusDN375;
      remoteCtrValid302 = modelV3.remoteCtrValidDN302;
    }
    // 未长期授权 且 未 临时授权
    if (modelV3.newNationalStandard &&
        longTimeAuthorizationStatus375.value ==
            longTimeAuthorizationStatus375Off &&
        remoteCtrValid302.value == remoteCtrValid302Off) {
      return SmartHomeConstant.toastWashAuthorizeAtMachine;
    }
    return '';
  }

  void executeCommandsWithOperateType(bool showProgram, OperateType operateType,
      Map<String, String>? commands, int unitIndex, bool writable) {
    checkDeviceState(
      writable: writable && !alarm,
      checkChildLock: operateType != OperateType.stop,
      checkOnlineNotReady: true,
    ).then((bool pass) {
      if (pass) {
        /// 上筒未通电
        if (unitIndex == upUnitIndex && !upUnitPowerOn()) {
          ToastHelper.showToast(SmartHomeConstant.toastPowerOff);
          return;
        }

        /// 下筒未通电
        if (unitIndex == downUnitIndex && !downUnitPowerOn()) {
          ToastHelper.showToast(SmartHomeConstant.toastPowerOff);
          return;
        }

        /// 上筒休眠中
        if (unitIndex == upUnitIndex && !upUnitRemoteControlOn()) {
          ToastHelper.showToast(SmartHomeConstant.toastWakeUp);
          return;
        }

        /// 下筒未通电
        if (unitIndex == downUnitIndex && !downUnitRemoteControlOn()) {
          ToastHelper.showToast(SmartHomeConstant.toastWakeUp);
          return;
        }

        /// 远程授权
        final String authorize =
            remoteControlAuthorizationCheckMessage(unitIndex);
        if (authorize.isNotEmpty) {
          ToastHelper.showToast(authorize);
          return;
        }
        // if (!writable) {
        //   ToastHelper.showToast(SmartHomeConstant.toastFailureAndTryAgain);
        //   return;
        // }
        if (!showProgram) {
          bool supportCancel = modelV3.supportCancelUP;
          if (unitIndex == downUnitIndex) {
            supportCancel = modelV3.supportCancelDN;
          }
          if (operateType == OperateType.stop) {
            if (commands == null || commands.isEmpty || !supportCancel) {
              ToastHelper.showToast(
                  SmartHomeConstant.toastUnsupportedStopProgramInApp);
              return;
            }
            // 需要先发暂停再发回到待机的型号先暂停，然后停止

            bool supportCard = modelV3.supportCardUP;
            bool suspend = modelV3.upSuspend;
            bool supportStopReserve = modelV3.supportStopReserveUP;
            bool resn = modelV3.upResn;
            final Map<String, String>? suspendCommand =
                suspendCommands(unitIndex);
            if (unitIndex == downUnitIndex) {
              supportCard = modelV3.supportCardDN;
              suspend = modelV3.downSuspend;
              supportStopReserve = modelV3.supportStopReserveDN;
              resn = modelV3.downResn;
            }
            // 需要先发暂停再发回到待机的型号，
            // 根据筒上没有supportCard标签，且设备运行中（runningMode==1），且（筒上有supportStopReserve标签 或 设备非预约中阶段
            if (!supportCard && !suspend && (supportStopReserve || !resn)) {
              if (suspendCommand == null || suspendCommand.isEmpty) {
                ToastHelper.showToast(SmartHomeConstant.toastFailure);
                return;
              }
              executeCommand(<Map<String, String>>[suspendCommand],
                  onSuccess: () {
                executeCommand(<Map<String, String>>[commands]);
              });
            } else {
              executeCommand(<Map<String, String>>[commands]);
            }
          } else {
            if (commands == null || commands.isEmpty) {
              ToastHelper.showToast(SmartHomeConstant.toastFailure);
              return;
            }
            executeCommand(<Map<String, String>>[commands]);
          }
        } else {
          /// 启动程序
          if (operateType == OperateType.start) {
            final WashDeviceUserPreferProgram? userPreferProgram =
                selectedProgram[selectedFunctionSetKey];
            final List<Map<String, String>> commands =
                programCommands(userPreferProgram);
            final String? groupName =
                programCommandsGroupName(userPreferProgram);
            if (commands.isEmpty) {
              ToastHelper.showToast(SmartHomeConstant.toastSelectProgram);
              return;
            }

            ///  程序运行结束
            if (isProgramEnded(unitIndex)) {
              /// 先让设备回到待机状态
              final Map<String, String>? stopCommand = stopCommands(unitIndex);
              if (stopCommand != null && stopCommand.isNotEmpty) {
                executeCommand(<Map<String, String>>[stopCommand],
                    onSuccess: () {
                  executeCommand(commands, groupName: groupName);
                });
              }
            } else {
              executeCommand(commands, groupName: groupName);
            }
          }
        }
      }
    });
  }

  /// 启动命令
  Map<String, String>? resumeCommands(int unitIndex) {
    if (unitIndex == upUnitIndex) {
      final WashDeviceOperateCommand? command = modelV3.startCommandUP;
      if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
        return <String, String>{command.name: command.value};
      }
      return null;
    }
    final WashDeviceOperateCommand? command = modelV3.startCommandDN;
    if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
      return <String, String>{command.name: command.value};
    }
    return null;
  }

  /// 暂停命令
  Map<String, String>? suspendCommands(int unitIndex) {
    if (unitIndex == upUnitIndex) {
      final WashDeviceOperateCommand? command = modelV3.suspendCommandUP;
      if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
        return <String, String>{command.name: command.value};
      }
      return null;
    }
    final WashDeviceOperateCommand? command = modelV3.suspendCommandDN;
    if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
      return <String, String>{command.name: command.value};
    }
    return null;
  }

  /// 停止命令
  Map<String, String>? stopCommands(int unitIndex) {
    if (unitIndex == upUnitIndex) {
      final WashDeviceOperateCommand? command = modelV3.returnStandbyCommandUP;
      if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
        return <String, String>{command.name: command.value};
      }
      return null;
    }
    final WashDeviceOperateCommand? command = modelV3.returnStandbyCommandDN;
    if (modelV3.returnStandbyCommandDN is WashDeviceOperateCommand) {
      if (command is WashDeviceOperateCommand && command.name.isNotEmpty) {
        return <String, String>{command.name: command.value};
      }
    }
    return null;
  }

  String? programCommandsGroupName(
      WashDeviceUserPreferProgram? userPreferProgram) {
    if (userPreferProgram is! WashDeviceUserPreferProgram) {
      return null;
    }
    return userPreferProgram.groupName;
  }

  List<Map<String, String>> programCommands(
      WashDeviceUserPreferProgram? userPreferProgram) {
    if (userPreferProgram is! WashDeviceUserPreferProgram) {
      return <Map<String, String>>[];
    }
    final List<Map<String, String>> commands = <Map<String, String>>[];
    userPreferProgram.commands.forEach((WashDeviceOperateCommand element) {
      commands.add(<String, String>{element.name: element.value});
    });
    return commands;
  }

  void executeCommand(List<Map<String, String>> commands,
      {String? groupName, void Function()? onSuccess}) {
    washCommand(device.basicInfo.deviceId, commands,
        groupName: groupName,
        onSuccess: onSuccess, onErrorCallback: (String msg) {
      ToastHelper.showToast(msg);
    });
  }

  void washCommand(String deviceId, List<Map<String, String>> cmdMaps,
      {String? groupName,
      void Function()? onSuccess,
      void Function(String)? onErrorCallback}) {
    final List<Command> commands = <Command>[];
    cmdMaps.forEach((Map<String, String> cmdMap) {
      cmdMap.forEach((String key, String value) {
        if (value.isNotEmpty) {
          commands.add(
              Command.fromMap(<String, String>{'name': key, 'value': value}));
        }
      });
    });
    DevLogger.info(
        tag: 'washCommand',
        msg:
            'deviceId: $deviceId, groupName: $groupName,commandsSize: ${commands.length}, commands: $commands');
    UpDevicePlugin.executeOperation(deviceId, commands, groupName: groupName)
        .then((_) {
      onSuccess?.call();
      DevLogger.info(
          tag: 'washCommand',
          msg:
              'deviceId: $deviceId, groupName: $groupName, commands: $commands');
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: 'washCommand err',
          msg:
              'deviceId: $deviceId, groupName: $groupName, err: $err, commands: $commands');
      if (onErrorCallback != null) {
        onErrorCallback(SmartHomeConstant.toastFailure);
      }
    });
  }
}

class WashDeviceStateModel {
  String state = '';
  String cyclePhase = '';
  bool isTimeState = false;
  bool runningState = false;

  WashDeviceStateModel(this.state, this.cyclePhase,
      {this.isTimeState = false, this.runningState = false});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WashDeviceStateModel &&
          runtimeType == other.runtimeType &&
          isTimeState == other.isTimeState &&
          state == other.state &&
          cyclePhase == other.cyclePhase &&
          runningState == other.runningState;

  @override
  int get hashCode =>
      isTimeState.hashCode ^
      state.hashCode ^
      cyclePhase.hashCode ^
      runningState.hashCode;
}

enum MultiRollerRunningState {
  standBy,
  washEnd,
  running,
  powerOff,
  offline,
}

class MultiRollerDeviceStateModel {
  MultiRollerRunningState runningState;
  String state;
  String timeDesc;

  MultiRollerDeviceStateModel(
      {required this.runningState,
      required this.state,
      required this.timeDesc});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MultiRollerDeviceStateModel &&
          runtimeType == other.runtimeType &&
          runningState == other.runningState &&
          state == other.state &&
          timeDesc == other.timeDesc;

  @override
  int get hashCode =>
      runningState.hashCode ^ state.hashCode ^ timeDesc.hashCode;

  @override
  String toString() {
    return 'MultiRollerDeviceStateModel{runningState: $runningState, state: $state, timeDesc: $timeDesc}';
  }
}
