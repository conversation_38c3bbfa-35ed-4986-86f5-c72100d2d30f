name: flutter_common_ui
description: A new Flutter package project.
version: 0.0.1
author: <EMAIL>
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter
  dio: 5.3.2
  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"
  provider: 6.0.5
  tuple: 2.0.2
  crypto: 3.0.1
  json_annotation: 4.8.1
  flutter_screenutil: 5.0.0+2
  extended_image: 8.2.0
  url_launcher: 6.1.10
  visibility_detector: 0.4.0+2
  card_swiper: 2.0.4
  watcher: ^1.1.0
  gradient_borders: 1.0.1
  redux: 5.0.0
  flutter_redux: 0.8.2

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.0.4"
    # version: "0.2.0+1b2022031601"

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.2"
    # version: "1.0.3+1b2022042901"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.1"
    # version: "0.2.2+1b2022040101"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.2"
    # version: "1.0.2+1b2022031701"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.0.12"
    # version: "0.2.0+1b2022031601"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=0.0.12"
    # version: "2.0.0+1b2022051301"

  share:
    hosted:
      name: share
      url: http://**************:8083
    version: ">=0.0.11"
    # version: "1.1.3+1b2022042901"

  location:
    hosted:
      name: location
      url: http://**************:8083
    version: ">=0.1.2"
    # version: "2.1.0+1b2022042801"

  syn_dotted_border:
    hosted:
      name: syn_dotted_border
      url: http://**************:8083
    version: 3.4.0+2024011901

  umeng:
    hosted:
      name: umeng
      url: http://**************:8083
    version: ">=0.1.7"

dev_dependencies:
  build_runner: ^2.4.6 # 用于自动生成代码
  flutter_test:
    sdk: flutter
  json_serializable: ">=4.0.0"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/waterfall/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
