import 'package:flutter/material.dart';

import '../../basic_color/function_colors/orange_colors.dart';
import '../color_mode.dart';

class ItemRemindColors {
  // 私有构造函数
  const ItemRemindColors._();

  static const ItemRemindColors instance = ItemRemindColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: OrangeColors.orange6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: OrangeColors.orange4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: OrangeColors.orange3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: OrangeColors.orange1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
