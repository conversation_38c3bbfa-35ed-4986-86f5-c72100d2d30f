import 'package:flutter/material.dart';

import '../../basic_color/function_colors/green_colors.dart';
import '../color_mode.dart';

class ItemSuccessColors {
  // 私有构造函数
  const ItemSuccessColors._();

  static const ItemSuccessColors instance = ItemSuccessColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: GreenColors.green6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: GreenColors.green4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: GreenColors.green3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: GreenColors.green1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
