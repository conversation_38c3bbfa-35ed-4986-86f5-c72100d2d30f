import 'package:flutter/material.dart';

import '../../basic_color/neutral_colors/grey_colors.dart';
import '../../basic_color/neutral_colors/white_colors.dart';
import '../color_mode.dart';
import 'item_cyan_colors.dart';
import 'item_information_colors.dart';
import 'item_noble_colors.dart';
import 'item_remind_colors.dart';
import 'item_success_colors.dart';
import 'item_warn_colors.dart';

class ItemColors {
  // 私有构造函数
  const ItemColors._();

  static const ItemColors instance = ItemColors._();

  /// Primary 主要
  static const ColorMode _primary = ColorMode(
    light: GreyColors.grey14,
  );

  /// PriWeaken 主-弱化
  static const ColorMode _priWeaken = ColorMode(
    light: GreyColors.grey12,
  );

  /// Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: GreyColors.grey9,
  );

  /// SecWeaken 次-弱化
  static const ColorMode _secWeaken = ColorMode(
    light: GreyColors.grey7,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: GreyColors.grey5,
  );

  /// TerWeaken 三-弱化
  static const ColorMode _terWeaken = ColorMode(
    light: GreyColors.grey2,
  );

  /// Invert 反色
  static const ColorMode _invert = ColorMode(
    light: WhiteColors.white100,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get priWeaken => _priWeaken.value;
  Color get secondary => _secondary.value;
  Color get secWeaken => _secWeaken.value;
  Color get tertiary => _tertiary.value;
  Color get terWeaken => _terWeaken.value;
  Color get invert => _invert.value;
  ItemInformationColors get information => ItemInformationColors.instance;
  ItemWarnColors get warn => ItemWarnColors.instance;
  ItemRemindColors get remind => ItemRemindColors.instance;
  ItemSuccessColors get success => ItemSuccessColors.instance;
  ItemCyanColors get cyan => ItemCyanColors.instance;
  ItemNobleColors get noble => ItemNobleColors.instance;
}
