import 'package:flutter/material.dart';

import '../../basic_color/function_colors/purple_colors.dart';
import '../color_mode.dart';

class ItemNobleColors {
  // 私有构造函数
  const ItemNobleColors._();

  static const ItemNobleColors instance = ItemNobleColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: PurpleColors.purple6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: PurpleColors.purple4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: PurpleColors.purple3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: PurpleColors.purple1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
