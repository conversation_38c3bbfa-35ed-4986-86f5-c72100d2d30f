import 'package:flutter/material.dart';

import '../../basic_color/function_colors/red_colors.dart';
import '../color_mode.dart';

class ItemWarnColors {
  // 私有构造函数
  const ItemWarnColors._();

  static const ItemWarnColors instance = ItemWarnColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: RedColors.red6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: RedColors.red4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: RedColors.red3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: RedColors.red1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
