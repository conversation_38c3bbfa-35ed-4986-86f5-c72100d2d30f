import 'package:flutter/material.dart';

import '../../basic_color/function_colors/cyan_colors.dart';
import '../color_mode.dart';

class ItemCyanColors {
  // 私有构造函数
  const ItemCyanColors._();

  static const ItemCyanColors instance = ItemCyanColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: CyanColors.cyan6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: CyanColors.cyan4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: CyanColors.cyan3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: CyanColors.cyan1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
