// Information
import 'package:flutter/material.dart';

import '../../basic_color/brand_colors/intelligent_colors.dart';
import '../color_mode.dart';

class ItemInformationColors {
  // 私有构造函数
  const ItemInformationColors._();

  static const ItemInformationColors instance = ItemInformationColors._();

  // Primary 主要
  static const ColorMode _primary = ColorMode(
    light: IntelligentColors.intelligent6,
  );

  // Secondary 次要
  static const ColorMode _secondary = ColorMode(
    light: IntelligentColors.intelligent4,
  );

  /// Tertiary 三级
  static const ColorMode _tertiary = ColorMode(
    light: IntelligentColors.intelligent3,
  );

  /// Substrate 衬底
  static const ColorMode _substrate = ColorMode(
    light: IntelligentColors.intelligent1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
  Color get tertiary => _tertiary.value;
  Color get substrate => _substrate.value;
}
