import 'package:flutter/material.dart';

import '../../basic_color/neutral_colors/grey_colors.dart';
import '../../basic_color/neutral_colors/white_colors.dart';
import '../color_mode.dart';

class BackgroundColors {
  // 私有构造函数
  const BackgroundColors._();

  static const BackgroundColors instance = BackgroundColors._();

  /// Primary 一级
  static const ColorMode _primary = ColorMode(
    light: WhiteColors.white100,
  );

  /// Secondary 二级
  static const ColorMode _secondary = ColorMode(
    light: GreyColors.grey1,
  );

  // 对外暴露的属性
  Color get primary => _primary.value;
  Color get secondary => _secondary.value;
}
