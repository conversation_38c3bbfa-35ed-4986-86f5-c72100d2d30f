import 'package:flutter/material.dart';

import '../../basic_color/neutral_colors/black_colors.dart';
import '../../basic_color/neutral_colors/grey_colors.dart';
import '../../basic_color/neutral_colors/white_colors.dart';
import '../color_mode.dart';

class ContainerColors {
  // 私有构造函数
  const ContainerColors._();

  static const ContainerColors instance = ContainerColors._();

  /// Card 一级卡片
  static const ColorMode _card = ColorMode(
    light: WhiteColors.white100,
  );

  /// CardSec 二级卡片
  static const ColorMode _cardSec = ColorMode(
    light: GreyColors.grey1,
  );

  /// CardClick 卡片点击
  static const ColorMode _cardClick = ColorMode(
    light: GreyColors.grey3,
  );

  /// Block 选框
  static const ColorMode _block = ColorMode(
    light: BlackColors.black5,
  );

  /// Musk 蒙层
  static const ColorMode _musk = ColorMode(
    light: BlackColors.black10,
  );

  /// Float 浮层
  static const ColorMode _float = ColorMode(
    light: BlackColors.black26,
  );

  /// Cover 遮罩
  static const ColorMode _cover = ColorMode(
    light: BlackColors.black50,
  );

  /// Toast 吐司
  static const ColorMode _toast = ColorMode(
    light: BlackColors.black80,
  );

  /// Notice 通知
  static const ColorMode _notice = ColorMode(
    light: WhiteColors.white55,
  );

  /// Menu 菜单
  static const ColorMode _menu = ColorMode(
    light: WhiteColors.white70,
  );

  /// Popup 弹层
  static const ColorMode _popup = ColorMode(
    light: GreyColors.grey1,
  );

  /// Window 浮窗
  static const ColorMode _window = ColorMode(
    light: WhiteColors.white100,
  );

  /// Tabbar 底tab
  static const ColorMode _tabbar = ColorMode(
    light: WhiteColors.white100,
  );

  // 对外暴露的属性
  Color get card => _card.value;
  Color get cardSec => _cardSec.value;
  Color get cardClick => _cardClick.value;
  Color get block => _block.value;
  Color get musk => _musk.value;
  Color get float => _float.value;
  Color get cover => _cover.value;
  Color get toast => _toast.value;
  Color get notice => _notice.value;
  Color get menu => _menu.value;
  Color get popup => _popup.value;
  Color get window => _window.value;
  Color get tabbar => _tabbar.value;
}
