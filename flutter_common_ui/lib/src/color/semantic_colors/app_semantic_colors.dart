import 'background_colors/background_colors.dart';
import 'component_colors/component_colors.dart';
import 'container_colors/container_colors.dart';
import 'item_colors/item_colors.dart';

/// 语义颜色统一入口
/// 参考文档：https://ihaier.feishu.cn/wiki/UzygwV9bxit9yyksrvdcqdernyb
/// 包含四类语义颜色：背景BackgroundColors、容器ContainerColors、组件ComponentColors、元素ItemColors
class AppSemanticColors {
  // 私有构造函数
  const AppSemanticColors._();

  static BackgroundColors get background => BackgroundColors.instance;
  static ContainerColors get container => ContainerColors.instance;
  static ComponentColors get component => ComponentColors.instance;
  static ItemColors get item => ItemColors.instance;
}
