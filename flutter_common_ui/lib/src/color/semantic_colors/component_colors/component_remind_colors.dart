import 'package:flutter/material.dart';

import '../../basic_color/function_colors/orange_colors.dart';
import '../color_mode.dart';

class ComponentRemindColors {
  // 私有构造函数
  const ComponentRemindColors._();

  static const ComponentRemindColors instance = ComponentRemindColors._();

  /// Fill 填充
  static const ColorMode _fill = ColorMode(
    light: OrangeColors.orange1,
  );

  /// Emphasize 强调
  static const ColorMode _emphasize = ColorMode(
    light: OrangeColors.orange2,
  );

  /// On 之上
  static const ColorMode _on = ColorMode(
    light: OrangeColors.orange6,
  );

  // 对外暴露的属性
  Color get fill => _fill.value;
  Color get emphasize => _emphasize.value;
  Color get on => _on.value;
}
