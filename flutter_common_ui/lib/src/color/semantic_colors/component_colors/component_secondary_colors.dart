import 'package:flutter/material.dart';

import '../../basic_color/neutral_colors/grey_colors.dart';
import '../../basic_color/neutral_colors/white_colors.dart';
import '../color_mode.dart';

class ComponentSecondaryColors {
  // 私有构造函数
  const ComponentSecondaryColors._();

  static const ComponentSecondaryColors instance = ComponentSecondaryColors._();

  /// Fill 填充
  static const ColorMode _fill = ColorMode(
    light: GreyColors.grey1,
  );

  /// Emphasize 强调
  static const ColorMode _emphasize = ColorMode(
    light: GreyColors.grey3,
  );

  /// On 之上
  static const ColorMode _on = ColorMode(
    light: GreyColors.grey14,
  );

  /// Invert 反义
  static const ColorMode _invert = ColorMode(
    light: WhiteColors.white100,
  );

  // 对外暴露的属性
  Color get fill => _fill.value;
  Color get emphasize => _emphasize.value;
  Color get on => _on.value;
  Color get invert => _invert.value;
}
