import 'package:flutter/material.dart';

import '../../basic_color/function_colors/green_colors.dart';
import '../color_mode.dart';

class ComponentSuccessColors {
  // 私有构造函数
  const ComponentSuccessColors._();

  static const ComponentSuccessColors instance = ComponentSuccessColors._();

  /// Fill 填充
  static const ColorMode _fill = ColorMode(
    light: GreenColors.green1,
  );

  /// Emphasize 强调
  static const ColorMode _emphasize = ColorMode(
    light: GreenColors.green2,
  );

  /// On 之上
  static const ColorMode _on = ColorMode(
    light: GreenColors.green6,
  );

  // 对外暴露的属性
  Color get fill => _fill.value;
  Color get emphasize => _emphasize.value;
  Color get on => _on.value;
}
