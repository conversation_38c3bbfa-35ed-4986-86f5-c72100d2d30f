import 'package:flutter/material.dart';

import '../../basic_color/function_colors/red_colors.dart';
import '../color_mode.dart';

class ComponentWarnColors {
  // 私有构造函数
  const ComponentWarnColors._();

  static const ComponentWarnColors instance = ComponentWarnColors._();

  /// Fill 填充
  static const ColorMode _fill = ColorMode(
    light: RedColors.red1,
  );

  /// Emphasize 强调
  static const ColorMode _emphasize = ColorMode(
    light: RedColors.red2,
  );

  /// On 之上
  static const ColorMode _on = ColorMode(
    light: RedColors.red6,
  );

  // 对外暴露的属性
  Color get fill => _fill.value;
  Color get emphasize => _emphasize.value;
  Color get on => _on.value;
}
