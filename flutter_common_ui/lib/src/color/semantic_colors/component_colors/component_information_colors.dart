import 'package:flutter/material.dart';

import '../../basic_color/brand_colors/intelligent_colors.dart';
import '../color_mode.dart';

class ComponentInformationColors {
  // 私有构造函数
  const ComponentInformationColors._();

  static const ComponentInformationColors instance =
      ComponentInformationColors._();

  /// Fill 填充
  static const ColorMode _fill = ColorMode(
    light: IntelligentColors.intelligent1,
  );

  /// Emphasize 强调
  static const ColorMode _emphasize = ColorMode(
    light: IntelligentColors.intelligent2,
  );

  /// On 之上
  static const ColorMode _on = ColorMode(
    light: IntelligentColors.intelligent6,
  );

  // 对外暴露的属性
  Color get fill => _fill.value;
  Color get emphasize => _emphasize.value;
  Color get on => _on.value;
}
