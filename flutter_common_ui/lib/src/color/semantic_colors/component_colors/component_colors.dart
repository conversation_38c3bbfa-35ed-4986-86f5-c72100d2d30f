import 'package:flutter/material.dart';

import '../../basic_color/brand_colors/intelligent_colors.dart';
import '../../basic_color/neutral_colors/grey_colors.dart';
import '../../basic_color/neutral_colors/white_colors.dart';
import '../color_mode.dart';
import 'component_information_colors.dart';
import 'component_primary_colors.dart';
import 'component_remind_colors.dart';
import 'component_secondary_colors.dart';
import 'component_success_colors.dart';
import 'component_warn_colors.dart';

class ComponentColors {
  // 私有构造函数
  const ComponentColors._();

  static const ComponentColors instance = ComponentColors._();

  /// Divider 分割线
  static const ColorMode _divider = ColorMode(
    light: GreyColors.grey2,
  );

  /// Link 链接
  static const ColorMode _link = ColorMode(
    light: IntelligentColors.intelligent6,
  );

  /// Indicator 指示
  static const ColorMode _indicator = ColorMode(
    light: IntelligentColors.intelligent6,
  );

  /// IndicatorInvert 指示反白
  static const ColorMode _indicatorInvert = ColorMode(
    light: WhiteColors.white100,
  );

  // 对外暴露的属性
  Color get divider => _divider.value;
  Color get link => _link.value;
  Color get indicator => _indicator.value;
  Color get indicatorInvert => _indicatorInvert.value;

  ComponentPrimaryColors get primary => ComponentPrimaryColors.instance;
  ComponentSecondaryColors get secondary => ComponentSecondaryColors.instance;
  ComponentInformationColors get information =>
      ComponentInformationColors.instance;
  ComponentWarnColors get warn => ComponentWarnColors.instance;
  ComponentRemindColors get remind => ComponentRemindColors.instance;
  ComponentSuccessColors get success => ComponentSuccessColors.instance;
}
