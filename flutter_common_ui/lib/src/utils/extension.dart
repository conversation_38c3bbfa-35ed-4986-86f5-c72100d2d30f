/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-10 17:14:55
 * @description: 
 */
import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

extension EdgeInsetsExt on EdgeInsets {
  /// 合并阴影 BoxShadow
  EdgeInsets mergeShadow({BoxShadow? shadow}) {
    if (shadow == null) {
      return this;
    }

    final shadowRadius = shadow.spreadRadius + shadow.blurRadius;
    final marginNew = EdgeInsets.only(
      top: this.top + shadowRadius - shadow.offset.dy,
      bottom: this.bottom + shadowRadius + shadow.offset.dy,
      right: this.right + shadowRadius + shadow.offset.dx,
      left: this.left + shadowRadius - shadow.offset.dx,
    );
    return marginNew;
  }
}

extension ColorExt on Color {
  ///随机颜色
  static Color get random {
    return Color.fromRGBO(
        Random().nextInt(256), Random().nextInt(256), Random().nextInt(256), 1);
  }

  /// 颜色转渐进类型
  Gradient toGradient() => LinearGradient(colors: [
        this,
        this,
      ], stops: [
        0.0,
        1
      ]);
}

extension StringUtil on String {
  static bool notEmpty(String? s) {
    return s != null && s.isNotEmpty;
  }

  int parseInt() {
    return int.parse(this);
  }

  double parseDouble() {
    return double.parse(this);
  }
}
