/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-10 15:27:14
 * @description: 公共util方法
 */
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';

class CommonUtil {
  /// 状态栏高度
  static double _statusBarHeight = 0;

  static double get statusBarHeight =>
      _statusBarHeight == 0 ? 35 : _statusBarHeight;

  /// 设置状态栏高度
  static void setStatusBarHeight(BuildContext context) {
    if (_statusBarHeight == 0) {
      double paddingTop = MediaQuery.of(context).padding.top;
      if (paddingTop > 0) {
        _statusBarHeight = paddingTop;
        DevLogger.info(
            tag: Constant.tagCommonUI,
            msg: 'setStatusBarHeight _statusBarHeight:$_statusBarHeight');
      }
    }
  }

  /// 过滤 null 和负值
  static double filterNullAndPositiveValue(double? value) {
    if (value == null || value <= 0) {
      return 0.0;
    }
    return value;
  }

  /// 颜色字符串转int
  static Color? hexToColor(String? code) {
    if (code == null ||
        code == "" ||
        code.length != 7 && code.length != 9 && code.length != 4) {
      return null;
    }
    if (code.length == 4) {
      int values = int.parse(code.substring(1, 2), radix: 16);
      int values1 = int.parse(code.substring(2, 3), radix: 16);
      int values2 = int.parse(code.substring(3, 4), radix: 16);
      return Color.fromRGBO(values * 17, values1 * 17, values2 * 17, 1);
    }
    if (code.length == 7)
      return Color(int.parse(code.substring(1, 7), radix: 16) + 0xFF000000);
    if (code.length == 9) {
      int value = int.parse(code.substring(7, 9), radix: 16);
      Color tmp = Color(int.parse(code.substring(1, 7), radix: 16) + 0xFF000000)
          .withAlpha(value);
      return tmp;
    }
    return null;
  }

  static Color? rgboToColor(String? code) {
    if (code == null || code == "") {
      return null;
    }
    code = code.replaceAll(")", "").replaceAll("rgba(", "").replaceAll(" ", "");

    // code = code.replaceAll(RegExp(r'rgba(|)| '), "");

    List<String> list = code.split(",");
    if (list.length != 4) {
      return null;
    }
    final r = int.parse(list[0]);
    final g = int.parse(list[1]);
    final b = int.parse(list[2]);
    final a = double.parse(list[3]);
    final result = Color.fromRGBO(r, g, b, a);
    return result;
  }

  // 颜色转换
  static Color? toColor(String? code) {
    if (code == null || code == "") {
      return null;
    }
    if (code.startsWith("#") == true) {
      Color? color = hexToColor(code);
      if (color != null) {
        if (color.toString().contains("0x00")) {
          return null;
        }
      }
      return color;
    }
    if (code.startsWith("rgba(") == true) {
      Color? color = rgboToColor(code);
      if (color != null) {
        if (color.toString().contains("0x00")) {
          return null;
        }
      }
      return color;
    }
    return null;
  }

  // 阴影
  static List<BoxShadow>? toBoxShadow(ShadowModel? shadow, {String? name}) {
    Color? shadowColor = toColor(shadow?.color?.value);
    if (shadow?.color == null ||
        shadow!.X == null ||
        shadow.Y == null ||
        (shadow.Y == 0 && shadow.X == 0) ||
        shadowColor == null) {
      return null;
    }

    BoxShadow sh = BoxShadow(
      color: shadowColor,
      offset: Offset(shadow.X!, shadow.Y!),
      blurRadius: shadow.blur ?? 0,
      spreadRadius: shadow.spread ?? 0,
    );
    return [sh];
  }

  /// 渐变
  static Gradient? toGradient(ColorModel? color,
      {double? width, double? height}) {
    if (color == null || color.isGradient == false) {
      return null;
    }
    AttrGradient? gradient = color.gradient;
    List<double> stop = [];
    List<Color> colors = [];
    if (gradient?.stop == null) {
      return null;
    }
    List stopData = gradient!.stop!;
    stopData.forEach((element) {
      try {
        List item = element as List;
        if (item.length == 2) {
          stop.add(double.parse(item[1].toString()));
          colors.add(toColor(item[0].toString())!);
        }
      } catch (e, trace) {
        DevLogger.info(
            tag: Constant.tagCommonUI, msg: {'toGradient_e': e.toString()});
        DevLogger.info(
            tag: Constant.tagCommonUI,
            msg: {'toGradient_trace': trace.toString()});
      }
    });

    if (colors.length == 0 ||
        stop.length == 0 && colors.length != stop.length) {
      return null;
    }
    stop[stop.length - 1] = 1.0;
    if (gradient.type == "linear" && gradient.angle != null) {
      double angle = gradient.angle! % 360;
      return LinearGradient(
          colors: colors,
          stops: stop,
          transform: GradientRotation(angle / 360 * pi * 2),
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter);
    } else if (gradient.type == "conic" && gradient.angle != null) {
      AlignmentGeometry center = Alignment.center;
      return SweepGradient(
        center: Alignment.center,
        colors: colors,
        startAngle: 0,
        endAngle: pi * 2,
        stops: stop,
        transform: GradientRotation(-pi / 2),
      );
    } else if (gradient.type == "radial") {
      String? loctionsx = gradient.direction;
      String? loctions = loctionsx?.replaceAll("circle at", "");

      Alignment center = Alignment.center;
      if (loctions == null) {
      } else {
        bool left = false;
        bool right = false;
        bool top = false;
        bool bottom = false;
        if (loctions.contains("left")) {
          left = true;
        }
        if (loctions.contains("right")) {
          right = true;
        }
        if (loctions.contains("top")) {
          top = true;
        }
        if (loctions.contains("bottom")) {
          bottom = true;
        }
        bool cal = false;
        center = Alignment(
            left
                ? -1
                : right
                    ? 1
                    : 0,
            bottom
                ? 1
                : top
                    ? -1
                    : 0);
      }
      if (width == null || height == null || width <= 0 || height <= 0) {
        return RadialGradient(
          colors: colors,
          stops: stop,
          center: center,
        );
      }
      double anchor = 1;
      double max_x = max(height, width);
      double min_y = min(height, width);
      if (center == Alignment.topCenter || center == Alignment.bottomCenter) {
        anchor = sqrt(pow(width / 2, 2) + pow(height, 2)) / min_y * 1.2;
      } else if (center == Alignment.center) {
        anchor = max_x / min_y;
      } else if (center == Alignment.centerLeft ||
          center == Alignment.centerRight) {
        anchor = max_x / min_y * 1.3;
      } else {
        anchor = sqrt(pow(width, 2) + pow(height, 2)) / min_y * 1.2;
      }
      return RadialGradient(
          colors: colors, stops: stop, center: center, radius: anchor);
    }
  }

  /// bool值判断
  static bool getBoolResult(dynamic value) {
    if (value != null && (value is bool) && value) {
      return true;
    }
    return false;
  }

  /// 获得背景图
  static DecorationImage? getDecorationImage(String? bgUrl,
      [BoxFit? imageFit]) {
    return bgUrl != null && bgUrl.startsWith('http')
        ? DecorationImage(
            image: NetworkImage(bgUrl),
            fit: imageFit ?? BoxFit.cover,
          )
        : null;
  }

  /// 参数类型与T一致时返回参数值,否则反null
  static T? safeGet<T>(dynamic value) {
    if (value is T) {
      return value;
    }
    return null;
  }
}
