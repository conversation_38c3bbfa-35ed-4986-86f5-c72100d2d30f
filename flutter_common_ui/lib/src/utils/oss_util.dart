/*
 * @Author: ma<PERSON><PERSON><PERSON> ma<PERSON>@haier.com
 * @Date: 2023-03-03 16:23:29
 * @description: // oss处理图片文件
// 阿里云文档链接：https://help.aliyun.com/document_detail/44688.html

 */
import 'dart:ui';

class OssUtil {
  // 视频截取第一帧
  static const OssVideoSnap =
      'x-oss-process=video/snapshot,t_1,f_jpg,h_0,w_320,ar_auto';
  //分享到微信的缩略图
  static const OssWxShare =
      'x-oss-process=image/resize,m_lfit,w_300,limit_0/auto-orient,1';

  // 获取width * height图片的oss处理字符串（超过居中裁剪）
  // 参数传设计图给出的尺寸+.w
  static double getDpr() {
    return window.devicePixelRatio > 1.0 ? window.devicePixelRatio : 2.0;
  }

  static String getOssTypeBySize(num width, num height) {
    return 'x-oss-process=image/resize,m_fill,w_${(width * getDpr()).ceil()},h_${(height * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 获取width,高度自适应图片的oss处理字符串
  // 参数单位是px,设计图给出得尺寸
  static String getOssTypeByWidth(num width) {
    return 'x-oss-process=image/resize,m_lfit,w_${(width * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 获取height,宽度自适应图片的oss处理字符串
  // 参数单位是px,设计图给出得尺寸
  static String getOssTypeByHeight(num height) {
    return 'x-oss-process=image/resize,m_lfit,h_${(height * getDpr()).ceil()},limit_0/auto-orient,1';
  }

  // 添加图片oss处理
  static String addOssProcess(String? originUrl, String processType) {
    // .webp格式的动图，如果加 oss 的参数，会导致图片损坏，出不来，因此排除webp格式
    // .webp格式的静图，加oss参数，是可是的。但是由于目前没有方法去判断为webp格式的图片是静图还是动图，因此 一块排除掉了webp格式。
    if (originUrl != null &&
        originUrl != '' &&
        originUrl.indexOf('oss-process') < 0 &&
        originUrl.indexOf('.webp') < 0) {
      // 图片在iOS 14及以上才支持webp, 所以分享这里去掉webp的转换
      // 视频取第一帧也不转webp
      if (processType != OssUtil.OssVideoSnap &&
          processType != OssUtil.OssWxShare) {
        processType += '/format,webp';
      }
      // gif图进行了大小变化后，不加format不能动，所以针对没有转webp的，加转gif
      if (originUrl.indexOf('.gif') > -1 && processType.indexOf('format') < 0) {
        processType = processType + '/format,gif';
      }
      // 有其他参数，将oss参数加到第一项，然后再添加上其他参数
      if (originUrl.indexOf('?') > -1) {
        return originUrl.split('?')[0] +
            '?' +
            processType +
            '&' +
            originUrl.split('?')[1];
      }
      // 无其他参数，进行oss处理
      return originUrl + '?' + processType;
    }
    return originUrl ?? '';
  }

  // 添加oss处理
  static String addOssParams(String url, num? width, num? height) {
    String processType = '';
    // .webp格式的动图，如果加 oss 的参数，会导致图片损坏，出不来，因此排除webp格式
    // .webp格式的静图，加oss参数，是可是的。但是由于目前没有方法去判断为webp格式的图片是静图还是动图，因此 一块排除掉了webp格式。
    if (!url.contains('.webp')) {
      if (width != null && width.isFinite && height != null && height.isFinite) {
        processType = OssUtil.getOssTypeBySize(width, height);
      } else if (width != null && width.isFinite) {
        processType = OssUtil.getOssTypeByWidth(width);
      } else if (height != null && height.isFinite) {
        processType = OssUtil.getOssTypeByHeight(height);
      }
      url = OssUtil.addOssProcess(url, processType);
    }
    return url;
  }
}
