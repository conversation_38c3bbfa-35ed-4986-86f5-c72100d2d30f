import 'dart:async';
import 'dart:ui';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/debounce.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/common/toast_helper.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:trace/trace.dart';
import 'package:vdn/back.dart';
import 'package:vdn/vdn.dart';

// goToPage共用一个timer
final Debounce _debounceGoToPage = Debounce();

/// 页面停留时长打点事件id
String _pageStayTimeEventId = 'MB37141';

/// 页面停留时长
int? pageStayTime;

// 背景模糊度px转sigma
double blurPxToSigma(num? px) {
  // 根据7.1.3版本毛玻璃效果，flutter Sigma 16 与 H5 px 20 效果相同，所以以此作为依据将后台传来的px模糊度转为sigma
  return px is num ? px * (16 / 20) : 0.0;
}

// gio打点, 参数可选variable
Future<void> gioTrack(String? eventId, [Map<String, dynamic>? variable]) async {
  try {
    if (eventId is String && eventId != '') {
      if (variable != null) {
        // 循环判断每一项是否为null或者空字串，是的话则修改value为'null'
        if (variable is Map && variable.length > 0) {
          variable.forEach((String key, dynamic value) {
            if (value == null || value == '') {
              variable[key] = 'null';
            }
          });
        }
        DevLogger.debug(tag: Constant.tagCommonUI, msg: <String, Object>{
          'fn': 'gioTrack',
          'data': <String, Object>{'eventId': eventId, 'variable': variable}
        });
        await Trace.traceEventWithVariable(
            eventId: eventId, variable: variable);
      } else {
        DevLogger.debug(tag: Constant.tagCommonUI, msg: <String, Object>{
          'fn': 'gioTrack',
          'data': <String, String>{'eventId': eventId}
        });
        await Trace.traceEvent(eventId: eventId);
      }
    }
  } catch (err) {
    DevLogger.error(
        tag: Constant.tagCommonUI, msg: <String, Object>{'fn': 'gioTrack', 'err': err});
  }
}

// vdn跳转
Future<void> goToPage(String? url, {Map<String, dynamic>? params}) async {
  try {
    _debounceGoToPage.run(() async {
      try {
        final IsOnline isOnline = await (Network.isOnline());
        if (!isOnline.isOnline) {
          ToastHelper.showToast(Constant.netWorkError);
        } else {
          DevLogger.debug(tag: Constant.tagCommonUI, msg: <String, Object>{
            'fn': 'goToPage',
            'data': <Object?, Object?>{url: url, params: params}
          });
          if (url != '' && url != null) {
            await Vdn.goToPage(url, params: <String, dynamic>{...?params, 'checkGuestMode': 1});
          }
        }
      } catch (err) {
        DevLogger.error(tag: Constant.tagCommonUI, msg: <String, Object>{'goToPage': err});
      }
    });
  } catch (err) {}
}

// url 添加指定参数
String addUriQueryParam(String url, String name, String value) {
  final List<String> list = url.split('?');
  final String before = list[0];
  String after = '';
  if (list.length > 1) {
    after = list[1];
  }
  if (after.isNotEmpty) {
    after = '&' + after;
  }
  final String newUrl = '$before?$name=$value$after';
  return newUrl;
}

// 图片oss处理
RegExp _domin =
    new RegExp("/accountstatic.haier.com|zjrs.haier.net|synrs.haier.net/gi");
String? addOssProcess(String? originUrl, String processType) {
  if (originUrl != null &&
      originUrl != '' &&
      _domin.hasMatch(originUrl) &&
      originUrl.indexOf('oss-process') < 0) {
    if (processType != Constant.OSS_WXSHARE) {
      if (originUrl.indexOf('.gif') > -1) {
        processType = processType + '/format,gif';
      } else {
        processType += '/format,webp';
      }
    }
    if (originUrl.indexOf('?') > -1) {
      return originUrl.split('?')[0] +
          '?' +
          processType +
          '&' +
          originUrl.split('?')[1];
    }
    // 无其他参数，进行oss处理
    return originUrl + '?' + processType;
  }
  return originUrl;
}

String? addOssProcessWeather(String? originUrl, String processType) {
  if (originUrl != null &&
      originUrl != '' &&
      _domin.hasMatch(originUrl) &&
      originUrl.indexOf('oss-process') < 0 &&
      originUrl.indexOf('.gif') > -1) {
    processType = processType + '/format,webp';
    if (originUrl.indexOf('?') > -1) {
      return originUrl.split('?')[0] +
          '?' +
          processType +
          '&' +
          originUrl.split('?')[1];
    }
    // 无其他参数，进行oss处理
    return originUrl + '?' + processType;
  }
  return originUrl;
}

/// 根据背景色系，返回颜色反转参数colorFilter属性
ColorFilter getColorFilterValue({ColorSystemEnum? colorSystem}) {
  return colorSystem == ColorSystemEnum.dark
      ? Constant.invert
      : Constant.identity;
}

// 解决：字符截断问题
// 在字符串的每两个字符中间插入一个不可见的空格符号'\u200B'
String breakWord(String text) {
  if (text.isEmpty) {
    return text;
  }
  String breakWord = '';
  text.runes.forEach((int element) {
    breakWord += String.fromCharCode(element);
    breakWord += '\u200B';
  });
  return breakWord;
}

/*
  * 取webp
  * @param {string} url 图片url
  */
String imageToWebp(String? url) {
  if (url == null || url.isEmpty) {
    //  || url.endsWith('.gif')
    return '';
  }
  if (url.indexOf('aliyuncs.com') > 0 ||
      url.indexOf('cdn50.ehaier.com') > 0 ||
      url.indexOf('cdn51.ehaier.com') > 0) {
    url = url.indexOf('?x-oss-process=image') > 0
        ? '$url/format,webp'
        : '$url?x-oss-process=image/format,webp';
  }
  return url;
}

// 转换 1080 -》 375
num translateUnit(num value) {
  return value * 2.88;
}

/// url 添加参数
String addQueryParam(String url, Map<String, dynamic> params) {
  if (url.isEmpty) {
    return url;
  }

  String addQueryString = Uri(
      queryParameters: params.map<String, dynamic>(
          (String key, dynamic value) =>
              MapEntry<String, dynamic>(key, value?.toString()))).query;

  addQueryString = (url.contains('?') ? '&' : '?') + addQueryString;
  if (url.contains('#')) {
    List<String> arr = url.split('#');
    String startStr = arr[0];
    String endStr = arr[1];
    url = startStr + addQueryString + '#' + endStr;
  } else {
    url = url + addQueryString;
  }
  return url;
}

/*
   * 格式化Url
  */
Future<dynamic> modifyUrl(String url,
    {bool isFullPath = false, bool showHeader = false}) async {
  if (url.startsWith('flutter://')) {
    final IsOnline isOnline = await (Network.isOnline());
    if (!isOnline.isOnline) {
      ToastHelper.showToast(Constant.netWorkError);
    } else {
      return Vdn.goToPage(url, params: <String, int>{'checkGuestMode': 1});
    }
  } else {
    if (url.contains('#')) {
      List<String> arr = url.split('#');
      String startStr = arr[0];
      String endStr = arr[1];
      if (arr[0].contains('?')) {
        startStr += '&container_type=3&hidesBottomBarWhenPushed=1';
      } else {
        startStr += '?container_type=3&hidesBottomBarWhenPushed=1';
      }
      url = startStr + '#' + endStr;
    } else {
      url.contains('?')
          ? url += '&container_type=3&hidesBottomBarWhenPushed=1'
          : url += '?container_type=3&hidesBottomBarWhenPushed=1';
    }
    goToPage(url);
  }
}

void pageIn() {
  /// 页面停留时长赋初始值
  pageStayTime = DateTime.now().millisecondsSinceEpoch;
}

/// 需求：https://ihaier.feishu.cn/wiki/YPTnwDstEiHW7ZkDZdIcjW0nnKe
/// title: 智家/服务/商城/我的
void pageLeave({required String title, String? url}) {
  if (pageStayTime == null) {
    return;
  }
  final int stayTime = DateTime.now().millisecondsSinceEpoch - pageStayTime!;
  gioTrack(_pageStayTimeEventId, <String, dynamic>{
    'title': title,
    'static_url': url ?? '',
    'page_view_time': '$stayTime',
    'page_view_time_int': stayTime,
  });
  pageStayTime = null;
}

class InterceptSystemBackUtil {
  // 注册监听
  static void interceptSystemBack({
    required String pageName,
    BuildContext? context,
    void Function()? callback,
  }) {
    MessageBackKeyManager.registerMessageChannel();
    MessageBackKeyManager.interceptSystemBack(pageName, false, () {
      if (context != null && context.mounted) {
        Navigator.of(context).pop();
      }
      if (callback != null) {
        callback();
      }
    });
  }

  // 移除监听
  static void cancelInterceptSystemBack(String pageName) {
    MessageBackKeyManager.cancelInterceptSystemBack(pageName);
  }
}
