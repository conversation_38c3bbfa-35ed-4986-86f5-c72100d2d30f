/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-13 20:01:21
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';

class TextCommonUtils {
  /// 获取字重
  static FontWeight getFontWeightFromString(TextWeightType? fontWeight) {
    if (fontWeight != null) {
      switch (fontWeight) {
        case TextWeightType.normalWeight:
          return FontWeight.w400;
        case TextWeightType.boldWeight:
          return FontWeight.w500;
        case TextWeightType.bolderWeight:
          return FontWeight.w600;
        default:
          break;
      }
    }
    return FontWeight.w400;
  }

  /// 获取垂直对齐方式
  static MainAxisAlignment getVerticalAlignmentGeometry(
      TextVerticalType? verticalAlign) {
    if (verticalAlign != null) {
      switch (verticalAlign) {
        case TextVerticalType.topAlignment:
          return MainAxisAlignment.start;
        case TextVerticalType.middleAlignment:
          return MainAxisAlignment.center;
        case TextVerticalType.bottomAlignment:
          return MainAxisAlignment.end;
        default:
          break;
      }
    }
    return MainAxisAlignment.start;
  }

  /// 获取水平对齐方式
  static CrossAxisAlignment getAlignmentGeometry(TextHorizontalType? align) {
    if (align != null) {
      switch (align) {
        case TextHorizontalType.leftAlignment:
          return CrossAxisAlignment.start;
        case TextHorizontalType.centerAlignment:
          return CrossAxisAlignment.center;
        case TextHorizontalType.rightAlignment:
          return CrossAxisAlignment.end;
        default:
          break;
      }
    }
    return CrossAxisAlignment.start;
  }
}
