/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-02-14 10:19:26
 * @description: 
 */
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';

class GIOUtil {
  //*****************************可视化埋点*****************************************/

  /* 埋点逻辑处理
   * ownTrack: 埋点事件全量参数(key:value)
   * trackInfo: 可视化埋点对象
   * isExpose: 是否是曝光埋点
   */
  static void dealTrackInfo(Map ownTrack, TrackInfoModel? trackInfo,
      {required bool isExpose}) {
    try {
      AttrTrackDetail? track =
          isExpose ? trackInfo?.exposure : trackInfo?.click;
      if (track?.code != null && track!.code!.length > 0) {
        if (track.attr != null && track.attr!.isNotEmpty) {
          final copyTrackAttr = {...track.attr!};
          copyTrackAttr
              .removeWhere((key, value) => (!CommonUtil.getBoolResult(value)));
          copyTrackAttr.updateAll((key, value) =>
              ownTrack.putIfAbsent(key, () => value.toString()));

          ///发送埋点事件 track.code! / track.attr!
          addTrackEvent(track.code!, copyTrackAttr);
        } else {
          ///直接发送埋点事件id
          addTrackEvent(track.code!, null);
        }
      }
    } catch (e, trace) {
      DevLogger.error(
          tag: Constant.tagCommonUI,
          msg: {'fn': 'home_crash:dealTrackInfo error : $e $trace'});
    }
  }

  ///上传埋点信息
  static void addTrackEvent(String gioString, Map<String, dynamic>? trackInfo) {
    gioTrack(gioString, trackInfo);
  }
}
