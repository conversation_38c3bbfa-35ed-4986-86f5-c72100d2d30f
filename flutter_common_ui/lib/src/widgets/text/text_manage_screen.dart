/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-14 13:56:11
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/text/text_model.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 文本组件
class TextManageScreen extends StatelessWidget {
  /// [config] 文本组件的配置信息
  final TextModel? config;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// [onTap] 跳转的额外事件，兼容卡片导航的标题组件，点击以后，要跳转到应用管理二级页面
  final Function? onTap;

  /// [colorSystem] 主题色系
  final ColorSystemEnum? colorSystem;

  const TextManageScreen(
      {Key? key,
      this.config,
      this.relative = false,
      this.onTap,
      this.colorSystem})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.attr?.text == '' || config?.style?.attr == null) {
      return const SizedBox();
    }

    /// 文本内容
    Widget textContainer() {
      return Column(

          /// 垂直对齐方式
          mainAxisAlignment: TextCommonUtils.getVerticalAlignmentGeometry(
              config?.attr?.verticalAlign),

          /// 水平对齐方式
          crossAxisAlignment:
              TextCommonUtils.getAlignmentGeometry(config?.attr?.textAlign),
          children: [
            Text(
              config?.attr?.text ?? '',
              style: TextStyle(
                color: colorSystem == null
                    ? config!.attr!.color!.color
                    : Color.fromRGBO(0, 0, 0, 0.93),
                fontSize: config?.attr?.fontSize?.sp,

                /// 斜体
                fontStyle: config?.attr?.italic == true
                    ? FontStyle.italic
                    : FontStyle.normal,

                /// 下划线
                decoration: config?.attr?.underline == true
                    ? TextDecoration.underline
                    : config?.attr?.linethrough == true
                        ? TextDecoration.lineThrough
                        : TextDecoration.none,
                fontWeight: TextCommonUtils.getFontWeightFromString(
                    config?.attr?.fontWeight),
              ),
              softWrap: true,
            ),
          ]);
    }

    return config!.style!.attr!.toDecorationWidget(
        relative: relative,
        colorSystem: colorSystem,
        child: Container(
          width: config!.style!.attr!.width?.w,
          height: config!.style!.attr!.height?.w,
          child: TrackWidget(textContainer(),
              key: ValueKey(config?.aliasUnit ?? 'text'), onTap: () {
            if (onTap != null) onTap!();
          },
              trackInfo: config?.attr?.trackInfo,

              /// 埋点参数
              trackMap: {},
              onExpose: () {}),
        ));
  }
}
