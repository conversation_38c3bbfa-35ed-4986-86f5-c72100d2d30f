/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-23 14:56:24
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/horizontal_scroll_list_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 一页多个轮播
class MultiCarousel extends StatelessWidget {
  const MultiCarousel(
      {Key? key, required this.config, required this.items, this.needReload});

  // [config] 图片组件的配置信息
  final CarouselModel config;

  /// [items] 轮播项
  final List<AttrCarouseItem?> items;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// 轮播项个数
  double get _itemCount => config.attr?.singleNorms?.rowSize ?? 1;

  /// 左内边距
  double get _paddingLeft => config.style?.attr?.padding?.left ?? 0;

  /// 右内边距
  double get _paddingRight => config.style?.attr?.padding?.right ?? 0;

  /// 轮播间距
  double get _gutter => config.attr?.gutter ?? 0;

  /// [_itemWidth] 轮播项的宽度
  /// (轮播组件的宽 - 左右内边距 一 间距 * (轮播项个数.向上取整 - 1)) / 轮播项个数
  double get _itemWidth => (((config.style?.attr?.width ?? 0) -
              _paddingLeft -
              _paddingRight -
              ((_itemCount.ceilToDouble() - 1) * _gutter)) /
          _itemCount)
      .w;

  @override
  Widget build(BuildContext context) {
    return HorizontalScrollListView(
      itemWidth: _itemWidth,
      items: items,
      gutter: _gutter,
      needReload: needReload,
      // 轮播项的埋点配置
      trackInfo: config.attr?.trackInfo,
      radius: config.attr?.radius,
      shadow: config.attr?.shadow,

      /// 布局为一页一个的时候才有指示器
      pagination: items.length > 1 && _itemCount == 1 ? true : false,
    );
  }
}
