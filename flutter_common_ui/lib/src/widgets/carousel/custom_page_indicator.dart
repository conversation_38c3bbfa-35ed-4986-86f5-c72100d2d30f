/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-14 15:11:19
 * @description: 
 */
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/page_indicator_screen.dart';

class CustomPageIndicator extends StatefulWidget {
  /// [config] 轮播滚动的配置
  final SwiperPluginConfig? config;

  /// [count] 轮播项个数
  final int count;

  /// [轮播的宽]
  final double swiperWidth;

  CustomPageIndicator({
    Key? key,
    this.config,
    this.count = 1,
    this.swiperWidth = 0,
  }) : super(key: key);

  @override
  _CustomPageIndicatorState createState() => _CustomPageIndicatorState();
}

/// 参考我的页面
/// 1 白色底的指示条宽75.w
/// 2 右边的活动指示条，相对白色框绝对定位，left: page * itemWidth， width: itemWidth
/// 3 昨天活动指示条：当滑动到最后一张的时候，左边慢慢出现，比如 itemCount=4， page=3.2, left: 0, width:1-(4-3.2) * itemWidth
class _CustomPageIndicatorState extends State<CustomPageIndicator> {
  /// [_page]轮播当前滚动的page
  double _page = 0;

  @override
  void initState() {
    super.initState();
    // pageController监听
    widget.config?.pageController?.addListener(_onController);
  }

  @override
  void didUpdateWidget(CustomPageIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    // pageController监听变化
    if (widget.config?.pageController != oldWidget.config?.pageController) {
      oldWidget.config?.pageController?.removeListener(_onController);
      widget.config?.pageController?.addListener(_onController);
    }
  }

  // 刷新页面索引
  void _onController() {
    // 更新page
    if (widget.config?.pageController?.hasClients == true) {
      try {
        setState(() {
          _page = widget.config!.pageController!.page ?? 0.0;
        });
      } catch (err) {
        DevLogger.error(
            tag: Constant.tagCommonUI,
            msg: {'fn': 'custom_page_indicator _onController', 'error': err});
      }
    }
  }

  @override
  void dispose() {
    // 移除监听
    widget.config?.pageController?.removeListener(_onController);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.swiperWidth == 0.0) {
      return const SizedBox();
    }
    return Stack(
      children: <Widget>[
        PageIndicatorScreen(
          swiperWidth: widget.swiperWidth,
          count: widget.count,
          page: _page,
        )
      ],
    );
  }
}
