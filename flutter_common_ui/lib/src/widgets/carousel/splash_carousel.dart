import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/carousel_item.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 闪屏轮播
/// 交替动效：
/// 第一个图片进入：缩放 50->100%  500ms, 保持 2000ms; 透明度 0->100%  500ms, 保持 2000ms
/// 同时第二个图片退出: 缩放 100->50%  500ms, 保持 2000ms; 透明度 100%->0  500ms, 保持 2000ms
class SplashCarousel extends StatefulWidget {
  const SplashCarousel({
    Key? key,
    required this.config,
    required this.items,
    this.needReload,
  });

  // [config] 图片组件的配置信息
  final CarouselModel config;

  /// [items] 轮播项
  final List<AttrCarouseItem?> items;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  @override
  State<SplashCarousel> createState() => _SplashCarouselState();
}

///
class _SplashCarouselState extends State<SplashCarousel>
    with SingleTickerProviderStateMixin {
  /// [_index] 第一张进入的轮播索引
  int _index = 0;

  /// [_preIndex] 前一张退出的轮播索引
  int get _preIndex => (_index - 1 + widget.items.length) % widget.items.length;

  /// [_controller] 动画的controller
  AnimationController? _controller;

  /// [_enterSizeAnimation] 进入尺寸动画变化规律
  Animation<double>? _enterSizeAnimation;

  /// [_enterOpacityAnimation] 进入透明度动画变化规律
  Animation<double>? _enterOpacityAnimation;

  /// [_outSizeAnimation] 退出尺寸动画变化规律
  Animation<double>? _outSizeAnimation;

  /// [_outOpacityAnimation] 退出透明度动画变化规律
  Animation<double>? _outOpacityAnimation;

  /// 动画间隔定时器
  Timer? _timer;

  /// 是否首次进入
  bool _firstEnter = true;

  /// 唯一key
  UniqueKey _key = UniqueKey();

  /// 是否自动播放
  bool autoPlay = true;

  @override
  void initState() {
    super.initState();

    _controller =
        AnimationController(vsync: this, duration: Duration(milliseconds: 2000))
          ..forward();

    _enterSizeAnimation = getAnimation(0.5, 1, 500);

    _enterOpacityAnimation = getAnimation(0, 1, 500);

    _outSizeAnimation = getAnimation(1, 0.5, 500);

    _outOpacityAnimation = getAnimation(1, 0, 500);

    if (widget.items.length >= 1) {
      _enterSizeAnimation?.addStatusListener(statusListenerCallback);
    }
  }

  /// 动画状态变化的回调
  void statusListenerCallback(AnimationStatus status) {
    // 在一轮动画结束后增加图片的index, 下一个图片进入
    if (status == AnimationStatus.completed) {
      _controller?.stop();
      _timer = Timer(Duration(milliseconds: 3000), () {
        _controller
          ?..reset()
          ..forward();
        setState(() {
          _index = (_index + 1) % widget.items.length;
        });
      });
    }
  }

  /// 构建动画
  Animation<double>? getAnimation(
      double forwardBegin, double forwardEnd, int forwardDuration) {
    return TweenSequence<double>([
      TweenSequenceItem(
          tween: Tween(begin: forwardBegin, end: forwardEnd),
          weight: forwardDuration.toDouble()),
    ]).animate(_controller!);
  }

  @override
  void dispose() {
    _enterSizeAnimation?.removeStatusListener(statusListenerCallback);
    _controller?.dispose();
    super.dispose();
    _timer?.cancel();
  }

  /// 获得动画图片
  AnimatedWidget _getAnimatedImage(int index,
      {Animation<double>? sizeAnimation, Animation<double>? opacityAnimation}) {
    return AnimatedBuilder(
        animation: _controller!,
        child: CarouselItem(
          key: ValueKey(
              '$index-${widget.items[index]?.id ?? ''}-${widget.items[index]?.pictureUrl ?? ''}'),
          item: widget.items[index],
          needReload: widget.needReload,
          index: index,
          trackInfo: widget.config.attr?.trackInfo,
          radius: widget.config.attr?.radius,
          shadow: widget.config.attr?.shadow,

          /// 闪屏轮播外城添加了RepaintBoundary， 里面的子图片就不需要添加了
          addRepaintBoundaries: false,
        ),
        builder: (BuildContext context, Widget? child) {
          return Transform.scale(
              scale: sizeAnimation?.value ?? 1,
              child:
                  Opacity(opacity: opacityAnimation?.value ?? 1, child: child));
        });
  }

  void onVisibilityChanged(VisibilityInfo info) {
    if (_firstEnter) {
      _firstEnter = false;
      return;
    }
    if (info.visibleFraction <= 0 && autoPlay == true) {
      _controller?.stop();
      _timer?.cancel();
      autoPlay = false;
    } else if (info.visibleFraction > 0 && autoPlay == false) {
      _controller
        ?..reset()
        ..forward();
      autoPlay = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: VisibilityDetector(
        key: _key,
        onVisibilityChanged: onVisibilityChanged,
        child: Stack(
          children: [
            _getAnimatedImage(_index,
                sizeAnimation: _enterSizeAnimation,
                opacityAnimation: _enterOpacityAnimation),
            _getAnimatedImage(_preIndex,
                sizeAnimation: _outSizeAnimation,
                opacityAnimation: _outOpacityAnimation)
          ],
        ),
      ),
    );
  }
}
