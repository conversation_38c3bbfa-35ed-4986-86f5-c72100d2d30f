import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';

mixin CarouselMixin {
  /// 国家编码，和可视化后台约定
  static const countryCode = '100000';

  /// 判断 地域&时效性
  /// 1 轮播的地域列表里不包含定位的城市、省、全国的编码，则为无效数据
  /// 2 轮播的有效时间段不包含当前时间戳，则为无效数据
  bool isActiveCarousel(List<String?>? regionList,
      List<int?>? entryIntoForceTime, String? cityCode) {
    try {
      if (cityCode?.length != null && cityCode!.length >= 2) {
        String? provinceCode = '${cityCode.substring(0, 2)}0000';
        if (regionList != null &&
            !regionList.contains(cityCode) &&
            !regionList.contains(provinceCode) &&
            !regionList.contains(countryCode)) {
          return false;
        }
      }

      if (entryIntoForceTime != null && entryIntoForceTime.isNotEmpty) {
        final int _timestamp = DateTime.now().millisecondsSinceEpoch;
        if (_timestamp <= (entryIntoForceTime.first ?? 0) ||
            _timestamp >= (entryIntoForceTime.last ?? 0)) {
          return false;
        }
      }
      return true;
    } catch (err) {
      DevLogger.error(
          tag: Constant.tagCommonUI,
          msg: {'fn': 'isActiveCarousel', 'err': err});
      return false;
    }
  }

  /// 轮播项
  /// [dataSource] 为 自定义[CarouselDataSourceEnum.custom]时，取配置数据[content]
  /// [dataSource] 为 推荐[CarouselDataSourceEnum.recommend]时，取业务数据[data]
  List<AttrCarouseItem?>? getDisplayItems(
      CarouselDataSourceEnum? dataSource,
      final List<AttrCarouseItem?>? data,
      List<AttrCarouseItem>? content,
      String? cityCode) {
    return dataSource == CarouselDataSourceEnum.recommend
        ? data
        : (dataSource == CarouselDataSourceEnum.custom
            ? content
                ?.where((item) => isActiveCarousel(
                    item.region, item.entryIntoForceTime, cityCode))
                .toList()
            : null);
  }
}
