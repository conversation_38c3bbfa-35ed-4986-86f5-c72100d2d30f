/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-03-13 20:01:21
 * @description: 
 */
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/toast_helper.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/models/common_models/empty_scheme_model.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/empty_widget.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/placeholder_image.dart';
import 'package:flutter_common_ui/src/widgets/carousel/carousel_mixin.dart';
import 'package:flutter_common_ui/src/widgets/carousel/multi_carousel.dart';
import 'package:flutter_common_ui/src/widgets/carousel/splash_carousel.dart';
import 'package:flutter_common_ui/src/widgets/carousel/standard_carousel.dart';

/// 轮播组件
/// 标准轮播支持：拖拽、整屏滑动
/// 一页多个支持：拖拽
/// 闪屏
class Carousel extends StatelessWidget with CarouselMixin {
  /// [config] 图片组件的配置信息
  final CarouselModel? config;

  /// [data] 业务接口的图片数据
  final List<AttrCarouseItem?>? data;

  /// [cityCode] 定位的城市编码
  final String? cityCode;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onIndexChanged] 索引变化回调, 只针对标准轮播的整屏滑动模式
  final ValueChanged<int>? onIndexChanged;

  Carousel(
      {Key? key,
      this.config,
      this.data,
      this.cityCode,
      this.isSliver = false,
      this.relative = false,
      this.needReload,
      this.onIndexChanged});

  @override
  Widget build(BuildContext context) {
    final CarouselStyleEnum? _style = config?.attr?.style;
    final List<AttrCarouseItem?>? _items = getDisplayItems(
        config?.attr?.dataSource, data, config?.attr?.content, cityCode);

    /// 轮播组件
    Widget? _carousel;

    /// 没有配置返回空组件
    if (config?.style?.attr == null) {
      return EmptyWidget(
        isSliver: isSliver,
      );
    } else if (_items == null || _items.isEmpty) {
      /**
       emptyScheme: {
         value: 1,
         // 当空数据处理逻辑选择展示默认图片时，该字段生效
         defaultImgInfo: {
           // 默认图片地址
           value: '',
           // 用于回显图片字段
           name:'',
           // 默认链接
           defaultUrl: ''
         },
       }
       */
      EmptySchemeModel? emptyData = config?.attr?.emptyScheme;
      /**
      * 选项包括
      * 1: 展示默认图片
      * 默认为‘隐藏组件和同分组组件’
      */
      if (emptyData != null && emptyData.value == 1) {
        // 空数据占位图片
        String picUrl = emptyData.defaultImgInfo?.picUrl ?? '';
        _carousel = picUrl.isNotEmpty
            ? GestureDetector(
                onTap: () async {
                  final connectivityResult =
                      await (Network.isOnline());
                  if (!isOnline.isOnline) {
                    ToastHelper.showToast(Constant.netWorkError);
                  } else {
                    String defaultUrl = emptyData?.defaultImgInfo?.defaultUrl ?? '';
                    if (defaultUrl.isNotEmpty) {
                      goToPage(defaultUrl);
                    }
                  }
                },
                child: CommonNetWorkImage(
                  url: picUrl,
                  fit: BoxFit.cover,
                  placeHolder: SizedBox(),
                  errorWidget: PlaceHolderImage(),
                  needReload: needReload,
                ),
              )
            : const PlaceHolderImage();
      } else {
        /// 有配置，没有数据返回占位组件
        _carousel = const PlaceHolderImage(); // todo: 占位图片替换空数据图片
      }
    } else {
      _carousel = _style == CarouselStyleEnum.multiple
          // 一页多个
          ? MultiCarousel(
              config: config!, items: _items, needReload: needReload)
          // 闪屏
          : (_style == CarouselStyleEnum.splash
              ? SplashCarousel(
                  config: config!, items: _items, needReload: needReload)
              // 标准轮播
              : (_style == CarouselStyleEnum.standard
                  ? StandardCarousel(
                      config: config!,
                      items: _items,
                      needReload: needReload,
                      onIndexChanged: onIndexChanged,
                    )
                  : const SizedBox()));
    }

    final _child = config!.style!.attr!.toDecorationWidget(
      child: _carousel,
      relative: relative,
    );

    return isSliver ? SliverToBoxAdapter(child: _child) : _child;
  }
}
