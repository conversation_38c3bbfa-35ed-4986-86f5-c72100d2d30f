import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/widgets/carousel/card_swiper.dart';
import 'package:flutter_common_ui/src/widgets/carousel/multi_carousel.dart';

/// 标准轮播
class StandardCarousel extends StatelessWidget {
  const StandardCarousel(
      {Key? key,
      required this.config,
      required this.items,
      this.needReload,
      this.onIndexChanged});

  // [config] 图片组件的配置信息
  final CarouselModel config;

  /// [items] 轮播项
  final List<AttrCarouseItem?> items;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onIndexChanged] 索引变化回调
  final ValueChanged<int>? onIndexChanged;

  @override
  Widget build(BuildContext context) {
    /// 整屏滑动使用Swiper
    /// 拖拽使用ListView
    if (config.attr?.singleNorms?.sliderType == CarouselSlideTypeEnum.swipe) {
      return CardSwiper(
        config: config,
        items: items,
        needReload: needReload,
        onIndexChanged: onIndexChanged,
      );
    } else if (config.attr?.singleNorms?.sliderType ==
        CarouselSlideTypeEnum.scrollHorizontal) {
      return MultiCarousel(
        config: config,
        items: items,
        needReload: needReload,
      );
    }
    return const SizedBox();
  }
}
