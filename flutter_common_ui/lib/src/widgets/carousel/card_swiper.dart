import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/carousel_item.dart';
import 'package:flutter_common_ui/src/widgets/carousel/custom_page_indicator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 标准轮播
class CardSwiper extends StatefulWidget {
  const CardSwiper(
      {Key? key,
      this.config,
      required this.items,
      this.needReload,
      this.onIndexChanged});

  // [config] 图片组件的配置信息
  final CarouselModel? config;

  /// [items] 轮播项
  final List<AttrCarouseItem?> items;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onIndexChanged] 索引变化回调
  final ValueChanged<int>? onIndexChanged;

  @override
  State<CardSwiper> createState() => _CardSwiperState();
}

class _CardSwiperState extends State<CardSwiper> {
  /// [_key] 轮播个数不一样的时候更新
  UniqueKey _key = UniqueKey();

  // 轮播的controller
  SwiperController? _swiperController;

  /// 是否自动播放
  bool autoPlay = true;

  @override
  void initState() {
    super.initState();
    _swiperController = new SwiperController();
  }

  @override
  void didUpdateWidget(CardSwiper oldWidget) {
    if (oldWidget.items.length != widget.items.length) {
      setState(() {
        _key = UniqueKey();
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _swiperController?.dispose();
    super.dispose();
  }

  /// 轮播子项builder
  Widget _itemBuilder(BuildContext context, int index) {
    return CarouselItem(
      key: ValueKey(
          '$index-${widget.items[index]?.id ?? ''}-${widget.items[index]?.pictureUrl ?? ''}'),
      index: index,
      item: widget.items[index],
      needReload: widget.needReload,
      trackInfo: widget.config?.attr?.trackInfo,
      radius: widget.config?.attr?.radius,
      shadow: widget.config?.attr?.shadow,
      addRepaintBoundaries: false,
    );
  }

  /// 分页器
  SwiperPlugin? _pagination() {
    return widget.items.length == 1
        ? null
        : SwiperCustomPagination(
            builder: (BuildContext context, SwiperPluginConfig swiperConfig) {
              return CustomPageIndicator(
                  swiperWidth: widget.config?.style?.attr?.width?.w ?? 0,
                  count: widget.items.length,
                  config: swiperConfig);
            },
          );
  }

  void onVisibilityChanged(VisibilityInfo info) {
    if (info.visibleFraction <= 0 && autoPlay == true) {
      _swiperController?.stopAutoplay();
      autoPlay = false;
    } else if (info.visibleFraction > 0 && autoPlay == false) {
      _swiperController?.startAutoplay();
      autoPlay = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: VisibilityDetector(
        key: _key,
        onVisibilityChanged: onVisibilityChanged,
        child: Swiper(
          /// 轮播个数不一样时，更新key
          key: _key,
          controller: _swiperController,
          itemBuilder: _itemBuilder,
          autoplay: widget.items.length > 1,
          loop: widget.items.length > 1,
          itemCount: widget.items.length,
          pagination: _pagination(),
          onIndexChanged: widget.onIndexChanged,
        ),
      ),
    );
  }
}
