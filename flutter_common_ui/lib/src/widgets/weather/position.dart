/*
 * @Author: ma<PERSON><PERSON><PERSON> ma<PERSON>@haier.com
 * @Date: 2023-03-02 10:34:49
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/weather/city_data_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:location/location.dart';

class Position extends StatelessWidget {
  final WeatherConfig? weatherConfig;
  final CityDataModel? locationData;
  final bool? isTouristMode;
  const Position({
    Key? key,
    required this.weatherConfig,
    this.locationData,
    this.isTouristMode,
  }) : super(key: key);
  // 点击城市
  // 么有权限的话需要申请权限，如果同意，则跳转城市页面，否则，不处理
  _handleTapCity() async {
    try {
      // 查询定位权限
      if (isTouristMode != null && isTouristMode != true) {
        await Location.getLocation(isNeedRequestPermission: true);
      }
      goToPage(Constant.SELECT_CITY);
    } catch (err) {
      goToPage(Constant.SELECT_CITY);
      DevLogger.debug(
          tag: Constant.tagCommonUI, msg: {'fn': '_handleTapCity', 'err': err});
    }
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(9.w),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _handleTapCity,
        child: Container(
          height: 18.w,
          color: weatherConfig?.posBackColor?.color,
          padding: EdgeInsets.only(left: 4.w, right: 4.w),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Image.asset(
                  'assets/images/icon_loc_white.png',
                  width: 12.w,
                  height: 12.w,
                  package: Constant.packageName,
                ),
                Container(
                    margin: EdgeInsets.only(left: 1.w),
                    constraints: BoxConstraints(maxWidth: 48.w),
                    child: Text(
                      locationData?.areaName != null
                          ? locationData!.areaName
                          : '--',
                      softWrap: false,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontWeight: TextCommonUtils.getFontWeightFromString(
                              weatherConfig?.posFontWeight),
                          fontSize: weatherConfig?.posFontSize?.sp,
                          color: weatherConfig?.posColor?.color),
                    ))
              ]),
        ),
      ),
    );
  }
}
