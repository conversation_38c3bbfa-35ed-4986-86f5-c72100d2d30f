/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-02 14:59:24
 * @description: 天气样式为 文字天气卡片
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/weather/city_data_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_widget_model.dart';
import 'package:flutter_common_ui/src/widgets/weather/position.dart';
import 'package:flutter_common_ui/src/widgets/weather/weather_data.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class WeatherText extends StatelessWidget {
  /// [attr]直播组件的配置信息
  final WeatherAttr? attr;

  /// 高度
  final double? height;

  /// 天气温度 湿度等数据
  final WeatherPayloadModel? weatherData;

  /// 定位信息
  final CityDataModel? locationData;

  /// 游客模式
  final bool? isTouristMode;
  const WeatherText(
      {Key? key,
      this.attr,
      this.height,
      this.weatherData,
      this.locationData,
      this.isTouristMode})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        // 城市
        Position(
            weatherConfig: attr!.weatherConfig!,
            locationData: locationData,
            isTouristMode: isTouristMode),
        // 天气
        Flexible(
            child: Container(
                decoration: BoxDecoration(
                  // borderRadius目前可视化不可配置，根据UI 效果 固定
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.w),
                    topRight: attr!.radius != null
                        ? attr!.radius!.borderRadius.topRight
                        : Radius.zero,
                    bottomLeft: Radius.circular(0.w),
                    bottomRight: attr!.radius != null
                        ? attr!.radius!.borderRadius.bottomRight
                        : Radius.zero,
                  ),
                  // 颜色目前可视化不可配置，根据UI 效果 固定
                  color: Color.fromRGBO(255, 255, 255, 0.80),
                ),
                height: 32.w,
                margin: EdgeInsets.only(left: 12.w),
                child: WeatherData(
                    height: height,
                    weatherConfig: attr?.weatherConfig,
                    weatherData: weatherData,
                    type: attr?.style)))
      ],
    );
  }
}
