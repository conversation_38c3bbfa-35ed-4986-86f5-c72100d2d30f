/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-28 14:29:08
 * @description: 
 */
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_widget_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 天气
class WeatherData extends StatelessWidget {
  /// 天气数据
  final WeatherPayloadModel? weatherData;

  /// 天气样式
  final WeatherConfig? weatherConfig;

  /// 天气数据的高度
  final double? height;
  final AttrWeatherStyle? type;
  WeatherData(
      {Key? key, this.weatherData, this.weatherConfig, this.type, this.height})
      : super(key: key);

  String _getWeatherStr() {
    String _weather = '';
    try {
      if (weatherData != null) {
        // 取当天时间，格式eg: 2021-01-25，  接口需要
        String date = (new DateTime.now()).toString().split(' ')[0];
        String tempMin = weatherData?.dayForecast != null &&
                weatherData!.dayForecast!.containsKey(date)
            ? weatherData!.dayForecast![date]?.tempMin ?? '-'
            : '-';
        String tempMax = weatherData?.dayForecast != null &&
                weatherData!.dayForecast!.containsKey(date)
            ? weatherData!.dayForecast![date]?.tempMax ?? '-'
            : '-';
        var weather = weatherData?.weatherCondition?.condition ?? '-';
        var humidity = weatherData?.weatherCondition?.humidity ?? '-';
        var pm25 = weatherData?.airCondition?.pm25 ?? '-';
        if (weatherConfig != null && type == AttrWeatherStyle.one) {
          _weather =
              ' $tempMin℃/$tempMax℃   $weather丨湿度 $humidity%丨PM2.5 $pm25';
        } else if (weatherConfig != null && type == AttrWeatherStyle.two) {
          _weather = ' $tempMin°/$tempMax° $weather丨湿度 $humidity%丨PM2.5 $pm25';
        }
      } else {
        _weather = ' —/—   —   湿度  —   PM2.5  —';
      }
    } catch (e) {
      _weather = ' —/—   —   湿度  —   PM2.5  —';
    }
    return _weather;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 240.w),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Container(
                margin: EdgeInsets.only(left: 5.w),
                child: Text(
                  _getWeatherStr(),
                  maxLines: 1,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      textBaseline: TextBaseline.alphabetic,
                      fontSize: weatherConfig?.fontSize?.sp,
                      fontWeight: TextCommonUtils.getFontWeightFromString(
                          weatherConfig?.fontWeight),
                      // 为了和智家保持一致，屏蔽height
                      // height: 1.16,
                      fontFamily: 'PingFangSC-Regular',
                      color: weatherConfig?.color?.color),
                ),
              ),
            ))
      ],
    );
  }
}
