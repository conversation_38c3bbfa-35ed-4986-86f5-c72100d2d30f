/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-27 19:07:50
 * @description: 天气组件UI
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/weather/city_data_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_widget_model.dart';
import 'package:flutter_common_ui/src/widgets/weather/weather_avatar.dart';
import 'package:flutter_common_ui/src/widgets/weather/weather_text.dart';

class WeatherScreen extends StatelessWidget {
  /// [attr]直播组件的配置信息
  final WeatherAttr? attr;

  /// [height] 内容区域高度
  final double? height;

  /// [width] 内容区域宽度
  final double? width;

  /// [weatherData]天气温度 湿度等数据
  final WeatherPayloadModel? weatherData;

  /// [locationData]定位信息
  final CityDataModel? locationData;

  /// [materialData]背景、头像、描述的数据
  final MaterialData? materialData;

  /// [avatorUrl]智慧小屋头像
  final String? avatorUrl;

  /// [isTouristMode]游客模式
  final bool? isTouristMode;

  const WeatherScreen({
    Key? key,
    this.attr,
    this.height,
    this.width,
    this.weatherData,
    this.locationData,
    this.materialData,
    this.avatorUrl,
    this.isTouristMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// attr?.style == 1 文字天气卡片  2 === 头像天气卡片
    return Align(
        child: Container(
            decoration: BoxDecoration(
              boxShadow: attr?.shadow?.boxShadow,
              borderRadius: attr?.radius?.borderRadius,
            ),
            child: attr?.style == AttrWeatherStyle.two
                ? WeatherAvatar(
                    attr: attr,
                    height: height,
                    width: width,
                    weatherData: weatherData,
                    locationData: locationData,
                    materialData: materialData,
                    avatorUrl: avatorUrl,
                    isTouristMode: isTouristMode)
                : WeatherText(
                    attr: attr,
                    height: height,
                    weatherData: weatherData,
                    locationData: locationData,
                    isTouristMode: isTouristMode)));
  }
}
