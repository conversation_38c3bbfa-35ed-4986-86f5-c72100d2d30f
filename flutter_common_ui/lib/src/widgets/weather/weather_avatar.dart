/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-02 15:03:26
 * @description: 天气样式为 头像天气卡片
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/weather/city_data_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_widget_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/custom_card.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/placeholder_image.dart';
import 'package:flutter_common_ui/src/widgets/weather/position.dart';
import 'package:flutter_common_ui/src/widgets/weather/weather_data.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_common_ui/src/widgets/weather/gif_view.dart';

class WeatherAvatar extends StatelessWidget {
  /// 【config】直播组件的配置信息
  final WeatherAttr? attr;

  ///
  final double? height;

  final double? width;

  /// 天气温度 湿度等数据
  final WeatherPayloadModel? weatherData;

  /// 定位信息
  final CityDataModel? locationData;

  /// 背景、头像、描述的数据
  final MaterialData? materialData;

  /// 智慧小屋头像
  final String? avatorUrl;

  /// 游客模式
  final bool? isTouristMode;

  const WeatherAvatar({
    Key? key,
    this.attr,
    this.height,
    this.width,
    this.weatherData,
    this.locationData,
    this.materialData,
    this.avatorUrl,
    this.isTouristMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 智慧小屋头像
    // todo 根据接口返回的数据 更细头像
    Widget headImage() {
      return Flexible(
          child: Container(
              margin: EdgeInsets.only(bottom: 2.36.w),
              child: avatorUrl != '' && avatorUrl != null
                  ? GestureDetector(
                      child: RepaintBoundary(
                        // 如果是 GIF、webp 图片，则走GifView逻辑
                        child: avatorUrl!.indexOf('.gif') > 0 ||
                                avatorUrl!.indexOf('.webp') > 0
                            ? GifView.network(
                                avatorUrl!,
                                width: 50.w,
                                height: 63.64.w,
                                fit: BoxFit.fill,
                              )
                            : Image.network(
                                avatorUrl!,
                                width: 50.w,
                                height: 63.64.w,
                                fit: BoxFit.fill,
                              ),
                      ),
                      onTap: () {
                        goToPage(Constant.smallRoomHead);
                      },
                    )
                  : Container(
                      width: 50.w,
                      height: 63.64.w,
                    )));
    }

    // 天气背景图片
    Widget weatherBgImage() {
      // 背景图片
      String bgImage = '';
      if (materialData != null &&
          materialData?.backgroundUrl != null &&
          materialData?.backgroundUrl != '') {
        bgImage = materialData!.backgroundUrl!;
      } else if (attr!.cardConfig != null &&
          attr!.cardConfig!.backImg != null &&
          attr!.cardConfig!.backImg != '') {
        bgImage = attr!.cardConfig!.backImg!;
      } else {
        bgImage = '';
      }
      return Positioned(
        bottom: 0,
        child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(12.w)),
            child: CustomCard(
                isShowBorder: false,
                isShowShadow: false,
                hasBlur: true,
                redius: 12.w,
                topLeftRedius:
                    attr!.radius != null ? attr!.radius!.lt?.w : 30.w,
                topRightRedius:
                    attr!.radius != null ? attr!.radius!.rt?.w : 30.w,
                bottomLeftRedius:
                    attr!.radius != null ? attr!.radius!.lb?.w : 12.w,
                bottomRightRedius:
                    attr!.radius != null ? attr!.radius!.rb?.w : 12.w,
                child: bgImage != ''
                    ? CommonNetWorkImage(
                        url: bgImage,
                        height: height,
                        width: width,
                        fit: BoxFit.cover,
                        errorWidget: PlaceHolderImage(),
                      )
                    : Image.asset(
                        'assets/images/weater_default.png',
                        height: height,
                        width: width,
                        package: Constant.packageName,
                        fit: BoxFit.cover,
                      ))),
      );
    }

    return Stack(
      clipBehavior: Clip.none,
      fit: StackFit.expand,
      children: [
        // 背景图片
        weatherBgImage(),
        Row(crossAxisAlignment: CrossAxisAlignment.end, children: <Widget>[
          Container(
            margin: EdgeInsets.only(left: 12.w, bottom: 6.w, right: 12.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // 小屋头像
                headImage(),
                // 城市
                Position(
                    weatherConfig: attr!.weatherConfig!,
                    isTouristMode: isTouristMode,
                    locationData: locationData)
              ],
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(bottom: 0.w),
              height: 68.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 文字
                  ConstrainedBox(
                    constraints: BoxConstraints(maxHeight: 35.w),
                    child: Container(
                      child: Text(
                          materialData != null &&
                                  materialData?.contentDesc != null &&
                                  materialData?.contentDesc != ''
                              ? materialData!.contentDesc!
                              : attr?.cardConfig?.text ?? '',
                          style: TextStyle(
                            color: attr?.cardConfig?.color?.color,
                            fontSize: attr?.cardConfig?.fontSize?.sp,
                            fontWeight: TextCommonUtils.getFontWeightFromString(
                                attr?.cardConfig?.fontWeight),
                          ),
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                          maxLines: 1),
                    ),
                  ),

                  // 天气
                  Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20.w),
                          bottomLeft: Radius.circular(0.w),
                          bottomRight: attr!.radius != null
                              ? attr!.radius!.borderRadius.bottomRight
                              : Radius.zero,
                        ),
                        color: Color.fromRGBO(255, 255, 255, 0.64),
                      ),
                      height: 32.w,
                      child: WeatherData(
                          height: height,
                          weatherConfig: attr?.weatherConfig,
                          weatherData: weatherData,
                          type: attr?.style))
                ],
              ),
            ),
          )
        ])
      ],
    );
  }
}
