/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-27 19:04:17
 * @description: 天气组件
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/weather/city_data_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_model.dart';
import 'package:flutter_common_ui/src/models/weather/weather_widget_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/weather/weather_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Weather extends StatelessWidget {
  /// [config] 图片组件的配置信息
  final WeatherModel? config;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [weatherData]天气温度 湿度等数据
  final WeatherPayloadModel? weatherData;

  /// [locationData]定位信息 显示的位置信息，比如：雁塔区
  final CityDataModel? locationData;

  /// [materialData]背景、头像、描述的数据
  final MaterialData? materialData;

  /// [avatorUrl]智慧小屋头像
  final String? avatorUrl;

  /// [isTouristMode]游客模式
  final bool? isTouristMode;

  /// 绑定事件方法
  final Function? onTap;
  const Weather(
      {Key? key,
      this.config,
      this.isSliver = false,
      this.weatherData,
      this.locationData,
      this.materialData,
      this.avatorUrl,
      this.isTouristMode,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// 计算宽度和高度

    double height = config?.style?.attr?.height ?? 0;
    double width = config?.style?.attr?.width ?? 0;
    double paddingTop = (config?.style?.attr?.padding?.top ?? 0) + 10;
    double paddingBottom = config?.style?.attr?.padding?.bottom ?? 0;
    double paddingLeft = config?.style?.attr?.padding?.left ?? 0;
    double paddingRight = config?.style?.attr?.padding?.right ?? 0;
    double contentHeight = height - paddingTop - paddingBottom;
    double contentWidth = width - paddingLeft - paddingRight;
    final _child = config!.style!.attr!.toDecorationWidget(
        aliasName: config?.alias,
        type: config?.type,
        child: Container(
            padding: config?.style?.attr?.padding?.padding,
            width: width.w,
            height: height.w,
            child: TrackWidget(
                WeatherScreen(
                    height: contentHeight.w,
                    width: contentWidth.w,
                    attr: config?.attr,
                    weatherData: weatherData,
                    locationData: locationData,
                    materialData: materialData,
                    avatorUrl: avatorUrl,
                    isTouristMode: isTouristMode),
                key: ValueKey(config?.aliasUnit ?? 'weather'), onTap: () {
              if (onTap != null) onTap!(config?.customFeature, context);
            },
                trackInfo: config?.attr?.trackInfo,
                trackMap: {},
                onExpose: () {})));
    return isSliver == true ? SliverToBoxAdapter(child: _child) : _child;
  }
}
