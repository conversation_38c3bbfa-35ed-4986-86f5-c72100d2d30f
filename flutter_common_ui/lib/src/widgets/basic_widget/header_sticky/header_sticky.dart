/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-21 13:49:45
 * @description: 置顶组件
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/header_sticky/sliver_header_delegate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HeaderSticky extends StatelessWidget {
  final Widget child;
  final double fixedHeight;
  const HeaderSticky({Key? key, required this.child, required this.fixedHeight})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverPersistentHeader(
        pinned: true, // 是否固定在顶部
        floating: false,
        delegate: SliverHeaderDelegate.fixedHeight(
            height: fixedHeight.w, //收起的高度
            child: child));
  }
}
