import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gradient_borders/gradient_borders.dart';

import '../../color/semantic_colors/app_semantic_colors.dart';
import '../../common/constant.dart';

class NetworkUnavailable extends StatelessWidget {
  const NetworkUnavailable({super.key, required this.netAvailable});

  final bool netAvailable;

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: netAvailable ? 0 : 44,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: const GradientBoxBorder(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              // -83度
              transform: GradientRotation(pi / 180 * -83),
              colors: <Color>[
                Color.fromRGBO(255, 255, 255, 1),
                Color.fromRGBO(255, 255, 255, 0),
                Color.fromRGBO(255, 255, 255, 0),
                Color.fromRGBO(255, 255, 255, .5)
              ],
              stops: <double>[0, .63, .72, 1],
            ),
          ),
          borderRadius: BorderRadius.circular(16),
          color: AppSemanticColors.container.notice,
        ),
        child: Row(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Image.asset(
                'assets/images/no_net.webp',
                package: Constant.packageName,
                width: 20,
                height: 20,
              ),
            ),
            Text(
              '网络不可用，请检查网络连接',
              style: TextStyle(
                fontSize: 14,
                color: AppSemanticColors.item.primary,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
