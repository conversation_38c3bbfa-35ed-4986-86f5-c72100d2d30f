import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 带边框和阴影的卡片
/// 边框宽度1.5 白色透明度0.8 从上至下渐变
/// 阴影 黑色透明度0.08
class CustomCard extends StatelessWidget {
  const CustomCard(
      {Key? key,

      /// 卡片宽度
      this.width,

      /// 卡片高度
      this.height,

      /// 卡片背景、边框、阴影的透明度 （用于未启用卡片）
      this.opacity,

      /// 卡片倒角
      /// 默认4个角统一为这个值
      this.redius,

      /// 子元素
      required this.child,

      /// 右上倒角
      /// 如果设置了会覆盖右上角的倒角，但不影响其他角
      this.topRightRedius,

      /// 左上倒角
      /// 如果设置了会覆盖左上角的倒角，但不影响其他角
      this.topLeftRedius,

      /// 右下倒角
      /// 如果设置了会覆盖右下角的倒角，但不影响其他角
      this.bottomRightRedius,

      /// 左下圆角
      /// 如果设置了会覆盖左下角的倒角，但不影响其他角
      this.bottomLeftRedius,

      /// 卡片是否有毛玻璃
      this.hasBlur,
      this.isShowBorder,
      this.isShowShadow,
      this.blur,
      this.source})
      : super(key: key);

  final double? width;
  final double? height;
  final double? redius;
  final Widget child;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  final bool? hasBlur;
  final bool? isShowShadow;
  final bool? isShowBorder;
  final double? blur;
  final String? source;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? null,
      height: height ?? null,
      child: CustomPaint(
        painter: isShowShadow != null && isShowShadow == true
            ? ShadowPainter(
                redius: redius,
                topRightRedius: topRightRedius,
                topLeftRedius: topLeftRedius,
                bottomRightRedius: bottomRightRedius,
                bottomLeftRedius: bottomLeftRedius,
                source: source,
              )
            : null,
        foregroundPainter: isShowBorder != null && isShowBorder == true
            ? BorderPainer(
                redius: redius,
                topRightRedius: topRightRedius,
                topLeftRedius: topLeftRedius,
                bottomRightRedius: bottomRightRedius,
                bottomLeftRedius: bottomLeftRedius,
              )
            : null,
        child: ClipRRect(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(topLeftRedius ?? redius ?? 36.w),
            topRight: Radius.circular(topRightRedius ?? redius ?? 36.w),
            bottomLeft: Radius.circular(bottomLeftRedius ?? redius ?? 36.w),
            bottomRight: Radius.circular(bottomRightRedius ?? redius ?? 36.w),
          ),
          child: hasBlur != null && hasBlur == true
              ? BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 14.0, sigmaY: 14.0),
                  child: Container(
                    color: Colors.white.withOpacity((opacity ?? 0.3)),
                    child: child,
                  ),
                )
              : child,
        ),
      ),
    );
  }
}

/// 卡片阴影Painer
/// 黑色透明度0.08
/// 模糊 x = 2 y = 4
/// 向下偏移4px
class ShadowPainter extends CustomPainter {
  ShadowPainter(
      {this.redius,
      this.topRightRedius,
      this.topLeftRedius,
      this.bottomRightRedius,
      this.bottomLeftRedius,
      this.opacity,
      this.source});

  final double? redius;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  final String? source;
  @override
  void paint(Canvas canvas, Size size) {
    // 阴影
    //创建画笔
    Paint shadow = Paint()
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 12.24.w
      ..color = Colors.black.withOpacity(0.02)
      ..imageFilter = ImageFilter.blur(sigmaX: 2, sigmaY: 4);

    RRect rRect = RRect.fromLTRBAndCorners(
      1,
      source != null && source == 'weather' ? (18 + 72).w : 18.w,
      size.width - 4.15.w,
      size.height + 1,
      topLeft: Radius.circular(topLeftRedius ?? redius ?? 36.w),
      topRight: Radius.circular(topRightRedius ?? redius ?? 36.w),
      bottomLeft: Radius.circular(bottomLeftRedius ?? redius ?? 36.w),
      bottomRight: Radius.circular(bottomRightRedius ?? redius ?? 36.w),
    );

    canvas.drawRRect(rRect, shadow);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

/// 边框Painer
/// 白色 透明度0.08
/// 宽度1.5
/// 从卡片上到下渐变
class BorderPainer extends CustomPainter {
  BorderPainer({
    this.redius,
    this.topRightRedius,
    this.topLeftRedius,
    this.bottomRightRedius,
    this.bottomLeftRedius,
    this.opacity,
  });

  final double? redius;
  final double? topRightRedius;
  final double? topLeftRedius;
  final double? bottomRightRedius;
  final double? bottomLeftRedius;
  final double? opacity;
  @override
  void paint(Canvas canvas, Size size) {
    // 高光
    //创建画笔
    Paint highlight = Paint()
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.32.w
      ..color = Colors.white.withOpacity(0.5);

    Rect rect = Rect.fromLTRB(
      0.5,
      0.5,
      size.width - 1.8.w,
      size.height,
    );
    RRect rRect = RRect.fromLTRBAndCorners(
      0.5,
      0.5,
      size.width - 1.8.w,
      size.height,
      topLeft: Radius.circular(topLeftRedius ?? redius ?? 36.w),
      topRight: Radius.circular(topRightRedius ?? redius ?? 36.w),
      bottomLeft: Radius.circular(bottomLeftRedius ?? redius ?? 36.w),
      bottomRight: Radius.circular(bottomRightRedius ?? redius ?? 36.w),
    );

    highlight.shader = LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      colors: [Colors.white.withOpacity(0), Colors.white.withOpacity(0.5)],
    ).createShader(
      rect,
    );
    canvas.drawRRect(rRect, highlight);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
