/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-10 19:46:27
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/animated_corner_mark/corner_mark.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/splash_animation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 动态角标
/// 尺寸: 300ms: 80->100%; 2900ms: 100%; 100ms: 100->60%
/// 透明度: 300ms: 0->100%; 2900ms: 100%; 100ms: 100->0%
class AnimatedCornerMark extends StatelessWidget {
  const AnimatedCornerMark({Key? key, this.attrCornerMark, this.isShowDot});

  /// [attrCornerMark] 角标
  final AttrCornerMark? attrCornerMark;

  /// [isShowDot] 小红点是否显示
  final bool? isShowDot;
  int? get count => attrCornerMark?.textContent?.length;
  @override
  Widget build(BuildContext context) {
    /// 红点
    if (attrCornerMark?.type == AttrCornerMarkTypeEnum.redDot &&
        isShowDot == true) {
      return CornerMark(
        attrCornerMark: attrCornerMark,
      );
    }

    /// 动态文字
    if (attrCornerMark?.type == AttrCornerMarkTypeEnum.text) {
      if (count == null) {
        return const SizedBox();
      }
      if (count != null && count == 1) {
        return CornerMark(
          attrCornerMark: attrCornerMark,
        );
      }
      return SplashAnimation(
          origin: Offset(-19.w, 7.w),
          sizeScaleForwardBegin: 0.8,
          sizeScaleForwardEnd: 1,
          sizeScaleReverseEnd: 0.6,
          opacityForwardBegin: 0,
          opacityForwardEnd: 1,
          opacityReverseEnd: 0,
          duration: 3300,
          forwardDuration: 300,
          freezeDuration: 2900,
          reverseDuration: 100,
          builder: (context, controller) {
            return CornerMark(
              animationController: controller,
              attrCornerMark: attrCornerMark,
            );
          });
    }

    /// 其他情况返回空
    return const SizedBox();
  }
}
