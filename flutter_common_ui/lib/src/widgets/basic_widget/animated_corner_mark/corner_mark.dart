/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-20 14:36:12
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/widgets/menu/menu_red_point.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 角标
class CornerMark extends StatefulWidget {
  /// [attrCornerMark] 角标
  final AttrCornerMark? attrCornerMark;

  /// [animationController] 动画controller
  final AnimationController? animationController;

  CornerMark({Key? key, this.attrCornerMark, this.animationController});

  @override
  State<CornerMark> createState() => _CornerMarkState();
}

class _CornerMarkState extends State<CornerMark> {
  /// [_i] 文字循环的索引
  int _i = 0;

  @override
  void initState() {
    super.initState();
    // 角标文字 >=1 的时候，循环展示
    if (widget.attrCornerMark?.textContent?.length != null &&
        widget.attrCornerMark!.textContent!.length >= 1) {
      widget.animationController?.addStatusListener(statusListenerCallback);
    }
  }

  @override
  void dispose() {
    widget.animationController?.removeStatusListener(statusListenerCallback);
    super.dispose();
  }

  /// 动画状态变化的回调
  void statusListenerCallback(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      setState(() {
        _i = (_i + 1) % widget.attrCornerMark!.textContent!.length;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.attrCornerMark?.type != null) {
      switch (widget.attrCornerMark!.type) {
        case AttrCornerMarkTypeEnum.text:
          return textSuperscript();
        case AttrCornerMarkTypeEnum.redDot:
          return MenuRedPoint();
        default:
          return const SizedBox();
      }
    }
    return const SizedBox();
  }

  /// 动态文字角标
  /// 动态角标配置右上角，高出图片/卡片4px,右对齐
  /// 注意：配置的时候需要给图片、卡片导航留出内上边距4px
  Widget textSuperscript() {
    if (widget.attrCornerMark?.textContent?.length != null) {
      return widget.attrCornerMark!.toCornerMarkDecorationWidget(
          child: Center(
              child: Text(
        widget.attrCornerMark!.textContent![_i].text ?? '',
        style: TextStyle(
            color: Color(0xffffffff),
            fontFamily: 'PingFangSC-Medium',
            fontSize: 10.sp,
            fontWeight: FontWeight.w500),
      )));
    }
    return const SizedBox();
  }
}
