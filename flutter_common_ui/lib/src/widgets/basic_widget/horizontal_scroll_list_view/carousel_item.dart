import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/utils/extension.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/animated_corner_mark/animated_corner_mark.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/placeholder_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:json_annotation/json_annotation.dart';

/// 轮播项组件
class CarouselItem extends StatelessWidget {
  final Key? key;

  /// [width] 轮播项的宽
  final double? width;

  /// [padding] 轮播项的内边距
  final EdgeInsets? padding;

  /// [radius] 轮播项图片圆角
  final RadiusModel? radius;

  /// [shadow]轮播项阴影
  final ShadowModel? shadow;

  /// [item] 轮播项
  final AttrCarouseItem? item;

  /// [needReload] 是否需要重新加载图片
  final int? needReload;

  /// [index] 轮播项的索引
  final int index;

  /// 角标类型
  final AttrCornerMark? attrCornerMark;

  /// [trackInfo] 打点配置
  final TrackInfoModel? trackInfo;

  /// [customFeature] 绑定事件
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  /// [fit]图片的fit方式
  final BoxFit? fit;

  /// 小红点是否显示
  final bool? isShowDot;

  /// [addRepaintBoundaries] 是否需要添加RepaintBoundary
  final bool? addRepaintBoundaries;

  /// [hyperlink] 跳转的链接，打点使用
  String get hyperlink {
    final String? navUrl = customFeature?.eventParams?.nav_url;
    return navUrl != null && navUrl != '' ? navUrl : (item?.detailsUrl ?? '');
  }

  /// [onTap]绑定事件方法、小红点额外处理逻辑，点击过的 缓存起来，不再显示小红点
  final Function? onTap;

  /// 遍历[dataAttributionList]获得打点的属性值，使用','连接，打点使用
  /// [key] [dataAttributionList]的属性名
  String _getDataAttributionListValue(String key) {
    return item?.dataAttributionList
            ?.map((item) => _getAttributionValue(key, item))
            .join(',') ??
        '';
  }

  /// 根据key获得attribution的属性值，打点使用
  String _getAttributionValue(
      String key, AttrDataAttributionModel? attribution) {
    switch (key) {
      case 'industryCode':
        return attribution?.industryCode ?? '';
      case 'industryValue':
        return attribution?.industryValue ?? '';
      case 'microCode':
        return attribution?.microCode ?? '';
      case 'microName':
        return attribution?.microValue ?? '';
      default:
        return '';
    }
  }

  const CarouselItem(
      {this.key,
      this.width,
      this.padding,
      this.radius,
      this.shadow,
      this.item,
      this.needReload,
      this.attrCornerMark,
      required this.index,
      this.trackInfo,
      this.customFeature,
      this.fit,
      this.isShowDot,
      this.addRepaintBoundaries,
      this.onTap});

  /// 点击事件
  /// [index] 为轮播项索引
  void _onTap(BuildContext context, int index) {
    onTap != null
        ? onTap!(item, customFeature, context)
        : goToPage(item?.detailsUrl);
  }

  Map<String, dynamic> getFullTrackMap() {
    return {
      'hyperlink': hyperlink,
      'content_title': item?.title,
      'site_number': index.toString(),
      'content_attribution_id': _getDataAttributionListValue('industryCode'),
      'content_attribution_name': _getDataAttributionListValue('industryValue'),
      'microCode': _getDataAttributionListValue('microCode'),
      'microName': _getDataAttributionListValue('microValue'),
      'material_id': item?.id?.toString(),
      "material_name": item?.materialName,
      "operation_plan_id": item?.planId
    };
  }

  @override
  Widget build(BuildContext context) {
    if (item?.pictureUrl == null || item?.pictureUrl == '') {
      return const SizedBox();
    }
    final Widget child = CarouselItemContent(
      width: width,
      padding: padding,
      radius: radius,
      shadow: shadow,
      item: item,
      needReload: needReload,
      fit: fit,
      addRepaintBoundaries: addRepaintBoundaries,
    );
    return TrackWidget(
        attrCornerMark?.type == null ||
                attrCornerMark?.type == AttrCornerMarkTypeEnum.none
            ? child
            : Stack(children: [
                child,
                Positioned(
                    right: 0.w,
                    top: 0.w,
                    child: AnimatedCornerMark(
                        attrCornerMark: attrCornerMark, isShowDot: isShowDot))
              ]),
        key: key,
        trackInfo: trackInfo,
        trackMap: getFullTrackMap(),
        onTap: () => _onTap(context, index));
  }
}

class CarouselItemContent extends StatelessWidget {
  const CarouselItemContent(
      {Key? key,
      this.width,
      this.padding,
      this.radius,
      this.shadow,
      this.item,
      this.needReload,
      this.fit,
      this.addRepaintBoundaries});

  /// [width] 轮播项的宽
  final double? width;

  /// [padding] 轮播项的内边距
  final EdgeInsets? padding;

  /// [radius] 轮播项图片圆角
  final RadiusModel? radius;

  /// [shadow]轮播项阴影
  final ShadowModel? shadow;

  /// [item] 轮播项
  final AttrCarouseItem? item;

  /// [needReload] 是否需要重新加载图片
  final int? needReload;

  /// [fit]图片的fit方式
  final BoxFit? fit;

  /// [addRepaintBoundaries] 是否需要添加RepaintBoundary
  final bool? addRepaintBoundaries;

  @override
  Widget build(BuildContext context) {
    final List<BoxShadow>? boxShadow = CommonUtil.toBoxShadow(shadow);
    EdgeInsets _margin = EdgeInsets.all(0);
    if (boxShadow?.length != null && boxShadow!.length > 0) {
      BoxShadow shadow = boxShadow[0];

      /// 留出阴影空间
      _margin = _margin.mergeShadow(shadow: shadow);
      _margin = _margin.isNonNegative ? _margin : EdgeInsets.zero;
    }
    final Widget content = ClipRRect(
        borderRadius: radius?.borderRadius ?? BorderRadius.zero,
        // 遗留问题：加height: double.infinity，fit的值，为BoxFit.cover 时，是否有问题？？？？
        // 之前遇到过，后续发现没有问题了，不知道为什么？？？？？
        child: Container(
            margin: _margin,
            decoration: BoxDecoration(
              boxShadow: boxShadow,
            ),
            child: CommonNetWorkImage(
                url: item?.pictureUrl,
                width: width,
                fit: fit ?? BoxFit.cover,
                errorWidget: PlaceHolderImage(),
                needReload: needReload,
                addRepaintBoundaries: addRepaintBoundaries)));
    return Container(
        width: width,
        padding: padding,
        height: double.infinity,

        /// 添加Center是给导航卡片使用，否则阴影会充满垂直方向
        child: fit == BoxFit.contain ? Center(child: content) : content);
  }
}
