import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/page_indicator_screen.dart';

class HorizontalScrollPagination extends StatefulWidget {
  const HorizontalScrollPagination(
      {Key? key, this.controller, this.count = 1, this.itemWidth});

  /// [controller] 滚动控制器
  final ScrollController? controller;

  /// [count] 轮播项个数
  final int count;

  /// [itemWidth] 轮播项宽度
  final double? itemWidth;

  @override
  State<HorizontalScrollPagination> createState() =>
      _HorizontalScrollPaginationState();
}

class _HorizontalScrollPaginationState
    extends State<HorizontalScrollPagination> {
  /// 分页滑动的距离，最小值:0.0, 最大值: 轮播个数
  double page = 0.0;

  ScrollController? _controller;
  @override
  void initState() {
    super.initState();
    _controller = widget.controller;
    _controller?.addListener(_pagination);
  }

  @override
  void didUpdateWidget(HorizontalScrollPagination oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.controller != widget.controller) {
      if (oldWidget.controller != null) {
        oldWidget.controller!.removeListener(_pagination);
        _controller = oldWidget.controller;
        _controller!.addListener(_pagination);
      }
    }
  }

  /// 分页监听
  void _pagination() {
    /// 滚动位置是否attach了[scrollController]
    if (_controller != null && _controller!.hasClients) {
      final double maxScrollExtent = _controller!.position.maxScrollExtent;
      final double minScrollExtent = _controller!.position.minScrollExtent;
      final double pixels = _controller!.position.pixels;

      /// 一页轮播的宽度
      if (pixels >= minScrollExtent &&
          pixels <= maxScrollExtent &&
          widget.itemWidth != null &&
          widget.itemWidth! > 0.0) {
        setState(() {
          page = pixels / widget.itemWidth!;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageIndicatorScreen(
        swiperWidth: widget.itemWidth ?? 0, count: widget.count, page: page);
  }
}
