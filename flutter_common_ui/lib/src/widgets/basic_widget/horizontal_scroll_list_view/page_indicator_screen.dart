import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 分页指示器
// 分页指示器的缩放系数，以轮播的宽度为基准，当轮播图片宽167px, 指示器宽36px
const fraction = 36 / 167;

const Color selected = Colors.white;
const Color unselected = Color.fromRGBO(255, 255, 255, .2);

class PageIndicatorScreen extends StatelessWidget {
  const PageIndicatorScreen(
      {Key? key, this.swiperWidth = 0, this.count = 1, this.page = 0});

  /// [轮播项的宽]
  final double swiperWidth;

  /// [count] 轮播项个数
  final int count;

  /// [page]轮播当前滚动的page
  final double page;

  @override
  Widget build(BuildContext context) {
    /// 每个分页指示器的宽度
    final double _itemWidth = fraction * swiperWidth / count;
    if (swiperWidth == 0.0) {
      return const SizedBox();
    }
    return Positioned(
        bottom: 28.w,
        right: swiperWidth * (1 - fraction)/2,
        width: fraction * swiperWidth,
        child: Stack(children: <Widget>[
          indicatorBar(color: unselected),
          Positioned(
            left: page * _itemWidth,
            bottom: 0,
            child: indicatorBar(width: _itemWidth, color: selected),
          ),
          count - page < 1
              ? Positioned(
                  left: 0,
                  bottom: 0,
                  child: indicatorBar(
                      width: (1 - (count - page)) * _itemWidth,
                      color: selected))
              : const SizedBox(),
        ]));
  }
}

// 进度条
class indicatorBar extends StatelessWidget {
  final double? width;
  final Color color;
  const indicatorBar({Key? key, this.width, required this.color})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: 2.w,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.all(Radius.circular(3.w)),
      ),
    );
  }
}
