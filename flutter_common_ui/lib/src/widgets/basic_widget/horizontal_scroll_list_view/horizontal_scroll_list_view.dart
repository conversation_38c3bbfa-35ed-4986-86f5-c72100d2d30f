/*
 * @Author: ma<PERSON><PERSON><PERSON> ma<PERSON><EMAIL>
 * @Date: 2023-03-07 18:35:01
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/models/common_models/padding_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/carousel_item.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/horizontal_scroll_list_view/horizontal_pagination.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 水平滑动的组件, 支持一页多个、标准轮播里的拖拽
class HorizontalScrollListView extends StatefulWidget {
  const HorizontalScrollListView(
      {Key? key,
      this.itemWidth,
      this.gutter,
      required this.items,
      this.needReload,
      this.trackInfo,
      this.padding,
      this.radius,
      this.shadow,
      this.fit,
      this.pagination,
      this.onTap});

  /// [itemWidth] 轮播项宽度
  final double? itemWidth;

  /// [gutter] 轮播项间距
  final double? gutter;

  /// [padding] 内边距
  final PaddingModel? padding;

  /// [fit]图片的fit方式
  final BoxFit? fit;

  /// [radius] 轮播项图片圆角
  final RadiusModel? radius;

  /// [shadow]轮播项阴影
  final ShadowModel? shadow;

  /// [items] 轮播项
  final List<AttrCarouseItem?> items;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [trackInfo] 打点配置
  /// 轮播的trackInfo是在轮播组件里，而非轮播项里，所以这里传过来
  /// 卡片导航是在子项里配置的
  final TrackInfoModel? trackInfo;

  /// [pagination] 是否显示分页
  final bool? pagination;

  /// [onTap]绑定事件方法、小红点额外处理逻辑，点击过的 缓存起来，不再显示小红点
  final Function? onTap;

  @override
  State<HorizontalScrollListView> createState() =>
      _HorizontalScrollListView1State();
}

class _HorizontalScrollListView1State extends State<HorizontalScrollListView> {
  /// [ListView]滚动的控制器
  ScrollController controller = ScrollController();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final listView = ListView.separated(
        controller: controller,
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext context, int index) {
          final double paddingLeft = index == 0 &&
                  widget.padding?.left != null &&
                  widget.padding!.left! > 0
              ? widget.padding!.left!.w
              : 0;
          final double paddingRight = index == widget.items.length - 1 &&
                  widget.padding?.right != null &&
                  widget.padding!.right! > 0
              ? widget.padding!.right!.w
              : 0;
          return CarouselItem(
              key: ValueKey(
                  '$index-${widget.items[index]?.id ?? ''}-${widget.items[index]?.pictureUrl ?? ''}'),
              width: (widget.itemWidth ?? 0) + paddingRight + paddingLeft,
              padding: EdgeInsets.only(
                  left: paddingLeft,
                  right: paddingRight,
                  top: widget.padding?.top?.w ?? 0,
                  bottom: widget.padding?.top?.w ?? 0),
              radius: widget.radius,
              shadow: widget.shadow,
              item: widget.items[index],
              index: index,
              needReload: widget.needReload,
              trackInfo: widget.items[index]?.trackInfo ?? widget.trackInfo,
              customFeature: widget.items[index]?.customFeatureModel,
              fit: widget.fit,
              attrCornerMark: widget.items[index]?.attrCornerMark,
              isShowDot: widget.items[index]?.isShowDot ?? false,

              /// ListView添加了addRepaintBoundaries = true, 所以子图不需要添加了
              addRepaintBoundaries: false,
              onTap: widget.onTap);
        },
        separatorBuilder: (BuildContext context, int index) {
          return Divider(height: .0, indent: widget.gutter);
        },
        itemCount: widget.items.length);

    if (widget.pagination == true) {
      List<Widget> list = [listView];

      list.add(HorizontalScrollPagination(
          controller: controller,
          count: widget.items.length,
          itemWidth: widget.itemWidth));

      /// 带分页器的轮播需要添加RepaintBoundary，限制重绘
      return RepaintBoundary(
          child: Stack(
        children: list,
      ));
    }
    return listView;
  }
}
