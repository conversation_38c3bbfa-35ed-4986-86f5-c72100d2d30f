/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-27 15:36:55
 * @description: 分享弹框
 */
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/common/toast_helper.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:share/model/shareparam.dart';
import 'package:share/share.dart';
import 'package:umeng/umeng.dart';

class ShareDialog {
  /// webUrl 分享的 web 的 url
  /// thumImage URL 或 base64 字串
  static show(BuildContext context, String title, String content, String webUrl,
      String thumImage,
      [List<ShareWay>? shareList, TextMessage? textMessage]) {
    showGeneralDialog(
        useRootNavigator: false,
        transitionBuilder: (
          BuildContext context,
          Animation<double> animation,
          Animation<double> secondaryAnimation,
          Widget child,
        ) {
          return ScaleTransition(scale: animation, child: child);
        },
        context: context,
        barrierDismissible: true,
        barrierLabel: '',
        transitionDuration: Duration(milliseconds: 1),
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return Container(
            child: ShareDialogWidget(
                title, content, webUrl, thumImage, shareList, textMessage),
          );
        });
  }
}

class ShareDialogWidget extends StatefulWidget {
  final String? title;
  final String? content;
  final String? webUrl;
  final String? thumImage;
  final List<ShareWay>? shareList;
  final TextMessage? textMessage;

  const ShareDialogWidget(this.title, this.content, this.webUrl, this.thumImage,
      this.shareList, this.textMessage,
      {Key? key})
      : super(key: key);

  @override
  _ShareDialogWidgetState createState() => _ShareDialogWidgetState();
}

class _ShareDialogWidgetState extends State<ShareDialogWidget> {
  ScreenUtil screenUtil = ScreenUtil();
  double? additionalBottomPadding;
  // 分享的类型
  List<ShareWay> shares = [
    ShareWay("微信", "assets/images/icon_wechat.webp",
        SharePlatform.wechatSession, ''),
    ShareWay("朋友圈", "assets/images/icon_friend.webp",
        SharePlatform.wechatTimeLine, ''),
    ShareWay("微博", "assets/images/icon_weibo.webp", SharePlatform.sina, ''),
    ShareWay("QQ", "assets/images/QQ.webp", SharePlatform.qq, ''),
    ShareWay(
        "QQ空间", "assets/images/icon_kongjian.webp", SharePlatform.qqZone, '')
  ];
  // 类型图标
  Map<SharePlatform, String> icon = {
    //  微信
    SharePlatform.wechatSession: 'assets/images/icon_wechat.webp',
    // 微信朋友圈
    SharePlatform.wechatTimeLine: 'assets/images/icon_friend.webp',
    // 微博
    SharePlatform.sina: 'assets/images/icon_weibo.webp',
    // QQ
    SharePlatform.qq: 'assets/images/QQ.webp',
    // QQ空间
    SharePlatform.qqZone: 'assets/images/icon_kongjian.webp'
  };

  @override
  void initState() {
    super.initState();
    if (widget.shareList != null && widget.shareList!.length > 0) {
      shares = widget.shareList!;
      shares.forEach((element) {
        if (element.icon == null || element.icon == '') {
          element.icon = icon[element.platform];
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    additionalBottomPadding = MediaQuery.of(context).padding.bottom > 0
        ? MediaQuery.of(context).padding.bottom
        : 0;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.w),
                  topRight: Radius.circular(12.w)),
              color: Colors.black54.withAlpha(50),
            ),
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Expanded(
                    flex: 1,
                    child: Container(
                      alignment: Alignment.center,
                      child: Container(),
                    )),
                _middleWidget(),
                _lineWidget(5),
                _bottomWidget(),
              ],
            )),
      ),
    );
  }

  Widget _lineWidget(int height) {
    return Container(
      height: screenUtil.setWidth(height.w),
      color: Color(0xFFF5F5F5),
    );
  }

  Widget _bottomWidget() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(
          bottom: screenUtil.setWidth(additionalBottomPadding ?? 0)),
      height: 56.w + (additionalBottomPadding ?? 0).w,
      alignment: Alignment.topCenter,
      child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          child: Container(
            height: screenUtil.setWidth(56),
            alignment: Alignment.center,
            child: Text(widget.textMessage?.cancelbtnText ?? '取消',
                style: TextStyle(
                  color: Color(0xFF333333),
                  decoration: TextDecorationfalse,
                  fontWeight: FontWeight.w500,
                  fontSize: screenUtil.setSp(17),
                )),
          ),
          onTap: () {
            Navigator.pop(context);
          }),
    );
  }

  Widget _middleWidget() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.w), topRight: Radius.circular(12.w)),
        color: Colors.white,
      ),
      height: 145.w,
      alignment: Alignment.bottomCenter,
      child: ListView.separated(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.zero,
        itemCount: shares.length,
        itemBuilder: (context, index) => shareItem(shares[index]),
        separatorBuilder: (context, index) => Container(
          width: screenUtil.setWidth(22),
        ),
      ),
    );
  }

  Widget shareItem(ShareWay item) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.w), topRight: Radius.circular(12.w)),
      ),
      height: screenUtil.setWidth(145),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          GestureDetector(
              child: Container(
                width: screenUtil.setWidth(50),
                height: screenUtil.setWidth(50),
                child: item.icon != null && item.icon!.startsWith('http')
                    ? CommonNetWorkImage(
                        url: item.icon,
                        width: screenUtil.setWidth(50),
                        height: screenUtil.setWidth(50),
                        errorWidget: SizedBox(),
                      )
                    : Image.asset(
                        item.icon ?? "",
                        width: screenUtil.setWidth(50),
                        height: screenUtil.setWidth(50),
                        package: Constant.packageName,
                      ),
              ),
              onTap: () {
                // 防重复提交
                // if (_loading == null) {
                //   share(item);
                // }
                share(item);
              }),
          Container(
            height: screenUtil.setWidth(6),
          ),
          Text(item.title ?? "",
              style: TextStyle(
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                  fontSize: screenUtil.setSp(12))),
        ],
      ),
    );
  }

  Future<void> share(ShareWay shareData) async {
    final IsOnline isOnline = await Network.isOnline();
    // 没有网络的情况下
    if (!isOnline.isOnline) {
      ToastHelper.showToast(Constant.netWorkError);
      return;
    }

    if (shareData.platform == SharePlatform.qq ||
        shareData.platform == SharePlatform.qqZone) {
      if ((await UmengPlugin.isInstalled("qq")).status !.isOnline) {
        ToastHelper.showToast(widget.textMessage?.applicationUninstalled ??
            Constant.applicationUninstalled);
        return;
      }
    }

    if (shareData.platform == SharePlatform.wechatSession ||
        shareData.platform == SharePlatform.wechatTimeLine) {
      if ((await UmengPlugin.isInstalled("wechat")).status !.isOnline) {
        ToastHelper.showToast(widget.textMessage?.applicationUninstalled ??
            Constant.applicationUninstalled);
        return;
      }
    }

    if (shareData.platform == SharePlatform.sina) {
      if ((await UmengPlugin.isInstalled("weibo")).status !.isOnline) {
        ToastHelper.showToast(widget.textMessage?.applicationUninstalled ??
            Constant.applicationUninstalled);
        return;
      }
    }

    // _loading = SYNLoading();
    // _loading?.showProgressDialog(context);

    ShareParam param = ShareParam.web(shareData.platform, widget.title!,
        widget.content!, widget.webUrl!, widget.thumImage!);
    Share.shareContent(param).then((result) async {
      // _dismissProgressDialog();
      Navigator.pop(context);
    }).catchError((error) {
      // _dismissProgressDialog();
      DevLogger.info(
          tag: Constant.packageName,
          msg: {'fn': '------失败： + ${error?.toString()}'});
      if (error is PlatformException) {
        PlatformException exception = error;
        ToastHelper.showToast(exception.message ??
            widget.textMessage?.shareFailTip ??
            Constant.shareFailTip);
      } else {
        ToastHelper.showToast(
            widget.textMessage?.shareFailTip ?? Constant.shareFailTip);
      }
    });
    // _dismissProgressDialog();
  }
}

class ShareWay {
  String? title;
  String? icon;
  SharePlatform? platform;
  String? gioId;

  ShareWay(this.title, this.icon, this.platform, this.gioId);
}

class TextMessage {
  String? cancelbtnText;
  String? shareFailTip;
  String? applicationUninstalled;
  TextMessage(
      this.cancelbtnText, this.shareFailTip, this.applicationUninstalled);
}
