/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-14 10:04:14
 * @description: 埋点和曝光点套壳
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/utils/gio_util.dart';
import 'package:visibility_detector/visibility_detector.dart';

// ignore: must_be_immutable
class TrackWidget extends StatelessWidget {
  ///  子元素
  Widget child;

  /// 用于存储是否埋过点的key, swiper轮播的child.hashCode每次都不一样
  Key? key;

  /// 埋点信息
  TrackInfoModel? trackInfo;

  /// 埋点参数
  Map<dynamic, dynamic>? trackMap;

  /// 点击事件
  Function onTap;

  /// 曝光事件
  Function? onExpose;

  TrackWidget(this.child,
      {this.key,
      required this.trackInfo,
      required this.trackMap,
      required this.onTap,
      this.onExpose});

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: key ?? ValueKey('${child.hashCode}'),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        child: child,
        onTap: () async {
          await onTap();
          if (trackInfo != null && trackMap != null) {
            GIOUtil.dealTrackInfo(trackMap!, trackInfo, isExpose: false);
          }
        },
      ),
      onVisibilityChanged: (VisibilityInfo info) async {
        // 在可视区域
        /// 动态角标显示的最大值为0.7，这里暂定0.5为显示的阈值
        if (info.visibleFraction > 0.5) {
          /// 如果未存储过，或者存储的是未曝光，则写入曝光true
          if (_TrackRecordMap.trackRecordMap[info.key] != true) {
            _TrackRecordMap.trackRecordMap[info.key] = true;
            if (onExpose != null) {
              await onExpose!();
            }
            if (trackInfo != null &&
                trackMap != null &&
                trackInfo?.exposure != null) {
              GIOUtil.dealTrackInfo(trackMap!, trackInfo, isExpose: true);
            }
          }
        } else if (info.visibleFraction == 0.0) {
          /// 离开视图时
          _TrackRecordMap.trackRecordMap[info.key] = false;
        }
      },
    );
  }
}

class _TrackRecordMap {
  /// 存储组件是否曝光过
  /// true: 曝光过
  /// false: 未曝光过
  static Map<Key, bool> trackRecordMap = {};
}
