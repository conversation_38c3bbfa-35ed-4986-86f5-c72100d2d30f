/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-20 17:00:14
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InitialImage extends StatefulWidget {
  final String url;
  final double? imageHeight;
  final double? imageDefWidth;
  const InitialImage(
      {Key? key, required this.url, this.imageHeight, this.imageDefWidth})
      : super(key: key);

  @override
  _InitialImageState createState() => _InitialImageState();
}

class _InitialImageState extends State<InitialImage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.imageDefWidth != 0
        ? Image.network(
            widget.url,
            width: widget.imageDefWidth?.w,
            height: widget.imageHeight?.w,
            fit: BoxFit.fitHeight,
            errorBuilder: (context, error, stackTrace) => Image.asset(
              'assets/images/default_48_48.png',
              height: widget.imageHeight?.w,
              fit: BoxFit.fitHeight,
              package: Constant.packageName,
            ),
          )
        : Image.network(
            widget.url,
            height: widget.imageHeight?.w,
            fit: BoxFit.fitHeight,
            errorBuilder: (context, error, stackTrace) => Image.asset(
              'assets/images/default_48_48.png',
              height: widget.imageHeight?.w,
              fit: BoxFit.fitHeight,
              package: Constant.packageName,
            ),
          );
  }
}
