import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/animation_visibility_detector_wrapper.dart';

/// 闪屏动画组件的builder
/// 包含两个动画：尺寸、透明度
typedef SplashAnimationBuilder = Widget Function(
    BuildContext context, AnimationController? animationController);

/// 闪屏动画
class SplashAnimation extends StatefulWidget {
  /// 需要实现动画的子组件
  final Widget? child;

  /// [duration] 动画持续时间， 默认1000毫秒，单位为毫秒
  final int duration;

  /// [builder] 动画创建的builder
  final SplashAnimationBuilder? builder;

  /// [sizeScaleForwardBegin] 第一阶段尺寸开始值
  final double sizeScaleForwardBegin;

  /// [sizeScaleForwardEnd] 第一阶段尺寸结束值，第三阶段尺寸开始值
  final double sizeScaleForwardEnd;

  /// [sizeScaleReverseEnd] 第三阶段尺寸结束值
  final double sizeScaleReverseEnd;

  /// [opacityForwardBegin] 第一阶段透明度开始值
  final double opacityForwardBegin;

  /// [opacityForwardEnd] 第一阶段透明度结束值，第三阶段透明度开始值
  final double opacityForwardEnd;

  /// [opacityReverseEnd] 第三阶段透明度结束值
  final double opacityReverseEnd;

  /// [forwardDuration] 第一阶段时长，一般为尺寸增大，透明度增加, 单位为毫秒
  final int forwardDuration;

  /// [freezeDuration] 第二阶段时长，动画保持不变的时长, 单位为毫秒
  final int freezeDuration;

  /// [reverseDuration] 第三阶段时长，一般为尺寸减小，透明度减小, 单位为毫秒
  final int reverseDuration;

  /// 动画的中心点
  final Offset? origin;

  const SplashAnimation(
      {Key? key,
      this.child,
      this.duration = 1000,
      this.sizeScaleForwardBegin = 0,
      this.sizeScaleForwardEnd = 0,
      this.sizeScaleReverseEnd = 0,
      this.opacityForwardBegin = 1,
      this.opacityForwardEnd = 1,
      this.opacityReverseEnd = 1,
      this.forwardDuration = 0,
      this.freezeDuration = 0,
      this.reverseDuration = 0,
      this.origin,
      this.builder});

  @override
  State<SplashAnimation> createState() => _SplashAnimationState();
}

class _SplashAnimationState extends State<SplashAnimation>
    with SingleTickerProviderStateMixin {
  /// [_controller] 动画的controller
  AnimationController? _controller;

  /// [_sizeAnimation] 尺寸动画变化规律
  Animation<double>? _sizeAnimation;

  /// [_opacityAnimation] 透明度动画变化规律
  Animation<double>? _opacityAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
        vsync: this, duration: Duration(milliseconds: widget.duration))
      ..forward();

    /// [_sizeAnimation] 尺寸动画
    _sizeAnimation = getAnimation(
        widget.sizeScaleForwardBegin,
        widget.sizeScaleForwardEnd,
        widget.sizeScaleReverseEnd,
        widget.forwardDuration,
        widget.freezeDuration,
        widget.reverseDuration);

    /// [_opacityAnimation] 透明度动画
    _opacityAnimation = getAnimation(
        widget.opacityForwardBegin,
        widget.opacityForwardEnd,
        widget.opacityReverseEnd,
        widget.forwardDuration,
        widget.freezeDuration,
        widget.reverseDuration);

    _opacityAnimation?.addStatusListener(statusListenerCallback);
  }

  /// 动画状态变化的回调
  void statusListenerCallback(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      _controller
        ?..reset()
        ..forward();
    }
  }

  /// 构建动画
  Animation<double>? getAnimation(
      double forwardBegin,
      double forwardEnd,
      double reverseEnd,
      int forwardDuration,
      int freezeDuration,
      int reverseDuration) {
    return TweenSequence<double>([
      TweenSequenceItem(
          tween: Tween(begin: forwardBegin, end: forwardEnd),
          weight: forwardDuration.toDouble()),
      TweenSequenceItem(
          tween: ConstantTween(forwardEnd), weight: freezeDuration.toDouble()),
      TweenSequenceItem(
          tween: Tween(begin: forwardEnd, end: reverseEnd),
          weight: reverseDuration.toDouble())
    ]).animate(_controller!);
  }

  @override
  void dispose() {
    _controller?.dispose();
    _opacityAnimation?.removeStatusListener(statusListenerCallback);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
        child: AnimationVisibilityDetectorWrapper(
            controller: _controller,
            child: AnimatedBuilder(
                animation: _controller!,
                child: widget.builder != null
                    ? widget.builder!(context, _controller)
                    : widget.child,
                builder: (BuildContext context, Widget? child) {
                  return Transform.scale(
                      origin: widget.origin,
                      scale: _sizeAnimation?.value ?? 1,
                      child: Opacity(
                          opacity: _opacityAnimation?.value ?? 1,
                          child: child));
                })));
  }
}
