import 'package:extended_image/extended_image.dart';
import 'package:flutter/widgets.dart';

import '../../utils/oss_util.dart';

class FadeInNetworkImage extends StatefulWidget {
  final String url;
  final Widget? placeHolder;
  final Widget? errorWidget;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final int? needReload;
  final Duration? animationDuration;
  final Curve? animationCurve;
  final Alignment? alignment;
  final bool withOssParam;

  /// [addRepaintBoundaries] 是否需要添加RepaintBoundary
  final bool? addRepaintBoundaries;

  const FadeInNetworkImage(
      {Key? key,
      required this.url,
      this.placeHolder,
      this.errorWidget,
      this.width,
      this.height,
      this.fit,
      this.needReload,
      this.animationDuration,
      this.animationCurve,
      this.alignment,
      this.addRepaintBoundaries,
      this.withOssParam = true})
      : super(key: key);

  @override
  State<FadeInNetworkImage> createState() => _FadeInNetworkImageState();
}

class _FadeInNetworkImageState extends State<FadeInNetworkImage>
    with SingleTickerProviderStateMixin {
  bool isImageLoadFail = false;
  bool reload = false;

  AnimationController? _controller;

  CurvedAnimation? _animation;

  @override
  void initState() {
    _controller = AnimationController(
      duration: widget.animationDuration ?? const Duration(milliseconds: 200),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller!,
      curve: widget.animationCurve ?? Curves.easeIn,
    );
    super.initState();
  }

  @override
  void didUpdateWidget(FadeInNetworkImage oldWidget) {
    if (oldWidget.needReload != widget.needReload) {
      if (isImageLoadFail) {
        setState(() {
          reload = true;
        });
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    /// url为空 返回占位符 或者空组件
    if (widget.url.isEmpty) {
      return widget.errorWidget ?? const SizedBox.shrink();
    }

    /// oss处理
    String url = widget.url;
    if (widget.withOssParam &&
        (widget.width != null && (widget.width!.isFinite) ||
            (widget.height != null && widget.height!.isFinite))) {
      url = OssUtil.addOssParams(widget.url, widget.width, widget.height);
    }

    /// cacheHeight处理，优化memory
    final int? cacheHeight = widget.height != null && widget.height!.isFinite
        ? (widget.height! * MediaQuery.of(context).devicePixelRatio).toInt()
        : null;

    return ExtendedImage.network(url,
        width: widget.width,
        height: widget.height,
        cacheHeight: cacheHeight,
        alignment: widget.alignment ?? Alignment.center,
        fit: widget.fit ?? BoxFit.contain,
        cacheMaxAge: const Duration(days: 7),
        loadStateChanged: (ExtendedImageState state) {
      switch (state.extendedImageLoadState) {
        case LoadState.loading:
          _controller!.reset();
          return widget.placeHolder ?? const SizedBox.shrink();
        case LoadState.failed:
          _controller!.reset();
          isImageLoadFail = true;
          if (reload) {
            state.reLoadImage();
            reload = false;
          }
          return widget.errorWidget ?? const SizedBox.shrink();
        case LoadState.completed:
          _controller!.forward();
          isImageLoadFail = false;
          // 调用方指定 addRepaintBoundaries = false， 不添加 RepaintBoundary
          if (widget.addRepaintBoundaries == false) {
            return FadeTransition(
                opacity: _animation!, child: state.completedWidget);
          }
          // 添加RepaintBoundary的情况：
          // 1 调用方指定 addRepaintBoundaries == true
          // 2 动图渲染多帧
          if ((widget.addRepaintBoundaries ?? false) ||
              state.frameNumber != null && state.frameNumber! > 0) {
            return RepaintBoundary(
                child: FadeTransition(
                    opacity: _animation!, child: state.completedWidget));
          }
          // 未指定 addRepaintBoundaries，且只能渲染一帧，不添加 RepaintBoundary
          return FadeTransition(
              opacity: _animation!, child: state.completedWidget);
      }
    });
  }
}
