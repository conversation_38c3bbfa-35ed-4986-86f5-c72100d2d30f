import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';

/// 反转滤镜包裹组件
class ColorFilteredWrapper extends StatelessWidget {
  final ColorSystemEnum? colorSystem;
  final Widget child;
  const ColorFilteredWrapper({Key? key, this.colorSystem, required this.child});

  @override
  Widget build(BuildContext context) {
    return colorSystem != null
        ? ColorFiltered(
            colorFilter: getColorFilterValue(colorSystem: colorSystem),
            child: child)
        : child;
  }
}
