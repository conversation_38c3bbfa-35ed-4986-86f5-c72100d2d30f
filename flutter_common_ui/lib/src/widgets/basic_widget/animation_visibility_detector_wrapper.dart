import 'package:flutter/material.dart';
import 'package:visibility_detector/visibility_detector.dart';

/// 动画组件可见性检测的包裹组件
class AnimationVisibilityDetectorWrapper extends StatefulWidget {
  const AnimationVisibilityDetectorWrapper({
    Key? key,
    required this.child,
    this.controller,
  });

  /// [child]包含动画的子组件
  final Widget child;

  /// [controller] 动画的controller
  final AnimationController? controller;

  @override
  State<AnimationVisibilityDetectorWrapper> createState() =>
      _AnimationVisibilityDetectorWrapperState();
}

class _AnimationVisibilityDetectorWrapperState
    extends State<AnimationVisibilityDetectorWrapper> {
  UniqueKey key = UniqueKey();

  /// 是否停止动画
  bool stopAnimation = false;

  void onVisibilityChanged(VisibilityInfo info) {
    if (info.visibleFraction <= 0 && stopAnimation == false) {
      widget.controller?.stop();
      stopAnimation = true;
    } else if (info.visibleFraction > 0 && stopAnimation == true) {
      widget.controller?.forward();
      stopAnimation = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
        key: key,
        onVisibilityChanged: onVisibilityChanged,
        child: widget.child);
  }
}
