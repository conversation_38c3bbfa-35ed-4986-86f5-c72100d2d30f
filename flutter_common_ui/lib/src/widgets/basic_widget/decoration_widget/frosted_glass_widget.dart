import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/color_filtered_widget.dart';

/// 毛玻璃包裹组件
class FrostedGlassWidget extends StatelessWidget {
  const FrostedGlassWidget({
    Key? key,
    this.width,
    this.height,
    this.top = 0.0,
    this.left = 0.0,
    this.relative = false,
    this.opacity = 1.0,
    this.blur = 0,
    required this.margin,
    this.padding = const EdgeInsets.all(0),
    this.borderRadius = const BorderRadius.all(Radius.circular(0)),
    this.colorSystem,
    required this.child,
  });

  /// [width] 宽度
  final double? width;

  /// [height] 高度
  final double? height;

  /// [top] 相对父容器顶部距离
  final double? top;

  /// [left] 相对父容器左边距离
  final double? left;

  /// [relative] 是否为相对位置布局
  final bool? relative;

  /// 透明度 0 - 1
  final double? opacity;

  /// 高斯模糊
  final double? blur;

  /// 外间距
  final EdgeInsets margin;

  /// 内间距
  final EdgeInsets? padding;

  /// 四个位置圆角
  final BorderRadius borderRadius;

  /// 组件子组件
  final Widget child;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  @override
  Widget build(BuildContext context) {
    final opacityNew = opacity?.clamp(0, 1.0).toDouble() ?? 1.0;

    /// 透明度 + 色系滤镜
    final Widget opacityWidget = opacityNew == 1.0
        ? ColorFilteredWrapper(colorSystem: colorSystem, child: child)
        : Opacity(
            opacity: opacityNew,
            child:
                ColorFilteredWrapper(colorSystem: colorSystem, child: child));

    /// 相对定位
    final Widget positionedWidget = relative == true
        ? Positioned(top: top, left: left, child: opacityWidget)
        : opacityWidget;

    if (blur != null && blur! > 0) {
      return Stack(
        children: [
          positionedWidget,
          Positioned(
              top: top,
              left: left,
              child: IgnorePointer(
                  child: ClipRRect(
                      borderRadius: borderRadius,
                      child: BackdropFilter(
                          filter:
                              ImageFilter.blur(sigmaX: blur!, sigmaY: blur!),
                          child: Container(
                              width: (width ?? 0) + margin.left + margin.right,
                              height:
                                  (height ?? 0) + margin.top + margin.bottom,
                              padding: padding,
                              color: Colors.transparent))))),
        ],
      );
    }
    return positionedWidget;
  }
}
