/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 16:22:07
 * @description: 组件通用外观设置
 */
import 'dart:ui';

/// 组件通用外观设置
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/utils/extension.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/decoration_widget/frosted_glass_widget.dart';

class DecorationWidget extends StatelessWidget {
  const DecorationWidget({
    Key? key,
    this.width,
    this.height,
    this.top = 0.0,
    this.left = 0.0,
    this.relative = false,
    this.isSDKBorder,
    this.opacity = 1.0,
    this.blur = 0,
    this.margin = const EdgeInsets.all(0),
    this.padding = const EdgeInsets.all(0),
    this.borderRadius = const BorderRadius.all(Radius.circular(0)),
    this.border = const Border(),
    this.bgColor = Colors.transparent,
    this.bgGradient,
    this.bgUrl,
    this.imageFit = BoxFit.cover,
    this.boxShadow,
    this.colorSystem,
    required this.child,
  }) : super(key: key);

  /// [width] 宽度
  final double? width;

  /// [height] 高度
  final double? height;

  /// [top] 相对父容器顶部距离
  final double? top;

  /// [left] 相对父容器左边距离
  final double? left;

  /// [relative] 是否为相对位置布局
  final bool? relative;

  /// [isSDKBorder] 是否为flutter官方支持的边框
  final bool? isSDKBorder;

  /// 透明度 0 - 1
  final double? opacity;

  /// 高斯模糊
  final double? blur;

  /// 外间距
  final EdgeInsets? margin;

  /// 内间距
  final EdgeInsets? padding;

  /// 四个位置圆角
  final BorderRadius borderRadius;

  /// 描边
  final BoxBorder? border;

  /// 组件背景
  final String? bgUrl;

  /// 组件背景 fit 模式,默认 BoxFit.cover
  final BoxFit? imageFit;

  /// 组件背景颜色
  final Color? bgColor;

  /// 渐变色背景色
  final Gradient? bgGradient;

  /// 组件子组件
  final Widget child;

  /// 阴影
  final List<BoxShadow>? boxShadow;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  @override
  Widget build(BuildContext context) {
    EdgeInsets _margin = margin ?? EdgeInsets.all(0);
    if (boxShadow?.length != null && boxShadow!.length > 0) {
      BoxShadow shadow = boxShadow![0];

      /// 留出阴影空间
      _margin = _margin.mergeShadow(shadow: shadow);
      _margin = _margin.isNonNegative ? _margin : EdgeInsets.zero;
    }

    final decoration = BoxDecoration(
        borderRadius: borderRadius,
        gradient: bgGradient,
        boxShadow: boxShadow,
        image: CommonUtil.getDecorationImage(bgUrl, imageFit),
        border: border,
        color: bgColor);

    // 包含装饰的组件
    final Widget outerWidget = ClipRRect(
      borderRadius: borderRadius,
      child: Container(
          width: width,
          height: height,
          margin: _margin,
          padding: padding,
          decoration: decoration,
          child: Container(
            constraints: BoxConstraints.expand(),
            child: child,
          )),
    );

    /// 带毛玻璃效果
    return FrostedGlassWidget(
        width: width,
        height: height,
        padding: padding,
        margin: _margin,
        top: top,
        left: left,
        borderRadius: borderRadius,
        opacity: opacity,
        relative: relative,
        blur: blur,
        colorSystem: colorSystem,
        child: outerWidget);
  }
}
