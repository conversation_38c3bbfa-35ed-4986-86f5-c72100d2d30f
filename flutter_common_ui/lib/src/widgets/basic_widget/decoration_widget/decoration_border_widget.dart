import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/border_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/utils/extension.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/decoration_widget/frosted_glass_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:syn_dotted_border/syn_dotted_border.dart';

/// 点线/虚线支持
class DecorationBorderWidget extends StatelessWidget {
  DecorationBorderWidget({
    Key? key,
    this.width,
    this.height,
    this.top = 0.0,
    this.left = 0.0,
    this.relative = false,
    this.isSDKBorder,
    this.opacity = 1.0,
    this.blur = 0,
    this.margin = const EdgeInsets.all(0),
    this.padding = const EdgeInsets.all(0),
    this.borderRadius = const BorderRadius.all(Radius.circular(0)),
    this.border,
    this.bgColor = Colors.transparent,
    this.bgGradient,
    this.bgUrl,
    this.imageFit = BoxFit.cover,
    this.boxShadow,
    this.colorSystem,
    required this.child,
  }) : super(key: key);

  /// [width] 宽度
  final double? width;

  /// [height] 高度
  final double? height;

  final BorderModel? border;

  /// 四个位置圆角
  final BorderRadius borderRadius;

  /// 组件背景
  final String? bgUrl;

  /// 组件背景 fit 模式,默认 BoxFit.cover
  final BoxFit? imageFit;

  /// 组件背景颜色
  final Color? bgColor;

  /// 渐变色背景色
  final Gradient? bgGradient;

  /// 透明度 0 - 1
  final double? opacity;

  /// 高斯模糊
  final double? blur;

  /// 外间距
  final EdgeInsets? margin;

  /// 内间距
  final EdgeInsets? padding;

  /// 是否是 flutter SDK 边框
  final bool? isSDKBorder;

  /// [relative] 是否是相对位置，是的话使用Positioned包裹
  final bool? relative;

  /// [top] 相对父容器顶部距离
  final double? top;

  /// [left] 相对父容器左边距离
  final double? left;

  final Widget child;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  /// 阴影
  final List<BoxShadow>? boxShadow;

  @override
  Widget build(BuildContext context) {
    if (isSDKBorder != false) {
      return child;
    }

    double borderWith = this.border?.width?.w ?? 0;

    /// 边框的预留空间， border使用Positioned组件在下面，上层是实际的组件，实际的组件需要留出margin显示边框
    EdgeInsets _borderMargin = margin ?? EdgeInsets.all(0);

    /// 阴影的预留空间
    EdgeInsets _shadowMargin = margin ?? EdgeInsets.all(0);

    /// 实际的宽度，考虑border和shadow
    double _width = width ?? 0;

    /// 实际的高度，考虑border和shadow
    double _height = height ?? 0;

    /// 给border预留空间
    if (borderWith > 0) {
      _borderMargin += EdgeInsets.all(borderWith);
      _width -= borderWith * 2;
      _height -= borderWith * 2;
      _width = _width.isNegative ? 0 : _width;
      _height = _height.isNegative ? 0 : _height;
    }

    final decoration = BoxDecoration(
        borderRadius: borderRadius,
        gradient: bgGradient,
        image: CommonUtil.getDecorationImage(bgUrl, imageFit),
        color: bgColor);

    // 包含装饰的组件
    final Widget outerWidget = ClipRRect(
        borderRadius: borderRadius,
        child: Container(
            width: _width,
            height: _height,
            padding: padding,
            decoration: decoration,
            margin: _borderMargin,
            child: Container(
              constraints: BoxConstraints.expand(),
              child: child,
            )));

    /// 带描边的组件
    final dottedBorderWidget = DottedBorderWrapper(
        border: border, borderRadius: borderRadius, child: outerWidget);

    if (boxShadow?.length != null && boxShadow!.length > 0) {
      BoxShadow shadow = boxShadow![0];

      /// 留出阴影空间
      _shadowMargin = _shadowMargin.mergeShadow(shadow: shadow);
      _shadowMargin =
          _shadowMargin.isNonNegative ? _shadowMargin : EdgeInsets.zero;
    }

    /// 带阴影
    final shadowWidget = ClipRRect(
        borderRadius: borderRadius,
        child: Container(
            decoration: BoxDecoration(boxShadow: boxShadow),
            margin: _shadowMargin,
            child: dottedBorderWidget));

    /// 带毛玻璃效果
    return FrostedGlassWidget(
        width: width,
        height: height,
        padding: padding,
        margin: _shadowMargin,
        top: top,
        left: left,
        borderRadius: borderRadius,
        opacity: opacity,
        relative: relative,
        blur: blur,
        colorSystem: colorSystem,
        child: shadowWidget);
  }
}

/// dotted、dashed border包裹的组件
class DottedBorderWrapper extends StatelessWidget {
  const DottedBorderWrapper(
      {Key? key, this.border, this.borderRadius, required this.child});

  final BorderModel? border;

  /// 四个位置圆角
  final BorderRadius? borderRadius;

  final Widget child;

  @override
  Widget build(BuildContext context) {
    if (this.border?.style == BorderStyleEnum.unset ||
        this.border?.width == null ||
        this.border!.width == 0) {
      return child;
    }

    final color = CommonUtil.toColor(this.border?.color?.value);
    if (color == null) {
      return child;
    }

    final strokeWidth = this.border?.width?.w ?? 0;
    final radius = borderRadius?.topLeft ?? Radius.circular(0);

    final dashPattern = this.border?.lineDashPattern ?? [4, 0];

    return DottedBorder(
        padding: EdgeInsets.all(0),
        borderType: BorderType.RRect,
        color: color,
        strokeWidth: strokeWidth,
        dashPattern: dashPattern,
        radius: radius,
        borderRadius: borderRadius,
        child: child);
  }
}
