import 'package:extended_image/extended_image.dart';
import 'package:flutter/widgets.dart';

import '../../utils/oss_util.dart';

class CommonNetWorkImage extends StatefulWidget {
  final String? url;
  final Widget? placeHolder;
  final Widget? errorWidget;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Alignment? alignment;
  final int? needReload;

  /// [addRepaintBoundaries] 是否需要添加RepaintBoundary
  final bool? addRepaintBoundaries;

  // 圆形卡片导航的图片已经是小图片，不需要处理oss参数，否则会显示模糊，增加参数单独处理
  /// [withOssParam] 是否处理oss参数，默认为true需要根据情况判定是否处理，如果false则直接不处理
  final bool withOssParam;

  const CommonNetWorkImage(
      {Key? key,
      required this.url,
      this.placeHolder,
      this.errorWidget,
      this.width,
      this.height,
      this.fit,
      this.alignment,
      this.needReload,
      this.addRepaintBoundaries,
      this.withOssParam = true})
      : super(key: key);

  @override
  State<CommonNetWorkImage> createState() => _CommonNetWorkImage();
}

class _CommonNetWorkImage extends State<CommonNetWorkImage> {
  bool isImageLoadFail = false;
  bool reload = false;

  @override
  void didUpdateWidget(CommonNetWorkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.needReload != widget.needReload) {
      if (isImageLoadFail) {
        setState(() {
          reload = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return (widget.url == null || widget.url == '')
        ? widget.errorWidget ?? Container()
        : _loadImage(context);
  }

  // / cachedNetworkImage方式，由于ios存在清缓存后无法加载的情况，用ExtendedImage替换
  Widget _loadImage(BuildContext context) {
    final double scale = MediaQuery.of(context).devicePixelRatio;
    int? cacheHeight =
        widget.height != null ? (widget.height! * scale).toInt() : null;
    String providerUrl = '';
    if (widget.withOssParam == true &&
        (widget.width != null || widget.height != null)) {
      providerUrl = OssUtil.addOssParams(widget.url ?? '', widget.width, widget.height);
    } else {
      providerUrl = widget.url ?? '';
    }

    return ExtendedImage(
        alignment: widget.alignment ?? Alignment.center,
        width: widget.width,
        height: widget.height,
        fit: widget.fit ?? BoxFit.contain,
        image: ResizeImage.resizeIfNeeded(
            null, cacheHeight, CommonNetWorkImageProvider.get(providerUrl)),
        loadStateChanged: (ExtendedImageState state) {
          switch (state.extendedImageLoadState) {
            case LoadState.loading:
              return widget.placeHolder ?? Container();
            case LoadState.failed:
              isImageLoadFail = true;
              if (reload) {
                state.reLoadImage();
                reload = false;
              }
              return widget.errorWidget ?? Container();
            case LoadState.completed:
              isImageLoadFail = false;
              // 调用方指定 addRepaintBoundaries = false， 不添加 RepaintBoundary
              if (widget.addRepaintBoundaries == false) {
                return null;
              }
              // 添加RepaintBoundary的情况：
              // 1 调用方指定 addRepaintBoundaries == true
              // 2 动图渲染多帧
              if (widget.addRepaintBoundaries == true ||
                  state.frameNumber != null && state.frameNumber! > 0) {
                return RepaintBoundary(child: state.completedWidget);
              }
              // 未指定 addRepaintBoundaries，且只能渲染一帧，不添加 RepaintBoundary
              return null;
          }
        });
  }
}

class CommonNetWorkImageProvider {
  static ImageProvider get(String url) {
    return ExtendedNetworkImageProvider(
      url,
      cache: true,
      cacheMaxAge: Duration(days: 7),
    );
  }
}
