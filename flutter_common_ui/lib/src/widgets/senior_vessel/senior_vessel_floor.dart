import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/empty_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 楼层动态容器
/// 可以包含7类子组件：轮播、图片、文本、内容、菜单、直播、家庭
class SeniorVesselFloor extends StatelessWidget {
  /// [config] 动态容器的配置信息
  final SeniorVesselModel? config;

  /// [widgetList] 为业务组件
  final List<Widget>? widgetList;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [height] 高度
  final double? height;

  final EdgeInsets? padding;
  const SeniorVesselFloor(
      {Key? key,
      this.config,
      this.widgetList,
      this.isSliver = false,
      this.height,
      this.padding});

  @override
  Widget build(BuildContext context) {
    if (widgetList?.length != null && widgetList!.length > 0) {
      final _opacity = config?.style?.attr?.opacity;
      final _height = height != null ? height : config?.style?.attr?.height?.w;
      final _child = Container(
          height: _height,
          padding: padding,
          child: Stack(children: widgetList!));
      final _outerChild = _opacity == null || _opacity == 1
          ? _child
          : Opacity(opacity: _opacity, child: _child);
      return isSliver == true
          ? SliverToBoxAdapter(child: _outerChild)
          : _outerChild;
    }
    return EmptyWidget(isSliver: isSliver);
  }
}
