/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-20 14:36:12
 * @description: 
 */
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/background_model.dart';
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_custom_style_model.dart';
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_model.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/header_sticky/header_sticky.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/header_sticky/sliver_header_delegate.dart';
import 'package:flutter_common_ui/src/widgets/senior_vessel/senior_vessel_floor.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 吸顶动态容器
/// 可以包含7类子组件：轮播、图片、文本、内容、菜单、直播、家庭
class SeniorVesselPersistent extends StatelessWidget {
  /// [config] 动态容器的配置信息
  final SeniorVesselModel? config;

  /// [widgetList] 为业务组件
  final List<List<Widget>>? widgetList;

  /// [customStyle] 吸顶容器上滑和初始状态时自定义样式
  final List<SeniorVesselCustomStyleModel>? customStyle;

  /// [pageIsScroll] 页面是否上滑, 只有吸顶导航需要关注，吸顶导航才有值
  final bool? pageIsScroll;

  SeniorVesselPersistent(
      {Key? key,
      this.config,
      this.widgetList,
      this.customStyle,
      this.pageIsScroll});

  /// 上下滑动时吸顶组件切换两个状态
  Widget _switchStatusBuilder(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    // print('pqpq: shrink: $shrinkOffset，overlaps:$overlapsContent');
    return _PersistentHeaderWidget(
        config: config,
        widgetList: widgetList,
        customStyle: customStyle,
        pageIsScroll: pageIsScroll,
        shrinkOffset: shrinkOffset);
  }

  @override
  Widget build(BuildContext context) {
    if (config?.content?.vesselType == VesselTypeEnum.persistent &&
        widgetList?.length != null &&
        widgetList!.length > 0) {
      // 个数为两个的情况，第一项为初始状态，第二项为上滑状态，和可视化后端开发赵腾约定
      /// 有两种情况：
      /// 1 pageIsScroll有值(true/false)， 表示为顶部导航，无法检测到shrinkOffset的值，使用pageIsScroll检测滑动
      /// 2 pageIsScroll为null， 为非顶部导航，使用shrinkOffset检测滑动
      final double _height = (config?.style?.attr?.height?.w ?? 0);
      if (widgetList!.length == 2) {
        return pageIsScroll != null
            ? SliverToBoxAdapter(
                child: _PersistentHeaderWidget(
                config: config,
                widgetList: widgetList,
                customStyle: customStyle,
                pageIsScroll: pageIsScroll,
              ))
            : SliverPersistentHeader(
                pinned: true,
                delegate: SliverHeaderDelegate.builder(
                    maxHeight: _height,
                    minHeight: _height,
                    builder: _switchStatusBuilder));
      } else if (widgetList!.length == 1) {
        // 只有一个初始状态的页面
        return HeaderSticky(
            child:
                SeniorVesselFloor(config: config, widgetList: widgetList?[0]),
            fixedHeight: _height);
      }
    }
    return const SliverToBoxAdapter();
  }
}

/// 顶部导航
class _PersistentHeaderWidget extends StatelessWidget {
  const _PersistentHeaderWidget(
      {Key? key,
      this.config,
      this.widgetList,
      this.customStyle,
      this.pageIsScroll,
      this.shrinkOffset = 0});

  /// [config] 动态容器的配置信息
  final SeniorVesselModel? config;

  /// [widgetList] 为业务组件
  final List<List<Widget>>? widgetList;

  /// [customStyle] 吸顶容器上滑和初始状态时自定义样式
  final List<SeniorVesselCustomStyleModel>? customStyle;

  /// [pageIsScroll] 页面是否上滑, 只有吸顶导航需要关注，吸顶导航才有值
  final bool? pageIsScroll;

  /// SliverHeaderDelegate的builder参数，吸顶组件被遮挡的偏移量
  final double shrinkOffset;

  @override
  Widget build(BuildContext context) {
    final _firstChild = SeniorVesselChild(
      config: config,
      customStyle: customStyle?[0],
      widgetListItem: widgetList![0],
      pageIsScroll: pageIsScroll,
    );

    final _secondChild = SeniorVesselChild(
      config: config,
      customStyle: customStyle?[1],
      widgetListItem: widgetList![1],
      pageIsScroll: pageIsScroll,
    );
    final List<Widget> _children = [
      /// 有两种情况：
      /// 1 pageIsScroll有值(true/false)， 表示为顶部导航，无法检测到shrinkOffset的值，使用pageIsScroll检测滑动
      /// 2 pageIsScroll为null， 为非顶部导航，使用shrinkOffset检测滑动
      Opacity(
          opacity: pageIsScroll != null
              ? (pageIsScroll == true ? 0 : 1)
              : (shrinkOffset > 0 ? 0 : 1),
          child: _firstChild),
      Opacity(
          opacity: pageIsScroll != null
              ? (pageIsScroll == true ? 1 : 0)
              : shrinkOffset > 0
                  ? 1
                  : 0,
          child: _secondChild),
    ];

    return Stack(
      children: _children,
    );
  }
}

/// 吸顶组件的子组件
class SeniorVesselChild extends StatelessWidget {
  const SeniorVesselChild(
      {Key? key,
      this.config,
      this.customStyle,
      required this.widgetListItem,
      this.pageIsScroll});

  /// [config] 动态容器的配置信息
  final SeniorVesselModel? config;

  /// [customStyle] 吸顶容器上滑和初始状态时自定义样式
  final SeniorVesselCustomStyleModel? customStyle;

  /// [widgetListItem] 为初始状态或者上滑状态的组件列表
  final List<Widget> widgetListItem;

  /// [pageIsScroll] 页面是否上滑, 只有吸顶导航需要关注，吸顶导航才有值
  final bool? pageIsScroll;

  @override
  Widget build(BuildContext context) {
    /// [pageIsScroll] 只有当顶部导航时才有值，顶部导航的高度包含状态栏高度
    double _height = config?.style?.attr?.height?.w ?? 0;

    if (pageIsScroll != null) {
      CommonUtil.setStatusBarHeight(context);
      _height += CommonUtil.statusBarHeight;
    }
    final EdgeInsets _padding = EdgeInsets.only(
        top: pageIsScroll != null ? CommonUtil.statusBarHeight : 0);

    return customStyle != null
        ? BackgroundAndFilterDecoration(
            background: customStyle!.background,
            child: SeniorVesselFloor(
                config: config,
                widgetList: widgetListItem,
                height: _height,
                padding: _padding))
        : SeniorVesselFloor(
            config: config,
            widgetList: widgetListItem,
            height: _height,
            padding: _padding);
  }
}

/// 只支持背景色、滤镜转外壳样式组件
class BackgroundAndFilterDecoration extends StatelessWidget {
  const BackgroundAndFilterDecoration(
      {Key? key, this.background, required this.child});

  /// [background] 背景模型
  final BackgroundModel? background;

  final Widget child;

  @override
  Widget build(BuildContext context) {
    final _blur = background?.filter?.blur ?? 0;
    // 最内层组件添加背景色
    final _innerWidget = Container(
        decoration: BoxDecoration(
            color: this.background?.bgColor,
            gradient: CommonUtil.toGradient(background?.color)),
        child: child);
    final _decorationWidget = _blur <= 0
        ? _innerWidget
        : ClipRect(
            child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: _blur, sigmaY: _blur),
                child: _innerWidget));

    return _decorationWidget;
  }
}
