import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_custom_style_model.dart';
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/empty_widget.dart';
import 'package:flutter_common_ui/src/widgets/senior_vessel/senior_vessel_floor.dart';
import 'package:flutter_common_ui/src/widgets/senior_vessel/senior_vessel_persistent.dart';

/// 楼层动态容器
/// 可以包含7类子组件：轮播、图片、文本、内容、菜单、直播、家庭
class SeniorVessel extends StatelessWidget {
  /// [config] 动态容器的配置信息
  final SeniorVesselModel? config;

  /// [widgetList] 为业务组件
  final List<List<Widget>>? widgetList;

  /// [customStyle] 吸顶容器上滑和初始状态时自定义样式
  final List<SeniorVesselCustomStyleModel>? customStyle;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [pageIsScroll] 页面是否上滑, 只有吸顶导航需要关注，吸顶导航才有值
  final bool? pageIsScroll;

  const SeniorVessel(
      {Key? key,
      this.config,
      this.widgetList,
      this.customStyle,
      this.isSliver = false,
      this.pageIsScroll});

  @override
  Widget build(BuildContext context) {
    if (config?.content?.vesselType == VesselTypeEnum.floor) {
      return SeniorVesselFloor(
          config: config, widgetList: widgetList?[0], isSliver: isSliver);
    } else if (config?.content?.vesselType == VesselTypeEnum.persistent) {
      return SeniorVesselPersistent(
          config: config,
          widgetList: widgetList,
          customStyle: customStyle,
          pageIsScroll: pageIsScroll);
    }
    return EmptyWidget(isSliver: isSliver);
  }
}
