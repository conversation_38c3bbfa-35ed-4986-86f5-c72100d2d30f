/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-20 14:36:12
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/family/family_info_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/family/family_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 家庭组件
class FamilyContainer extends StatelessWidget {
  /// [config] 家庭组件的配置信息
  final FamilyInfoModel? config;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  /// 家庭要展示的标题
  final String? familyTitle;

  /// 是否展示家庭标题后面的箭头
  final bool? isArrow;

  /// 点击家庭标题触发的方法
  final Function? onFamilyTap;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// 绑定事件方法
  final Function? onTap;

  const FamilyContainer(
      {Key? key,
      this.config,
      this.colorSystem,
      this.familyTitle,
      this.isArrow,
      this.onFamilyTap,
      this.relative = false,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.style?.attr == null) {
      return const SizedBox();
    }
    return config!.style!.attr!.toDecorationWidget(
        relative: relative,
        colorSystem: colorSystem,
        child: TrackWidget(
          Container(
              width: config!.style!.attr!.width!.w,
              height: config!.style!.attr!.height!.w,
              child: FamilyScreen(
                  config: config, familyTitle: familyTitle, isArrow: isArrow)),
          trackMap: {"button_name": familyTitle},
          key: ValueKey(config?.aliasUnit ?? 'family'),
          onTap: () {
            if (onTap != null) onTap!(config?.customFeature, context);
          },
          trackInfo: config?.attr?.trackInfo,
        ));
  }
}
