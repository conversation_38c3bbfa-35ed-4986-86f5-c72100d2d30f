import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/family/family_info_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 家庭子组件
class FamilyScreen extends StatelessWidget {
  /// [config] 家庭布局配置
  final FamilyInfoModel? config;

  /// 家庭要展示的标题
  final String? familyTitle;

  /// 是否展示家庭标题后面的箭头
  final bool? isArrow;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  FamilyScreen(
      {Key? key, this.config, this.familyTitle, this.isArrow, this.colorSystem})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    if (config?.style?.attr == null) {
      return const SizedBox();
    }

    return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Flexible(
              child: Container(
                  margin: EdgeInsets.only(right: 4.w),
                  child: Text(
                    familyTitle ?? '',
                    style: TextStyle(

                        /// 家庭名称颜色不根据可视化系统进行配置,根据主题换肤的深浅色系展示
                        color: Color.fromRGBO(0, 0, 0, 0.93),
                        fontSize: config?.attr?.fontSize?.sp,
                        height: 1.2,
                        fontWeight: TextCommonUtils.getFontWeightFromString(
                            config?.attr?.fontWeight)),
                    softWrap: false,
                    overflow: TextOverflow.ellipsis,
                  ))),
          isArrow == true
              ? Image.asset('assets/images/arrow-right-round.png',
                  width: 16.w, height: 16.w, package: Constant.packageName)
              : SizedBox()
        ]);
  }
}
