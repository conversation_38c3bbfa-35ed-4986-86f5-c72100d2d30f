/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-01 15:07:29
 * @description: 直播组件 UI 样式
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/live/live_data_model.dart';
import 'package:flutter_common_ui/src/models/live/live_visual_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/live/live_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Live extends StatelessWidget {
  /// [config] 直播组件的配置信息
  final LiveVisualModel? config;

  /// 直播数据
  final LiveDataModel? liveData;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// 绑定事件方法
  final Function? onTap;

  const Live(
      {Key? key, this.config, this.liveData, this.relative = false, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.attr?.content == null || config?.style?.attr == null) {
      return const SizedBox();
    }
    return config!.style!.attr!.toDecorationWidget(
        relative: relative,
        child: Container(
            width: config?.style?.attr?.width?.w,
            height: config?.style?.attr?.height?.w,
            child: TrackWidget(
              LiveScreen(
                  attrConfig: config?.attr,
                  liveData: liveData,
                  radius: config?.style?.attr?.radius),
              key: ValueKey(config?.aliasUnit ?? 'live'),
              onTap: () {
                if (onTap != null) onTap!(config?.customFeature, context);
              },
              trackInfo: config?.attr?.trackInfo,
              trackMap: {},
              onExpose: () {},
            )));
  }
}
