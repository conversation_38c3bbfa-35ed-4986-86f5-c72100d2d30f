/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-02-10 17:09:44
 * @description: 直播组件
 */
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/live/live_data_model.dart';
import 'package:flutter_common_ui/src/models/live/live_visual_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/placeholder_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 直播中
const isLiving = '1';
// 预约
const subscribe = '2';
// 回放
const playback = '3';

class LiveScreen extends StatelessWidget {
  /// 【config】直播组件的配置信息
  final LiveAttrInfo? attrConfig;

  /// 直播数据
  final LiveDataModel? liveData;

  final RadiusModel? radius;
  const LiveScreen({Key? key, this.attrConfig, this.liveData, this.radius})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    AttrState? _liveStatusConfig;
    if (attrConfig?.content == null) {
      return const SizedBox();
    }

    /// 设置直播状态的UI配置
    void _setLiveStatusConfig() {
      if (liveData?.status != null &&
          liveData!.status != '' &&
          attrConfig?.content?.liveBroadcastStatus?.checked == true) {
        _liveStatusConfig = (liveData!.status == isLiving
            ? attrConfig?.content?.liveBroadcastStatus?.live
            : (liveData!.status == subscribe
                ? attrConfig?.content?.liveBroadcastStatus?.appointment
                : attrConfig?.content?.liveBroadcastStatus?.playback));
      }
    }

    _setLiveStatusConfig();

    // 直播中、预约、回顾的标签
    Widget _getLabel() {
      // 没有数据、配置不显示直播状态时不展示label
      if (attrConfig?.content?.liveBroadcastStatus?.checked != true) {
        return const SizedBox();
      }
      // 拿直播状态，为空则不显示
      if (liveData?.text == '' || liveData?.text == null) {
        return const SizedBox();
      }
      return Align(
        alignment: Alignment.topLeft,
        child: Container(
            constraints: BoxConstraints(minWidth: 61.w),
            padding:
                EdgeInsets.only(left: 9.w, right: 9.w, top: 1.w, bottom: 1.w),
            decoration: BoxDecoration(
              gradient: _liveStatusConfig?.background?.color?.colorOfGradient,
              color: _liveStatusConfig?.background?.color?.color,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.w),
                  bottomRight: Radius.circular(12.w)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                    child: Text(
                  liveData!.text != null ? liveData!.text! : '',
                  style: TextStyle(
                      fontSize: attrConfig
                          ?.content?.liveBroadcastStatus?.fontSize?.sp,
                      color: attrConfig
                          ?.content?.liveBroadcastStatus?.color?.color,
                      fontWeight: TextCommonUtils.getFontWeightFromString(
                          attrConfig
                              ?.content?.liveBroadcastStatus?.fontWeight)),
                )),
                // 直播状态的小图标
                _liveStatusConfig?.icon != ''
                    ? Container(
                        width: 15.w,
                        height: 15.w,
                        margin: EdgeInsets.only(left: 1.w),
                        child: CommonNetWorkImage(
                          url: _liveStatusConfig?.icon ?? '',
                          fit: attrConfig?.shadow != null
                              ? BoxFit.cover
                              : BoxFit.contain,
                          errorWidget: PlaceHolderImage(),
                        ))
                    : SizedBox()
              ],
            )),
      );
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        Container(
          child: ClipRRect(
              borderRadius: radius?.borderRadius ?? BorderRadius.zero,
              child: (liveData?.coverPicture != null &&
                      liveData?.coverPicture != '')
                  ? CommonNetWorkImage(
                      url: liveData?.coverPicture!,
                      fit: BoxFit.cover,
                      placeHolder: SizedBox(),
                      errorWidget: PlaceHolderImage(),
                    )
                  : const PlaceHolderImage()),
        ),
        _getLabel()
      ],
    );
  }
}
