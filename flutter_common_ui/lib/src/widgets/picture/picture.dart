/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-13 14:35:59
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/empty_widget.dart';
import 'package:flutter_common_ui/src/widgets/picture/picture_layout.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片组件
class Picture extends StatelessWidget {
  /// [config] 图片组件的配置信息
  final PictureModel? config;

  /// [data] 业务接口的图片数据
  final List<AttrBannerImg>? data;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  /// [needReload] 是否需要重新加载图片
  final int? needReload;

  /// 绑定事件方法
  final Function? onTap;

  /// 页面展示的子图片[displayData]
  List<AttrBannerImg>? get displayData =>
      data?.length != null && data!.length > 0 ? data : config?.attr?.sourceMap;

  const Picture(
      {Key? key,
      this.config,
      this.data,
      this.isSliver = false,
      this.relative = false,
      this.colorSystem,
      this.needReload,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.attr?.mode == null || config?.style?.attr == null) {
      return EmptyWidget(isSliver: isSliver);
    }
    final _child = config!.style!.attr!.toDecorationWidget(
        type: config?.type,
        relative: relative,
        colorSystem: colorSystem,
        child: Container(
            padding: config!.style!.attr!.padding?.padding,
            width: config!.style!.attr!.width?.w,
            height: config!.style!.attr!.height?.w,
            child: PictureLayout(
                displayData: displayData,
                attr: config!.attr!,
                width: config!.style!.attr!.width,
                height: config!.style!.attr!.height,
                needReload: needReload,
                onTap: onTap)));

    return isSliver == true ? SliverToBoxAdapter(child: _child) : _child;
  }
}
