import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/widgets/picture/expanded_pic_item.dart';
import 'package:flutter_common_ui/src/widgets/picture/picture_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片布局，包含6种：1-一行一个 2-一行两个 3-一行三个 4-一行四个 5-左一右二 6-上一下二
/// width: 图片组件宽度(非子图片宽度)， n: 子图片个数, index: 子图片索引， gutter: 间距， gutterIn: 二次分割的间距,
/// 1-4: 子图片宽 = (width - (n - 1) * gutter) / n ,子图片间距 = gutter;
/// 5: 子图片宽 = (width - gutter) / 2, 左右间距gutter, 上下子图片间距 = gutterIn;
/// 6: 子图片宽 = (width - gutterIn) / 2, 上下图片间距gutter, 左右子图片间距 = gutterIn;
class PictureLayout extends StatelessWidget {
  /// [attr] 图片组件的配置信息,包括布局，子图片内容，子图片样式
  final PictureAttr attr;

  /// [width] 图片组件的宽
  final double? width;

  /// [height] 图片组件的高
  final double? height;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [displayData] 展示的子图片
  final List<AttrBannerImg>? displayData;

  /// 绑定事件方法
  final Function? onTap;

  PictureLayout(
      {Key? key,
      required this.attr,
      this.width,
      this.height,
      this.needReload,
      this.displayData,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final mode = this.attr.mode;
    final width = this.width ?? 0;
    final height = this.height ?? 0;
    // 这里圆角转成了BorderRadius类，直接使用
    final BorderRadius? radius = this.attr.bannerStyle?.radius?.borderRadius;
    final gutter = this.attr.bannerStyle?.gutter ?? 0;
    final gutterIn = this.attr.bannerStyle?.gutterIn ?? 0;
    final cornerMark = this.attr.extraContent?.cornerMark;
    // 图片有布局并且有子图片数据
    if (mode != null &&
        displayData?.length != null &&
        displayData!.length > 0) {
      switch (mode) {
        case PictureModeType.single:
        case PictureModeType.twoInRow:
        case PictureModeType.threeInRow:
        case PictureModeType.fourInRow:
          // 行布局：一行一个，两个，三个，四个
          return _PictureRowLayout(
              mode: mode,
              displayData: displayData!,
              width: width,
              height: height,
              radius: radius,
              gutter: gutter,
              cornerMark: cornerMark,
              needReload: needReload,
              onTap: onTap);
        case PictureModeType.leftOneRightTwo:
          // 左一右二
          return _PictureLeftOneRightTwoLayout(
              mode: mode,
              displayData: displayData!,
              width: width,
              height: height,
              radius: radius,
              gutter: gutter,
              gutterIn: gutterIn,
              cornerMark: cornerMark,
              needReload: needReload,
              onTap: onTap);
        case PictureModeType.upOneDownTwo:
          // 上一下二
          return _PictureUpOneDownTwoLayout(
              mode: mode,
              displayData: displayData!,
              width: width,
              height: height,
              radius: radius,
              gutter: gutter,
              gutterIn: gutterIn,
              cornerMark: cornerMark,
              needReload: needReload,
              onTap: onTap);
        default:
          return const SizedBox();
      }
    }
    return const SizedBox();
  }
}

class _PictureRowLayout extends StatelessWidget {
  /// [mode] 图片布局参数
  final PictureModeType mode;

  /// [displayData] 展示的子图片
  final List<AttrBannerImg> displayData;

  /// [width] 图片组件宽
  final double width;

  /// [height] 图片组件高
  final double height;

  /// [radius] 子图片组件圆角
  final BorderRadius? radius;

  /// [gutter] 子图片间距1
  final double gutter;

  /// [cornerMark] 角标
  final AttrCornerMark? cornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onTap]绑定事件方法
  final Function? onTap;

  _PictureRowLayout(
      {Key? key,
      required this.mode,
      required this.displayData,
      required this.width,
      required this.height,
      this.radius,
      required this.gutter,
      this.cornerMark,
      this.needReload,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
        children: loopPics(
            expanded: true,
            startIndex: 0,
            mode: mode,
            displayData: displayData,
            width: width,
            height: height,
            radius: radius,
            cornerMark: cornerMark,
            gutter: gutter,
            needReload: needReload,
            onTap: onTap));
  }
}

/// 左一右二的图片布局
class _PictureLeftOneRightTwoLayout extends StatelessWidget {
  /// [mode] 图片布局参数
  final PictureModeType mode;

  /// [displayData] 展示的子图片
  final List<AttrBannerImg> displayData;

  /// [width] 图片组件宽
  final double width;

  /// [height] 图片组件高
  final double height;

  /// [radius] 子图片组件圆角
  final BorderRadius? radius;

  /// [gutter] 子图片间距1
  final double gutter;

  /// [gutterIn] 子图片间距2
  final double gutterIn;

  /// [cornerMark] 角标
  final AttrCornerMark? cornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onTap]绑定事件方法
  final Function? onTap;

  _PictureLeftOneRightTwoLayout(
      {Key? key,
      required this.mode,
      required this.displayData,
      required this.width,
      required this.height,
      this.radius,
      required this.gutter,
      required this.gutterIn,
      this.cornerMark,
      this.needReload,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      ExpandedPicItem(
          item: displayData[0],
          index: 0,
          width: (width - gutter) / 2,
          radius: radius,
          cornerMark: cornerMark,
          needReload: needReload,
          onTap: onTap),
      Padding(padding: EdgeInsets.only(right: this.gutter.w)),
      Expanded(
          child: Column(
              children: loopPics(
                  mode: mode,
                  startIndex: 1,
                  displayData: displayData.sublist(1),
                  width: width,
                  height: height,
                  radius: radius,
                  gutter: gutterIn,
                  cornerMark: cornerMark,
                  needReload: needReload,
                  onTap: onTap)))
    ]);
  }
}

/// 上一下二的图片布局
class _PictureUpOneDownTwoLayout extends StatelessWidget {
  /// [mode] 图片布局参数
  final PictureModeType mode;

  /// [displayData] 展示的子图片
  final List<AttrBannerImg> displayData;

  /// [width] 图片组件宽
  final double width;

  /// [height] 图片组件高
  final double height;

  /// [radius] 子图片组件圆角
  final BorderRadius? radius;

  /// [gutter] 子图片间距1
  final double gutter;

  /// [gutterIn] 子图片间距2
  final double gutterIn;

  /// [cornerMark] 角标
  final AttrCornerMark? cornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onTap]绑定事件方法
  final Function? onTap;

  _PictureUpOneDownTwoLayout(
      {Key? key,
      required this.mode,
      required this.displayData,
      required this.width,
      required this.height,
      this.radius,
      required this.gutter,
      required this.gutterIn,
      this.cornerMark,
      this.needReload,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      ExpandedPicItem(
          item: displayData[0],
          index: 0,
          width: width,
          height: height,
          radius: radius,
          cornerMark: cornerMark,
          needReload: needReload,
          onTap: onTap),
      Padding(padding: EdgeInsets.only(bottom: this.gutter.w)),
      Row(
          children: loopPics(
              mode: mode,
              startIndex: 1,
              displayData: displayData.sublist(1),
              width: width,
              height: height,
              radius: radius,
              gutter: gutterIn,
              cornerMark: cornerMark,
              needReload: needReload,
              onTap: onTap))
    ]);
  }
}
