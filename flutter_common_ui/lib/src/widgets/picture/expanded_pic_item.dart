import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/animated_corner_mark/animated_corner_mark.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/placeholder_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 图片角标偏移量
final picItemCornerMarkOffset = Offset(0, -4.w);

/// 子图片项
class _PicItem extends StatelessWidget {
  /// [item] 图片
  final AttrBannerImg? item;

  /// [index] 索引 从左往右，从上到下
  final int index;

  /// [width] 图片宽
  final double? width;

  /// [height] 图片高
  final double? height;

  /// [radius] 圆角值
  final BorderRadius? radius;

  /// [attrCornerMark] 角标配置
  final AttrCornerMark? attrCornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// 绑定事件方法
  final Function? onTap;

  _PicItem(
      {Key? key,
      this.item,
      this.index = 0,
      this.width,
      this.height,
      this.radius,
      this.attrCornerMark,
      this.needReload,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
        borderRadius: radius ?? BorderRadius.zero,
        child: TrackWidget(
          CommonNetWorkImage(
            url: item?.img ?? '',
            width: width?.w,
            height: height?.w,
            fit: BoxFit.fitWidth,
            errorWidget: PlaceHolderImage(),
            needReload: needReload,
          ),
          key: item?.id != null ? ValueKey(item?.id) : null,
          trackMap: {
            'hyperlink': item?.customFeature?.eventParams?.nav_url ?? '',
            // 和server沟通，驰骛那边配置没有title, 使用物料名称代替
            'content_title': item?.materialName ?? '',
            'material_id': item?.id?.toString() ?? '',
            "material_name": item?.materialName ?? '',
            "operation_plan_id": item?.planId ?? '',
            'site_number': (index + 1).toString(),
          },
          trackInfo: item?.trackInfo,
          onTap: () =>
              onTap != null ? onTap!(item?.customFeature, context) : null,
        ));
  }
}

/// 带角标的图片
class PicItemWithSuperscript extends StatelessWidget {
  /// [item] 图片
  final AttrBannerImg? item;

  /// [index] 编号 从左往右，从上到下，从1开始
  final int index;

  /// [width] 图片宽
  final double? width;

  /// [height] 图片高
  final double? height;

  /// [radius] 圆角值
  final BorderRadius? radius;

  /// [attrCornerMark] 角标
  final AttrCornerMark? attrCornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [offset]
  final Offset? offset;

  /// [onTap]绑定事件方法
  final Function? onTap;

  const PicItemWithSuperscript(
      {Key? key,
      this.index = 0,
      this.item,
      this.width,
      this.height,
      this.radius,
      this.attrCornerMark,
      this.needReload,
      this.offset,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    /// 动态文字角标
    return (this.attrCornerMark?.type == AttrCornerMarkTypeEnum.text &&
                this.attrCornerMark?.textContent?[0].text?.isNotEmpty ==
                    true) ||
            this.attrCornerMark?.type == AttrCornerMarkTypeEnum.redDot
        ? Stack(fit: StackFit.expand, clipBehavior: Clip.none, children: [
            _PicItem(
                index: index,
                item: item,
                width: width,
                height: height,
                radius: radius,
                attrCornerMark: attrCornerMark,
                needReload: needReload,
                onTap: onTap),
            Positioned(
                right: offset?.dx,
                top: offset?.dy,
                child: AnimatedCornerMark(
                  attrCornerMark: attrCornerMark,
                ))
          ])
        : _PicItem(
            index: index,
            item: item,
            width: width,
            height: height,
            radius: radius,
            needReload: needReload,
            onTap: onTap);
  }
}

// 用Expanded包裹的带角标的图片
class ExpandedPicItem extends StatelessWidget {
  /// [item] 图片
  final AttrBannerImg? item;

  /// [index] 索引 从左往右，从上到下，从开始
  final int index;

  /// [width] 图片宽
  final double? width;

  /// [height] 图片高
  final double? height;

  /// [radius] 圆角值
  final BorderRadius? radius;

  /// [cornerMark] 角标
  final AttrCornerMark? cornerMark;

  /// [needReload] 是否要更新图片extendedImage使用
  final int? needReload;

  /// [onTap]绑定事件方法
  final Function? onTap;

  const ExpandedPicItem(
      {Key? key,
      this.item,
      this.index = 0,
      this.width,
      this.height,
      this.radius,
      this.cornerMark,
      this.needReload,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: PicItemWithSuperscript(
            item: item,
            width: width,
            height: height,
            radius: radius,
            attrCornerMark: cornerMark,
            needReload: needReload,
            offset: picItemCornerMarkOffset,
            onTap: onTap));
  }
}
