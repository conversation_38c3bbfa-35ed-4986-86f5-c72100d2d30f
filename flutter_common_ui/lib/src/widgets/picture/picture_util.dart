/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-13 20:01:21
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/widgets/picture/expanded_pic_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 返回值为图片列表
List<Widget> loopPics(
    {

    /// [expanded] 子图片是否需要Expanded组件包裹
    bool expanded = false,

    /// [mode] 图片布局参数
    required PictureModeType mode,

    /// [startIndex] 开始的索引
    required int startIndex,

    /// [displayData] 展示的子图片
    required final List<AttrBannerImg> displayData,

    /// [width] 图片组件宽
    required double width,

    /// [height] 图片组件高
    required double height,

    /// [radius] 子图片组件圆角
    BorderRadius? radius,

    /// [gutter] 子图片间距
    required double gutter,

    /// [cornerMark] 角标
    final AttrCornerMark? cornerMark,

    /// [needReload] 是否要更新图片extendedImage使用
    final int? needReload,

    /// [onTap]绑定事件方法
    final Function? onTap}) {
  List<Widget> _list = [];

  /// 图片宽度
  /// 1 一行n个： (组件宽度 - (间距 * 图片个数 -1)) / 图片个数, 1个图的时候不考虑图片间距
  /// 2 左一右二：(组件宽度 - 间距) / 2
  /// 3 上一下二：(组件宽度 - 间距) / 2
  /// 图片高度
  /// 1 一行n个： 组件高度
  /// 2 左一右二：(组件高度 - 间距) / 2
  /// 3 上一下二：(组件高度 - 间距) / 2
  /// [_itemWidth] 子图片宽
  double _itemWidth = width;

  /// [_itemHeight] 子图片高
  double _itemHeight = height;
  if (mode == PictureModeType.leftOneRightTwo ||
      mode == PictureModeType.upOneDownTwo) {
    _itemWidth = (width - gutter) / 2;
    _itemHeight = (height - gutter) / 2;
  } else {
    _itemWidth = displayData.length == 1
        ? width
        : (width - gutter * (displayData.length - 1)) / displayData.length;
    _itemHeight = height;
  }

  displayData.asMap().entries.forEach((entry) {
    // 根据expanded= true ExpandedPicItem
    _list.add(expanded == true
        ? ExpandedPicItem(
            item: entry.value,
            index: startIndex + entry.key,
            width: _itemWidth,
            height: _itemHeight,
            radius: radius,
            cornerMark: cornerMark,
            needReload: needReload,
            onTap: onTap)
        : (entry.key == displayData.length - 1
            // 最后一张图使用Expanded包裹，防止越界
            ? ExpandedPicItem(
                item: entry.value,
                index: startIndex + entry.key,
                width: _itemWidth,
                height: _itemHeight,
                radius: radius,
                cornerMark: cornerMark,
                needReload: needReload,
                onTap: onTap)
            : PicItemWithSuperscript(
                item: entry.value,
                index: startIndex + entry.key,
                width: _itemWidth,
                height: _itemHeight,
                radius: radius,
                attrCornerMark: cornerMark,
                needReload: needReload,
                offset: picItemCornerMarkOffset,
                onTap: onTap)));

    //  mode = leftOneRightTwo 是左一右二，子图片有下边距，其他都是子图片有右边距
    if (entry.key != displayData.length - 1) {
      if (mode != PictureModeType.leftOneRightTwo) {
        _list.add(Padding(padding: EdgeInsets.only(right: gutter.w)));
      } else {
        _list.add(Padding(padding: EdgeInsets.only(bottom: gutter.w)));
      }
    }
  });
  return _list;
}
