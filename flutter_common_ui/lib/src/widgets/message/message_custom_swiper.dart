import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/message/message_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 消息通知内容
class MessageCustomSwiper extends StatelessWidget {
  final MsgHistoriesModel serviceMsgData;
  final MessageAttr? attrConfig;
  MessageCustomSwiper({
    Key? key,
    required this.serviceMsgData,
    this.attrConfig,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Swiper(
        itemBuilder: (BuildContext context, int index) {
          final _id = serviceMsgData.msgHistories![index].taskId;
          String? _title =
              serviceMsgData.msgHistories![index].message?.notification?.title;
          return TrackWidget(
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Expanded(
                    flex: 6,
                    child: Row(
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.only(right: 4.w, left: 8.w),
                          child: attrConfig?.icon == ''
                              ? Image(
                                  width: 30.w,
                                  height: 30.w,
                                  image: AssetImage(
                                      'assets/images/message_icon.png',
                                      package: Constant.packageName),
                                )
                              : CommonNetWorkImage(
                                  url: imageToWebp(attrConfig?.icon),
                                  width: 30.w,
                                  height: 30.w,
                                  fit: BoxFit.fill,
                                  errorWidget: Image.asset(
                                    'assets/images/message_icon.png',
                                    width: 30.w,
                                    height: 30.w,
                                    fit: BoxFit.fill,
                                    package: Constant.packageName,
                                    // mxd todo
                                  ),
                                ),
                        ),
                        Expanded(
                          child: Text(
                              serviceMsgData.msgHistories![index].message !=
                                          null &&
                                      serviceMsgData.msgHistories![index]
                                              .message!.notification !=
                                          null &&
                                      serviceMsgData.msgHistories![index]
                                              .message!.notification!.title !=
                                          null
                                  ? serviceMsgData.msgHistories![index].message!
                                      .notification!.title!
                                  : "",
                              overflow: TextOverflow.ellipsis,
                              softWrap: false,
                              style: TextStyle(
                                  fontSize: attrConfig!.fontSize?.sp,
                                  fontFamily: 'PingFangSC-Medium',
                                  fontWeight:
                                      TextCommonUtils.getFontWeightFromString(
                                          attrConfig!.fontWeight),
                                  color: attrConfig?.color?.color)),
                        )
                      ],
                    )),
                Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: <Widget>[
                        Flexible(
                            child: Text(
                          (serviceMsgData.msgHistories![index].receivedTime !=
                                  null
                              ? serviceMsgData.msgHistories![index].receivedTime
                              : '')!,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: attrConfig!.timeFontSize?.sp,
                              fontWeight:
                                  TextCommonUtils.getFontWeightFromString(
                                      attrConfig!.timeFontWeight),
                              color: attrConfig?.timeColor?.color),
                        )),
                        Container(
                          padding: EdgeInsets.only(top: 2.w, right: 12.w),
                          child: Image.asset(
                            'assets/images/icon_arrow.png',
                            width: 16.w,
                            height: 16.w,
                            color: Color(0xFF333333),
                            package: Constant.packageName,
                          ),
                        )
                      ],
                    ))
              ],
            ),
            key: ValueKey(_id ?? 'message-list-${index}'),
            trackMap: {'content_id': _id, 'content_title': _title},
            onTap: () {
              String? _url;
              try {
                if (serviceMsgData.msgHistories![index].message?.data?.body
                        ?.extData?.reviewPage !=
                    null) {
                  _url = serviceMsgData.msgHistories![index].message?.data?.body
                      ?.extData?.reviewPage;
                } else if (serviceMsgData.msgHistories![index].message?.data
                            ?.body?.extData?.pages !=
                        null &&
                    serviceMsgData.msgHistories![index].message?.data?.body
                            ?.extData?.pages?.length !=
                        0) {
                  _url = serviceMsgData.msgHistories![index].message?.data?.body
                      ?.extData?.pages![0].url;
                }
                if (_url != null && _url.length > 0) {
                  goToPage(_url);
                }
              } catch (err) {
                DevLogger.error(
                    tag: Constant.tagCommonUI,
                    msg: {'fn': 'message_notify_view ontap', 'err': err});
              }
            },
            trackInfo: attrConfig!.trackInfo,
          );
        },
        itemCount: serviceMsgData.msgHistories!.length,
        autoplay: serviceMsgData.msgHistories!.length > 1 ? true : false,
        physics: serviceMsgData.msgHistories!.length > 1
            ? ClampingScrollPhysics()
            : NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
      ),
    );
  }
}
