/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-24 17:34:56
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/message/message_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/message/message_screen.dart';

class MessageContainer extends StatelessWidget {
  /// [config] 消息通知组件的配置信息
  final MessageModel? config;

  /// [messageListData] 消息通知业务接口的消息数据
  final MsgHistoriesModel? messageListData;

  /// 绑定事件方法
  final Function? onTap;

  const MessageContainer(
      {Key? key, this.config, this.messageListData, this.onTap})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    if (config?.style?.attr == null ||
        messageListData?.msgHistories == null ||
        messageListData?.msgHistories!.length == 0) {
      return const SizedBox();
    }
    return config!.style!.attr!.toDecorationWidget(
        child: Container(
            child: TrackWidget(
                MessageScreen(
                  attrConfig: config?.attr,
                  serviceMsgData: messageListData,
                ), onTap: () {
      if (onTap != null) onTap!(config?.customFeature, context);
    },
                key: ValueKey(config?.aliasUnit ?? 'message'),
                trackInfo: config?.attr?.trackInfo,

                /// 埋点信息
                trackMap: {},
                onExpose: () {})));
  }
}
