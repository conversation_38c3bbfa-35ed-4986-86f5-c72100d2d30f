import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/message/message_model.dart';
import 'package:flutter_common_ui/src/widgets/message/message_custom_swiper.dart';

class MessageScreen extends StatelessWidget {
  /// [config] 消息通知组件自己的配置信息
  final MessageAttr? attrConfig;

  /// [styleConfig] 组件公共样式信息
  final StyleInfoModel? styleConfig;

  /// [serviceMsgData] 业务接口的消息数据
  final MsgHistoriesModel? serviceMsgData;

  MessageScreen({
    Key? key,
    this.attrConfig,
    this.styleConfig,
    this.serviceMsgData,
  });

  /// 消息内容组件
  Widget messageContainer() {
    return ClipRect(
        child: BackdropFilter(
            filter: ImageFilter.blur(
                sigmaX: blurPxToSigma(attrConfig?.shadow?.blur),
                sigmaY: blurPxToSigma(attrConfig?.shadow?.blur)),
            child: Container(
              child: MessageCustomSwiper(
                  serviceMsgData: serviceMsgData!, attrConfig: attrConfig),
            )));
  }

  @override
  Widget build(BuildContext context) {
    return serviceMsgData != null &&
            serviceMsgData?.msgHistories != null &&
            serviceMsgData?.msgHistories?.length != null &&
            serviceMsgData!.msgHistories!.length > 0
        ? Container(
            decoration: BoxDecoration(boxShadow: attrConfig?.shadow?.boxShadow),
            child: messageContainer())
        : SizedBox();
  }
}
