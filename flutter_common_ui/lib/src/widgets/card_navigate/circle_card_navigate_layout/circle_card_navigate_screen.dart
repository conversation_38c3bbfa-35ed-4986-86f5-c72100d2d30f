/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-06 15:33:59
 * @description: 卡片导航UI
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/card_navigate/card_navigate_model.dart';
import 'package:flutter_common_ui/src/models/card_navigate/card_navigate_attri_circle_item.dart';
import 'package:flutter_common_ui/src/models/common_models/padding_model.dart';
import 'package:flutter_common_ui/src/widgets/card_navigate/circle_card_navigate_layout/circle_card_navigate_layout.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CircleCardNavigateScreen extends StatelessWidget {
  /// [attr] 卡片导航组件的配置信息,包括布局，子卡片内容，子卡片样式
  final CardNavigateAttr attr;

  /// [width] 卡片组件的宽
  final double? width;

  /// [height] 卡片组件的高
  final double? height;

  /// [padding] 内边距
  final PaddingModel? padding;

  /// 卡片导航显示的数据
  final List<AttrCircleCardItem> items;

  /// [needReload] 图片刷新标志位
  final int? needReload;

  /// [onTap]绑定事件方法、小红点额外处理逻辑，点击过的 缓存起来，不再显示小红点
  final Function? onTap;

  final double borderWidth;

  const CircleCardNavigateScreen(
      {Key? key,
      required this.attr,
      this.width,
      this.height,
      this.padding,
      required this.items,
      this.needReload,
      this.onTap,
      this.borderWidth = 0.0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    /// [_itemWidth] 轮播项的宽度
    /// (轮播组件的宽 - 左右内边距 一 间距 * (轮播项个数.向上取整 - 1)) / 轮播项个数
    num _itemCount = attr.singleSize?.col ?? 1;
    num _gutter = attr.gutter ?? 0;
    double _itemWidth = (((width ?? 0) -
                (padding?.left ?? 0) -
                (padding?.right ?? 0) -
                borderWidth * 2 -
                ((_itemCount.ceilToDouble() - 1) * _gutter)) /
            _itemCount)
        .w;

    return Container(
      child: CircleCardNavigateLayout(
          itemWidth: _itemWidth,
          gutter: attr.gutter,
          padding: padding,
          items: items,
          needReload: needReload,
          shadow: attr.shadow,
          radius: attr.radius,
          fit: BoxFit.contain,
          onTap: onTap),
    );
  }
}
