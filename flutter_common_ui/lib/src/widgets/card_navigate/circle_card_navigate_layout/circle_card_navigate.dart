/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-06 14:31:29
 * @description: 卡片导航UI样式
 */
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/card_navigate/card_navigate_attri_circle_item.dart';
import 'package:flutter_common_ui/src/models/card_navigate/card_navigate_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/empty_widget.dart';
import 'package:flutter_common_ui/src/widgets/card_navigate/circle_card_navigate_layout/circle_card_navigate_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// 卡片导航 配置的时候，上边距 需要配置成4.要不然角标会被截断
class CircleCardNavigate extends StatelessWidget {
  /// [config] 图片组件的配置信息
  final CardNavigateModel? config;

  /// [isSliver] 是否为sliver组件, 默认false
  final bool isSliver;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  /// 卡片导航显示的数据
  final List<AttrCircleCardItem> items;

  /// [needReload] 图片刷新标志位
  final int? needReload;

  /// 绑定事件方法、小红点额外处理逻辑，点击过的 缓存起来，不再显示小红点
  final Function? onTap;

  const CircleCardNavigate(
      {Key? key,
      this.config,
      this.isSliver = false,
      this.relative = false,
      required this.items,
      this.needReload,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.style?.attr == null ||
        config?.attr?.items == null ||
        config?.attr?.items?.length == 0) {
      return EmptyWidget(isSliver: isSliver);
    }
    double borderWidth = config!.style!.attr!.border!.width ?? 0;
    final _child = config!.style!.attr!.toDecorationWidget(
      type: config?.type,
      relative: relative,
      withBackgroudColor: config!.style!.attr?.background?.bgColor == null,
      child: Stack(
          children: [
            config!.style!.attr?.background?.bgColor != null ? ClipRRect(
              borderRadius:
                  config!.style!.attr?.radius?.borderRadius ?? BorderRadius.zero,
              child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
                  child: Container(
                    width: config!.style!.attr!.width?.w,
                    height: config!.style!.attr!.height?.w,
                    color: config!.style!.attr?.background?.bgColor,
                  )),
            ) : Container(),
            Container(
                width: config!.style!.attr!.width?.w,
                height: config!.style!.attr!.height?.w,
                child: CircleCardNavigateScreen(
                    attr: config!.attr!,
                    width: config!.style!.attr!.width,
                    height: config!.style!.attr!.height,
                    padding: config!.style!.attr!.padding,
                    items: items,
                    needReload: needReload,
                    onTap: onTap,
                    borderWidth: config!.style!.attr!.border!.isSDKBorder ? 0 : borderWidth,))
          ],
        ));
    return isSliver == true ? SliverToBoxAdapter(child: _child) : _child;
  }
}
