/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2022-12-14 15:16:06
 * @description: 
 */

import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/hex_color.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_common_ui/src/models/waterfall/common_models.dart';
import 'package:flutter_common_ui/src/models/waterfall/content_card_model.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/waterfall/waterfall_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:storage/storage.dart';

class UgcGridContent extends StatefulWidget {
  final ContentCardModel? data;
  final int? index;
  final String? roughlyClassificationId;
  final String? roughlyClassificationName;
  final String? detailedClassificationId;
  final String? detailedClassificationName;
  final ValueNotifier<num?>? likeCountNotifier;
  final bool? isAppear;
  UgcGridContent(
      {Key? key,
      this.data,
      this.index,
      this.roughlyClassificationId,
      this.roughlyClassificationName,
      this.detailedClassificationId,
      this.detailedClassificationName,
      this.likeCountNotifier,
      this.isAppear})
      : super(key: key) {}

  @override
  _UgcGridContentState createState() => _UgcGridContentState();
}

class _UgcGridContentState extends State<UgcGridContent> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isAppear != oldWidget.isAppear) {
      // 从h5页面 返回的时候 点赞数量 变化
      if (widget.isAppear == true && widget.data != null) {
        _retrieveLikeCount(widget.data!.contentId ?? '');
      }
    }
  }

  Widget _buildItem() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
      ),
      padding: EdgeInsets.only(bottom: 13.w),
      child: Column(
        children: <Widget>[
          ClipRRect(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.w),
                  topRight: Radius.circular(12.w)),
              child: Stack(
                children: <Widget>[
                  widget.data != null &&
                          widget.data?.contentType != null &&
                          widget.data?.contentType != ''
                      ? Container(
                          color: HexColor('#dddddd'),
                          child:
                              _getImageWidget(widget.data?.contentType ?? ''),
                        )
                      : Container(),
                  widget.data?.contentType == 'video'
                      ? Positioned(
                          top: 8.w,
                          right: 8.w,
                          child: Image.asset(
                            'assets/images/waterfall/play.png',
                            package: Constant.packageName,
                            fit: BoxFit.cover,
                            height: 20.w,
                            width: 20.w,
                          ),
                        )
                      : Container()
                ],
              )),

          widget.data?.title != '' && widget.data?.title != null
              ? Container(
                  margin: EdgeInsets.only(top: 12.w, left: 10.w, right: 10.w),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    widget.data?.title ?? '',
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    style: TextStyle(
                      color: Color.fromRGBO(0, 0, 0, 0.8),
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              : Container(),
          // 头部头像、昵称、时间、分享弹窗
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(top: 8.w, bottom: 0.w),
            margin: EdgeInsets.only(left: 10.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Container(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Stack(
                        clipBehavior: Clip.none,
                        // overflow: Overflow.visible,
                        children: <Widget>[
                          ClipRRect(
                              borderRadius: BorderRadius.circular(11.w),
                              child: CommonNetWorkImage(
                                url: widget.data?.user?.icon ?? '',
                                width: 22.w,
                                height: 22.w,
                                fit: BoxFit.fitWidth,
                                placeHolder: Image.asset(
                                  'assets/images/waterfall/img_bg.png',
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fitWidth,
                                  package: Constant.packageName,
                                  // mxd todo
                                ),
                                errorWidget: Image.asset(
                                  'assets/images/waterfall/img_bg.png',
                                  width: 22.w,
                                  height: 22.w,
                                  fit: BoxFit.fitWidth,
                                  package: Constant.packageName,
                                  // mxd todo
                                ),
                              )),
                          widget.data?.user != null &&
                                  widget.data?.user?.userType != null &&
                                  (widget.data?.user?.userType == 2 ||
                                      widget.data?.user?.userType == 3)
                              ? Positioned(
                                  bottom: 0,
                                  right: -4.w,
                                  child: GestureDetector(
                                    onTap: () {},
                                    child: Image.asset(
                                      widget.data?.user?.userType == 2
                                          ? 'assets/images/waterfall/avatar_pgc.png'
                                          : 'assets/images/waterfall/avatar_zhijiahao.png',
                                      package: Constant.packageName,
                                      fit: BoxFit.cover,
                                      height: 14.w,
                                      width: 14.w,
                                    ),
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                      widget.data?.user != null &&
                              widget.data?.user?.nickname != null &&
                              widget.data?.user?.nickname != ''
                          ? Container(
                              margin: EdgeInsets.only(left: 6.w),
                              width: 70.w,
                              child: Text(
                                widget.data?.user?.nickname ?? '',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                style: TextStyle(
                                  color: Color.fromRGBO(0, 0, 0, 0.8),
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            )
                          : Container(),
                    ],
                  ),
                ),
                ValueListenableBuilder<num?>(
                    valueListenable:
                        widget.likeCountNotifier ?? ValueNotifier<num?>(0),
                    builder: (BuildContext context, num? likeCount, Widget? _) {
                      return likeCount != null
                          ? Container(
                              margin: EdgeInsets.only(right: 10.w),
                              child: Text(
                                likeCount > 0
                                    ? '${WaterfallUtil.numberFormat(likeCount.toInt())}赞'
                                    : '',
                                style: TextStyle(
                                  color: Color.fromRGBO(0, 0, 0, 0.8),
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            )
                          : Container();
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 区分.gif动图和其他图片 做不同处理，.gif动图，添加height属性
  Widget _getImageWidget(String contentType) {
    List<dynamic>? cover = jsonDecode(contentType == 'picture'
        ? widget.data!.imageUrlInfo!
        : widget.data!.coverUrlInfo!) as List<dynamic>?;
    double percent = 1.0;
    if (cover != null &&
        cover.isNotEmpty &&
        cover[0] != null &&
        cover[0]['height'] != null &&
        cover[0]['height'] != 0 &&
        cover[0]['width'] != null &&
        cover[0]['width'] != 0) {
      percent = (cover[0]['width'] as int) / (cover[0]['height'] as int);
    }

    if (percent < 9 / 16) {
      percent = 9 / 16;
    }
    if (percent > 3) {
      percent = 3;
    }
    return CommonNetWorkImage(
      url: _toGetImage(widget.data?.contentType ?? ''),
      width: 168.w,
      height: (168.w / percent).ceil().toDouble(),
      fit: BoxFit.fill,
      placeHolder: Image.asset(
        'assets/images/waterfall/img_bg.png',
        width: 168.w,
        height: (168.w / percent).ceil().toDouble(),
        fit: BoxFit.fill,
        package: Constant.packageName,
      ),
      errorWidget: Image.asset(
        'assets/images/waterfall/img_bg.png',
        width: 168.w,
        height: (168.w / percent).ceil().toDouble(),
        fit: BoxFit.fill,
        package: Constant.packageName,
        // mxd todo
      ),
    );
  }

  //对宽高比不小于9:16和宽高比不大于3:1的图片进行裁剪
  String _toCuttingDownImg(String contentType) {
    String? _imageUrl = '';
    List<dynamic>? cover = jsonDecode(contentType == 'picture'
        ? widget.data!.imageUrlInfo!
        : widget.data!.coverUrlInfo!) as List<dynamic>?;
    if (cover != null &&
        cover.isNotEmpty &&
        cover[0] != null &&
        (cover[0] as Map<String, dynamic>)['imageUrl'] != null) {
      _imageUrl = cover[0]['imageUrl'] as String;
    }
    if (_imageUrl != '') {
      if (_imageUrl.indexOf('?') > 0) {
        if (_imageUrl.indexOf('x-oss-process') > 0) {
          return _imageUrl;
        } else {
          return '${_imageUrl.split('?')[0]}?x-oss-process=image/format,webp&${_imageUrl.split('?')[1]}';
        }
      } else {
        return '$_imageUrl?x-oss-process=image/format,webp';
      }
    } else {
      return '';
    }
  }

  // 计算宽高比
  String _toGetImage(String contentType) {
    List<dynamic>? cover = jsonDecode((contentType == 'picture'
        ? widget.data?.imageUrlInfo
        : widget.data?.coverUrlInfo)!) as List<dynamic>;
    double dpr = window.devicePixelRatio;

    final safeGet = CommonUtil.safeGet;

    if ((cover).isNotEmpty &&
        cover[0] != null &&
        cover[0]['imageUrl'] != null) {
      String url = safeGet<String>(cover[0]['imageUrl']) ?? '';
      //如果图片已经做过裁剪
      if (url.indexOf('x-oss-process') > 0) {
        //图片非视频第一帧裁剪过
        if (!url.contains('x-oss-process=video/snapshot')) {
          url = url.split('?x-oss-process')[0];
        } else {
          //图片是裁剪的视频第一帧
          return _toCuttingDownImg(contentType);
        }
      }
      if (safeGet<num>(cover[0]['width'])! / safeGet<num>(cover[0]['height'])! <
          9 / 16) {
        // 宽高比  <9：16的，按9:16 比例从中心点裁剪
        return '$url?x-oss-process=image/resize,m_fill,w_${(480.w * dpr).ceil()},h_${(480.w * dpr * 16 / 9).ceil()},limit_0/auto-orient,1';
      } else if (safeGet<num>(cover[0]['width'])! /
              safeGet<num>(cover[0]['height'])! >
          3 / 1) {
        // 宽高比 >3:1的，按3:1比例从中心点裁剪。
        return '$url?x-oss-process=image/resize,m_fill,w_${(480.w * dpr).ceil()},h_${(480.w * dpr * 1 / 3).ceil()},limit_0/auto-orient,1';
      } else {
        //普通图片处理
        return _toCuttingDownImg(contentType);
      }
    }
    return '';
  }

  //用户类型转换
  String _handleUserType() {
    final String _type = widget.data?.user?.userType?.toString() ?? 'null';
    if (_type == '1') {
      return 'UGC';
    } else if (_type == '2') {
      return 'PGC';
    } else if (_type == '3') {
      return '智家号';
    } else {
      return 'null';
    }
  }

  //跳转详情页面以及埋点
  void _toGoodsDetail() {
    gioTrack('MB32474', <String, Object>{
      'roughly_classification_id': widget.detailedClassificationId != null &&
              widget.detailedClassificationId != ''
          ? widget.detailedClassificationId!.toString()
          : 'null', //内容分类id
      'content_id': widget.data?.contentId?.toString() ?? 'null', //内容id
      'content_title': widget.data?.title?.toString() ?? 'null', //标题名称
      'account_id': widget.data?.user?.userId?.toString() ?? 'null', //发布者ID
      'account_name': widget.data?.user?.nickname?.toString() ?? 'null', //发布者名称
      'content_type': widget.data?.contentType?.toString() ?? 'null', //内容类型
      'contentTopic': widget.data?.topics != <ContentCardTopic>[] &&
              widget.data!.topics!.isNotEmpty
          ? widget.data!.topics![0].name!.toString()
          : 'null', //话题名称
      'topicId': widget.data?.topics != <ContentCardTopic>[] &&
              widget.data!.topics!.isNotEmpty
          ? widget.data!.topics![0].id!.toString()
          : 'null', //话题id
      'account_type': _handleUserType(), //发布者类型
      'site_number': widget.index.toString(),//内容位置编码
    });

    String jumpUrl = addQueryParam(
        widget.data!.jumpUrl!, <String, dynamic>{'contentIndex': widget.index});
    modifyUrl(jumpUrl);
  }

  Future<void> _retrieveLikeCount(String contentId) async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    try {
      final String? res = await Storage.getTemporaryStorage('tempDataCache');
      if (res != null) {
        final dynamic tempDataCache = json.decode(res);
        if (tempDataCache != null &&
            tempDataCache['value'] != null &&
            tempDataCache['value'] != '' &&
            tempDataCache['value'] is String) {
          final dynamic tmpList = json.decode(tempDataCache['value'] as String);
          if (tmpList is List) {
            for (int i = 0; i < tmpList.length; i++) {
              dynamic item = tmpList[i];
              if (item['contentId'] == contentId) {
                if (item['likeCount'] != null && item['likeCount'] is num) {
                  _setLikeCount(item['likeCount'] as num);
                  return;
                }
              }
            }
          }
        }
      }
    } catch (e) {
      DevLogger.error(
          tag: Constant.tagCommonUI,
          msg: {'fn': '_retrieveLikeCount', 'err': e});
    }
  }

  void _setLikeCount(num likeCount) {
    if (likeCount != widget.data?.likeCount) {
      widget.data?.likeCount = likeCount;
      widget.likeCountNotifier?.value = likeCount;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data != null &&
        ((widget.data?.contentType == 'picture' &&
                widget.data?.imageUrlInfo != '' &&
                widget.data!.imageUrlInfo!.isNotEmpty) ||
            (widget.data?.contentType != 'picture' &&
                widget.data?.coverUrlInfo != '' &&
                widget.data!.coverUrlInfo!.isNotEmpty))) {
      return InkWell(
        onTap: () => this._toGoodsDetail(),
        child: _buildItem(),
      );
    } else {
      return Container();
    }
  }
}
