/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-23 15:53:43
 * @description: 
 */
class WaterfallUtil {
  // 数字格式处理
// 0~9999
// 1W~9.9W
// 10W/10W+
// 11W/11W+
// .......
// 99W/99w+
  static String numberFormat(int? num) {
    if (num == null) {
      return '0';
    }
    if (num >= 10000 && num <= 100000) {
      if ((num / 1000).floor() / 10 == num ~/ 10000) {
        return '${num ~/ 10000}W';
      }
      return '${(num / 1000).floor() / 10}W';
    } else if (num > 100000 && num <= 990000) {
      if (num % 10000 == 0) {
        return '${(num / 10000).floor()}W';
      }
      return '${(num / 10000).floor()}W+';
    } else if (num > 990000) {
      return '99W+';
    }
    if (num <= 0) {
      return '0';
    }
    return num.toString();
  }
}
