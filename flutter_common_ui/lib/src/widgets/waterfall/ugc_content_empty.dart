/*
 * @Author: ma<PERSON><PERSON><PERSON> ma<PERSON>@haier.com
 * @Date: 2022-12-09 09:41:09
 * @description: 
 */
//千人千面瀑布流接口请求数据为空时，显示缺省页面

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/hex_color.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UgcContentEmpty extends StatelessWidget {
  UgcContentEmpty({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 298.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
            child: Image.asset(
              'assets/images/waterfall/contentEmpty.png',
              package: Constant.packageName,
              fit: BoxFit.cover,
              height: 118.w,
              width: 119.w,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 12.w),
            child: Text(
              '没有相关内容~',
              style: TextStyle(
                color: HexColor('#999999'),
                fontSize: 12.sp,
              ),
            ),
          )
        ],
      ),
    );
  }
}
