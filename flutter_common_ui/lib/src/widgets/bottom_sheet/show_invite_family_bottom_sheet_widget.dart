import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:vdn/back.dart';
import '../../../flutter_common_ui.dart';
import '../../common/constant.dart';

const double _topMargin = 98; // 距离顶部安全区域的固定间距
const double _titleHeight = 56; // 标题区域高度
const double _bottomHeight = 76; // 底部取消按钮高度
const double _minContentHeight = 99; // 内容区域最小高度

const Color _textColor = Color(0xFF111111);
const Color _subTextColor = Color(0xFF666666);
const Color _selectedIconColor = Color(0xFF0081FF);
const Color _unselectedIconColor = Color(0xFFE5E5E5);

Future<void> showInviteFamilyBottomSheetWidget(BuildContext buildContext,
    {required void Function(BuildContext context, FamilyRole familyRole)?
        onNextTap,
    required String package,
    required String sourceName}) {
  final String pageName = '$package/show_invite_family_bottom_sheet_widget';
  WidgetsBinding.instance.addPostFrameCallback((_) {
    SystemBackInterceptor.interceptSystemBack(
        pageName: pageName,
        callback: () {
          Navigator.of(buildContext).pop();
          SystemBackInterceptor.cancelInterceptSystemBack(pageName);
        });
  });
  if (buildContext.mounted) {
    return showModalBottomSheet<void>(
      context: buildContext,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        final ValueNotifier<bool> isSelectMemberNotifier =
            ValueNotifier<bool>(true);
        return DefaultTextStyle(
          style: TextStyle(
            color: _textColor,
            fontFamilyFallback: provideFontFallback(),
          ),
          child: Container(
            margin: EdgeInsets.only(
                left: 12, right: 12, bottom: _getBottomMargin(context)),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: const Color(0xfff5f5f5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const _DragHandlerWidget(),
                _buildTitleWidget(),
                // 内容部分
                ValueListenableBuilder<bool>(
                  valueListenable: isSelectMemberNotifier,
                  builder: (BuildContext context, bool isSelectMember, _) {
                    return _ChildItemWidget(
                      isSelectMemberNotifier,
                    );
                  },
                ),
                // 底部按钮
                ValueListenableBuilder<bool>(
                  valueListenable: isSelectMemberNotifier,
                  builder: (BuildContext context, bool isSelectMember, _) {
                    return _BottomButtonWidget(
                      onNextTap: onNextTap,
                      isSelectMember: isSelectMember,
                      sourceName: sourceName,
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    ).whenComplete(() {
      SystemBackInterceptor.cancelInterceptSystemBack(pageName);
    });
  } else {
    return Future<void>.value();
  }
}

Widget _buildTitleWidget() {
  return Container(
    height: 44,
    alignment: Alignment.topCenter,
    padding: const EdgeInsets.only(top: 8),
    child: const Text(
      '请选择家人权限',
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        color: _textColor,
        fontSize: 17,
        fontWeight: FontWeight.w500,
      ),
    ),
  );
}

double _calculateMaxHeight(BuildContext context) {
  final double marginBottom = _getBottomMargin(context);
  return math.max(
    _minContentHeight,
    MediaQuery.of(context).size.height -
        MediaQuery.of(context).padding.top -
        marginBottom -
        _topMargin -
        _titleHeight -
        _bottomHeight,
  );
}

double _getBottomMargin(BuildContext context) {
  final double bottomPadding = MediaQuery.of(context).padding.bottom;
  return bottomPadding == 0 ? 12 : bottomPadding;
}

/// 底部取消按钮
class _ChildItemWidget extends StatefulWidget {
  const _ChildItemWidget(this.isSelectMemberNotifier);

  final ValueNotifier<bool> isSelectMemberNotifier;

  @override
  State<StatefulWidget> createState() => _ChildItemState();
}

class _ChildItemState extends State<_ChildItemWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(maxHeight: _calculateMaxHeight(context)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: ValueListenableBuilder<bool>(
          valueListenable: widget.isSelectMemberNotifier,
          builder: (BuildContext context, bool isSelectMember, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                _buildItemWidget(
                  title: '成员',
                  description: '作为成员可以使用家庭中的设备',
                  itemHeight: 75,
                  isSelected: isSelectMember,
                  onTap: () {
                    widget.isSelectMemberNotifier.value = true;
                  },
                ),
                const SizedBox(height: 12),
                _buildItemWidget(
                  title: '管理员',
                  description: '除使用设备外，作为管理员可以添加、删除设备、移除家人，修改家人权限等',
                  itemHeight: 92,
                  isSelected: !isSelectMember,
                  onTap: () {
                    widget.isSelectMemberNotifier.value = false;
                  },
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildItemWidget({
    required String title,
    required String description,
    required bool isSelected,
    required double itemHeight,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: itemHeight,
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: ShapeDecoration(
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    title,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontFamilyFallback: provideFontFallback(),
                      fontSize: 16,
                      color: _textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Text(
                    description,
                    maxLines: 2,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontFamilyFallback: provideFontFallback(),
                      fontSize: 12,
                      color: _subTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 40,
              padding: const EdgeInsets.only(left: 16),
              child: Image.asset(
                isSelected
                    ? 'assets/images/invite_family_selected.webp'
                    : 'assets/images/invite_family_unselected.webp',
                package: Constant.packageName,
                width: 24,
                height: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _DragHandlerWidget extends StatelessWidget {
  const _DragHandlerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      height: 4,
      width: 32,
      decoration: BoxDecoration(
        color: _unselectedIconColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }
}

///邀请家人弹窗
const String familyInvitePopupGio = 'MB4002';

// 底部取消按钮
class _BottomButtonWidget extends StatelessWidget {
  const _BottomButtonWidget(
      {this.onNextTap, required this.isSelectMember, required this.sourceName});

  final String sourceName;
  final bool isSelectMember;
  final void Function(BuildContext context, FamilyRole familyRole)? onNextTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Expanded(
          child: _buildButton(
            label: '取消',
            color: Colors.white,
            textColor: _textColor,
            onTap: () {
              Navigator.of(context).pop();
              gioTrack(familyInvitePopupGio, <String, String?>{
                'button-name': '取消',
                'sourceName': sourceName,
              });
            },
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildButton(
            label: '下一步',
            color: _selectedIconColor,
            textColor: Colors.white,
            onTap: () {
              if (onNextTap != null) {
                isSelectMember
                    ? onNextTap!(context, FamilyRole.member)
                    : onNextTap!(context, FamilyRole.admin);
                gioTrack(familyInvitePopupGio, <String, String?>{
                  'button-name': '下一步',
                  'sourceName': sourceName,
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildButton({
    required String label,
    required Color color,
    required Color textColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        alignment: Alignment.center,
        height: 44,
        margin: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: color,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class SystemBackInterceptor {
  // 注册监听
  static void interceptSystemBack({
    required String pageName,
    bool isNeedNativeBack = false,
    BuildContext? context,
    void Function()? callback,
  }) {
    MessageBackKeyManager.registerMessageChannel();
    MessageBackKeyManager.interceptSystemBack(pageName, false, () {
      if (context != null && context.mounted) {
        Navigator.of(context).pop();
      }
      if (callback != null) {
        callback();
      }
    });
  }

  // 移除监听
  static void cancelInterceptSystemBack(String pageName) {
    MessageBackKeyManager.cancelInterceptSystemBack(pageName);
  }
}

List<String>? provideFontFallback() {
  return Platform.isIOS ? <String>['PingFang SC'] : null;
}

enum FamilyRole {
  creator(0),
  admin(1),
  member(2);

  const FamilyRole(this.value);

  final int value;

  static FamilyRole fromValue(int value) {
    return values.firstWhere((FamilyRole role) => role.value == value,
        orElse: () => FamilyRole.member);
  }
}
