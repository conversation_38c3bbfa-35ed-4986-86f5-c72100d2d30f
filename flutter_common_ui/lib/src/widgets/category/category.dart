/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-16 14:41:54
 * @description: 分类目录
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/category/category_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/category/category_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Category extends StatelessWidget {
  /// [config] 分类目录组件的配置信息
  final CategoryModel? config;
  const Category({Key? key, this.config}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.attr?.categoryList == null || config?.style?.attr == null) {
      return const SizedBox();
    }
    return config!.style!.attr!.toDecorationWidget(
        child: Container(
            width: config?.style?.attr?.width?.w,
            height: config?.style?.attr?.height?.w,
            child: TrackWidget(
              // 分类目录
              CategoryScreen(
                attrConfig: config?.attr,
              ),
              onTap: () {},
              trackInfo: config?.attr?.trackInfo,
              // 测试埋点信息
              trackMap: {"index": 1},
            )));
  }
}
