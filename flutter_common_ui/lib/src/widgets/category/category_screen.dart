/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-02-16 14:41:54
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/category/category_model.dart';
import 'package:flutter_common_ui/src/widgets/category/category_item_view.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoryScreen extends StatefulWidget {
  final CatrgoryAttrInfo? attrConfig;
  const CategoryScreen({Key? key, this.attrConfig}) : super(key: key);

  @override
  _CategoryScreenState createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  TabController? _controller;
  CategoryListInfo? currentTab;
  // 默认选中和默认图片的宽度
  double imageWidth = 0;
  @override
  void initState() {
    super.initState();
    if (_controller != null) {
      _controller = TabController(
        length: 4,
        vsync: ScrollableState(),
      );
    }
    setState(() {
      if (widget.attrConfig != null &&
          widget.attrConfig!.categoryList != null) {
        currentTab = widget
            .attrConfig!.categoryList![widget.attrConfig?.defaultSelect ?? 0];
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.attrConfig != null && widget.attrConfig?.categoryList == null ||
        widget.attrConfig?.categoryList?.length == 0) {
      return SizedBox();
    }

    /// 分类每一个样式
    return Container(
        padding: widget.attrConfig?.padding?.padding,
        height: widget.attrConfig?.navBar?.height?.w,
        width: 1.sw,
        child: DefaultTabController(
          length: widget.attrConfig!.categoryList!.length,
          child: TabBar(
            controller: _controller,
            indicator: BoxDecoration(),
            labelPadding: EdgeInsets.zero,
            isScrollable: true,
            onTap: (index) {
              setState(() {
                currentTab = widget.attrConfig!.categoryList![index];
              });
            },
            tabs: List<Widget>.generate(widget.attrConfig!.categoryList!.length,
                (index) {
              return Tab(
                child: CategoryItemView(
                    navBarInfo: widget.attrConfig?.navBar,
                    currentTab: currentTab,
                    spacing: (widget.attrConfig?.spacing ?? 0).w,
                    categoryInfo: widget.attrConfig!.categoryList![index]),
              );
            }),
          ),
        ));
  }
}
