/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-16 15:23:50
 * @description: 分类目录每个类目样式
 */
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/models/category/category_model.dart';
import 'package:flutter_common_ui/src/utils/extension.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/initial_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoryItemView extends StatelessWidget {
  final NavBarInfo? navBarInfo;
  final CategoryListInfo categoryInfo;
  final CategoryListInfo? currentTab;
  final double? spacing;
  final double? imageWidth;
  const CategoryItemView(
      {Key? key,
      required this.categoryInfo,
      this.spacing,
      this.currentTab,
      this.navBarInfo,
      this.imageWidth})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isSelected = currentTab?.cateName == categoryInfo.cateName;
    // 分类名称
    String cateName = categoryInfo.cateName ?? '';
    // 每个分类目录默认的宽度
    double imageDefWidth = 0.0;
    // 默认图片 或 选中图片任意一个配置项没有上传图片，均展示分类名称
    bool isShowImage = StringUtil.notEmpty(categoryInfo.defaultImgSrc) &&
        StringUtil.notEmpty(categoryInfo.selectedImgSrc);
    // 计算图片的宽度
    calculateWidth() {
      if (isShowImage) {
        Image image = Image.network(categoryInfo.defaultImgSrc!);
        image.image
            .resolve(ImageConfiguration())
            .addListener(new ImageStreamListener((ImageInfo info, bool _) {
          double scale = info.image.width / info.image.height;
          imageDefWidth = (navBarInfo?.height ?? 0).toDouble() * scale;
        }));
      }
    }

    calculateWidth();

    return Container(
      height: navBarInfo?.height?.w,
      margin: EdgeInsets.only(left: spacing! / 2, right: spacing! / 2),
      child: _itemWidget(isSelected, cateName, imageDefWidth, isShowImage),
    );
  }

  Widget _itemWidget(
      bool isSelected, String name, double imageDefWidth, bool isShowImage) {
    final bgUrl = navBarInfo?.navRowType?.image?.url ?? '';
    final decorationImage =
        navBarInfo?.navRowType?.type == 'image' && bgUrl.startsWith('http')
            ? DecorationImage(
                image: NetworkImage(bgUrl),
                fit: BoxFit.cover,
              )
            : null;
    // 选中分类目录
    if (isSelected) {
      return Stack(
        alignment: AlignmentDirectional.center,
        children: [
          Center(
              child: isShowImage == true
                  ? InitialImage(
                      url: categoryInfo.selectedImgSrc!,
                      imageHeight: navBarInfo?.height?.w,
                      imageDefWidth: imageDefWidth)
                  : Container(
                      child: Text(
                        name,
                        style: TextStyle(
                          fontSize: navBarInfo?.selectFontSize?.sp,
                          color: navBarInfo?.selectFontColor?.color,
                          fontWeight: TextCommonUtils.getFontWeightFromString(
                              navBarInfo?.selectFontWeight),
                        ),
                        maxLines: 1,
                      ),
                    )),
          //  导航条
          Container(
            padding: EdgeInsets.only(bottom: 2.w),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                width: 20.w,
                height: 4.w,
                decoration: BoxDecoration(
                  borderRadius: navBarInfo?.navRowRadius?.borderRadius,
                  boxShadow: navBarInfo?.navRowShadow?.boxShadow,
                  image: decorationImage,
                  gradient: navBarInfo?.navRowType?.color?.colorOfGradient,
                  color: navBarInfo?.navRowType?.color?.color,
                ),
                child: ClipRRect(
                  borderRadius: navBarInfo?.navRowRadius?.borderRadius ??
                      BorderRadius.zero,
                  child: BackdropFilter(
                    filter: ui.ImageFilter.blur(
                        sigmaX: navBarInfo?.navRowType?.filter?.blur ?? 0,
                        sigmaY: navBarInfo?.navRowType?.filter?.blur ?? 0),
                  ),
                ),
              ),
            ),
          )
        ],
      );
    } else {
      // 默认分类目录样式
      return Center(
        child: isShowImage
            ? InitialImage(
                url: categoryInfo.defaultImgSrc!,
                imageHeight: navBarInfo?.height?.w,
                imageDefWidth: imageDefWidth)
            : Container(
                child: Text(
                  name,
                  style: TextStyle(
                    fontSize: navBarInfo?.defaultFontSize?.sp,
                    color: navBarInfo?.defaultFontColor?.color,
                    fontWeight: TextCommonUtils.getFontWeightFromString(
                        navBarInfo?.defaultFontWeight),
                  ),
                  maxLines: 1,
                ),
              ),
      );
    }
  }
}
