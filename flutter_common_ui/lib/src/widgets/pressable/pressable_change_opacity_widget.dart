import 'package:flutter/material.dart';

class PressableChangeOpacityWidget extends StatefulWidget {
  final Widget child;
  final double opacity;

  const PressableChangeOpacityWidget({
    super.key,
    required this.child,
    required this.opacity,
  });

  @override
  _PressableChangeOpacityWidgetState createState() =>
      _PressableChangeOpacityWidgetState();
}

class _PressableChangeOpacityWidgetState
    extends State<PressableChangeOpacityWidget> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        setState(() {
          _isPressed = true;
        });
      },
      onPointerCancel: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      onPointerUp: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      child: Opacity(
        opacity: _isPressed ? widget.opacity : 1.0,
        child: widget.child,
      ),
    );
  }
}
