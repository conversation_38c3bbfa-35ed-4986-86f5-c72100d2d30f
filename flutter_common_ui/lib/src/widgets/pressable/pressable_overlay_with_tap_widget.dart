import 'package:flutter/material.dart';

class PressableOverlayWithTapWidget extends StatefulWidget {
  const PressableOverlayWithTapWidget({
    super.key,
    required this.child,
    this.overlayClick,
    this.color,
    this.borderRadius,
    this.duration = const Duration(milliseconds: 100),
  });

  /// 需要被包裹的子组件
  final Widget child;

  /// 点击时的回调函数
  final VoidCallback? overlayClick;

  /// 按下时显示的遮罩颜色，默认为0.1透明度的黑色
  final Color? color;

  /// 遮罩的圆角，如果不设置则使用矩形
  final BorderRadius? borderRadius;

  /// 遮罩显示/隐藏的动画持续时间
  final Duration duration;

  @override
  _PressableOverlayWithTapWidgetState createState() =>
      _PressableOverlayWithTapWidgetState();
}

class _PressableOverlayWithTapWidgetState
    extends State<PressableOverlayWithTapWidget> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTapDown: (TapDownDetails details) {
        setState(() {
          _isPressed = true;
        });
      },
      onTapCancel: () {
        setState(() {
          _isPressed = false;
        });
      },
      onTapUp: (TapUpDetails details) {
        widget.overlayClick?.call();
        // 加一个200ms的延迟，避免快速点击时没有效果
        Future<void>.delayed(const Duration(milliseconds: 200), () {
          setState(() {
            _isPressed = false;
          });
        });
      },
      child: Stack(
        children: <Widget>[
          widget.child,
          if (_isPressed)
            Positioned.fill(
              child: AnimatedOpacity(
                duration: widget.duration,
                opacity: _isPressed ? 1.0 : 0.8,
                child: ClipRRect(
                  borderRadius: widget.borderRadius ?? BorderRadius.zero,
                  child: Container(
                    color: widget.color ?? Colors.black.withOpacity(0.1),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
