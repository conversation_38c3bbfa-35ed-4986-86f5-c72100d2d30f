import 'package:flutter/material.dart';

class PressableOverlayWithWHWidget extends StatefulWidget {
  final Widget child;
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final double? opacity;
  final Color? color;
  void Function(double currentValue)? overlayClick;

  PressableOverlayWithWHWidget({
    super.key,
    required this.child,
    required this.width,
    required this.height,
    this.borderRadius,
    this.opacity,
    this.color,
    this.overlayClick,
  });

  @override
  _PressableOverlayWidgetState createState() => _PressableOverlayWidgetState();
}

class _PressableOverlayWidgetState extends State<PressableOverlayWithWHWidget> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (_) {
        setState(() {
          _isPressed = true;
        });
      },
      onPointerCancel: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      onPointerUp: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      child: Stack(
        children: <Widget>[
          widget.child,
          if (_isPressed)
            Positioned(
              top: 0,
              left: 0,
              child: Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius,
                  color: _isPressed
                      ? (widget.color ?? const Color(0xff000000))
                          .withOpacity(widget.opacity ?? 0.1)
                      : Colors.transparent,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
