import 'package:flutter/material.dart';

class PressableOverlayWidget extends StatefulWidget {
  final Widget child;
  final BorderRadius? borderRadius;
  final double? opacity;
  final Color? color;
  void Function(double currentValue)? overlayClick;

  PressableOverlayWidget({
    super.key,
    required this.child,
    this.borderRadius,
    this.opacity,
    this.color,
    this.overlayClick,
  });

  @override
  _PressableOverlayWidgetState createState() => _PressableOverlayWidgetState();
}

class _PressableOverlayWidgetState extends State<PressableOverlayWidget> {
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (PointerDownEvent event) {
        setState(() {
          _isPressed = true;
        });
      },
      onPointerCancel: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      onPointerUp: (_) {
        setState(() {
          _isPressed = false;
        });
      },
      child: Stack(
        children: <Widget>[
          widget.child,
          if (_isPressed)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: widget.borderRadius,
                  color: _isPressed
                      ? (widget.color ?? const Color(0xff000000))
                          .withOpacity(widget.opacity ?? 0.1)
                      : Colors.transparent,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
