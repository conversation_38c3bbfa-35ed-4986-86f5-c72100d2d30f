/*
 * 描述：UI组件库-按钮组件
 * 作者：fancunshuo
 * 建立时间: 2025/5/14
 */

import 'package:flutter/material.dart';

import '../../../flutter_common_ui.dart';
import '../../utils/card_text_style.dart';

enum ButtonType {
  primary,
  secondary,
  tertiary,
  warning,
}

/// 通栏按钮UI组件
/// - [text] - 按钮显示的文字
/// - [callback] - 点击按钮的回调方法
/// - [type] - 按钮的类型，具体参见[ButtonType]定义
/// - [enable] - 按钮是否启用，注：此属性仅用于UI展示，不控制回调方法是否执行
///
/// 默认提供取消、确认按钮实现，示例如下：
///
/// ```
/// ButtonFill.cancel(
///   callback: () {
///     Dialogs.closeSmartHomeModalBottomSheet();
///   },
/// )
/// ```
class ButtonFill extends StatefulWidget {
  const ButtonFill({
    super.key,
    required this.text,
    this.callback,
    this.type = ButtonType.primary,
    this.enable = true,
    this.invert = true,
  });

  const ButtonFill.cancel(
      {Key? key, VoidCallback? callback, bool enable = true})
      : this(
          key: key,
          text: '取消',
          callback: callback,
          type: ButtonType.secondary,
          enable: enable,
        );

  const ButtonFill.confirm(
      {Key? key, VoidCallback? callback, bool enable = true})
      : this(
          key: key,
          text: '确定',
          callback: callback,
          type: ButtonType.primary,
          enable: enable,
        );

  const ButtonFill.primary({
    Key? key,
    required String text,
    required VoidCallback callback,
    bool enable = true,
  }) : this(
          key: key,
          text: text,
          callback: callback,
          type: ButtonType.primary,
          enable: enable,
        );

  final String text;
  final VoidCallback? callback;
  final ButtonType type;
  final bool enable;
  final bool invert;

  @override
  State<ButtonFill> createState() => _ButtonFillState();
}

class _ButtonFillState extends State<ButtonFill> {
  bool _tapDown = false;

  Color get backgroundColor {
    if (_tapDown && widget.enable) {
      return _getClickBackgroundColor();
    }
    if (widget.invert) {
      return _getInvertBackgroundColor();
    }
    return _getNormalBackgroundColor();
  }

  Color _getNormalBackgroundColor() {
    switch (widget.type) {
      case ButtonType.primary:
        return AppSemanticColors.component.primary.fill;
      case ButtonType.secondary:
        return AppSemanticColors.component.secondary.fill;
      case ButtonType.tertiary:
        return AppSemanticColors.component.information.fill;
      case ButtonType.warning:
        return AppSemanticColors.component.secondary.fill;
    }
  }

  Color _getClickBackgroundColor() {
    switch (widget.type) {
      case ButtonType.primary:
        return AppSemanticColors.component.primary.emphasize;
      case ButtonType.secondary:
        return AppSemanticColors.component.secondary.emphasize;
      case ButtonType.tertiary:
        return widget.invert
            ? AppSemanticColors.component.secondary.emphasize
            : AppSemanticColors.component.information.emphasize;
      case ButtonType.warning:
        return AppSemanticColors.component.secondary.emphasize;
    }
  }

  Color _getInvertBackgroundColor() {
    switch (widget.type) {
      case ButtonType.primary:
        return AppSemanticColors.component.primary.fill;
      case ButtonType.secondary:
        return AppSemanticColors.component.secondary.invert;
      case ButtonType.tertiary:
        return AppSemanticColors.component.secondary.invert;
      case ButtonType.warning:
        return AppSemanticColors.component.secondary.invert;
    }
  }

  Color get foregroundColor {
    switch (widget.type) {
      case ButtonType.primary:
        return AppSemanticColors.component.primary.on;
      case ButtonType.secondary:
        return AppSemanticColors.component.secondary.on;
      case ButtonType.tertiary:
        return AppSemanticColors.component.information.on;
      case ButtonType.warning:
        return AppSemanticColors.component.warn.on;
    }
  }

  void _handlePointerDown() {
    if (!widget.enable) {
      return;
    }

    if (mounted) {
      setState(() {
        _tapDown = true;
      });
    }
  }

  void _handlePointerUpOrCancel() {
    if (mounted) {
      setState(() {
        _tapDown = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.callback?.call();
      },
      child: Listener(
        onPointerDown: (_) => _handlePointerDown(),
        onPointerCancel: (_) => _handlePointerUpOrCancel(),
        onPointerUp: (_) => _handlePointerUpOrCancel(),
        child: Opacity(
          opacity: widget.enable ? 1 : 0.39,
          child: Container(
            height: 44,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: const BorderRadius.all(Radius.circular(16)),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Text(
              widget.text,
              style: TextStyle(
                fontSize: 16,
                color: foregroundColor,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ),
    );
  }
}
