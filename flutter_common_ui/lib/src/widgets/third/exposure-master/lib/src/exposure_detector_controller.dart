import 'package:flutter/foundation.dart';

import './lru_list.dart';
import './exposure_detector_layer.dart';

class ExposureDetectorController {
  static final ExposureDetectorController _instance = ExposureDetectorController();

  static ExposureDetectorController get instance => _instance;

  Duration updateInterval = const Duration(milliseconds: 20);
  // Duration updateInterval = Duration.zero;

  int exposureTime = 10;

  double exposureFraction = 0.5;

  LruList<Key> _filterKeyList = LruList<Key>(maxLength: 1000);

  bool filterKeysContains(Key key) {
    return _filterKeyList.contains(key);
  }

  void forget(Key key) {
    _filterKeyList.add(key);
    ExposureDetectorLayer.forget(key);
  }

  // 设置过滤列表长度
  void setFilterList(int length) {
    _filterKeyList = LruList<Key>(maxLength: length);
  }
}
