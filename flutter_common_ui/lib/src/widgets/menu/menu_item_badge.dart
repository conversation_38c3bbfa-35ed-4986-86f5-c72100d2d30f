/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 15:01:54
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/menu/menu_model.dart';
import 'package:flutter_common_ui/src/widgets/menu/menu_red_point.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MenuItemBadge extends StatelessWidget {
  final MenuItemModel? config;
  final double? top;
  final double? right;
  final double? left;
  final bool? isShowPoint;
  final bool showMourningMode;
  const MenuItemBadge(
      {Key? key,
      this.config,
      this.top,
      this.right,
      this.left,
      this.isShowPoint,
      required this.showMourningMode})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.cornerMark?.type == AttrCornerMarkTypeEnum.redDot &&
        isShowPoint!) {
      return Positioned(
          top: this.top,
          right: this.right,
          left: this.left,
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular((6.w / 2).w)),
            child: ColorFiltered(
              colorFilter: ColorFilter.mode(
                showMourningMode
                    ? Colors.grey
                    : Colors.transparent,
                BlendMode.hue,
              ),
              child: MenuRedPoint(
                  width: 6.w, color: config?.cornerMark?.background?.color))));
    } else {
      return const SizedBox();
    }
  }
}
