/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-20 14:36:12
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/menu/menu_model.dart';
import 'package:flutter_common_ui/src/models/menu/menu_point_model.dart';
import 'package:flutter_common_ui/src/widgets/menu/menu_container.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 菜单组件
class MenuScreen extends StatelessWidget {
  /// [config] 菜单组件的配置信息
  final MenuModel? config;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;

  /// [needReload] 是否需要重新加载图片
  final int? needReload;

  /// 菜单项小红点
  final MenuPointModel? menuItemList;

  /// 是否显示菜单的小红点
  final bool? isShowMenuDot;

  /// 菜单项点击事件
  final Function? onTap;

  /// [relative] 是否相对位置，比如包含在动态容器中，默认false
  final bool relative;

  const MenuScreen(
      {Key? key,
      this.config,
      this.colorSystem,
      this.needReload,
      this.menuItemList,
      this.isShowMenuDot,
      this.onTap,
      this.relative = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (config?.style?.attr == null || config?.attr?.icon == '') {
      return const SizedBox();
    }
    return config!.style!.attr!.toDecorationWidget(
        relative: relative,
        child: Container(
            width: config?.style?.attr?.width?.w,
            height: config?.style?.attr?.height?.w,
            child: MenuContainer(
                config: config,
                colorSystem: colorSystem,
                needReload: needReload,
                menuItemList: menuItemList,
                isShowMenuDot: isShowMenuDot,
                onTap: onTap)));
  }
}
