import 'package:flutter/material.dart';

class CustomPopupMenuDivider extends PopupMenuEntry<void> {
  /// Creates a horizontal divider for a popup menu.
  ///
  /// By default, the divider has a height of 16 logical pixels.
  const CustomPopupMenuDivider(
      {Key? key, this.height = 16.0, this.color, this.indent})
      : super(key: key);

  /// The height of the divider entry.
  ///
  /// Defaults to 16 pixels.
  @override
  final double height;
  final Color? color;
  final double? indent;

  @override
  bool represents(void value) => false;

  @override
  _CustomPopupMenuDividerState createState() => _CustomPopupMenuDividerState();
}

class _CustomPopupMenuDividerState extends State<CustomPopupMenuDivider> {
  @override
  Widget build(BuildContext context) {
    return Divider(
        height: widget.height, color: widget.color, indent: widget.indent);
  }
}
