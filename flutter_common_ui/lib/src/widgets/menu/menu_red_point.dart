/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-10 19:11:29
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/hex_color.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 菜单项小红点
class MenuRedPoint extends StatelessWidget {
  const MenuRedPoint({Key? key, this.width = 8, this.color}) : super(key: key);

  final double width;
  final ColorModel? color;

  @override
  Widget build(BuildContext context) {
    return Container(
        width: width.w,
        height: width.w,
        decoration: BoxDecoration(
            color: color != null && color!.value != ''
                ? color!.color
                : HexColor('#EC2A41'),
            borderRadius: BorderRadius.all(Radius.circular((width / 2).w))));
  }
}
