import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/text_util.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/menu/menu_model.dart';
import 'package:flutter_common_ui/src/models/menu/menu_point_model.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/color_filtered_widget.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/common_network_image.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/track_info_widget.dart';
import 'package:flutter_common_ui/src/widgets/menu/custom_popup_menu_divider.dart';
import 'package:flutter_common_ui/src/widgets/menu/menu_item_badge.dart';
import 'package:flutter_common_ui/src/widgets/menu/menu_red_point.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 菜单组件内容
class MenuContainer extends StatefulWidget {
  /// [config] 菜单组件的配置信息
  final MenuModel? config;

  /// [needReload] 是否需要重新加载图片
  final int? needReload;

  /// 菜单项小红点
  final MenuPointModel? menuItemList;

  /// 是否显示菜单的小红点
  final bool? isShowMenuDot;

  /// 菜单项点击事件
  final Function? onTap;

  /// [colorSystem] 背景色系
  final ColorSystemEnum? colorSystem;
  MenuContainer(
      {Key? key,
      this.config,
      this.needReload,
      this.menuItemList,
      this.colorSystem,
      this.isShowMenuDot,
      this.onTap})
      : super(key: key);

  @override
  _MenuContentState createState() => _MenuContentState();
}

class _MenuContentState extends State<MenuContainer> {
  @override
  Widget build(BuildContext context) {
    return Theme(
        data:
            Theme.of(context).copyWith(cardColor: Color.fromRGBO(0, 0, 0, 0.8)),
        child: Builder(builder: (context) {
          return TrackWidget(
              Stack(clipBehavior: Clip.none, children: <Widget>[
                PopupMenuButton(
                  child: ColorFilteredWrapper(
                      colorSystem: widget.colorSystem,
                      child: CommonNetWorkImage(
                        url: widget.config!.attr!.icon,
                        width: widget.config?.style?.attr?.width?.w,
                        fit: BoxFit.contain,
                        placeHolder: SizedBox(),
                        errorWidget: SizedBox(),
                        needReload: widget.needReload,
                      )),
                  offset: Offset(0, -5),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.w)),
                  itemBuilder: (context) {
                    return _buildMenuEntries(context);
                  },
                ),
                widget.isShowMenuDot == true
                    ? Positioned(
                        top: 0, right: 0, child: MenuRedPoint(width: 8.w))
                    : const SizedBox(),
              ]),
              key: ValueKey(widget.config?.aliasUnit ?? 'menu'),
              trackInfo: widget.config?.attr?.trackInfo,
              trackMap: {},
              onTap: () {});
        }));
  }

  // 获取当前项
  bool? geItemPoint(
    MenuItemModel currentItem,
  ) {
    bool? isShow = false;
    if (widget.menuItemList != null) {
      widget.menuItemList!.menuItemList!.asMap().forEach((key, value) {
        if (value.itemName == currentItem.name) {
          isShow = value.isShowDot;
        }
      });
    }
    return isShow;
  }

  /// 生成菜单子项---扫一扫、添加设备、邀请家人的list
  List<PopupMenuEntry> _buildMenuEntries(context) {
    List<PopupMenuEntry> _list = [];
    widget.config!.attr!.list!.asMap().keys.forEach((index) {
      /// 加号项
      /// PopupMenuItem默认的水平padding为_kMenuHorizontalPadding = 16
      final PopupMenuItem<int> _item = PopupMenuItem<int>(
          height: 54.w,
          value: index,
          child: TrackWidget(
            Row(
              children: <Widget>[
                /// 菜单子项的图标
                widget.config!.attr?.list?[index].icon != ''
                    ? Container(
                        margin:
                            EdgeInsets.only(left: 7.4.w, right: 10.w, top: 2.w),
                        child: CommonNetWorkImage(
                          url: widget.config!.attr!.list![index].icon,
                          width: 16.6.w,
                          height: 16.6.w,
                          fit: BoxFit.contain,
                          placeHolder: SizedBox(),
                          errorWidget: SizedBox(),
                          needReload: widget.needReload,
                        ))
                    : SizedBox(),

                /// 菜单子项的文字
                Expanded(
                    child: Stack(
                  children: <Widget>[
                    Container(
                        height: 54.w,
                        width: 128.w,
                        padding: EdgeInsets.fromLTRB(0.w, 14.w, 0.0, 15.w),
                        child: Text(widget.config!.attr!.list![index].name!,
                            style: TextStyle(
                                fontSize: widget.config!.attr!.fontSize?.sp,
                                fontWeight:
                                    TextCommonUtils.getFontWeightFromString(
                                        widget.config!.attr!.fontWeight),
                                color: widget.config!.attr?.color?.color))),
                    MenuItemBadge(
                        config: widget.config!.attr!.list![index],
                        top: 24.w,
                        right: 0.0,
                        showMourningMode: false, // 此菜单栏已废弃
                        isShowPoint:
                            geItemPoint(widget.config!.attr!.list![index]))
                  ],
                ))
              ],
            ),
            key: ValueKey(
                widget.config!.attr?.list?[index].id ?? 'menu-list-${index}'),
            onTap: () async {
              Navigator.pop(this.context);
              if (widget.onTap != null)
                widget.onTap!(widget.config!.attr!.list![index], this.context);
            },
            trackInfo: widget.config!.attr!.list![index].trackInfo,
            trackMap: {},
          ));
      _list.add(_item);

      /// 分割线
      if (index < widget.config!.attr!.list!.length - 1) {
        final _divider = CustomPopupMenuDivider(
          height: 1.0,
          color: Color(0xff666666),
          indent: 52.w,
        );
        _list.add(_divider);
      }
    });

    return _list;
  }
}
