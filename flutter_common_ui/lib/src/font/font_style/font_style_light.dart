import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'font_style_base.dart';

class FontStyleLight extends FontStyleBase {
  @override
  TextStyle get textStyle1 =>
      TextStyle(fontSize: 20.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle2 =>
      TextStyle(fontSize: 10.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle3 =>
      TextStyle(fontSize: 12.w, color: const Color(0xff666666));
  @override
  TextStyle get textStyle4 =>
      TextStyle(fontSize: 16.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle5 =>
      TextStyle(fontSize: 18.w, color: const Color(0xff666666));
  @override
  TextStyle get textStyle6 =>
      TextStyle(fontSize: 18.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle7 =>
      TextStyle(fontSize: 14.w, color: const Color(0xff666666));
  @override
  TextStyle get textStyle8 =>
      TextStyle(fontSize: 14.w, color: const Color(0xff666666));
  @override
  TextStyle get textStyle9 =>
      TextStyle(fontSize: 14.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle10 =>
      TextStyle(fontSize: 12.w, color: const Color(0xff111111));
  @override
  TextStyle get textStyle11 =>
      TextStyle(fontSize: 10.w, color: const Color(0xff666666));
  @override
  TextStyle get textStyle12 =>
      TextStyle(fontSize: 12.w, color: const Color(0xff666666));
}
