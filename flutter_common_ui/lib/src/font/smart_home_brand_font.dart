
import 'font_style/font_style_base.dart';
import 'font_style/font_style_dark.dart';
import 'font_style/font_style_light.dart';


class SmartHomeBrandFont {

  factory SmartHomeBrandFont() {
    _instance ??= SmartHomeBrandFont._internal();
    return _instance!;
  }

  SmartHomeBrandFont._internal();

  static SmartHomeBrandFont? _instance;
  static String _brand = SmartHomeBrand.haier; // 默认品牌为海尔

  static void init({String brand = SmartHomeBrand.haier}) {
    _brand = brand;
  }

  static FontStyleBase get getFontStyle {
    // switch (_brand) {
    //   case SmartHomeBrand.haier:
    //   case SmartHomeBrand.casarte:
    //   case SmartHomeBrand.fisher:
    //     return FontStyleDark();
    //   case SmartHomeBrand.leader:
    //     return FontStyleLight();
    //   default:
    //     return FontStyleDark();
    // }
    return FontStyleLight();
  }
}

class SmartHomeBrand {
  static const String haier = 'haier';
  static const String casarte = 'casarte';
  static const String leader = 'leader';
  static const String fisher = 'fisher';
}
