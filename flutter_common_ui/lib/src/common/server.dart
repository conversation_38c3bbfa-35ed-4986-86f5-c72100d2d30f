import 'dart:convert';
import 'dart:typed_data';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';
import 'package:user/modle/oauth_data_model.dart';

/// 获取请求签名
/// [urlPath] 请求url路径
/// [paramsJson] 请求参数Json字符串(请求body)
/// [appId] appId
/// [appKey] appKey
/// [timestamp] 时间戳（毫秒）
String getSign(
  String urlPath,
  String paramsJson,
  String appId,
  String appKey,
  int timestamp,
) {
  const HEX_STRING = '0123456789abcdef';
  var bytes =
      utf8.encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
  var list = sha256.convert(bytes).bytes;
  if (list.length <= 0) {
    return '';
  }
  int length = list.length;
  Uint8List uList = new Uint8List(length << 1);
  int i = 0;
  for (int j = 0; j < length; j++) {
    int k = i + 1;
    final index = (list[j] >> 4) & 0xF;
    uList[i] = HEX_STRING[index].codeUnitAt(0);
    uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
    i = k + 1;
  }
  return String.fromCharCodes(uList);
}

// 获取时间戳
int getTimestamp() {
  return new DateTime.now().millisecondsSinceEpoch;
}

class LogsInterceptors extends InterceptorsWrapper {
  @override
  onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    DevLogger.info(tag: Constant.tagCommonUI + 'dio onRequest', msg: {
      'path': options.path,
      'headers': options.headers,
      'query': options.queryParameters,
      'data': options.data
    });
    return handler.next(options);
  }

  @override
  onResponse(Response response, ResponseInterceptorHandler handler) async {
    DevLogger.info(tag: Constant.tagCommonUI + 'dio onResponse', msg: {
      'path': response.requestOptions.path,
      'headers': response.requestOptions.headers,
      'queryParameters': response.requestOptions.queryParameters,
      'queryData': response.requestOptions.data,
      'response': response,
    });
    return handler.next(response); // continue
  }

  @override
  onError(DioError err, handler) async {
    DevLogger.error(tag: Constant.tagCommonUI + 'dio onError', msg: err);
    return handler.next(err); // continue;
  }
}

/// http 请求封装
class HttpManager {
  // 单例
  static final HttpManager _httpManager = HttpManager._internal();

  // dio的初始化
  Dio _dio = Dio(BaseOptions(
    baseUrl: Constant.BASE_URL,

    /// 超时时间设置 30s
    connectTimeout: Duration(seconds: 30),
  ));

  // 给unit test使用
  get dio => _dio;

  HttpManager._internal() {
    _dio.interceptors.add(new LogsInterceptors());
  }

  factory HttpManager() => _httpManager;

  /// appinfo的部分信息
  static String _appId = '';
  static String get appId => _appId;
  static String _appKey = '';
  static String get appKey => _appKey;
  static String _clientId = '';
  static String get clientId => _clientId;
  static String _appVersion = '';
  static String get appVersion => _appVersion;
  // false为生产环境
  static bool _grayMode = false;
  static bool get grayMode => _grayMode;
  // 验收/生产app
  static String _env = '';
  static String get env => _env;

  // oauthData的部分信息
  // 云平台token
  static String _accessToken = '';
  static String get accessToken => _accessToken;
  // 用户中心token
  static String _userCenterAccessToken = '';
  static String get userCenterAccessToken => _userCenterAccessToken;
  // 云平台用户id
  static String _userId = '';
  static String get userId => _userId;

  // 初始AppInfo，有的时候不更新，防止每次都去取
  static Future<void> updateAppInfoParams() async {
    if (_appId == '') {
      try {
        final appInfo = await AppInfoPlugin.getAppInfo();
        updateParams(appInfo: appInfo);
      } catch (err) {
        DevLogger.error(
            tag: Constant.packageName,
            msg: {'fn': 'updateAppInfoParams', 'err': err});
      }
    }
  }

// 更新参数
  static void updateParams({AppInfoModel? appInfo, OauthData? oauthData}) {
    // app信息
    if (appInfo != null) {
      _appId = appInfo.appId;
      _appKey = appInfo.appKey;
      _appVersion = appInfo.appVersion;
      _clientId = appInfo.clientId;
      _grayMode = appInfo.grayMode;
      _env = appInfo.env;
    }
    // 鉴权信息
    if (oauthData != null) {
      _accessToken = oauthData.uhome_access_token;
      _userCenterAccessToken = oauthData.user_center_access_token;
      _userId = oauthData.uhome_user_id;
    }
  }

  // 重置参数
  static void resetOauthData() {
    updateParams(oauthData: OauthData.fromMap({}));
  }

  static Map<String, dynamic> getHeader({
    Map<String, dynamic>? requestData,
    required String url,
    // header有两种多种情况: 1. 可视化统一接口，小红点接口，天气接口; 0是其他接口
    int? mode = 0,
  }) {
    Map<String, dynamic> requestMap = requestData == null ? {} : requestData;

    final _timestamp = getTimestamp();

    String bodyJson = json.encode(requestMap);
    String sign = getSign(
      url,
      bodyJson,
      _appId,
      _appKey,
      _timestamp,
    );

    return mode == 0
        ? {
            'timestamp': _timestamp.toString(),
            'sign': sign,
            'appId': _appId,
            'clientId': _clientId,
            'userId': _userId,
            'accessToken': _accessToken,
            'accountToken': _userCenterAccessToken,
            'appVersion': _appVersion,
          }
        : {
            'timestamp': _timestamp.toString(),
            'sign': sign,
            'appId': _appId,
            'clientId': _clientId,
            'accessToken': _accessToken,
            'accountToken': _userCenterAccessToken,
            'appVersion': _appVersion,
            'grayMode': _grayMode,
          };
  }

  /// 统一封装 get 请求
  Future<dynamic> getData(String url,
      {Map<String, dynamic>? params, int mode = 0}) async {
    await updateAppInfoParams();
    Options _options = Options(
      headers: getHeader(requestData: params, url: url, mode: mode),
    );
    Response response;
    try {
      response = await _dio.get(
          (_env == '验收'
                  ? (mode == 100 // 判断根据经纬度获取地址的域名
                      ? Constant.LOCATION_baseYsUrl
                      : Constant.baseYsUrl)
                  : (mode == 100
                      ? Constant.LOCATION_BASEURL
                      : Constant.BASE_URL)) +
              url,
          queryParameters: params,
          options: _options);
      var data = response.data ?? Map();
      if (data is String) {
        data = jsonDecode(data);
      }
      return data;
    } catch (err) {
      DevLogger.error(
          tag: Constant.tagCommonUI, msg: {'fn': 'dio getData', 'err': err});
      throw err;
    }
  }

  /// 统一封装 post 请求
  Future<dynamic> postData(String url,
      {Map<String, dynamic>? params, bool query = false, int mode = 0}) async {
    await updateAppInfoParams();
    Options _options = Options(
      headers: getHeader(requestData: params, url: url),
    );
    Response response;
    try {
      response = await _dio.post(
          (_env == '验收' ? Constant.baseYsUrl : Constant.BASE_URL) + url,
          data: params,
          queryParameters: query ? params : null,
          options: _options);
      var data = response.data ?? Map();
      if (data is String) {
        data = jsonDecode(data);
      }
      return data;
    } catch (err) {
      DevLogger.error(
          tag: Constant.tagCommonUI, msg: {'fn': 'dio postData', 'err': err});
      throw err;
    }
  }
}

final HttpManager httpManager = new HttpManager();
