/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2022-09-16 11:36:56
 * @description: 
 */
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/log.dart';

import '../../flutter_common_ui.dart';

class ToastHelper {
  static BuildContext? context;

  static void init(BuildContext ctx) {
    context = ctx;
  }

  static OverlayEntry? _entry;
  static Timer? _timer;
  static OverlayState? _overlay;
  // 是否可以弹toast, 服务页当前tab时，才可以弹
  static bool _canShowToast = true;

  // 更新是否可以弹窗
  static void updateCanShow(bool value) {
    _canShowToast = value;
  }

  static void showToast(String text) {
    if (_canShowToast) {
      closeToast();
      final TextStyle style = TextStyle(
        color: AppSemanticColors.item.invert,
        fontSize: 14,
        height: 1,
        fontWeight: FontWeight.normal,
        decoration: TextDecoration.none,
      );

      final Widget widget = Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 234),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            color: AppSemanticColors.container.toast,
          ),
          child: Text(
            text,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textHeightBehavior: const TextHeightBehavior(
              leadingDistribution: TextLeadingDistribution.even,
            ),
            style: style,
          ),
        ),
      );
      _entry = OverlayEntry(
        builder: (_) => widget,
      );

      if (_entry != null && context != null) {
        _overlay = Overlay.of(context!);
        if (_overlay != null) {
          _overlay!.insert(_entry!);
        } else {
          DevLogger.error(
          tag: Constant.tagCommonUI, msg: <String, String>{'fn': 'showToast ', 'err': 'overlay is null'});
        }
      }

      _timer = Timer(const Duration(seconds: 2), () {
        _entry?.remove();
        _entry = null;
      });
    }
  }

  static void closeToast() {
    if (_entry != null && _timer != null) {
      _entry?.remove();
      _timer?.cancel();
      _entry = null;
      _timer = null;
    }
  }
}
