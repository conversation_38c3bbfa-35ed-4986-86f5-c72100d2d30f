/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-13 20:01:21
 * @description: 
 */
import 'dart:ui';

enum ColorSystem { light, dark }

class Constant {
//   // 提示信息
  static const timeoutMsg = '请求服务器超时';
  static const netWorkError = '网络不可用';
  static const requestFailMsg = '请求服务器异常';
  // 选择城市
  static const SELECT_CITY = 'mpaas://citySelection?keepStorage=1';

  static const smallRoomHead =
      'https://zjrs.haier.net/haierActivitys/intelligentHouse/index.html?container_type=3&hybrid_navbar_hidden=true&needAuthLogin=1&needLogin=1&needShare=1#/';

//   // 智家请求接口域名
  static const BASE_URL = 'https://zj.haier.net';
//   // 智家验收请求接口域名
  static const baseYsUrl = 'https://zj-yanshou.haier.net';

//   // 获取首页滚动消息接口
  static const SERVERMSG_URL = '/api-gw/wisdomadorn/ums/v1/msg/homepage';

//   // 可视化统一查询接口url
  static const visualizeRecommendUrl =
      '/api-gw/shpmResource/servicePage/visualize/recommend';

  // log的tag
  static const tagCommonUI = "[flutter_common_ui]:";

  static const packageName = 'flutter_common_ui';

  // 设备dpr
  static final double dpr = window.devicePixelRatio;
//   // 图片oss处理
  //分享到微信的缩略图
  static const OSS_WXSHARE =
      'x-oss-process=image/resize,m_lfit,w_200,limit_0/auto-orient,1';

  static const ColorFilter identity = ColorFilter.matrix(<double>[
    1,
    0,
    0,
    0,
    0,
    0,
    1,
    0,
    0,
    0,
    0,
    0,
    1,
    0,
    0,
    0,
    0,
    0,
    1,
    0,
  ]);

  static const ColorFilter invert = ColorFilter.matrix(<double>[
    -1,
    0,
    0,
    0,
    255,
    0,
    -1,
    0,
    0,
    255,
    0,
    0,
    -1,
    0,
    255,
    0,
    0,
    0,
    1,
    0,
  ]);
  // waterfall start
//   //生活加发现tab数据接口
  static const String discoverSecondTab =
      '/api-gw/scs/commons/v1/recommend/classes';

  // 瀑布流v2
  static const String discoverListContent =
      '/api-gw/shpmResource/waterfall/recommend/list';
//   // 传经纬度 然后可以返回街道码等位置信息的 生产地址
  static const LOCATION_BASEURL = 'https://m.ehaier.com';
//   // 传经纬度 然后可以返回街道码等位置信息的 验收请求接口域名
  static const String LOCATION_baseYsUrl = 'https://m-test.ehaier.com';

  // waterfall end
  // 应用未安装提示信息
  static const applicationUninstalled = '应用未安装';
  static const shareFailTip = '分享失败';
}
