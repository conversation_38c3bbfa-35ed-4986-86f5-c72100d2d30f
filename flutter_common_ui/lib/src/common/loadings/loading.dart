import 'dart:io';

import 'package:flutter/material.dart';

import '../constant.dart';

/// 通用loading组件
class CommonLoading {
  static OverlayEntry? _entry;
  static OverlayState? _overlay;

  static void showLoading(BuildContext? context, String text) {
    if (context == null) {
      return;
    }
    _entry = OverlayEntry(
      builder: (_) => _CommonLoadingWidget(text: text),
    );

    if (_entry != null) {
      _overlay = Overlay.of(context);
      if (_overlay != null) {
        _overlay!.insert(_entry!);
      }
    }
  }

  static bool isShowing() {
    return _entry != null && _entry!.mounted;
  }

  static void dismiss() {
    _entry?.remove();
    _overlay = null;
    _entry = null;
  }
}

class _CommonLoadingWidget extends StatefulWidget {
  const _CommonLoadingWidget({super.key, required this.text});

  final String text;

  @override
  State<_CommonLoadingWidget> createState() => _CommonLoadingWidgetState();
}

class _CommonLoadingWidgetState extends State<_CommonLoadingWidget>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<double>? _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    _animation = Tween<double>(begin: 0, end: -1).animate(
      CurvedAnimation(
        parent: _controller!,
        curve: Curves.linear, // 确保匀速旋转
      ),
    );

    // 启动循环动画
    _controller!.repeat();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: MediaQueryData.fromView(View.of(context)).size.height / 2 - 70,
      left: MediaQueryData.fromView(View.of(context)).size.width / 2 - 70,
      child: Container(
        width: 140,
        height: 140,
        padding: const EdgeInsets.symmetric(horizontal: 12),
        decoration: ShapeDecoration(
          color: Colors.black.withAlpha(204),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            if (_animation == null)
              const SizedBox()
            else
              RotationTransition(
                turns: _animation!,
                child: SizedBox(
                  width: 40,
                  height: 40,
                  child: Image.asset(
                    'assets/images/common_loading.png',
                    package: Constant.packageName,
                  ),
                ),
              ),
            const SizedBox(
              height: 8,
            ),
            SizedBox(
              width: 116,
              child: Text(
                widget.text,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontFamilyFallback:
                      Platform.isIOS ? <String>['PingFang SC'] : null,
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.none,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
