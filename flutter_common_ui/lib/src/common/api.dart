/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-13 20:01:21
 * @description: 
 */
// 接口请求方法
import 'package:flutter_common_ui/src/common/server.dart';

import 'constant.dart';

// 可视化数据统一查询接口的dataType枚举：
// 1：轮播控件  2：图文导航  3：图片控件 4：商品控件  5：家电科普 6: 直播  7: 驰骛推荐的生活家7.15.0（含）版本之后
enum DataType {
  stub,
  swiper,
  transformer,
  image,
  commodity,
  life,
  live,
  lifeRecommend
}

class API {
  // 可视化数据查询统一接口
  static Future getVisualizeRecommend({
    // 数值类型- 查询数据类型（1：轮播控件  2：图文导航  3：图片控件 4：商品控件  5：家电科普 6：直播控件）
    /*required*/ required int dataType,
    // 字符串类型-资源位（轮播控件必传，传具体的轮播标识；图文导航、图片控件、直播控件必传，传visual；商品控件必传，传1）
    String? bannerId,
    // 数值类型-组件ID，图文导航、图片控件、直播控件时必传(取flag字段)
    int? componentId,
    // 数值类型-控件ID，图片控件、直播控件类型必传(取id字段)
    int? controlId,
    // 字符串类型-经度，查询商品控件、直播控件数据时必传
    String? longitude,
    // 字符串类型-维度，查询商品控件、直播控件数据时必传
    String? latitude,
    // 布尔类型-本次请求是否向下移动位置，默认为true,传入false时下次请求从本次位置开始，家电科普必传
    bool? isMoveFocus,
    // 字符串类型-家庭id，家电科普类型可以传
    String? familyId,
    // 数值类型-查询数目（轮播控件、图片控件、商品控件、家电科普、直播控件必传）
    int? queryNum,
    // 3：图片控件, 中选择智能推荐时，数据来源，物料投放-1，售后服务推荐模型-101
    String? dataSource,
  }) async {
    Map<String, dynamic> requestMap = {
      'dataType': dataType,
      'bannerId': bannerId,
      'componentId': componentId,
      'controlId': controlId,
      'longitude': longitude,
      'latitude': latitude,
      'isMoveFocus': isMoveFocus,
      'familyId': familyId,
      'queryNum': queryNum,
      'dataSource': dataSource,
    };

    return await httpManager.getData(Constant.visualizeRecommendUrl,
        params: requestMap, mode: 1);
  }

  // waterfall
  /*
   * 获取发现页面二级tab数据
   */
  static Future<dynamic> getDiscoverListContent(
      Map<String, dynamic> params) async {
    return httpManager.getData(Constant.discoverListContent, params: params);
  }

  /*
   * 获取发现页面二级tab数据
   */
  static Future<dynamic> getDiscoverSecondTab() async {
    return httpManager.getData(
      Constant.discoverSecondTab,
    );
  }
}
