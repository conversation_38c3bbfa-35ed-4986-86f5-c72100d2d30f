import 'dart:ui';

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    hexColor = hexColor.replaceAll('0X', '');
    if (hexColor.length == 6) {
      // 6位的颜色位库内自己使用的颜色，需在前面加FF不透明
      hexColor = "FF" + hexColor;
    } else if (hexColor.length == 8) {
      // 后端传来的 css hex 颜色除#号后共8位，前6位为颜色色值，后两位为透明度
      // flutter hex 颜色，前2位为透明度，后6位为颜色色值，所以换一下位置
      hexColor = hexColor.substring(6, 8) + hexColor.substring(0, 6);
    } else {
      hexColor = "FF000000";
    }

    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}
