import 'dart:async';

import 'package:flutter/foundation.dart';

class Debounce {
  /// 防抖的时间
  final int milliseconds;

  final bool immediately;

  /// 执行函数
  VoidCallback? action;

  /// 计时器
  Timer? _timer;

  // 默认1s
  Debounce({this.milliseconds = 1000, this.immediately = true});

  /// 暴露run函数
  run(VoidCallback action) {
    var delay = Duration(milliseconds: milliseconds);
    if (_timer?.isActive ?? false) {
      _timer?.cancel();
    }

    // 立即执行
    if (immediately) {
      // 没有定时器，立即执行
      var callNow = _timer == null;
      // 给定时器赋值
      _timer = Timer(delay, () {
        _timer?.cancel();
        _timer = null;
      });
      if (callNow) {
        action.call();
      }
    } else {
      _timer = Timer(delay, () {
        action.call();
      });
    }

    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}
