/*
 * 描述：弹窗工具封装
 * 作者：fancunshuo
 * 建立时间: 2025/5/12
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_common_ui/src/common/dialogs/bottom_sheet_dialog.dart';
import 'package:flutter_common_ui/src/common/dialogs/popup_dialog.dart';
import 'package:redux/redux.dart';

export 'package:flutter_common_ui/src/common/dialogs/dialog_components.dart';
export 'package:flutter_common_ui/src/common/dialogs/dialog_config.dart';

class Dialogs {
  PopupDialogs popupDialogs = PopupDialogs();

  /// 双按钮弹窗，带"取消"和"确认"按钮
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [cancelCallback] 弹窗取消按钮的点击回调事件
  /// - [confirmText] 弹窗确认按钮的文案，默认是"确定"
  /// - [confirmCallback] 弹窗确认按钮的点击回调事件
  /// - [confirmBtnType] 弹窗确认按钮的类型，默认是primary，详情见[ButtonType]
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showDoubleBtnDialog({
    required BuildContext context,
    required String title,
    required String content,
    VoidCallback? cancelCallback,
    String confirmText = '确定',
    required VoidCallback confirmCallback,
    ButtonType confirmBtnType = ButtonType.primary,
    bool barrierDismissible = true,
  }) {
    popupDialogs.showDoubleBtnDialog(
      context: context,
      title: title,
      content: content,
      cancelCallback: cancelCallback,
      confirmText: confirmText,
      confirmCallback: confirmCallback,
      confirmBtnType: confirmBtnType,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 单按钮弹窗
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [btnText] 弹窗按钮的标题，默认是"我知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showSingleBtnDialog({
    required BuildContext context,
    required String title,
    required String content,
    String btnText = '我知道了',
    required VoidCallback callback,
    bool barrierDismissible = true,
  }) {
    popupDialogs.showSingleBtnDialog(
      context: context,
      title: title,
      content: content,
      btnText: btnText,
      callback: callback,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 支持builder的单按钮弹窗
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [builder] 弹窗内容builder
  /// - [btnText] 弹窗按钮的标题，默认是"知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showSingleBtnDialogWithBuilder({
    required BuildContext context,
    required String title,
    required WidgetBuilder builder,
    String btnText = '知道了',
    required VoidCallback callback,
    bool barrierDismissible = true,
  }) {
    popupDialogs.showSingleBtnDialogWithBuilder(
      context: context,
      title: title,
      builder: builder,
      btnText: btnText,
      callback: callback,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 确认弹窗
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [actionConfig] 弹窗底部操作按钮配置，具体配置项参见[ActionConfig]
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  ///
  /// 使用示例：
  ///
  /// ```
  /// // 确认和取消按钮
  /// Dialogs.showConfirmDialog(
  ///   context: context,
  ///   title: '删除确认',
  ///   content: '是否删除是否删除是否删除是否删除',
  ///   actionConfig: ActionConfig.horizontal(
  ///     <Widget>[
  ///       ButtonFill.cancel(
  ///         callback: () {},
  ///       ),
  ///       ButtonFill.confirm(callback: () {}),
  ///     ],
  ///   ),
  /// );
  ///
  /// // 我知道了
  /// Dialogs.showConfirmDialog(
  ///   context: context,
  ///   title: '删除确认',
  ///   content: '是否删除是否删除是否删除是否删除',
  ///   actionConfig: ActionConfig.iKnow(),
  /// );
  ///
  /// ```
  void showConfirmDialog({
    required BuildContext context,
    String title = '',
    required String content,
    ActionConfig actionConfig = const ActionConfig.none(),
    bool barrierDismissible = true,
  }) {
    popupDialogs.showConfirmDialog(
      context: context,
      title: title,
      content: content,
      actionConfig: actionConfig,
      barrierDismissible: barrierDismissible,
    );
  }

  void closeDialog() {
    popupDialogs.closeDialog();
  }

  BottomSheetDialogs bottomSheetDialogs = BottomSheetDialogs();

  /// 双按钮底部弹窗，带"取消"和"确认"按钮
  /// 非实时刷新泛型传void即可
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [confirmText] 弹窗确认按钮的文案，默认是"确定"
  /// - [confirmCallback] 弹窗确认按钮的文案点击回调事件
  /// - [store] 需要提供弹窗内部刷新的场景需要提供的StoreProvider
  void showDoubleBtnModal<T>({
    required BuildContext context,
    required String title,
    bool enableDrag = false,
    required WidgetBuilder child,
    String confirmText = '确定',
    required VoidCallback confirmCallback,
    VoidCallback? cancelCallback,
    Store<T>? store,
  }) {
    bottomSheetDialogs.showDoubleBtnModal<T>(
      context: context,
      title: title,
      enableDrag: enableDrag,
      child: child,
      confirmText: confirmText,
      confirmCallback: confirmCallback,
      cancelCallback: cancelCallback,
      store: store,
    );
  }

  /// 单按钮底部弹窗，非实时刷新泛型传void即可
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [btnText] 弹窗按钮的标题，默认是"我知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [store] 需要提供弹窗内部刷新的场景需要提供的StoreProvider
  void showSingleBtnModal<T>({
    required BuildContext context,
    String title = '',
    bool enableDrag = false,
    required WidgetBuilder child,
    String btnText = '我知道了',
    required VoidCallback callback,
    ButtonType btnType = ButtonType.primary,
    Store<T>? store,
  }) {
    bottomSheetDialogs.showSingleBtnModal<T>(
      context: context,
      title: title,
      enableDrag: enableDrag,
      child: child,
      btnText: btnText,
      callback: callback,
      btnType: btnType,
      store: store,
    );
  }

  /// 底部弹窗，默认标题和底部操作固定，中间可滚动
  /// - [context] BuildContext
  /// - [headerConfig] 弹窗标题配置，具体配置项参见[HeaderConfig]
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [actionConfig] 弹窗底部操作按钮配置，具体配置项参见[ActionConfig]
  /// - [maxHeight] 弹窗的最大高度，不包含safeArea
  /// - [isDismissible] 是否可通过点击蒙层关闭，默认为true
  /// - [afterClose] 弹窗关闭后的回调
  /// - [decorator] 装饰器，用于实现StoreProvider等操作
  ///
  ///
  /// 使用示例：
  ///
  /// ```
  /// Dialogs.showSmartHomeModalBottomSheet(
  ///   context: context,
  ///   headerConfig: const HeaderConfig.title('主标题文本'),
  ///   child: (BuildContext context) => const Placeholder(),
  ///   actionConfig: ActionConfig.vertical(
  ///     <Widget>[
  ///       ButtonFill.cancel(
  ///         callback: () {
  ///           Dialogs.closeSmartHomeModalBottomSheet();
  ///         },
  ///       )
  ///     ],
  ///   ),
  /// );
  /// ```
  Future<void> showSmartHomeModalBottomSheet({
    required BuildContext context,
    required HeaderConfig headerConfig,
    required WidgetBuilder child,
    ActionConfig actionConfig = const ActionConfig.none(),
    double? maxHeight,
    bool isDismissible = true,
    VoidCallback? afterClose,
    WidgetDecorator? decorator,
  }) {
    return bottomSheetDialogs.showSmartHomeModalBottomSheet(
      context: context,
      headerConfig: headerConfig,
      child: child,
      actionConfig: actionConfig,
      maxHeight: maxHeight,
      isDismissible: isDismissible,
      afterClose: afterClose,
      decorator: decorator,
    );
  }

  void closeSmartHomeModalBottomSheet() {
    bottomSheetDialogs.closeSmartHomeModalBottomSheet();
  }
}
