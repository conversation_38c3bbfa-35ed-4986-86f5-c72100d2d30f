/*
 * 描述：通用弹窗UI组件
 * 作者：fancunshuo
 * 建立时间: 2025/5/15
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_common_ui/src/common/dialogs/dialog_config.dart';
import 'package:flutter_common_ui/src/utils/card_text_style.dart';

class DialogComponents {
  static Widget buildDragHandler() {
    return Container(
      height: 4,
      width: 32,
      decoration: BoxDecoration(
        color: AppSemanticColors.component.secondary.emphasize,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  static Widget buildBottomActions(List<Widget>? actions,
      {Axis actionDirection = Axis.horizontal}) {
    if (actions != null) {
      if (actionDirection == Axis.horizontal) {
        return Row(
          children: <Widget>[
            for (int i = 0; i < actions.length; i++)
              if (i < actions.length - 1) ...<Widget>[
                Expanded(child: actions[i]),
                const SizedBox(
                  width: 12,
                )
              ] else
                Expanded(child: actions[i]),
          ],
        );
      } else {
        return Column(
          children: <Widget>[
            for (int i = 0; i < actions.length; i++)
              if (i < actions.length - 1) ...<Widget>[
                actions[i],
                const SizedBox(
                  height: 12,
                )
              ] else
                actions[i],
          ],
        );
      }
    }
    return const SizedBox();
  }

  static Widget buildHeader(HeaderConfig config) {
    return Padding(
      padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 12.0),
      child: Column(
        children: <Widget>[
          if (config.title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Row(
                children: <Widget>[
                  if (config.leading != null || config.trailing != null)
                    Container(
                      alignment: Alignment.centerLeft,
                      width: 72,
                      padding: const EdgeInsets.only(left: 4),
                      child: config.leading,
                    ),
                  Expanded(
                    child: Align(
                      child: Text(
                        config.title,
                        style: TextStyle(
                          fontSize: 17,
                          color: AppSemanticColors.item.primary,
                          fontWeight: FontWeight.w500,
                          fontFamilyFallback: fontFamilyFallback(),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  if (config.leading != null || config.trailing != null)
                    Container(
                      alignment: Alignment.centerRight,
                      width: 72,
                      padding: const EdgeInsets.only(left: 4),
                      child: config.trailing,
                    ),
                ],
              ),
            ),
          if (config.subtitle.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                config.subtitle,
                style: TextStyle(
                  fontSize: 10,
                  color: AppSemanticColors.item.secWeaken,
                  fontWeight: FontWeight.w500,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              ),
            ),
          if (config.desc.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                config.desc,
                style: TextStyle(
                  fontSize: 14,
                  color: AppSemanticColors.item.secondary,
                  fontWeight: FontWeight.w500,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  static Widget buildActionSection(ActionConfig actionConfig) {
    if (actionConfig.actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: buildBottomActions(
        actionConfig.actions,
        actionDirection: actionConfig.actionDirection,
      ),
    );
  }
}
