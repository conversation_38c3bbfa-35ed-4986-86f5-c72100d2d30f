/*
 * 描述：底部弹窗相关实现
 * 作者：fancunshuo
 * 建立时间: 2025/5/15
 */
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/dialogs/dialog_components.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import '../log.dart';

class BottomSheetDialogs {
  /// 双按钮底部弹窗，带"取消"和"确认"按钮
  /// 非实时刷新泛型传void即可
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [confirmText] 弹窗确认按钮的文案，默认是"确定"
  /// - [confirmCallback] 弹窗确认按钮的文案点击回调事件
  /// - [store] 需要提供弹窗内部刷新的场景需要提供的StoreProvider
  void showDoubleBtnModal<T>({
    required BuildContext context,
    required String title,
    bool enableDrag = false,
    required WidgetBuilder child,
    String confirmText = '确定',
    required VoidCallback confirmCallback,
    VoidCallback? cancelCallback,
    Store<T>? store,
  }) {
    showSmartHomeModalBottomSheet(
      context: context,
      child: child,
      headerConfig: HeaderConfig.title(title, enableDrag: enableDrag),
      actionConfig: ActionConfig.cancelConfirm(
        confirmText: confirmText,
        cancelCallback: () {
          cancelCallback?.call();
          closeSmartHomeModalBottomSheet();
        },
        confirmCallback: () {
          confirmCallback.call();
          closeSmartHomeModalBottomSheet();
        },
      ),
      decorator: (Widget child) {
        if (store != null) {
          return StoreProvider<T>(
            store: store,
            child: child,
          );
        }
        return child;
      },
    );
  }

  /// 单按钮底部弹窗，非实时刷新泛型传void即可
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [btnText] 弹窗按钮的标题，默认是"我知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [store] 需要提供弹窗内部刷新的场景需要提供的StoreProvider
  void showSingleBtnModal<T>({
    required BuildContext context,
    String title = '',
    bool enableDrag = false,
    required WidgetBuilder child,
    String btnText = '我知道了',
    required VoidCallback callback,
    ButtonType btnType = ButtonType.primary,
    Store<T>? store,
  }) {
    showSmartHomeModalBottomSheet(
      context: context,
      child: child,
      headerConfig: HeaderConfig.title(title, enableDrag: enableDrag),
      actionConfig: ActionConfig.singleBtn(
        text: btnText,
        callback: () {
          callback.call();
          closeSmartHomeModalBottomSheet();
        },
        type: btnType,
      ),
      decorator: (Widget child) {
        if (store != null) {
          return StoreProvider<T>(
            store: store,
            child: child,
          );
        }
        return child;
      },
    );
  }

  final String _modalPageName =
      '${Constant.packageName}/show_smart_home_modal_bottom_sheet_widget_${DateTime.now().millisecondsSinceEpoch}';
  static const double _topMargin = 98; // 距离顶部安全区域的固定间距
  static const double _minContentHeight = 99; // 内容区域最小高度

  BuildContext? _modalContext;

  /// 底部弹窗，默认标题和底部操作固定，中间可滚动
  /// - [context] BuildContext
  /// - [headerConfig] 弹窗标题配置，具体配置项参见[HeaderConfig]
  /// - [child] 弹窗内容builder，参数context为弹窗内部context
  /// - [actionConfig] 弹窗底部操作按钮配置，具体配置项参见[ActionConfig]
  /// - [maxHeight] 弹窗的最大高度，不包含safeArea
  /// - [isDismissible] 是否可通过点击蒙层关闭，默认为true
  /// - [afterClose] 弹窗关闭后的回调
  /// - [decorator] 装饰器，用于实现StoreProvider等操作
  ///
  ///
  /// 使用示例：
  ///
  /// ```
  /// Dialogs.showSmartHomeModalBottomSheet(
  ///   context: context,
  ///   headerConfig: const HeaderConfig.title('主标题文本'),
  ///   child: (BuildContext context) => const Placeholder(),
  ///   actionConfig: ActionConfig.vertical(
  ///     <Widget>[
  ///       ButtonFill.cancel(
  ///         callback: () {
  ///           Dialogs.closeSmartHomeModalBottomSheet();
  ///         },
  ///       )
  ///     ],
  ///   ),
  /// );
  /// ```
  Future<void> showSmartHomeModalBottomSheet({
    required BuildContext context,
    required HeaderConfig headerConfig,
    required WidgetBuilder child,
    ActionConfig actionConfig = const ActionConfig.none(),
    double? maxHeight,
    bool isDismissible = true,
    VoidCallback? afterClose,
    WidgetDecorator? decorator,
  }) {
    closeSmartHomeModalBottomSheet();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      InterceptSystemBackUtil.interceptSystemBack(
          pageName: _modalPageName,
          callback: () {
            closeSmartHomeModalBottomSheet();
            InterceptSystemBackUtil.cancelInterceptSystemBack(_modalPageName);
          });
    });
    return showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: headerConfig.enableDrag,
      isScrollControlled: true,
      isDismissible: isDismissible,
      builder: (BuildContext context) {
        _modalContext = context;

        // 获取底部安全区域高度
        final double bottomPadding = MediaQuery.of(context).padding.bottom;
        final double marginBottom = bottomPadding == 0 ? 12 : bottomPadding;
        final double calculatedMaxHeight = (maxHeight ??
                (MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top)) -
            _topMargin -
            marginBottom;
        final double height = max(_minContentHeight, calculatedMaxHeight);
        Widget widget = Container(
          constraints: BoxConstraints(maxHeight: height),
          margin: EdgeInsets.only(left: 12, right: 12, bottom: marginBottom),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            color: AppSemanticColors.container.popup,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 4),
                  child: headerConfig.enableDrag
                      ? DialogComponents.buildDragHandler()
                      : const SizedBox(height: 4)),
              DialogComponents.buildHeader(headerConfig),
              // 内容部分
              Flexible(
                  child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: child(context),
              )),
              // 底部按钮
              DialogComponents.buildActionSection(actionConfig),
            ],
          ),
        );
        if (decorator != null) {
          widget = decorator(widget);
        }
        return widget;
      },
    ).whenComplete(() {
      InterceptSystemBackUtil.cancelInterceptSystemBack(_modalPageName);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        afterClose?.call();
        _modalContext = null;
      });
    });
  }

  void closeSmartHomeModalBottomSheet() {
    if (_modalContext != null) {
      try {
        Navigator.of(_modalContext!).maybePop();
      } catch (e, trace) {
        DevLogger.error(
            tag: Constant.tagCommonUI,
            msg: 'closeSmartHomeModalBottomSheet error: $e, $trace');
      }
    }
  }
}
