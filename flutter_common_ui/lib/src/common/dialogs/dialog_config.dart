/*
 * 描述：弹窗配置类
 * 作者：fancunshuo
 * 建立时间: 2025/5/15
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/common/constant.dart';
import 'package:flutter_common_ui/src/common/dialogs/dialogs.dart';
import 'package:flutter_common_ui/src/widgets/ui_kit/button.dart';

typedef WidgetDecorator = Widget Function(Widget child);

/// 底部弹窗的标题栏配置类
/// - [title] - 主标题文本
/// - [subtitle] - 副标题文本，UI注：单行简短-不常用
/// - [desc] - 描述文案，可多行不限字数，3行及以上左对齐
/// - [leading] - 底部弹窗标题栏左操控 eg: 返回按钮，取消按钮等
/// - [trailing] - 底部弹窗标题栏右操控 eg: 完成按钮，更多按钮等
/// - [enableDrag] 是否支持向下滑动关闭弹窗，如果true显示顶部小白条
///
/// 默认提供只有标题和带返回箭头的组件，用法见下方实例
/// ```
/// // 只有标题的配置方式
/// const HeaderConfig.title('主标题文本'),
///
/// // 带返回箭头的配置方式
/// HeaderConfig.titleWithNaviBack(
///   title: '主标题文本',
///   callback: () {
///     Dialogs.closeSmartHomeModalBottomSheet();
/// })
/// ```
class HeaderConfig {
  const HeaderConfig(this.title, this.subtitle, this.desc,
      {this.leading, this.trailing, this.enableDrag = true});

  const HeaderConfig.title(String title, {bool enableDrag = true})
      : this(title, '', '', enableDrag: enableDrag);

  HeaderConfig.titleWithNaviBack({
    required String title,
    required VoidCallback callback,
    bool enableDrag = false,
  }) : this(
          title,
          '',
          '',
          leading: GestureDetector(
            onTap: callback,
            child: Image.asset(
              'assets/images/navi_back.webp',
              package: Constant.packageName,
              height: 24,
              width: 24,
            ),
          ),
          enableDrag: enableDrag,
        );

  HeaderConfig.titleWithClose({
    required String title,
    required VoidCallback callback,
    bool enableDrag = false,
  }) : this(
          title,
          '',
          '',
          trailing: GestureDetector(
            onTap: callback,
            child: Image.asset(
              'assets/images/close.webp',
              package: Constant.packageName,
              height: 24,
              width: 24,
            ),
          ),
          enableDrag: enableDrag,
        );

  final String title;
  final String subtitle;
  final String desc;
  final Widget? leading;
  final Widget? trailing;
  final bool enableDrag;
}

const List<Widget> _emptyActions = <Widget>[];

/// 弹窗底部操作配置类
/// - [actions] 页底通栏按钮配置，具体配置参见[ButtonFill]
/// - [actionDirection] 按钮排布方式，横向或竖向
///
/// 默认提供取消按钮构造方法、横向排布构造方法和竖向排布构造方法，用法见下方实例
/// ```
/// // 取消按钮构造方法
/// ActionConfig.cancel(
///   callback: () {
///     Dialogs.closeSmartHomeModalBottomSheet();
///   },
/// )
///
/// // 横向排布构造方法
/// ActionConfig.horizontal(
///   <Widget>[
///     ButtonFill.cancel(
///       callback: () {
///         Dialogs.closeSmartHomeModalBottomSheet();
///       },
///     ),
///     ButtonFill.confirm(callback: () {}),
///   ],
/// )
///
/// // 竖向排布构造方法
/// ActionConfig.vertical(
///   <Widget>[
///     ButtonFill.cancel(
///       callback: () {
///         Dialogs.closeSmartHomeModalBottomSheet();
///       },
///     ),
///     ButtonFill.confirm(callback: () {}),
///   ],
/// )
/// ```
class ActionConfig {
  const ActionConfig(this.actions, {this.actionDirection = Axis.vertical});

  const ActionConfig.none()
      : this(_emptyActions, actionDirection: Axis.vertical);

  ActionConfig.cancel({VoidCallback? callback, Dialogs? dialogs})
      : this(
          <Widget>[
            ButtonFill.cancel(
              callback: () {
                callback?.call();
                dialogs?.closeSmartHomeModalBottomSheet();
              },
            )
          ],
          actionDirection: Axis.vertical,
        );

  ActionConfig.singleBtn({
    String text = '我知道了',
    VoidCallback? callback,
    ButtonType type = ButtonType.primary,
  }) : this(
          <Widget>[
            ButtonFill(
              text: text,
              callback: () {
                callback?.call();
              },
              type: type,
            )
          ],
          actionDirection: Axis.vertical,
        );

  ActionConfig.cancelConfirm({
    String confirmText = '确认',
    required VoidCallback cancelCallback,
    required VoidCallback confirmCallback,
    ButtonType confirmBtnType = ButtonType.primary,
  }) : this(<Widget>[
          ButtonFill.cancel(
            callback: cancelCallback,
          ),
          ButtonFill(
            text: confirmText,
            callback: confirmCallback,
            type: confirmBtnType,
          )
        ], actionDirection: Axis.horizontal);

  ActionConfig.vertical(List<Widget> actions)
      : this(actions, actionDirection: Axis.vertical);

  ActionConfig.horizontal(List<Widget> actions)
      : this(actions, actionDirection: Axis.horizontal);

  final List<Widget> actions;
  final Axis actionDirection;
}
