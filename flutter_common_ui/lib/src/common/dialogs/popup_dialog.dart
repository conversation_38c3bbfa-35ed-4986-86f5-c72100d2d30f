/*
 * 描述：弹出对话框相关实现
 * 作者：fancunshuo
 * 建立时间: 2025/5/15
 */
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_common_ui/src/common/dialogs/dialog_components.dart';
import 'package:flutter_common_ui/src/utils/util.dart';

import '../../utils/card_text_style.dart';
import '../constant.dart';
import 'dialog_config.dart';

class PopupDialogs {
  /// 双按钮弹窗，带"取消"和"确认"按钮
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [cancelCallback] 弹窗取消按钮的点击回调事件
  /// - [confirmText] 弹窗确认按钮的文案，默认是"确定"
  /// - [confirmCallback] 弹窗确认按钮的点击回调事件
  /// - [confirmBtnType] 弹窗确认按钮的类型，默认是primary，详情见[ButtonType]
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showDoubleBtnDialog({
    required BuildContext context,
    required String title,
    required String content,
    VoidCallback? cancelCallback,
    String confirmText = '确定',
    required VoidCallback confirmCallback,
    ButtonType confirmBtnType = ButtonType.primary,
    bool barrierDismissible = true,
  }) {
    showConfirmDialog(
      context: context,
      title: title,
      content: content,
      barrierDismissible: barrierDismissible,
      actionConfig: ActionConfig.cancelConfirm(
        cancelCallback: () {
          cancelCallback?.call();
          closeDialog();
        },
        confirmText: confirmText,
        confirmCallback: () {
          confirmCallback.call();
          closeDialog();
        },
        confirmBtnType: confirmBtnType,
      ),
    );
  }

  /// 单按钮弹窗
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [btnText] 弹窗按钮的标题，默认是"我知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showSingleBtnDialog({
    required BuildContext context,
    required String title,
    required String content,
    String btnText = '我知道了',
    required VoidCallback callback,
    bool barrierDismissible = true,
  }) {
    showConfirmDialog(
      context: context,
      title: title,
      content: content,
      actionConfig: ActionConfig.singleBtn(
          text: btnText,
          callback: () {
            callback.call();
            closeDialog();
          }),
      barrierDismissible: barrierDismissible,
    );
  }

  /// 支持builder的单按钮弹窗
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [builder] 弹窗内容builder
  /// - [btnText] 弹窗按钮的标题，默认是"知道了"
  /// - [callback] 弹窗按钮点击回调事件
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  void showSingleBtnDialogWithBuilder({
    required BuildContext context,
    required String title,
    required WidgetBuilder builder,
    String btnText = '知道了',
    required VoidCallback callback,
    bool barrierDismissible = true,
  }) {
    showConfirmDialog(
      context: context,
      title: title,
      content: '',
      builder: builder,
      actionConfig: ActionConfig.singleBtn(
          text: btnText,
          callback: () {
            callback.call();
            closeDialog();
          }),
      barrierDismissible: barrierDismissible,
    );
  }

  final String _dialogPageName =
      '${Constant.packageName}/show_confirm_dialog_widget_${DateTime.now().millisecondsSinceEpoch}';

  BuildContext? _dialogContext;

  /// 确认弹窗，优先使用builder构建内容，若builder为空则取content
  /// - [context] BuildContext
  /// - [title] 弹窗标题
  /// - [content] 弹窗内容文本
  /// - [builder] 弹窗内容builder，优先使用
  /// - [actionConfig] 弹窗底部操作按钮配置，具体配置项参见[ActionConfig]
  /// - [barrierDismissible] 是否允许点击弹窗蒙层关闭弹窗
  ///
  /// 使用示例：
  ///
  /// ```
  /// // 确认和取消按钮
  /// Dialogs.showConfirmDialog(
  ///   context: context,
  ///   title: '删除确认',
  ///   content: '是否删除是否删除是否删除是否删除',
  ///   actionConfig: ActionConfig.horizontal(
  ///     <Widget>[
  ///       ButtonFill.cancel(
  ///         callback: () {},
  ///       ),
  ///       ButtonFill.confirm(callback: () {}),
  ///     ],
  ///   ),
  /// );
  ///
  /// // 我知道了
  /// Dialogs.showConfirmDialog(
  ///   context: context,
  ///   title: '删除确认',
  ///   content: '是否删除是否删除是否删除是否删除',
  ///   actionConfig: ActionConfig.iKnow(),
  /// );
  ///
  /// ```
  void showConfirmDialog({
    required BuildContext context,
    String title = '',
    required String content,
    WidgetBuilder? builder,
    bool barrierDismissible = true,
    ActionConfig actionConfig = const ActionConfig.none(),
  }) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      InterceptSystemBackUtil.interceptSystemBack(
          pageName: _dialogPageName,
          callback: () {
            closeDialog();
            InterceptSystemBackUtil.cancelInterceptSystemBack(_dialogPageName);
          });
    });
    showDialog<void>(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (BuildContext context) {
        _dialogContext = context;

        final double bottomPadding = MediaQuery.of(context).padding.bottom;
        final double marginBottom = bottomPadding == 0 ? 12 : bottomPadding;

        final bool hasContent = content.isNotEmpty || builder != null;

        return Align(
          alignment: Alignment.bottomCenter,
          child: Material(
            color: Colors.transparent,
            child: Container(
              margin:
                  EdgeInsets.only(bottom: marginBottom, left: 12, right: 12),
              padding: const EdgeInsets.only(top: 16),
              decoration: BoxDecoration(
                color: AppSemanticColors.container.popup,
                borderRadius: const BorderRadius.all(Radius.circular(32)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        if (title.isNotEmpty)
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: 17,
                              color: AppSemanticColors.item.primary,
                              fontWeight: FontWeight.w500,
                              fontFamilyFallback: fontFamilyFallback(),
                            ),
                          ),
                        if (title.isNotEmpty && hasContent)
                          const SizedBox(
                            height: 12,
                          ),
                        if (hasContent)
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4.0),
                            child: builder != null
                                ? builder(context)
                                : Text(
                                    content,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppSemanticColors.item.secondary,
                                      fontFamilyFallback: fontFamilyFallback(),
                                    ),
                                  ),
                          ),
                      ],
                    ),
                  ),
                  DialogComponents.buildActionSection(actionConfig),
                ],
              ),
            ),
          ),
        );
      },
    ).whenComplete(() {
      InterceptSystemBackUtil.cancelInterceptSystemBack(_dialogPageName);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _dialogContext = null;
      });
    });
  }

  void closeDialog() {
    if (_dialogContext != null) {
      Navigator.of(_dialogContext!).maybePop();
    }
  }
}
