import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'family_detail_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class FamilyContentModel extends Attr {
  /// 组件id
  int? id;

  /// 样式 1: 文字名称
  int? style;

  /// 字号
  num? fontSize;

  /// 字重
  TextWeightType? fontWeight;

  /// 家庭名称颜色
  ColorModel? color;

  /// 默认文案
  String? defaultText;

  /// 埋点信息
  TrackInfoModel? trackInfo;

  /// 点击家庭下拉列表中内容项，切换家庭打点
  FamilyTrackInfo? list;

  /// 点击家庭组件设置(点击家庭管理)打点字段
  FamilyTrackInfo? manage;

  /// 构造函数
  FamilyContentModel(
      {this.id,
      this.style,
      this.fontSize,
      this.fontWeight,
      this.color,
      this.defaultText,
      this.trackInfo,
      this.list,
      this.manage});
  factory FamilyContentModel.fromJson(Map<String, dynamic> json) =>
      _$FamilyContentModelFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyContentModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class FamilyTrackInfo {
  TrackInfoModel? trackInfo;
  FamilyTrackInfo({this.trackInfo});
  factory FamilyTrackInfo.fromJson(Map<String, dynamic> json) =>
      _$FamilyTrackInfoFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyTrackInfoToJson(this);
}
