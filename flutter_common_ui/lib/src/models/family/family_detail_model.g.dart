// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'family_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FamilyContentModel _$FamilyContentModelFromJson(Map json) => FamilyContentModel(
      id: json['id'] as int?,
      style: json['style'] as int?,
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      defaultText: json['defaultText'] as String?,
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      list: json['list'] == null
          ? null
          : FamilyTrackInfo.fromJson(
              Map<String, dynamic>.from(json['list'] as Map)),
      manage: json['manage'] == null
          ? null
          : FamilyTrackInfo.fromJson(
              Map<String, dynamic>.from(json['manage'] as Map)),
    );

Map<String, dynamic> _$FamilyContentModelToJson(FamilyContentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'style': instance.style,
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'defaultText': instance.defaultText,
      'trackInfo': instance.trackInfo?.toJson(),
      'list': instance.list?.toJson(),
      'manage': instance.manage?.toJson(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

FamilyTrackInfo _$FamilyTrackInfoFromJson(Map json) => FamilyTrackInfo(
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
    );

Map<String, dynamic> _$FamilyTrackInfoToJson(FamilyTrackInfo instance) =>
    <String, dynamic>{
      'trackInfo': instance.trackInfo?.toJson(),
    };
