import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/family/family_detail_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'family_info_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class FamilyInfoModel extends BaseModel {
  ///组件自己的属性
  @override
  final FamilyContentModel? attr;

  /// 组件自己的样式
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [customFeature] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  /// 构造函数
  FamilyInfoModel(
      {String? alias,
      num? aliasCode,
      String? aliasName,
      String? aliasUnit,
      String? group,
      this.style,
      this.type,
      this.customFeature,
      this.attr})
      : super(
            type: type,
            alias: alias,
            aliasCode: aliasCode,
            aliasName: aliasName,
            aliasUnit: aliasUnit,
            group: group,
            style: style,
            customFeature: customFeature,
            attr: attr);
  factory FamilyInfoModel.fromJson(Map<String, dynamic> json) =>
      _$FamilyInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$FamilyInfoModelToJson(this);
}
