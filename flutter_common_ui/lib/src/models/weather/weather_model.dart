/*
 * @Author: ma<PERSON><PERSON><PERSON> ma<PERSON>@haier.com
 * @Date: 2023-02-27 18:24:29
 * @description: 天气组件可视化model
 */
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'weather_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class WeatherModel extends BaseModel {
  /// [attr] 图片的配置数据，包含布局、内容等
  @override
  final WeatherAttr? attr;

  /// [style] 组件样式 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  WeatherModel({
    this.type,
    this.attr,
    this.style,
    this.customFeature,
    String? alias,
    num? aliasCode,
    String? aliasName,
    String? aliasUnit,
    String? group,
  }) : super(
          attr: attr,
          style: style,
          customFeature: customFeature,
          type: type,
          alias: alias,
          aliasCode: aliasCode,
          aliasName: aliasName,
          aliasUnit: aliasUnit,
          group: group,
        );
  factory WeatherModel.fromJson(Map<String, dynamic> json) =>
      _$WeatherModelFromJson(json);
  Map<String, dynamic> toJson() => _$WeatherModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class WeatherAttr extends Attr {
  /// [style] 样式、1: 头像天气卡片 2: 文字天气卡片
  AttrWeatherStyle? style;

  /// 当样式选 头像天气卡片时 该项生效 卡片配置
  CardConfig? cardConfig;

  /// 当样式选 头像天气卡片时 该项生效 卡片配置
  WeatherConfig? weatherConfig;

  /// [trackInfo] 埋点信息
  TrackInfoModel? trackInfo;

  /// [radius] 天气卡片圆角
  RadiusModel? radius;

  /// [shadow] 卡片阴影   x   y  模糊 扩散  颜色
  ShadowModel? shadow;

  WeatherAttr(
      {this.style, this.cardConfig, this.trackInfo, this.radius, this.shadow});

  factory WeatherAttr.fromJson(Map<String, dynamic> json) =>
      _$WeatherAttrFromJson(json);
  Map<String, dynamic> toJson() => _$WeatherAttrToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class CardConfig extends Attr {
  /// [fontSize] 卡片文案字号
  final num? fontSize;

  /// [fontWeight] 卡片文案字重
  final TextWeightType? fontWeight;

  /// [color] 卡片文案颜色
  ColorModel? color;

  /// [avatar] 卡片默认头像
  final String? avatar;

  /// [backImg] 卡片默认背景图片
  final String? backImg;

  /// [text] 卡片默认文案
  final String? text;

  /// [dataSource] 文案/背景素材
  DataSource? dataSource;

  CardConfig(
      {this.fontSize,
      this.fontWeight,
      this.color,
      this.avatar,
      this.backImg,
      this.text,
      this.dataSource});

  factory CardConfig.fromJson(Map<String, dynamic> json) =>
      _$CardConfigFromJson(json);
  Map<String, dynamic> toJson() => _$CardConfigToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class DataSource extends Attr {
  /// [id] 素材ID
  final String? id;

  /// [name] 素材名字
  final String? name;

  DataSource({this.id, this.name});

  factory DataSource.fromJson(Map<String, dynamic> json) =>
      _$DataSourceFromJson(json);
  Map<String, dynamic> toJson() => _$DataSourceToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class WeatherConfig extends Attr {
  /// [posFontSize] 定位文案字号
  final num? posFontSize;

  /// [posFontWeight] 定位文案字重
  final TextWeightType? posFontWeight;

  /// [posColor] 定位文案颜色
  ColorModel? posColor;

  /// [posBackColor] 定位文案背景颜色
  ColorModel? posBackColor;

  /// [posFontSize] 天气文案字号
  final num? fontSize;

  /// [posFontWeight] 天气文案字重
  final TextWeightType? fontWeight;

  /// [color] 天气文案颜色
  ColorModel? color;

  WeatherConfig(
      {this.posFontSize,
      this.posFontWeight,
      this.posColor,
      this.posBackColor,
      this.fontSize,
      this.fontWeight,
      this.color});

  factory WeatherConfig.fromJson(Map<String, dynamic> json) =>
      _$WeatherConfigFromJson(json);
  Map<String, dynamic> toJson() => _$WeatherConfigToJson(this);
}

enum AttrWeatherStyle {
  @JsonValue(0)
  empty,

  /// [one] 文字天气卡片
  @JsonValue(1)
  one,

  /// [two] 头像天气卡片
  @JsonValue(2)
  two,
}
