// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'weather_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WeatherModel _$WeatherModelFromJson(Map json) => WeatherModel(
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      attr: json['attr'] == null
          ? null
          : WeatherAttr.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$WeatherModelToJson(WeatherModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

WeatherAttr _$WeatherAttrFromJson(Map json) => WeatherAttr(
      style: $enumDecodeNullable(_$AttrWeatherStyleEnumMap, json['style']),
      cardConfig: json['cardConfig'] == null
          ? null
          : CardConfig.fromJson(
              Map<String, dynamic>.from(json['cardConfig'] as Map)),
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
    )..weatherConfig = json['weatherConfig'] == null
        ? null
        : WeatherConfig.fromJson(
            Map<String, dynamic>.from(json['weatherConfig'] as Map));

Map<String, dynamic> _$WeatherAttrToJson(WeatherAttr instance) =>
    <String, dynamic>{
      'style': _$AttrWeatherStyleEnumMap[instance.style],
      'cardConfig': instance.cardConfig?.toJson(),
      'weatherConfig': instance.weatherConfig?.toJson(),
      'trackInfo': instance.trackInfo?.toJson(),
      'radius': instance.radius?.toJson(),
      'shadow': instance.shadow?.toJson(),
    };

const _$AttrWeatherStyleEnumMap = {
  AttrWeatherStyle.empty: 0,
  AttrWeatherStyle.one: 1,
  AttrWeatherStyle.two: 2,
};

CardConfig _$CardConfigFromJson(Map json) => CardConfig(
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      avatar: json['avatar'] as String?,
      backImg: json['backImg'] as String?,
      text: json['text'] as String?,
      dataSource: json['dataSource'] == null
          ? null
          : DataSource.fromJson(
              Map<String, dynamic>.from(json['dataSource'] as Map)),
    );

Map<String, dynamic> _$CardConfigToJson(CardConfig instance) =>
    <String, dynamic>{
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'avatar': instance.avatar,
      'backImg': instance.backImg,
      'text': instance.text,
      'dataSource': instance.dataSource?.toJson(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

DataSource _$DataSourceFromJson(Map json) => DataSource(
      id: json['id'] as String?,
      name: json['name'] as String?,
    );

Map<String, dynamic> _$DataSourceToJson(DataSource instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
    };

WeatherConfig _$WeatherConfigFromJson(Map json) => WeatherConfig(
      posFontSize: json['posFontSize'] as num?,
      posFontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['posFontWeight']),
      posColor: json['posColor'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['posColor'] as Map)),
      posBackColor: json['posBackColor'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['posBackColor'] as Map)),
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
    );

Map<String, dynamic> _$WeatherConfigToJson(WeatherConfig instance) =>
    <String, dynamic>{
      'posFontSize': instance.posFontSize,
      'posFontWeight': _$TextWeightTypeEnumMap[instance.posFontWeight],
      'posColor': instance.posColor?.toJson(),
      'posBackColor': instance.posBackColor?.toJson(),
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
    };
