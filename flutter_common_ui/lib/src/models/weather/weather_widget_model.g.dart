// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'weather_widget_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WeatherPayloadModel _$WeatherPayloadModelFromJson(Map json) =>
    WeatherPayloadModel(
      dayForecast: (json['dayForecast'] as Map?)?.map(
        (k, e) => MapEntry(k as String,
            DayForCast.fromJson(Map<String, dynamic>.from(e as Map))),
      ),
      weatherCondition: json['weatherCondition'] == null
          ? null
          : WeatherCondition.fromJson(
              Map<String, dynamic>.from(json['weatherCondition'] as Map)),
      airCondition: json['airCondition'] == null
          ? null
          : AirCondition.fromJson(
              Map<String, dynamic>.from(json['airCondition'] as Map)),
      area: json['area'] == null
          ? null
          : Area.fromJson(Map<String, dynamic>.from(json['area'] as Map)),
    );

Map<String, dynamic> _$WeatherPayloadModelToJson(
        WeatherPayloadModel instance) =>
    <String, dynamic>{
      'dayForecast':
          instance.dayForecast?.map((k, e) => MapEntry(k, e.toJson())),
      'weatherCondition': instance.weatherCondition?.toJson(),
      'airCondition': instance.airCondition?.toJson(),
      'area': instance.area?.toJson(),
    };

DayForCast _$DayForCastFromJson(Map json) => DayForCast(
      tempMax: json['tempMax'] as String?,
      tempMin: json['tempMin'] as String?,
    );

Map<String, dynamic> _$DayForCastToJson(DayForCast instance) =>
    <String, dynamic>{
      'tempMax': instance.tempMax,
      'tempMin': instance.tempMin,
    };

WeatherCondition _$WeatherConditionFromJson(Map json) => WeatherCondition(
      condition: json['condition'] as String?,
      humidity: json['humidity'] as String?,
    );

Map<String, dynamic> _$WeatherConditionToJson(WeatherCondition instance) =>
    <String, dynamic>{
      'humidity': instance.humidity,
      'condition': instance.condition,
    };

AirCondition _$AirConditionFromJson(Map json) => AirCondition(
      pm25: json['pm25'] as String?,
    );

Map<String, dynamic> _$AirConditionToJson(AirCondition instance) =>
    <String, dynamic>{
      'pm25': instance.pm25,
    };

Area _$AreaFromJson(Map json) => Area(
      areaId: json['areaId'] as String?,
    );

Map<String, dynamic> _$AreaToJson(Area instance) => <String, dynamic>{
      'areaId': instance.areaId,
    };

MaterialData _$MaterialDataFromJson(Map json) => MaterialData(
      backgroundUrl: json['backgroundUrl'] as String?,
      contentDesc: json['contentDesc'] as String?,
      contentType: json['contentType'] as String?,
      headIconUrl: json['headIconUrl'] as String?,
      weatheDesc: json['weatheDesc'] as String?,
      avatorUrl: json['avatorUrl'] as String?,
    );

Map<String, dynamic> _$MaterialDataToJson(MaterialData instance) =>
    <String, dynamic>{
      'headIconUrl': instance.headIconUrl,
      'contentDesc': instance.contentDesc,
      'backgroundUrl': instance.backgroundUrl,
      'contentType': instance.contentType,
      'weatheDesc': instance.weatheDesc,
      'avatorUrl': instance.avatorUrl,
    };
