/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-03-02 16:55:03
 * @description: 天气组件城市选择数据
 */
import 'package:json_annotation/json_annotation.dart';

part 'city_data_model.g.dart';

@JsonSerializable(explicitToJson: true)
// 城市数据的model
class CityDataModel {
  String areaId;
  String areaName;

  CityDataModel({this.areaId = '', this.areaName = ''});

  factory CityDataModel.fromJson(Map<String, dynamic> json) =>
      _$CityDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$CityDataModelToJson(this);
}
