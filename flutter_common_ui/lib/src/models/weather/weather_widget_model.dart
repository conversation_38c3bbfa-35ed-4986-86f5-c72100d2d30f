/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-02 18:28:03
 * @description: 
 */
import 'package:json_annotation/json_annotation.dart';

part 'weather_widget_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class WeatherPayloadModel {
  Map<String, DayForCast>? dayForecast;
  WeatherCondition? weatherCondition;
  AirCondition? airCondition;
  Area? area;

  WeatherPayloadModel(
      {this.dayForecast, this.weatherCondition, this.airCondition, this.area});

  factory WeatherPayloadModel.fromJson(Map<String, dynamic> json) =>
      _$WeatherPayloadModelFromJson(json);

  Map<String, dynamic> toJson() => _$WeatherPayloadModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class DayForCast {
  // 全天最高气温 单位：摄氏度（℃）
  String? tempMax;
  // 全天最低气温 单位：摄氏度（℃）
  String? tempMin;

  DayForCast({this.tempMax, this.tempMin});

  factory DayForCast.fromJson(Map<String, dynamic> json) =>
      _$DayForCastFromJson(json);

  Map<String, dynamic> toJson() => _$DayForCastToJson(this);
}

// 湿度和天气状况(阴/晴)
@JsonSerializable(explicitToJson: true, anyMap: true)
class WeatherCondition {
  String? humidity;
  String? condition;

  WeatherCondition({this.condition, this.humidity});

  factory WeatherCondition.fromJson(Map<String, dynamic> json) =>
      _$WeatherConditionFromJson(json);

  Map<String, dynamic> toJson() => _$WeatherConditionToJson(this);
}

// 空气质量
@JsonSerializable(explicitToJson: true, anyMap: true)
class AirCondition {
  String? pm25;

  AirCondition({this.pm25});
  factory AirCondition.fromJson(Map<String, dynamic> json) =>
      _$AirConditionFromJson(json);

  Map<String, dynamic> toJson() => _$AirConditionToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Area {
  String? areaId;

  Area({this.areaId});

  factory Area.fromJson(Map<String, dynamic> json) => _$AreaFromJson(json);

  Map<String, dynamic> toJson() => _$AreaToJson(this);
}

// 天气素材列表
@JsonSerializable(explicitToJson: true, anyMap: true)
class MaterialData {
  // 头像图片
  String? headIconUrl;

  // 描述
  String? contentDesc;

  // 卡片背景图片
  String? backgroundUrl;

  // 素材类型    0：节日素材 1：天气素材 2:  默认素材
  String? contentType;

  // 关联天气描述
  String? weatheDesc;

  // 智慧小屋头像
  String? avatorUrl;
  MaterialData(
      {this.backgroundUrl,
      this.contentDesc,
      this.contentType,
      this.headIconUrl,
      this.weatheDesc,
      this.avatorUrl});

  factory MaterialData.fromJson(Map<String, dynamic> json) =>
      _$MaterialDataFromJson(json);

  Map<String, dynamic> toJson() => _$MaterialDataToJson(this);
}
