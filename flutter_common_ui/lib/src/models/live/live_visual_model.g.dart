// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'live_visual_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LiveVisualModel _$LiveVisualModelFromJson(Map json) => LiveVisualModel(
      aliasUnit: json['aliasUnit'] as String?,
      attr: json['attr'] == null
          ? null
          : LiveAttrInfo.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$LiveVisualModelToJson(LiveVisualModel instance) =>
    <String, dynamic>{
      'aliasUnit': instance.aliasUnit,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

LiveAttrInfo _$LiveAttrInfoFromJson(Map json) => LiveAttrInfo(
      id: json['id'] as num?,
      layout: json['layout'] as num?,
      content: json['content'] == null
          ? null
          : AttrContent.fromJson(
              Map<String, dynamic>.from(json['content'] as Map)),
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
    );

Map<String, dynamic> _$LiveAttrInfoToJson(LiveAttrInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'layout': instance.layout,
      'content': instance.content?.toJson(),
      'shadow': instance.shadow?.toJson(),
      'trackInfo': instance.trackInfo?.toJson(),
    };

AttrContent _$AttrContentFromJson(Map json) => AttrContent(
      liveBroadcastStatus: json['liveBroadcastStatus'] == null
          ? null
          : LiveBroadcastStatus.fromJson(
              Map<String, dynamic>.from(json['liveBroadcastStatus'] as Map)),
    );

Map<String, dynamic> _$AttrContentToJson(AttrContent instance) =>
    <String, dynamic>{
      'liveBroadcastStatus': instance.liveBroadcastStatus?.toJson(),
    };

LiveBroadcastStatus _$LiveBroadcastStatusFromJson(Map json) =>
    LiveBroadcastStatus(
      checked: json['checked'] as bool?,
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      live: json['live'] == null
          ? null
          : AttrState.fromJson(Map<String, dynamic>.from(json['live'] as Map)),
      appointment: json['appointment'] == null
          ? null
          : AttrState.fromJson(
              Map<String, dynamic>.from(json['appointment'] as Map)),
      playback: json['playback'] == null
          ? null
          : AttrState.fromJson(
              Map<String, dynamic>.from(json['playback'] as Map)),
      emptyScheme: json['emptyScheme'] == null
          ? null
          : EmptySchemeModel.fromJson(
              Map<String, dynamic>.from(json['emptyScheme'] as Map)),
    );

Map<String, dynamic> _$LiveBroadcastStatusToJson(
        LiveBroadcastStatus instance) =>
    <String, dynamic>{
      'checked': instance.checked,
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'live': instance.live?.toJson(),
      'appointment': instance.appointment?.toJson(),
      'playback': instance.playback?.toJson(),
      'emptyScheme': instance.emptyScheme?.toJson(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

AttrState _$AttrStateFromJson(Map json) => AttrState(
      icon: json['icon'] as String?,
      background: json['background'] == null
          ? null
          : BackgroundModel.fromJson(
              Map<String, dynamic>.from(json['background'] as Map)),
    );

Map<String, dynamic> _$AttrStateToJson(AttrState instance) => <String, dynamic>{
      'icon': instance.icon,
      'background': instance.background?.toJson(),
    };
