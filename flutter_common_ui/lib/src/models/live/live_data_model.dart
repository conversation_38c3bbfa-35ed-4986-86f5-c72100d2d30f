/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-23 11:13:09
 * @description: 直播需要的业务数据模型
 */
import 'package:json_annotation/json_annotation.dart';

part 'live_data_model.g.dart';

@JsonSerializable()
class LiveDataModel {
  /// 直播封面图片地址
  final String? coverPicture;

  /// 直播状态 1. 直播中 、2预告 、3回放、4已结束
  final String? status;

  /// 预约有好礼开关状态 0：关闭 1：开启
  final int? drawSwitchStatus;

  /// 直播状态文案--- 直播中、预约有礼、预约、回放
  String? text;
  LiveDataModel(
      {this.coverPicture, this.status, this.drawSwitchStatus, this.text});

  factory LiveDataModel.fromJson(Map<String, dynamic> json) =>
      _$LiveDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$LiveDataModelToJson(this);
}
