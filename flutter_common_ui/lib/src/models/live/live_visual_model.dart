/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:51:48
 * @description: 直播控件的可视化数据模型
 */
/// 直播控件的可视化数据模型
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

import '../common_models/background_model.dart';
import '../common_models/empty_scheme_model.dart';

part 'live_visual_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class LiveVisualModel {
  /// [aliasUnit] 组件唯一标识
  final String? aliasUnit;

  /// 属性
  LiveAttrInfo? attr;

  /// 组件样式
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[CommonZJ]中已经定义，这里定义的原因是[SeniorVesselModel.fromJson] 没有调用父[CommonZJ.fromJson]
  @override
  final ComponentType? type;

  // 绑定的事件
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;
  LiveVisualModel(
      {this.aliasUnit, this.attr, this.style, this.customFeature, this.type});

  factory LiveVisualModel.fromJson(Map<String, dynamic> json) =>
      _$LiveVisualModelFromJson(json);

  Map<String, dynamic> toJson() => _$LiveVisualModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class LiveAttrInfo {
  /// id 组件的唯一标识 时间戳
  final num? id;

  ///  布局 1: 一行一个
  final num? layout;

  /// 显示内容
  AttrContent? content;

  /// 卡片阴影
  ShadowModel? shadow;

  /// 埋点信息
  TrackInfoModel? trackInfo;

  LiveAttrInfo({
    this.id,
    this.layout,
    this.content,
    this.shadow,
    this.trackInfo,
  });

  factory LiveAttrInfo.fromJson(Map<String, dynamic> json) =>
      _$LiveAttrInfoFromJson(json);

  Map<String, dynamic> toJson() => _$LiveAttrInfoToJson(this);
}

/// 直播内容
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrContent {
  LiveBroadcastStatus? liveBroadcastStatus;
  AttrContent({this.liveBroadcastStatus});
  factory AttrContent.fromJson(Map<String, dynamic> json) =>
      _$AttrContentFromJson(json);

  Map<String, dynamic> toJson() => _$AttrContentToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class LiveBroadcastStatus {
  /// 直播状态标签
  final bool? checked;

  /// 状态文案字号 代表直播左上角状态文字的大小
  final num? fontSize;

  /// 状态文案字重 代表直播左上角状态文字的粗细
  final TextWeightType? fontWeight;

  /// 颜色
  ColorModel? color;

  /// 状态 - 直播中 配置项包括：图标、背景颜色
  AttrState? live;

  /// 状态 预约
  AttrState? appointment;

  /// 状态 回放
  AttrState? playback;

  /// 空数据处理逻辑
  EmptySchemeModel? emptyScheme;

  LiveBroadcastStatus(
      {this.checked,
      this.fontSize,
      this.fontWeight,
      this.color,
      this.live,
      this.appointment,
      this.playback,
      this.emptyScheme});
  factory LiveBroadcastStatus.fromJson(Map<String, dynamic> json) =>
      _$LiveBroadcastStatusFromJson(json);

  Map<String, dynamic> toJson() => _$LiveBroadcastStatusToJson(this);
}

/// 直播状态
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrState {
  /// 图标 直播内容标签-回放左侧图标
  final String? icon;

  /// 背景颜色
  BackgroundModel? background;
  AttrState({this.icon, this.background}) : super();
  factory AttrState.fromJson(Map<String, dynamic> json) =>
      _$AttrStateFromJson(json);

  Map<String, dynamic> toJson() => _$AttrStateToJson(this);
}
