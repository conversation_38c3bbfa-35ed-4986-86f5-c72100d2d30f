// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_manage_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextManageModel _$TextManageModelFromJson(Map json) => TextManageModel(
      text: json['text'] as String?,
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      italic: json['italic'] as bool?,
      underline: json['underline'] as bool?,
      linethrough: json['linethrough'] as bool?,
      textAlign:
          $enumDecodeNullable(_$TextHorizontalTypeEnumMap, json['textAlign']),
      verticalAlign:
          $enumDecodeNullable(_$TextVerticalTypeEnumMap, json['verticalAlign']),
    );

Map<String, dynamic> _$TextManageModelToJson(TextManageModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'trackInfo': instance.trackInfo?.toJson(),
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'italic': instance.italic,
      'underline': instance.underline,
      'linethrough': instance.linethrough,
      'textAlign': _$TextHorizontalTypeEnumMap[instance.textAlign],
      'verticalAlign': _$TextVerticalTypeEnumMap[instance.verticalAlign],
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

const _$TextHorizontalTypeEnumMap = {
  TextHorizontalType.leftAlignment: 'left',
  TextHorizontalType.centerAlignment: 'center',
  TextHorizontalType.rightAlignment: 'right',
};

const _$TextVerticalTypeEnumMap = {
  TextVerticalType.topAlignment: 'top',
  TextVerticalType.middleAlignment: 'middle',
  TextVerticalType.bottomAlignment: 'bottom',
};
