import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'text_manage_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class TextManageModel extends Attr {
  /// 文本文案
  String? text;

  /// 埋点信息
  TrackInfoModel? trackInfo;

  /// 字号
  num? fontSize;

  /// 字重
  TextWeightType? fontWeight;

  /// 文字颜色
  ColorModel? color;

  /// 斜体 true: 斜体 false: 取消斜体
  bool? italic;

  /// 下划线 true：下划线 false：取消下划线
  bool? underline;

  /// 删除线 true：删除线 false： 取消删除线
  bool? linethrough;

  /// 文本水平对齐方式 left：水平左对齐 right：水平右对齐
  TextHorizontalType? textAlign;

  /// 垂直对齐方式 top：垂直顶部对齐 middle：垂直居中对齐 bottom：垂直底部对齐
  TextVerticalType? verticalAlign;

  /// 构造函数
  TextManageModel({
    this.text,
    this.trackInfo,
    this.fontSize,
    this.fontWeight,
    this.color,
    this.italic,
    this.underline,
    this.linethrough,
    this.textAlign,
    this.verticalAlign,
  });
  factory TextManageModel.fromJson(Map<String, dynamic> json) =>
      _$TextManageModelFromJson(json);

  Map<String, dynamic> toJson() => _$TextManageModelToJson(this);
}

/// 文本字重类型
enum TextWeightType {
  /// 字重为400
  @JsonValue(400)
  normalWeight,

  /// 字重为500
  @JsonValue(500)
  boldWeight,

  /// 字重为600
  @JsonValue(600)
  bolderWeight,
}

/// 垂直对齐方式
enum TextVerticalType {
  /// 垂直向上对齐
  @JsonValue('top')
  topAlignment,

  /// 垂直居中对齐
  @JsonValue('middle')
  middleAlignment,

  /// 垂直向下对齐
  @JsonValue('bottom')
  bottomAlignment,
}

/// 水平对齐方式
enum TextHorizontalType {
  /// 水平居左对齐
  @JsonValue('left')
  leftAlignment,

  /// 水平居中对齐
  @JsonValue('center')
  centerAlignment,

  /// 水平居右对齐
  @JsonValue('right')
  rightAlignment,
}
