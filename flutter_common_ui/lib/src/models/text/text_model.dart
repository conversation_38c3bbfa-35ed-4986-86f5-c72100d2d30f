import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'text_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class TextModel extends BaseModel {
  /// 组件自己的属性
  @override
  final TextManageModel? attr;

  /// 组件自己的样式
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [customfeature] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  /// 构造函数
  TextModel(
      {String? alias,
      num? aliasCode,
      String? aliasName,
      String? aliasUnit,
      String? group,
      this.type,
      this.attr,
      this.style,
      this.customFeature})
      : super(
            type: type,
            alias: alias,
            aliasCode: aliasCode,
            aliasName: aliasName,
            aliasUnit: aliasUnit,
            group: group,
            style: style,
            customFeature: customFeature,
            attr: attr);
  factory TextModel.fromJson(Map<String, dynamic> json) =>
      _$TextModelFromJson(json);

  Map<String, dynamic> toJson() => _$TextModelToJson(this);
}
