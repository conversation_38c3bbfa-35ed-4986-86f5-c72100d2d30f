
import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'card_navigate_attri_circle_item.g.dart';

/// 圆形卡片内容项
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrCircleCardItem {
  /// [id] 轮播广告ID
  int? id;
  // 轮播类型
  /// [type] 1-图片轮播 2-文字轮播
  AttrCarouselTypeEnum? type;

  /// [pictureUrl] 图片地址
  String? pictureUrl;

  /// [title] 内容标题
  String? title;

  /// 生效时间
  List<int?>? entryIntoForceTime;

  /// [detailsUrl] 详情地址/详情图片/详情视频/直播详情地址
  String? detailsUrl;

  /// [liveBroadcast] 直播时段
  List<int?>? liveBroadcast;

  /// [region] 适用地域 100000--全国  选择省只有省的regioncode
  List<String?>? region;

  /// [shareUrl] (业务层添加的) 分享链接（有值：分享；没值：不能分享）
  final String? shareUrl;

  /// [planId] (业务层添加的) 运营计划id
  final String? planId;

  /// [materialName] (业务层添加的) 物料名称
  final String? materialName;

  /// [dataAttributionList] (业务层添加的) 拓展属性列表，打点相关的值
  final List<AttrDataAttributionModel?>? dataAttributionList;

  /// [trackInfo] 埋点配置
  final TrackInfoModel? trackInfo;

  /// [customFeatureModel] 事件绑定，轮播没有，兼容了卡片导航
  ///
  final CustomFeatureModel? customFeatureModel;

  /// [attrCornerMark] 角标配置
  final AttrCornerMark? attrCornerMark;

  /// [isShowDot] 小红点是否显示 (业务层添加的) 轮播没有，兼容了卡片导航
  final bool? isShowDot;

  final bool? isMoreItem;

  AttrCircleCardItem(
      this.detailsUrl,
      this.entryIntoForceTime,
      this.id,
      this.liveBroadcast,
      this.pictureUrl,
      this.region,
      this.title,
      this.type,
      this.shareUrl,
      this.planId,
      this.materialName,
      this.dataAttributionList,
      this.trackInfo,
      this.customFeatureModel,
      this.attrCornerMark,
      this.isShowDot,
      this.isMoreItem);

  factory AttrCircleCardItem.fromJson(Map<String, dynamic> json) =>
      _$AttrCircleCardItemFromJson(json);
  Map<String, dynamic> toJson() => _$AttrCircleCardItemToJson(this);
}