// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card_navigate_attri_circle_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttrCircleCardItem _$AttrCircleCardItemFromJson(Map json) => AttrCircleCardItem(
      json['detailsUrl'] as String?,
      (json['entryIntoForceTime'] as List<dynamic>?)
          ?.map((e) => e as int?)
          .toList(),
      json['id'] as int?,
      (json['liveBroadcast'] as List<dynamic>?)?.map((e) => e as int?).toList(),
      json['pictureUrl'] as String?,
      (json['region'] as List<dynamic>?)?.map((e) => e as String?).toList(),
      json['title'] as String?,
      $enumDecodeNullable(_$AttrCarouselTypeEnumEnumMap, json['type']),
      json['shareUrl'] as String?,
      json['planId'] as String?,
      json['materialName'] as String?,
      (json['dataAttributionList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : AttrDataAttributionModel.fromJson(
                  Map<String, dynamic>.from(e as Map)))
          .toList(),
      json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      json['customFeatureModel'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customFeatureModel'] as Map)),
      json['attrCornerMark'] == null
          ? null
          : AttrCornerMark.fromJson(
              Map<String, dynamic>.from(json['attrCornerMark'] as Map)),
      json['isShowDot'] as bool?,
      json['isMoreItem'] as bool?,
    );

Map<String, dynamic> _$AttrCircleCardItemToJson(AttrCircleCardItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AttrCarouselTypeEnumEnumMap[instance.type],
      'pictureUrl': instance.pictureUrl,
      'title': instance.title,
      'entryIntoForceTime': instance.entryIntoForceTime,
      'detailsUrl': instance.detailsUrl,
      'liveBroadcast': instance.liveBroadcast,
      'region': instance.region,
      'shareUrl': instance.shareUrl,
      'planId': instance.planId,
      'materialName': instance.materialName,
      'dataAttributionList':
          instance.dataAttributionList?.map((e) => e?.toJson()).toList(),
      'trackInfo': instance.trackInfo?.toJson(),
      'customFeatureModel': instance.customFeatureModel?.toJson(),
      'attrCornerMark': instance.attrCornerMark?.toJson(),
      'isShowDot': instance.isShowDot,
      'isMoreItem': instance.isMoreItem,
    };

const _$AttrCarouselTypeEnumEnumMap = {
  AttrCarouselTypeEnum.picture: 1,
  AttrCarouselTypeEnum.text: 2,
};
