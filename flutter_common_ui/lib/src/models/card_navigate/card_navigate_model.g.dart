// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card_navigate_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CardNavigateModel _$CardNavigateModelFromJson(Map json) => CardNavigateModel(
      attr: json['attr'] == null
          ? null
          : CardNavigateAttr.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$CardNavigateModelToJson(CardNavigateModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

CardNavigateAttr _$CardNavigateAttrFromJson(Map json) => CardNavigateAttr(
      styleType: json['styleType'] as num?,
      extraInfo: json['extraInfo'] == null
          ? null
          : ExtraInfoModel.fromJson(
              Map<String, dynamic>.from(json['extraInfo'] as Map)),
      singleSize: json['singleSize'] == null
          ? null
          : SingleSize.fromJson(
              Map<String, dynamic>.from(json['singleSize'] as Map)),
      sliderType: json['sliderType'] as num?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) =>
              MenuItemModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
      gutter: (json['gutter'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$CardNavigateAttrToJson(CardNavigateAttr instance) =>
    <String, dynamic>{
      'styleType': instance.styleType,
      'extraInfo': instance.extraInfo?.toJson(),
      'singleSize': instance.singleSize?.toJson(),
      'sliderType': instance.sliderType,
      'items': instance.items?.map((e) => e.toJson()).toList(),
      'radius': instance.radius?.toJson(),
      'shadow': instance.shadow?.toJson(),
      'gutter': instance.gutter,
    };

SingleSize _$SingleSizeFromJson(Map json) => SingleSize(
      row: json['row'] as num?,
      col: json['col'] as num?,
    );

Map<String, dynamic> _$SingleSizeToJson(SingleSize instance) =>
    <String, dynamic>{
      'row': instance.row,
      'col': instance.col,
    };
