/*
 * @Author: ma<PERSON><PERSON><PERSON> maxia<PERSON>@haier.com
 * @Date: 2023-03-06 14:33:05
 * @description: 卡片导航 可视化数据模型
 */
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/extra_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/menu/menu_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'card_navigate_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class CardNavigateModel extends BaseModel {
  /// [attr] 卡片导航的配置数据，包含布局、内容等
  @override
  final CardNavigateAttr? attr;

  /// [style] 组件样式 在父[BaseModel]中已经定义，这里定义的原因是[CardNavigateModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[CardNavigateModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  CardNavigateModel({
    this.attr,
    this.type,
    this.style,
    this.customFeature,
    String? alias,
    num? aliasCode,
    String? aliasName,
    String? aliasUnit,
    String? group,
  }) : super(
          style: style,
          customFeature: customFeature,
          type: type,
          alias: alias,
          aliasCode: aliasCode,
          aliasName: aliasName,
          aliasUnit: aliasUnit,
          group: group,
        );
  factory CardNavigateModel.fromJson(Map<String, dynamic> json) =>
      _$CardNavigateModelFromJson(json);
  Map<String, dynamic> toJson() => _$CardNavigateModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class CardNavigateAttr extends Attr {
  /// [styleType]1：方形，2： 圆形
  final num? styleType;

  /// [extraInfo]额外的信息（更多item信息）
  final ExtraInfoModel? extraInfo;

  /// [singleSize]单页导航规格 1行3.5个
  final SingleSize? singleSize;

  /// [sliderType] 滑动设置 1: 横向拖拽滑动 目前只有这一种方式
  final num? sliderType;

  /// [items] 导航项
  final List<MenuItemModel>? items;

  /// [radius] 导航项图片圆角
  final RadiusModel? radius;

  /// [shadow]导航项图片阴影
  final ShadowModel? shadow;

  /// [gutter] 导航项间距
  final double? gutter;
  CardNavigateAttr(
      {this.styleType,
      this.extraInfo,
      this.singleSize,
      this.sliderType,
      this.items,
      this.radius,
      this.shadow,
      this.gutter});
  factory CardNavigateAttr.fromJson(Map<String, dynamic> json) =>
      _$CardNavigateAttrFromJson(json);
  Map<String, dynamic> toJson() => _$CardNavigateAttrToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class SingleSize {
  /// [row]最大行数，选项包括：1行；默认选择1行；
  final num? row;

  /// 单页每行最大展示个数 目前只有3.5
  final num? col;
  SingleSize({this.row, this.col});
  factory SingleSize.fromJson(Map<String, dynamic> json) =>
      _$SingleSizeFromJson(json);
  Map<String, dynamic> toJson() => _$SingleSizeToJson(this);
}
