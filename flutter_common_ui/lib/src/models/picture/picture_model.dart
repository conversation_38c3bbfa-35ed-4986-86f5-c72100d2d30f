import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'picture_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class PictureModel extends BaseModel {
  /// [attr] 图片的配置数据，包含布局、内容等
  @override
  final PictureAttr? attr;

  /// [style] 组件样式 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [customFeature] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  PictureModel({
    this.type,
    this.attr,
    this.style,
    this.customFeature,
    String? alias,
    num? aliasCode,
    String? aliasName,
    String? aliasUnit,
    String? group,
  }) : super(
          attr: attr,
          style: style,
          customFeature: customFeature,
          type: type,
          alias: alias,
          aliasCode: aliasCode,
          aliasName: aliasName,
          aliasUnit: aliasUnit,
          group: group,
        );
  factory PictureModel.fromJson(Map<String, dynamic> json) =>
      _$PictureModelFromJson(json);
  Map<String, dynamic> toJson() => _$PictureModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class PictureAttr extends Attr {
  /// [mode] 1-一行一个 2-一行两个 3-一行三个 4-一行四个 5-左一右二 6-上一下二
  final PictureModeType? mode;

  /// 图片列表 [sourceMap]
  final List<AttrBannerImg>? sourceMap;

  /// [bannerStyle]
  final AttrBannerStyle? bannerStyle;

  /// 显示内容 [extraContent]
  final AttrExtraContent? extraContent;

  PictureAttr({
    this.mode,
    this.sourceMap,
    this.bannerStyle,
    this.extraContent,
  });

  factory PictureAttr.fromJson(Map<String, dynamic> json) =>
      _$PictureAttrFromJson(json);
  Map<String, dynamic> toJson() => _$PictureAttrToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrBannerImg {
  /// 唯一标志 [id]
  final int? id;

  /// [img] 图片地址, 会被赋值当接口返回了数据
  String? img;

  /// [materialName] 物料名称
  String? materialName;

  /// [planId] 命中计划
  String? planId;

  /// [customFeature] 每个小图片的事件，会被赋值当接口返回了数据
  @JsonKey(name: 'customfeature')
  CustomFeatureModel? customFeature;

  /// [trackInfo] 埋点信息
  final TrackInfoModel? trackInfo;

  AttrBannerImg({
    this.id,
    this.customFeature,
    this.img,
    this.materialName,
    this.planId,
    this.trackInfo,
  });

  factory AttrBannerImg.copyWith(AttrBannerImg source) => AttrBannerImg(
      id: source.id,
      customFeature: source.customFeature,
      img: source.img,
      trackInfo: source.trackInfo);

  factory AttrBannerImg.fromJson(Map<String, dynamic> json) =>
      _$AttrBannerImgFromJson(json);
  Map<String, dynamic> toJson() => _$AttrBannerImgToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrBannerStyle {
  /// [gutter] 第一次布局分隔的间距，比如左一右二，[gutter]指的左右间距
  final double? gutter;

  /// [gutterIn] 第二次布局分隔的间距，比如左一右二，[gutter]指的右方上边两个内容间距
  final double? gutterIn;

  /// [radius] 图片圆角
  final RadiusModel? radius;

  AttrBannerStyle({this.gutter, this.gutterIn, this.radius});

  factory AttrBannerStyle.fromJson(Map<String, dynamic> json) =>
      _$AttrBannerStyleFromJson(json);
  Map<String, dynamic> toJson() => _$AttrBannerStyleToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrExtraContent {
  /// 角标 [cornerMark]
  final AttrCornerMark? cornerMark;
  AttrExtraContent({this.cornerMark});

  factory AttrExtraContent.fromJson(Map<String, dynamic> json) =>
      _$AttrExtraContentFromJson(json);
  Map<String, dynamic> toJson() => _$AttrExtraContentToJson(this);
}

/// 图片布局类型
enum PictureModeType {
  /// [single] 一个图
  /// ![]()
  @JsonValue(1)
  single,

  /// 一行两个图
  /// ![]()
  @JsonValue(2)
  twoInRow,

  /// 一行三个图
  /// ![]()
  @JsonValue(3)
  threeInRow,

  /// 一行四个图
  /// ![]()

  @JsonValue(4)
  fourInRow,

  /// 左一右二
  /// ![]()
  @JsonValue(5)
  leftOneRightTwo,

  /// 上一下二
  /// ![]()
  @JsonValue(6)
  upOneDownTwo
}
