// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'picture_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PictureModel _$PictureModelFromJson(Map json) => PictureModel(
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      attr: json['attr'] == null
          ? null
          : PictureAttr.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$PictureModelToJson(PictureModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

PictureAttr _$PictureAttrFromJson(Map json) => PictureAttr(
      mode: $enumDecodeNullable(_$PictureModeTypeEnumMap, json['mode']),
      sourceMap: (json['sourceMap'] as List<dynamic>?)
          ?.map((e) =>
              AttrBannerImg.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      bannerStyle: json['bannerStyle'] == null
          ? null
          : AttrBannerStyle.fromJson(
              Map<String, dynamic>.from(json['bannerStyle'] as Map)),
      extraContent: json['extraContent'] == null
          ? null
          : AttrExtraContent.fromJson(
              Map<String, dynamic>.from(json['extraContent'] as Map)),
    );

Map<String, dynamic> _$PictureAttrToJson(PictureAttr instance) =>
    <String, dynamic>{
      'mode': _$PictureModeTypeEnumMap[instance.mode],
      'sourceMap': instance.sourceMap?.map((e) => e.toJson()).toList(),
      'bannerStyle': instance.bannerStyle?.toJson(),
      'extraContent': instance.extraContent?.toJson(),
    };

const _$PictureModeTypeEnumMap = {
  PictureModeType.single: 1,
  PictureModeType.twoInRow: 2,
  PictureModeType.threeInRow: 3,
  PictureModeType.fourInRow: 4,
  PictureModeType.leftOneRightTwo: 5,
  PictureModeType.upOneDownTwo: 6,
};

AttrBannerImg _$AttrBannerImgFromJson(Map json) => AttrBannerImg(
      id: json['id'] as int?,
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      img: json['img'] as String?,
      materialName: json['materialName'] as String?,
      planId: json['planId'] as String?,
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
    );

Map<String, dynamic> _$AttrBannerImgToJson(AttrBannerImg instance) =>
    <String, dynamic>{
      'id': instance.id,
      'img': instance.img,
      'materialName': instance.materialName,
      'planId': instance.planId,
      'customfeature': instance.customFeature?.toJson(),
      'trackInfo': instance.trackInfo?.toJson(),
    };

AttrBannerStyle _$AttrBannerStyleFromJson(Map json) => AttrBannerStyle(
      gutter: (json['gutter'] as num?)?.toDouble(),
      gutterIn: (json['gutterIn'] as num?)?.toDouble(),
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
    );

Map<String, dynamic> _$AttrBannerStyleToJson(AttrBannerStyle instance) =>
    <String, dynamic>{
      'gutter': instance.gutter,
      'gutterIn': instance.gutterIn,
      'radius': instance.radius?.toJson(),
    };

AttrExtraContent _$AttrExtraContentFromJson(Map json) => AttrExtraContent(
      cornerMark: json['cornerMark'] == null
          ? null
          : AttrCornerMark.fromJson(
              Map<String, dynamic>.from(json['cornerMark'] as Map)),
    );

Map<String, dynamic> _$AttrExtraContentToJson(AttrExtraContent instance) =>
    <String, dynamic>{
      'cornerMark': instance.cornerMark?.toJson(),
    };
