// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageModel _$MessageModelFromJson(Map json) => MessageModel(
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      attr: json['attr'] == null
          ? null
          : MessageAttr.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
    );

Map<String, dynamic> _$MessageModelToJson(MessageModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

MessageAttr _$MessageAttrFromJson(Map json) => MessageAttr(
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      format: json['format'] as num?,
      icon: json['icon'] as String?,
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
      range: json['range'] as int?,
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
      style: json['style'] as num?,
      timeColor: json['timeColor'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['timeColor'] as Map)),
      timeFontSize: json['timeFontSize'] as num?,
      timeFontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['timeFontWeight']),
    )..templateScope = (json['templateScope'] as List<dynamic>?)
        ?.map((e) => MessageTemplateScopeModel.fromJson(
            Map<String, dynamic>.from(e as Map)))
        .toList();

Map<String, dynamic> _$MessageAttrToJson(MessageAttr instance) =>
    <String, dynamic>{
      'style': instance.style,
      'icon': instance.icon,
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'timeFontSize': instance.timeFontSize,
      'timeFontWeight': _$TextWeightTypeEnumMap[instance.timeFontWeight],
      'timeColor': instance.timeColor?.toJson(),
      'range': instance.range,
      'format': instance.format,
      'templateScope': instance.templateScope?.map((e) => e.toJson()).toList(),
      'radius': instance.radius?.toJson(),
      'shadow': instance.shadow?.toJson(),
      'trackInfo': instance.trackInfo?.toJson(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

MessageTemplateScopeModel _$MessageTemplateScopeModelFromJson(Map json) =>
    MessageTemplateScopeModel(
      msgName: json['msgName'] as String?,
      name: json['name'] as String?,
      msgType: json['msgType'] as String?,
      pushRuleId: json['pushRuleId'] as String?,
    );

Map<String, dynamic> _$MessageTemplateScopeModelToJson(
        MessageTemplateScopeModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'msgName': instance.msgName,
      'pushRuleId': instance.pushRuleId,
      'msgType': instance.msgType,
    };

MenuTrackAttrModel _$MenuTrackAttrModelFromJson(Map json) => MenuTrackAttrModel(
      content_id: json['content_id'] as bool?,
      content_title: json['content_title'] as bool?,
    );

Map<String, dynamic> _$MenuTrackAttrModelToJson(MenuTrackAttrModel instance) =>
    <String, dynamic>{
      'content_id': instance.content_id,
      'content_title': instance.content_title,
    };

MsgHistoriesModel _$MsgHistoriesModelFromJson(Map json) => MsgHistoriesModel(
      msgHistories: (json['msgHistories'] as List<dynamic>?)
          ?.map((e) => MsgHistoriesListModel.fromJson(
              Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$MsgHistoriesModelToJson(MsgHistoriesModel instance) =>
    <String, dynamic>{
      'msgHistories': instance.msgHistories?.map((e) => e.toJson()).toList(),
    };

MsgHistoriesListModel _$MsgHistoriesListModelFromJson(Map json) =>
    MsgHistoriesListModel(
      businessType: json['businessType'] as int?,
      message: json['message'] == null
          ? null
          : Message2.fromJson(
              Map<String, dynamic>.from(json['message'] as Map)),
      msgStatus: json['msgStatus'] as int?,
      pushTime: json['pushTime'] as String?,
      readStatus: json['readStatus'] as int?,
      receivedTime: json['receivedTime'] as String?,
      taskId: json['taskId'] as String?,
    );

Map<String, dynamic> _$MsgHistoriesListModelToJson(
        MsgHistoriesListModel instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'businessType': instance.businessType,
      'message': instance.message?.toJson(),
      'msgStatus': instance.msgStatus,
      'readStatus': instance.readStatus,
      'pushTime': instance.pushTime,
      'receivedTime': instance.receivedTime,
    };

Message2 _$Message2FromJson(Map json) => Message2(
      data: json['data'] == null
          ? null
          : Data.fromJson(Map<String, dynamic>.from(json['data'] as Map)),
      notification: json['notification'] == null
          ? null
          : Notification.fromJson(
              Map<String, dynamic>.from(json['notification'] as Map)),
    );

Map<String, dynamic> _$Message2ToJson(Message2 instance) => <String, dynamic>{
      'notification': instance.notification?.toJson(),
      'data': instance.data?.toJson(),
    };

Notification _$NotificationFromJson(Map json) => Notification(
      body: json['body'] as String?,
      title: json['title'] as String?,
    );

Map<String, dynamic> _$NotificationToJson(Notification instance) =>
    <String, dynamic>{
      'title': instance.title,
      'body': instance.body,
    };

Data _$DataFromJson(Map json) => Data(
      body: json['body'] == null
          ? null
          : Body.fromJson(Map<String, dynamic>.from(json['body'] as Map)),
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'body': instance.body?.toJson(),
    };

Body _$BodyFromJson(Map json) => Body(
      extData: json['extData'] == null
          ? null
          : ExtData.fromJson(Map<String, dynamic>.from(json['extData'] as Map)),
    );

Map<String, dynamic> _$BodyToJson(Body instance) => <String, dynamic>{
      'extData': instance.extData?.toJson(),
    };

ExtData _$ExtDataFromJson(Map json) => ExtData(
      pages: (json['pages'] as List<dynamic>?)
          ?.map((e) => Pages.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      reviewPage: json['reviewPage'] as String?,
    );

Map<String, dynamic> _$ExtDataToJson(ExtData instance) => <String, dynamic>{
      'pages': instance.pages?.map((e) => e.toJson()).toList(),
      'reviewPage': instance.reviewPage,
    };

Pages _$PagesFromJson(Map json) => Pages(
      callId: json['callId'] as int?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$PagesToJson(Pages instance) => <String, dynamic>{
      'callId': instance.callId,
      'url': instance.url,
    };
