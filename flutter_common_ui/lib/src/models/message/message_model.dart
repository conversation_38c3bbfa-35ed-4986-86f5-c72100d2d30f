import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class MessageModel extends BaseModel {
  /// 组件自己的属性
  @override
  final MessageAttr? attr;

  /// 组件自己的样式
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [customfeature] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;
  MessageModel(
      {String? alias,
      num? aliasCode,
      String? aliasName,
      String? aliasUnit,
      String? group,
      this.style,
      this.type,
      this.customFeature,
      this.attr})
      : super(
            type: type,
            alias: alias,
            aliasCode: aliasCode,
            aliasName: aliasName,
            aliasUnit: aliasUnit,
            group: group,
            style: style,
            customFeature: customFeature,
            attr: attr);
  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  Map<String, dynamic> toJson() => _$MessageModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MessageAttr extends Attr {
  /// 样式 1: 跑马灯
  num? style;

  /// 默认图标
  String? icon;

  /// 消息标题字号
  num? fontSize;

  /// 消息标题字重
  TextWeightType? fontWeight;

  /// 消息标题颜色
  ColorModel? color;

  /// 消息时间字号
  num? timeFontSize;

  /// 消息时间字重
  TextWeightType? timeFontWeight;

  /// 消息时间颜色
  ColorModel? timeColor;

  /// 时间范围 1: 7天内
  int? range;

  /// 时间格式 1: 样式1 2: 样式2 3: 样式3
  num? format;

  /// 消息模板范围 -- 具体数据是从接口读取
  /// * 猜想至少需要id，name,msgName用来后端回显
  List<MessageTemplateScopeModel>? templateScope;

  /// 消息通知卡片圆角
  RadiusModel? radius;

  /// 消息通知卡片阴影
  ShadowModel? shadow;

  /// 埋点信息
  TrackInfoModel? trackInfo;
  MessageAttr(
      {this.trackInfo,
      this.color,
      this.fontSize,
      this.fontWeight,
      this.format,
      this.icon,
      this.radius,
      this.range,
      this.shadow,
      this.style,
      this.timeColor,
      this.timeFontSize,
      this.timeFontWeight});
  factory MessageAttr.fromJson(Map<String, dynamic> json) =>
      _$MessageAttrFromJson(json);
  Map<String, dynamic> toJson() => _$MessageAttrToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MessageTemplateScopeModel {
  String? name;
  String? msgName;
  String? pushRuleId;
  String? msgType;
  MessageTemplateScopeModel(
      {this.msgName, this.name, this.msgType, this.pushRuleId});
  factory MessageTemplateScopeModel.fromJson(Map<String, dynamic> json) =>
      _$MessageTemplateScopeModelFromJson(json);
  Map<String, dynamic> toJson() => _$MessageTemplateScopeModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuTrackAttrModel {
  final bool? content_id;
  final bool? content_title;
  MenuTrackAttrModel({
    this.content_id,
    this.content_title,
  });
  factory MenuTrackAttrModel.fromJson(Map<String, dynamic> json) =>
      _$MenuTrackAttrModelFromJson(json);
  Map<String, dynamic> toJson() => _$MenuTrackAttrModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MsgHistoriesModel {
  List<MsgHistoriesListModel>? msgHistories;
  MsgHistoriesModel({this.msgHistories});
  factory MsgHistoriesModel.fromJson(Map<String, dynamic> json) =>
      _$MsgHistoriesModelFromJson(json);

  Map<String, dynamic> toJson() => _$MsgHistoriesModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MsgHistoriesListModel {
  String? taskId;
  int? businessType;
  Message2? message;
  int? msgStatus;
  int? readStatus;
  String? pushTime;
  String? receivedTime;
  MsgHistoriesListModel(
      {this.businessType,
      this.message,
      this.msgStatus,
      this.pushTime,
      this.readStatus,
      this.receivedTime,
      this.taskId});
  factory MsgHistoriesListModel.fromJson(Map<String, dynamic> json) =>
      _$MsgHistoriesListModelFromJson(json);

  Map<String, dynamic> toJson() => _$MsgHistoriesListModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Message2 {
  Notification? notification;
  Data? data;
  Message2({this.data, this.notification});
  factory Message2.fromJson(Map<String, dynamic> json) =>
      _$Message2FromJson(json);

  Map<String, dynamic> toJson() => _$Message2ToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Notification {
  String? title;
  String? body;
  Notification({this.body, this.title});
  factory Notification.fromJson(Map<String, dynamic> json) =>
      _$NotificationFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Data {
  Body? body;
  Data({this.body});
  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Body {
  ExtData? extData;
  Body({this.extData});

  factory Body.fromJson(Map<String, dynamic> json) => _$BodyFromJson(json);

  Map<String, dynamic> toJson() => _$BodyToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ExtData {
  List<Pages>? pages;
  String? reviewPage;
  ExtData({this.pages, this.reviewPage});
  factory ExtData.fromJson(Map<String, dynamic> json) =>
      _$ExtDataFromJson(json);

  Map<String, dynamic> toJson() => _$ExtDataToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Pages {
  int? callId;
  String? url;
  Pages({this.callId, this.url});
  factory Pages.fromJson(Map<String, dynamic> json) => _$PagesFromJson(json);

  Map<String, dynamic> toJson() => _$PagesToJson(this);
}
