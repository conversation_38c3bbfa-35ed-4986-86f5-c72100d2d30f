/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description:  阴影属性
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';

/// 阴影属性
import 'package:json_annotation/json_annotation.dart';

import 'color_model.dart';

part 'shadow_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class ShadowModel {
  ///  颜色
  ColorModel? color;
  final double? X;
  final double? Y;

  /// 模糊
  final double? blur;

  /// 扩散
  final double? spread;

  ShadowModel({this.X, this.Y, this.blur, this.spread, this.color});

  /// 阴影数组
  List<BoxShadow>? get boxShadow {
    return CommonUtil.toBoxShadow(this);
  }

  factory ShadowModel.fromJson(Map<String, dynamic> json) =>
      _$ShadowModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShadowModelToJson(this);
}
