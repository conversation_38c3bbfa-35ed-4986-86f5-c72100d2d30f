// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'padding_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaddingModel _$PaddingModelFromJson(Map json) => PaddingModel(
      top: (json['top'] as num?)?.toDouble() ?? 0,
      bottom: (json['bottom'] as num?)?.toDouble() ?? 0,
      left: (json['left'] as num?)?.toDouble() ?? 0,
      right: (json['right'] as num?)?.toDouble() ?? 0,
    );

Map<String, dynamic> _$PaddingModelToJson(PaddingModel instance) =>
    <String, dynamic>{
      'top': instance.top,
      'bottom': instance.bottom,
      'left': instance.left,
      'right': instance.right,
    };
