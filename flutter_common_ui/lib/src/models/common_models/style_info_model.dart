/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:51:48
 * @description: 组件样式 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_filter.dart';
/// 组件样式
import 'package:flutter_common_ui/src/models/common_models/background_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_system_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/decoration_widget/decoration_border_widget.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/decoration_widget/decoration_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:json_annotation/json_annotation.dart';

import 'border_model.dart';
import 'padding_model.dart';
import 'radius_model.dart';
import 'shadow_model.dart';

part 'style_info_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class StyleInfoModel {
  AttrInfo? attr;
  StyleInfoModel({this.attr});
  factory StyleInfoModel.fromJson(Map<String, dynamic> json) =>
      _$StyleInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$StyleInfoModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrInfo {
  ///  组件背景
  BackgroundModel? background;

  /// [width] 组件宽度
  final double? width;

  /// [height] 组件高度
  final double? height;

  /// [padding] 内边距
  PaddingModel? padding;

  /// [left] 组件在动态容器、分类目录的拖拽下生效的位置
  final double? left;

  /// [top] 组件在动态容器、分类目录的拖拽下生效的位置
  final double? top;

  /// [opacity] 组件的透明度
  final double? opacity;

  /// [border] 边框
  BorderModel? border;

  /// [radius] 圆角 左上圆角/右上圆角/右下圆角/左下圆角
  RadiusModel? radius;

  /// [shadow] 阴影
  ShadowModel? shadow;

  /// [filter] 滤镜
  AttrFilter? filter;

  AttrInfo({
    this.background,
    this.width,
    this.height,
    this.padding,
    this.left,
    this.top,
    this.opacity,
    this.border,
    this.radius,
    this.shadow,
  });

  /// 转 外壳组件 - DecorationWidget
  /// [type] 组件的类型，对应可视化配置的type字段
  Widget toDecorationWidget(
      {String? aliasName,
      ComponentType? type,
      required Widget child,
      bool isBanner = false,

      /// [relative] 是否是相对位置，是的话使用Positioned包裹
      bool relative = false,

      /// [colorSystem] 背景色系
      ColorSystemEnum? colorSystem,
      bool withBackgroudColor = true}) {
    // 内边距
    // 图片的内边距特殊处理，因为图片有角标，需要把内边距放在图片组件上，给角标留出空间
    final padding = (isBanner ||
            type == ComponentType.picture ||
            type == ComponentType.weather ||
            type == ComponentType.cardNav)
        ? EdgeInsets.zero
        : this.padding?.padding ?? EdgeInsets.zero;
    // 圆角
    final borderRadius = radius?.borderRadius ?? BorderRadius.circular(0);

    final decorationWidget = DecorationWidget(
      top: this.top?.w,
      left: this.left?.w,
      relative: relative,
      bgUrl: this.background?.bgUrl,
      bgColor: withBackgroudColor ? this.background?.bgColor : Colors.transparent,
      border: this.border?.isSDKBorder == true ? this.border?.border : null,
      isSDKBorder: this.border?.isSDKBorder == true,
      bgGradient: CommonUtil.toGradient(background?.color,
          width: width?.w, height: height?.w),
      width: width?.w,
      height: height?.w,
      opacity: opacity ?? 1.0,
      blur: filter?.blur ?? 0,
      borderRadius: borderRadius,
      boxShadow: CommonUtil.toBoxShadow(shadow, name: aliasName),
      padding: padding,
      colorSystem: colorSystem,
      child: child,
    );

    if (this.border?.isSDKBorder == true) {
      return decorationWidget;
    }

    return DecorationBorderWidget(
      top: this.top?.w,
      left: this.left?.w,
      relative: relative,
      bgUrl: this.background?.bgUrl,
      bgColor: withBackgroudColor ? this.background?.bgColor : Colors.transparent,
      border: border,
      isSDKBorder: this.border?.isSDKBorder == true,
      bgGradient: CommonUtil.toGradient(background?.color,
          width: width?.w, height: height?.w),
      width: width?.w,
      height: height?.w,
      opacity: opacity ?? 1.0,
      blur: filter?.blur ?? 0,
      borderRadius: borderRadius,
      boxShadow: CommonUtil.toBoxShadow(shadow, name: aliasName),
      padding: padding,
      colorSystem: colorSystem,
      child: child,
    );
  }

  factory AttrInfo.fromJson(Map<String, dynamic> json) =>
      _$AttrInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AttrInfoToJson(this);
}
