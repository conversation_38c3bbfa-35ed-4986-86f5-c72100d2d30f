/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: 埋点信息
 */

import 'package:json_annotation/json_annotation.dart';

part 'track_info_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class TrackInfoModel {
  /// 点击事件埋点
  AttrTrackDetail? click;

  /// 曝光埋点
  AttrTrackDetail? exposure;

  TrackInfoModel({this.click, this.exposure});

  factory TrackInfoModel.fromJson(Map<String, dynamic> json) =>
      _$TrackInfoModelFromJson(json);

  Map<dynamic, dynamic> toJson() => _$TrackInfoModelToJson(this);
}

/// 埋点信息
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrTrackDetail {
  String? code;
  Map<String, dynamic>? attr;

  AttrTrackDetail({this.attr, this.code});

  factory AttrTrackDetail.fromJson(Map<String, dynamic> json) =>
      _$AttrTrackDetailFromJson(json);

  Map<String, dynamic> toJson() => _$AttrTrackDetailToJson(this);
}
