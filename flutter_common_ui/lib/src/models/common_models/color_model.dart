/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: 颜色属性
 */

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/extension.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
///  颜色属性
import 'package:json_annotation/json_annotation.dart';

part 'color_model.g.dart';

///[anyMap] 添加的原因： _InternalLinkedHashMap<dynamic, dynamic>' is not a subtype of type 'Map<String, dynamic>
/// https://github.com/google/json_serializable.dart/issues/774
@JsonSerializable(explicitToJson: true, anyMap: true)
class ColorModel {
  /// 色值
  final String? value;

  /// 是否渐变  true-渐变  false-不渐变
  final bool? isGradient;

  /// 渐变的参数  若isGradient 为true ,渐变的具体属性
  AttrGradient? gradient;

  ColorModel({
    this.value,
    this.isGradient,
    this.gradient,
  }) : super();

  /// 颜色
  Color? get color => CommonUtil.toColor(this.value);

  /// 渐进色
  Gradient? get colorOfGradient {
    if (this.isGradient == null || this.isGradient == false) {
      return this.color?.toGradient();
    }
    return CommonUtil.toGradient(this);
  }

  factory ColorModel.fromJson(Map<String, dynamic> json) =>
      _$ColorModelFromJson(json);

  Map<dynamic, dynamic> toJson() => _$ColorModelToJson(this);
}

/// 渐变属性
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrGradient {
  /// 渐变类型 liner-线性渐变  radial -径向渐变
  final String? type;

  /// 渐变色阶和占比
  final List? stop;

  /// 渐变角度
  final double? angle;

  /// 渐变方向
  final String? direction;

  AttrGradient({
    this.type,
    this.stop,
    this.angle,
    this.direction,
  }) : super();
  factory AttrGradient.fromJson(Map<String, dynamic> json) =>
      _$AttrGradientFromJson(json);

  Map<String, dynamic> toJson() => _$AttrGradientToJson(this);
}
