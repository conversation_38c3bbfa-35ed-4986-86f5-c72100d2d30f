// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'style_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StyleInfoModel _$StyleInfoModelFromJson(Map json) => StyleInfoModel(
      attr: json['attr'] == null
          ? null
          : AttrInfo.fromJson(Map<String, dynamic>.from(json['attr'] as Map)),
    );

Map<String, dynamic> _$StyleInfoModelToJson(StyleInfoModel instance) =>
    <String, dynamic>{
      'attr': instance.attr?.toJson(),
    };

AttrInfo _$AttrInfoFromJson(Map json) => AttrInfo(
      background: json['background'] == null
          ? null
          : BackgroundModel.fromJson(
              Map<String, dynamic>.from(json['background'] as Map)),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      padding: json['padding'] == null
          ? null
          : PaddingModel.fromJson(
              Map<String, dynamic>.from(json['padding'] as Map)),
      left: (json['left'] as num?)?.toDouble(),
      top: (json['top'] as num?)?.toDouble(),
      opacity: (json['opacity'] as num?)?.toDouble(),
      border: json['border'] == null
          ? null
          : BorderModel.fromJson(
              Map<String, dynamic>.from(json['border'] as Map)),
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
    )..filter = json['filter'] == null
        ? null
        : AttrFilter.fromJson(Map<String, dynamic>.from(json['filter'] as Map));

Map<String, dynamic> _$AttrInfoToJson(AttrInfo instance) => <String, dynamic>{
      'background': instance.background?.toJson(),
      'width': instance.width,
      'height': instance.height,
      'padding': instance.padding?.toJson(),
      'left': instance.left,
      'top': instance.top,
      'opacity': instance.opacity,
      'border': instance.border?.toJson(),
      'radius': instance.radius?.toJson(),
      'shadow': instance.shadow?.toJson(),
      'filter': instance.filter?.toJson(),
    };
