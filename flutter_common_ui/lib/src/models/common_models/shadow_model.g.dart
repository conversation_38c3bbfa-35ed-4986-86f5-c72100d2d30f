// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shadow_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShadowModel _$ShadowModelFromJson(Map json) => ShadowModel(
      X: (json['X'] as num?)?.toDouble(),
      Y: (json['Y'] as num?)?.toDouble(),
      blur: (json['blur'] as num?)?.toDouble(),
      spread: (json['spread'] as num?)?.toDouble(),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
    );

Map<String, dynamic> _$ShadowModelToJson(ShadowModel instance) =>
    <String, dynamic>{
      'color': instance.color?.toJson(),
      'X': instance.X,
      'Y': instance.Y,
      'blur': instance.blur,
      'spread': instance.spread,
    };
