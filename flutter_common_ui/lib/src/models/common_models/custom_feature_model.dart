/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: 绑定事件
 */
/// 绑定事件
import 'package:json_annotation/json_annotation.dart';

part 'custom_feature_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class CustomFeatureModel {
  final String? eventHandler;
  AttrCustomFeatureDetail? eventParams;
  final int? currentEventIndex;

  CustomFeatureModel(
      {this.eventHandler, this.eventParams, this.currentEventIndex})
      : super();

  factory CustomFeatureModel.fromJson(Map<String, dynamic> json) =>
      _$CustomFeatureModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomFeatureModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrCustomFeatureDetail {
  /// 事件类型
  EventTypeEnum? eventType;

  /// 跳转链接
  String? nav_url;

  /// 是否直接 是 0 否 1
  IsJumpEnum? is_jump;

  /// 跳转逻辑  特定环境跳转
  JumpLogicEnum? jump_logic;

  /// 跳转环境  智家app=0  三翼鸟微信小程序=1  其他微信小程序=2  三翼鸟APP=3 智家微信小程序=4
  JumpEnvironmentEnum? jump_environment;

  /// 小程序appId
  String? miniProId;

  /// 小程序原始Id
  String? originId;

  /// 功能里面类型 说明3是拨打电话  5是分享
  FunctionTypeEnum? functionType;

  /// **---- functionType拨打电话 -- 电话------**
  String? phoneNumber;

  /// 页面类型 0H5  1微信小程序
  PageTypeEnum? pageType;
  String? shareTitle;
  String? shareSubTitle;
  String? shareImg;

  /// 微信分享图、小程序分享图  --图片名 -- app不用
  String? shareImgName;
  String? shareUrl;

  /// 启动环境. 0三翼鸟微信小程序
  String? beginEnvir;
  String? timestamp;
  AttrCustomFeatureDetail({
    this.beginEnvir,
    this.eventType,
    this.functionType,
    this.is_jump,
    this.jump_environment,
    this.jump_logic,
    this.miniProId,
    this.nav_url,
    this.originId,
    this.pageType,
    this.phoneNumber,
    this.shareImg,
    this.shareImgName,
    this.shareSubTitle,
    this.shareTitle,
    this.shareUrl,
    this.timestamp,
  });
  factory AttrCustomFeatureDetail.fromJson(Map<String, dynamic> json) =>
      _$AttrCustomFeatureDetailFromJson(json);

  Map<String, dynamic> toJson() => _$AttrCustomFeatureDetailToJson(this);
}

/// 事件类型 - eventType
enum EventTypeEnum {
  /// 跳转链接
  @JsonValue('1')
  jumpLink,

  /// 功能
  @JsonValue('4')
  function,

  /// 智家链接字典
  @JsonValue('5')
  linkDictionary,
}

/// 是否之间跳转 - is_jump
/// 0 是 1 否
enum IsJumpEnum {
  /// 直接跳转
  @JsonValue('0')
  directJump,

  /// 不直接跳转
  @JsonValue('1')
  notDirectJump,
}

/// 跳转逻辑 - jump_logic
/// 0 特定环境跳转  目前只有这一种方式
enum JumpLogicEnum {
  /// 特定环境跳转
  @JsonValue('0')
  specificEnvironmentJump,
}

/// 跳转环境 - jump_environment
/// 智家app=0  三翼鸟微信小程序=1  其他微信小程序=2  三翼鸟APP=3 智家微信小程序=4 抖音小程序=5
enum JumpEnvironmentEnum {
  /// 智家app
  @JsonValue('0')
  zhijiaApp,

  /// 三翼鸟微信小程序
  @JsonValue('1')
  sanyiniaoWechatMiniProgram,

  /// 其他微信小程序
  @JsonValue('2')
  otherAppMiniProgram,

  /// 三翼鸟APP
  @JsonValue('3')
  sanyiniaoApp,

  /// 智家微信小程序
  @JsonValue('4')
  zhijiaWechatMiniProgram,

  /// 抖音小程序
  @JsonValue('5')
  douyinMiniProgram,
}

/// 功能里面的类型 - functionType
/// 3 拨打电话 5 分享
enum FunctionTypeEnum {
  /// 拨打电话
  @JsonValue('3')
  phoneNumber,

  /// 分享
  @JsonValue('5')
  share,
}

/// 页面类型 - pageType
/// 0 H5  1 微信小程序
enum PageTypeEnum {
  /// H5
  @JsonValue(0)
  H5,

  /// 分微信小程序
  @JsonValue(1)
  wechatMiniProgram,
}
