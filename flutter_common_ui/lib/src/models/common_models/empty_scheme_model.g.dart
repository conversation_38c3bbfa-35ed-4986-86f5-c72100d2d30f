// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'empty_scheme_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmptySchemeModel _$EmptySchemeModelFromJson(Map json) => EmptySchemeModel(
      value: json['value'] as num?,
      defaultImgInfo: json['defaultImgInfo'] == null
          ? null
          : DefaultImgInfo.fromJson(
              Map<String, dynamic>.from(json['defaultImgInfo'] as Map)),
      sourceBitInfo: json['sourceBitInfo'] == null
          ? null
          : DefaultImgInfo.fromJson(
              Map<String, dynamic>.from(json['sourceBitInfo'] as Map)),
    );

Map<String, dynamic> _$EmptySchemeModelToJson(EmptySchemeModel instance) =>
    <String, dynamic>{
      'value': instance.value,
      'defaultImgInfo': instance.defaultImgInfo?.toJson(),
      'sourceBitInfo': instance.sourceBitInfo?.toJson(),
    };

DefaultImgInfo _$DefaultImgInfoFromJson(Map json) => DefaultImgInfo(
      size: json['size'] as List<dynamic>?,
      defaultUrl: json['defaultUrl'] as String?,
      picUrl: json['picUrl'] as String?,
      pollId: json['pollId'] as String?,
    );

Map<String, dynamic> _$DefaultImgInfoToJson(DefaultImgInfo instance) =>
    <String, dynamic>{
      'size': instance.size,
      'picUrl': instance.picUrl,
      'defaultUrl': instance.defaultUrl,
      'pollId': instance.pollId,
    };
