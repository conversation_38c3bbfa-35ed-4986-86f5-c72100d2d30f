/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: 基础model类型 公共库
 */

import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:json_annotation/json_annotation.dart';

import 'custom_feature_model.dart';
// 公共属性
import 'style_info_model.dart';

part 'base_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class BaseModel {
  /// 类型
  final ComponentType? type;

  /// 唯一标识
  final String? alias;

  /// 时间戳标识
  final num? aliasCode;

  /// 组件别名
  final String? aliasName;

  /// 组件唯一标识
  final String? aliasUnit;

  /// 组件分组
  final String? group;

  /// 组件样式
  final StyleInfoModel? style;

  /// 绑定事件
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  /// [attr] 组件的配置
  final Attr? attr;

  BaseModel(
      {this.type,
      this.alias,
      this.aliasCode,
      this.aliasName,
      this.aliasUnit,
      this.group,
      this.style,
      this.customFeature,
      this.attr});
  factory BaseModel.fromJson(Map<String, dynamic> json) =>
      _$BaseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BaseModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class Attr {
  Attr();

  factory Attr.fromJson(Map<String, dynamic> json) => _$AttrFromJson(json);

  Map<String, dynamic> toJson() => _$AttrToJson(this);
}

// /// 可视化组件类型
// enum ComponentType {
//   /// 图片
//   @JsonValue('picture')
//   picture,

//   /// 卡片导航组件
//   @JsonValue('cardNav')
//   cardNav,

//   /// 内容组件
//   @JsonValue('content')
//   content,

//   /// 轮播组件
//   @JsonValue('carousel')
//   carousel,

//   /// 分类目录
//   @JsonValue('category')
//   category,

//   /// 动态容器
//   @JsonValue('seniorvessel')
//   seniorVessel,

//   /// 家庭
//   @JsonValue('family')
//   family,

//   /// 天气
//   @JsonValue('weather')
//   weather,

//   /// 直播
//   @JsonValue('live_broadcast')
//   liveBroadcast,

//   /// 消息通知
//   @JsonValue('msNotify')
//   msNotify,

//   /// 文本
//   @JsonValue('text_syn')
//   text,

//   /// 菜单
//   @JsonValue('menu')
//   menu,
// }
