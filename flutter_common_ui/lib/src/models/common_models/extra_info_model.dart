import 'package:flutter_common_ui/src/models/common_models/more_item_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'extra_info_model.g.dart';


/**
 'extraInfo': {
        // 只有当是圆形的样式，才会展示有more的入口
        "isShowMore": true,
        // 更多选项的配置
        "moreItem": {
            // 入口图片
            "icon": "https://oss-zjrs.haier.net/content/img/2023021310532424937596.png",
            // 入口名称
            "name": "更多",
            // 导航项唯一标识
            "id": 1680230600956,
        }
    }
 */
@JsonSerializable(explicitToJson: true, anyMap: true)
class ExtraInfoModel {
  /// [isShowMore]只有当是圆形的样式，才会展示有more的入口
  final bool? isShowMore;

  /// [moreItem]更多选项
  final MoreItemModel? moreItem;


  ExtraInfoModel({this.isShowMore, this.moreItem});



  factory ExtraInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ExtraInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ExtraInfoModelToJson(this);
}