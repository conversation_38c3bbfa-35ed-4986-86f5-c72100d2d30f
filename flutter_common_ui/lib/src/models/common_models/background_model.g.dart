// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'background_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BackgroundModel _$BackgroundModelFromJson(Map json) => BackgroundModel(
      type: json['type'] as String?,
      image: json['image'] == null
          ? null
          : ImageInfo.fromJson(Map<String, dynamic>.from(json['image'] as Map)),
      filter: json['filter'] == null
          ? null
          : AttrFilter.fromJson(
              Map<String, dynamic>.from(json['filter'] as Map)),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
    );

Map<String, dynamic> _$BackgroundModelToJson(BackgroundModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'image': instance.image?.toJson(),
      'filter': instance.filter?.toJson(),
      'color': instance.color?.toJson(),
    };

ImageInfo _$ImageInfoFromJson(Map json) => ImageInfo(
      url: json['url'] as String?,
      repeat: json['repeat'] as String?,
      size: json['size'] as List<dynamic>?,
      position: json['position'] as String?,
    );

Map<String, dynamic> _$ImageInfoToJson(ImageInfo instance) => <String, dynamic>{
      'url': instance.url,
      'repeat': instance.repeat,
      'size': instance.size,
      'position': instance.position,
    };
