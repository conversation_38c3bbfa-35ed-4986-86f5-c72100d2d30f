// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attr_corner_mark_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttrCornerMark _$AttrCornerMarkFromJson(Map json) => AttrCornerMark(
      type: $enumDecodeNullable(_$AttrCornerMarkTypeEnumEnumMap, json['type']),
      background: json['background'] == null
          ? null
          : BackgroundModel.fromJson(
              Map<String, dynamic>.from(json['background'] as Map)),
      filter: json['filter'] == null
          ? null
          : AttrFilter.fromJson(
              Map<String, dynamic>.from(json['filter'] as Map)),
      textContent: (json['textContent'] as List<dynamic>?)
          ?.map((e) =>
              AttrTextContent.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AttrCornerMarkToJson(AttrCornerMark instance) =>
    <String, dynamic>{
      'type': _$AttrCornerMarkTypeEnumEnumMap[instance.type],
      'background': instance.background?.toJson(),
      'filter': instance.filter?.toJson(),
      'textContent': instance.textContent?.map((e) => e.toJson()).toList(),
    };

const _$AttrCornerMarkTypeEnumEnumMap = {
  AttrCornerMarkTypeEnum.none: 0,
  AttrCornerMarkTypeEnum.redDot: 1,
  AttrCornerMarkTypeEnum.text: 2,
};

AttrTextContent _$AttrTextContentFromJson(Map json) => AttrTextContent(
      id: json['id'] as int?,
      text: json['text'] as String?,
    );

Map<String, dynamic> _$AttrTextContentToJson(AttrTextContent instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
    };
