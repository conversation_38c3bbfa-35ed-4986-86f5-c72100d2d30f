// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseModel _$BaseModelFromJson(Map json) => BaseModel(
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      attr: json['attr'] == null
          ? null
          : Attr.fromJson(Map<String, dynamic>.from(json['attr'] as Map)),
    );

Map<String, dynamic> _$BaseModelToJson(BaseModel instance) => <String, dynamic>{
      'type': _$ComponentTypeEnumMap[instance.type],
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'style': instance.style?.toJson(),
      'customfeature': instance.customFeature?.toJson(),
      'attr': instance.attr?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

Attr _$AttrFromJson(Map json) => Attr();

Map<String, dynamic> _$AttrToJson(Attr instance) => <String, dynamic>{};
