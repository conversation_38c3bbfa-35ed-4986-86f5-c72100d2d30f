// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'color_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ColorModel _$ColorModelFromJson(Map json) => ColorModel(
      value: json['value'] as String?,
      isGradient: json['isGradient'] as bool?,
      gradient: json['gradient'] == null
          ? null
          : AttrGradient.fromJson(
              Map<String, dynamic>.from(json['gradient'] as Map)),
    );

Map<String, dynamic> _$ColorModelToJson(ColorModel instance) =>
    <String, dynamic>{
      'value': instance.value,
      'isGradient': instance.isGradient,
      'gradient': instance.gradient?.toJson(),
    };

AttrGradient _$AttrGradientFromJson(Map json) => AttrGradient(
      type: json['type'] as String?,
      stop: json['stop'] as List<dynamic>?,
      angle: (json['angle'] as num?)?.toDouble(),
      direction: json['direction'] as String?,
    );

Map<String, dynamic> _$AttrGradientToJson(AttrGradient instance) =>
    <String, dynamic>{
      'type': instance.type,
      'stop': instance.stop,
      'angle': instance.angle,
      'direction': instance.direction,
    };
