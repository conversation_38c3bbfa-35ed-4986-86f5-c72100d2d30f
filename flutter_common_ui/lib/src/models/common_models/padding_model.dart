/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: padding 内边距
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
/// padding 内边距
import 'package:json_annotation/json_annotation.dart';

part 'padding_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class PaddingModel {
  /// 上边距
  final double? top;

  /// 下边距
  final double? bottom;

  /// 左边距
  final double? left;

  /// 右边距
  final double? right;
  PaddingModel({this.top = 0, this.bottom = 0, this.left = 0, this.right = 0});

  /// 内边距
  EdgeInsets get padding => EdgeInsets.only(
        top: CommonUtil.filterNullAndPositiveValue(top).w,
        right: CommonUtil.filterNullAndPositiveValue(right).w,
        bottom: CommonUtil.filterNullAndPositiveValue(bottom).w,
        left: CommonUtil.filterNullAndPositiveValue(left).w,
      );

  factory PaddingModel.fromJson(Map<String, dynamic> json) =>
      _$PaddingModelFromJson(json);

  Map<String, dynamic> toJson() => _$PaddingModelToJson(this);
}
