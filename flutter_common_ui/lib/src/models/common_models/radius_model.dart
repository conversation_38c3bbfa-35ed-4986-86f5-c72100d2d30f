/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description:radius 圆角
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
/// radius 圆角
import 'package:json_annotation/json_annotation.dart';

part 'radius_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class RadiusModel {
  /// 左上圆角
  final double? lt;

  /// 右上圆角
  final double? rt;

  /// 左下圆角
  final double? lb;

  /// 右下圆角
  final double? rb;
  RadiusModel({this.lt = 0, this.rt = 0, this.lb = 0, this.rb = 0});

  /// 四个圆角
  BorderRadius get borderRadius => BorderRadius.only(
        topLeft:
            Radius.circular(CommonUtil.filterNullAndPositiveValue(this.lt).w),
        topRight:
            Radius.circular(CommonUtil.filterNullAndPositiveValue(this.rt).w),
        bottomLeft:
            Radius.circular(CommonUtil.filterNullAndPositiveValue(this.lb).w),
        bottomRight:
            Radius.circular(CommonUtil.filterNullAndPositiveValue(this.rb).w),
      );

  factory RadiusModel.fromJson(Map<String, dynamic> json) =>
      _$RadiusModelFromJson(json);

  Map<String, dynamic> toJson() => _$RadiusModelToJson(this);
}
