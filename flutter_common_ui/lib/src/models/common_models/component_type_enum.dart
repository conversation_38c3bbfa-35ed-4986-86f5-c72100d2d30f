import 'package:json_annotation/json_annotation.dart';

class ComponentName {
  /// 占位组件
  static const String locate = 'locate';

  /// 图片
  static const String picture = 'picture';

  /// 卡片导航组件
  static const String cardNav = 'cardNav';

  /// 内容组件
  static const String content = 'content';

  /// 分类目录
  static const String category = 'category';

  /// 轮播组件
  static const String carousel = 'carousel';

  /// 动态容器
  static const String seniorVessel = 'seniorvessel';

  /// 家庭
  static const String family = 'family';

  /// 天气
  static const String weather = 'weather';

  /// 直播
  static const String liveBroadcast = 'live_broadcast';

  /// 消息通知
  static const String msNotify = 'msNotify';

  /// 文本
  static const String text = 'text_syn';

  /// 菜单
  static const String menu = 'menu';
}

/// 可视化组件类型
enum ComponentType {
  /// 图片
  @JsonValue(ComponentName.picture)
  picture,

  /// 卡片导航组件
  @JsonValue(ComponentName.cardNav)
  cardNav,

  /// 内容组件
  @JsonValue(ComponentName.content)
  content,

  /// 轮播组件
  @JsonValue(ComponentName.carousel)
  carousel,

  /// 分类目录
  @JsonValue(ComponentName.category)
  category,

  /// 动态容器
  @JsonValue(ComponentName.seniorVessel)
  seniorVessel,

  /// 家庭
  @JsonValue(ComponentName.family)
  family,

  /// 天气
  @JsonValue(ComponentName.weather)
  weather,

  /// 直播
  @JsonValue(ComponentName.liveBroadcast)
  liveBroadcast,

  /// 消息通知
  @JsonValue(ComponentName.msNotify)
  msNotify,

  /// 文本
  @JsonValue(ComponentName.text)
  text,

  /// 菜单
  @JsonValue(ComponentName.menu)
  menu
}
