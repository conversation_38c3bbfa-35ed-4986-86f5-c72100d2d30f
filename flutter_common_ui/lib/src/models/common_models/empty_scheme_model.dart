/*
 * @Author: maxia<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-01 15:07:29
 * @description: 空数据处理逻辑
 */
/// 空数据处理逻辑
import 'package:json_annotation/json_annotation.dart';

part 'empty_scheme_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class EmptySchemeModel {
  ///  1: 隐藏组件和同分组组件  2:展示默认图片  3:展示资源位  隐藏组件和同分组组件
  final num? value;

  /// 当空数据处理逻辑选择展示默认图片时，该字段生效。
  DefaultImgInfo? defaultImgInfo;

  /// 当空数据处理逻辑选择展示资源位时，该字段生效
  DefaultImgInfo? sourceBitInfo;
  EmptySchemeModel({this.value, this.defaultImgInfo, this.sourceBitInfo});
  factory EmptySchemeModel.fromJson(Map<String, dynamic> json) =>
      _$EmptySchemeModelFromJson(json);

  Map<String, dynamic> toJson() => _$EmptySchemeModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class DefaultImgInfo {
  /// 图片尺寸 【宽，高】
  final List? size;

  /// 默认图片
  final String? picUrl;

  /// 默认链接
  final String? defaultUrl;

  /// 轮播标识
  final String? pollId;

  /// 当空数据处理逻辑选择展示资源位时，该字段生效
  DefaultImgInfo({this.size, this.defaultUrl, this.picUrl, this.pollId});
  factory DefaultImgInfo.fromJson(Map<String, dynamic> json) =>
      _$DefaultImgInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DefaultImgInfoToJson(this);
}
