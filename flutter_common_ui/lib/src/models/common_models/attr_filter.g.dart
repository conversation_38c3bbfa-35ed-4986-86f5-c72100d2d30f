// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attr_filter.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttrFilter _$AttrFilterFromJson(Map json) => AttrFilter(
      blur: (json['blur'] as num?)?.toDouble(),
      brightness: (json['brightness'] as num?)?.toDouble(),
      contrast: (json['contrast'] as num?)?.toDouble(),
      grayScale: (json['grayscale'] as num?)?.toDouble(),
      sepia: (json['sepia'] as num?)?.toDouble(),
      invert: (json['invert'] as num?)?.toDouble(),
      hueRotate: (json['hue-rotate'] as num?)?.toDouble(),
      hueRotateUnit: json['hue-rotate-unit'] as String? ?? _HueRotateUnit.deg,
    );

Map<String, dynamic> _$AttrFilterToJson(AttrFilter instance) =>
    <String, dynamic>{
      'blur': instance.blur,
      'brightness': instance.brightness,
      'contrast': instance.contrast,
      'grayscale': instance.grayScale,
      'sepia': instance.sepia,
      'invert': instance.invert,
      'hue-rotate': instance.hueRotate,
      'hue-rotate-unit': instance.hueRotateUnit,
    };
