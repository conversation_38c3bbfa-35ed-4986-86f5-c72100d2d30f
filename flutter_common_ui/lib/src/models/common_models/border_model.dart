/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 09:44:54
 * @description: 边框信息
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
/// 边框信息
import 'package:json_annotation/json_annotation.dart';

import 'color_model.dart';

part 'border_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class BorderModel {
  /// 边框类型 unset -无 solid - 实线 dashed - 虚线 dotted-点线
  final BorderStyleEnum? style;

  /// 边框宽度
  final double? width;

  /// 边框颜色
  ColorModel? color;
  BorderModel({this.style, this.width, this.color});

  /// 是否非为flutter sdk的border, 包含unset和solid实线
  bool get isSDKBorder =>
      [BorderStyleEnum.unset, BorderStyleEnum.solid].contains(style);

  /// [border] 边框线
  Border get border {
    Color? color = CommonUtil.toColor(this.color?.value);
    // 无边框
    if (this.style == BorderStyleEnum.unset ||
        [this.color?.value, color, this.width].contains(null)) {
      return Border();
    }
    // 实线边框
    return Border.all(
        color: color ?? Colors.transparent, width: this.width?.w ?? 0);
  }

  ///虚线模式
  List<double>? get lineDashPattern {
    if (this.width == null) {
      return null;
    }

    final width = this.width?.w ?? 0;

    var dashPattern = <double>[width, width];

    if (this.style == BorderStyleEnum.dotted) {
      // dashPattern = [3, 1];
      dashPattern = [width, width];
    } else if (this.style == BorderStyleEnum.dashed) {
      // dashPattern = [10, 5];
      dashPattern = [width * 2, width];
    } else {
      // dashPattern = [5, 0];
      dashPattern = [width, 0];
    }

    // dashPattern = [width * 1, width.w];//add test by bin
    return dashPattern;
  }

  factory BorderModel.fromJson(Map<String, dynamic> json) =>
      _$BorderModelFromJson(json);

  Map<String, dynamic> toJson() => _$BorderModelToJson(this);
}

/// 边框线条类型
enum BorderStyleEnum {
  /// [unset] 无边框
  @JsonValue('unset')
  unset,

  /// [unset] 实线
  @JsonValue('solid')
  solid,

  /// [unset] 虚线
  @JsonValue('dashed')
  dashed,

  /// [unset] 点线
  @JsonValue('dotted')
  dotted
}
