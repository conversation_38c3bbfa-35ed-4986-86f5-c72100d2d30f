// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'border_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BorderModel _$BorderModelFromJson(Map json) => BorderModel(
      style: $enumDecodeNullable(_$BorderStyleEnumEnumMap, json['style']),
      width: (json['width'] as num?)?.toDouble(),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
    );

Map<String, dynamic> _$BorderModelToJson(BorderModel instance) =>
    <String, dynamic>{
      'style': _$BorderStyleEnumEnumMap[instance.style],
      'width': instance.width,
      'color': instance.color?.toJson(),
    };

const _$BorderStyleEnumEnumMap = {
  BorderStyleEnum.unset: 'unset',
  BorderStyleEnum.solid: 'solid',
  BorderStyleEnum.dashed: 'dashed',
  BorderStyleEnum.dotted: 'dotted',
};
