/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-02-01 16:30:31
 * @description: 背景颜色
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_filter.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
/// 背景颜色
import 'package:json_annotation/json_annotation.dart';

import 'color_model.dart';

part 'background_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class BackgroundModel {
  /// 背景类型image - 图片   color-颜色
  final String? type;

  /// 当背景类型为图片时该字段生效
  ImageInfo? image;

  /// 模糊 亮度 对比度 饱和度 灰度 加温 色相 反色
  AttrFilter? filter;

  /// 当背景类型为颜色时该字段生效
  ColorModel? color;

  BackgroundModel({this.type, this.image, this.filter, this.color}) : super();
  factory BackgroundModel.fromJson(Map<String, dynamic> json) =>
      _$BackgroundModelFromJson(json);

  Map<String, dynamic> toJson() => _$BackgroundModelToJson(this);

  /// [bgUrl] 背景图片
  String? get bgUrl {
    if (this.type == 'color') {
      return null;
    }
    return this.image?.url;
  }

  /// [bgColor] 背景颜色
  Color? get bgColor {
    if (this.type == 'color' && this.color?.isGradient == false) {
      return CommonUtil.toColor(this.color?.value);
    }
    return null;
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ImageInfo {
  /// 背景图链接
  final String? url;

  /// 背景图 是否重复
  final String? repeat;

  ///  背景图的宽高
  final List? size;

  /// 位置
  final String? position;

  ImageInfo({this.url, this.repeat, this.size, this.position}) : super();
  factory ImageInfo.fromJson(Map<String, dynamic> json) =>
      _$ImageInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ImageInfoToJson(this);
}
