import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'more_item_model.g.dart';

/**
 "moreItem": {
            // 入口图片
            "icon": "https://oss-zjrs.haier.net/content/img/2023021310532424937596.png",
            // 入口名称
            "name": "更多",
            // 导航项唯一标识
            "id": 1680230600956,
        }
 */
@JsonSerializable(explicitToJson: true, anyMap: true)
class MoreItemModel {
  /// [icon]更多入口图片
  final String? icon;

  /// [name]入口名称
  final String? name;

  /// 每一菜单项的唯一标识，使用时间戳
  @JsonKey(
    fromJson: _moreItemModelFromJson,
  )
  String? id;

  MoreItemModel({this.icon, this.name, this.id});

  factory MoreItemModel.fromJson(Map<String, dynamic> json) =>
      _$MoreItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$MoreItemModelToJson(this);

  static String? _moreItemModelFromJson(dynamic id) {
    return id?.toString();
  }
}