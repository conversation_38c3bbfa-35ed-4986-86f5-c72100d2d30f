import 'package:json_annotation/json_annotation.dart';

part 'attr_filter.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrFilter {
  /// [blur] 模糊
  final double? blur;

  /// [brightness] 亮度
  final double? brightness;

  /// [contrast] 对比度
  final double? contrast;

  /// [grayScale] 灰度
  @Json<PERSON><PERSON>(name: 'grayscale')
  final double? grayScale;

  /// [sepia] 加温
  final double? sepia;

  ///[invert] 反色
  final double? invert;

  ///[invert] 色相
  @Json<PERSON><PERSON>(name: 'hue-rotate')
  double? hueRotate;

  ///[invert] 色相单位 'deg'
  @<PERSON><PERSON><PERSON><PERSON>(name: 'hue-rotate-unit')
  String? hueRotateUnit;

  AttrFilter(
      {this.blur,
      this.brightness,
      this.contrast,
      this.grayScale,
      this.sepia,
      this.invert,
      this.hueRotate,
      this.hueRotateUnit = _HueRotateUnit.deg});

  factory AttrFilter.fromJson(Map<String, dynamic> json) =>
      _$AttrFilterFromJson(json);
  Map<String, dynamic> toJson() => _$AttrFilterToJson(this);
}

/// 色相单位
class _HueRotateUnit {
  static const String deg = 'deg';
}
