/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-30 13:34:59
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/common_models/attr_filter.dart';
import 'package:flutter_common_ui/src/models/common_models/background_model.dart';
import 'package:flutter_common_ui/src/utils/home_common_util.dart';
import 'package:flutter_common_ui/src/widgets/basic_widget/decoration_widget/decoration_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:json_annotation/json_annotation.dart';

part 'attr_corner_mark_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrCornerMark {
  /// [width] 角标的宽固定38
  static final width = 38.w;

  /// [height] 角标的高固定14
  static final height = 14.w;

  /// [defaultBorderRadius] 角标默认的圆角 默认7
  static final defaultBorderRadius = BorderRadius.only(
    topLeft: Radius.circular(7.w),
    topRight: Radius.circular(7.w),
    bottomRight: Radius.circular(7.w),
    bottomLeft: Radius.zero,
  );

  /// [type] 角标类型
  AttrCornerMarkTypeEnum? type;

  /// [background]
  /// 角标类型为‘动态文字’时
  /// 角标背景颜色
  /// 根据UI设计确定 2023-1-16需求文档还未更新没有默认值
  /// 这里的值是文档创建者随便写的占位，具体值在开发时联系产品确认
  BackgroundModel? background;

  /// [filter] 角标背景模糊
  AttrFilter? filter;

  /// [textContent] 文字内容
  List<AttrTextContent>? textContent;

  AttrCornerMark({this.type, this.background, this.filter, this.textContent});

  factory AttrCornerMark.fromJson(Map<String, dynamic> json) =>
      _$AttrCornerMarkFromJson(json);
  Map<String, dynamic> toJson() => _$AttrCornerMarkToJson(this);

  /// 转 外壳组件 - DecorationWidget
  Widget toCornerMarkDecorationWidget({
    required Widget child,
  }) {
    /// 背景
    return DecorationWidget(
      bgUrl: this.background?.bgUrl,
      bgColor: this.background?.bgColor,
      bgGradient: CommonUtil.toGradient(background?.color,
          width: width, height: height),
      width: width,
      height: height,
      blur: filter?.blur ?? 0,
      borderRadius: defaultBorderRadius,
      child: child,
    );
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrTextContent {
  /// [id] 内容id
  final int? id;

  /// [text] 内容文本
  final String? text;
  AttrTextContent({this.id, this.text});

  factory AttrTextContent.fromJson(Map<String, dynamic> json) =>
      _$AttrTextContentFromJson(json);
  Map<String, dynamic> toJson() => _$AttrTextContentToJson(this);
}

enum AttrCornerMarkTypeEnum {
  /// [none] 无
  @JsonValue(0)
  none,

  /// [redDot] 小红点
  @JsonValue(1)
  redDot,

  /// [text] 文字
  @JsonValue(2)
  text,
}
