// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'track_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrackInfoModel _$TrackInfoModelFromJson(Map json) => TrackInfoModel(
      click: json['click'] == null
          ? null
          : AttrTrackDetail.fromJson(
              Map<String, dynamic>.from(json['click'] as Map)),
      exposure: json['exposure'] == null
          ? null
          : AttrTrackDetail.fromJson(
              Map<String, dynamic>.from(json['exposure'] as Map)),
    );

Map<String, dynamic> _$TrackInfoModelToJson(TrackInfoModel instance) =>
    <String, dynamic>{
      'click': instance.click?.toJson(),
      'exposure': instance.exposure?.toJson(),
    };

AttrTrackDetail _$AttrTrackDetailFromJson(Map json) => AttrTrackDetail(
      attr: (json['attr'] as Map?)?.map(
        (k, e) => MapEntry(k as String, e),
      ),
      code: json['code'] as String?,
    );

Map<String, dynamic> _$AttrTrackDetailToJson(AttrTrackDetail instance) =>
    <String, dynamic>{
      'code': instance.code,
      'attr': instance.attr,
    };
