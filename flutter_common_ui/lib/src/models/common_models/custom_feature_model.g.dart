// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'custom_feature_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomFeatureModel _$CustomFeatureModelFromJson(Map json) => CustomFeatureModel(
      eventHandler: json['eventHandler'] as String?,
      eventParams: json['eventParams'] == null
          ? null
          : AttrCustomFeatureDetail.fromJson(
              Map<String, dynamic>.from(json['eventParams'] as Map)),
      currentEventIndex: json['currentEventIndex'] as int?,
    );

Map<String, dynamic> _$CustomFeatureModelToJson(CustomFeatureModel instance) =>
    <String, dynamic>{
      'eventHandler': instance.eventHandler,
      'eventParams': instance.eventParams?.toJson(),
      'currentEventIndex': instance.currentEventIndex,
    };

AttrCustomFeatureDetail _$AttrCustomFeatureDetailFromJson(Map json) =>
    AttrCustomFeatureDetail(
      beginEnvir: json['beginEnvir'] as String?,
      eventType: $enumDecodeNullable(_$EventTypeEnumEnumMap, json['eventType']),
      functionType:
          $enumDecodeNullable(_$FunctionTypeEnumEnumMap, json['functionType']),
      is_jump: $enumDecodeNullable(_$IsJumpEnumEnumMap, json['is_jump']),
      jump_environment: $enumDecodeNullable(
          _$JumpEnvironmentEnumEnumMap, json['jump_environment']),
      jump_logic:
          $enumDecodeNullable(_$JumpLogicEnumEnumMap, json['jump_logic']),
      miniProId: json['miniProId'] as String?,
      nav_url: json['nav_url'] as String?,
      originId: json['originId'] as String?,
      pageType: $enumDecodeNullable(_$PageTypeEnumEnumMap, json['pageType']),
      phoneNumber: json['phoneNumber'] as String?,
      shareImg: json['shareImg'] as String?,
      shareImgName: json['shareImgName'] as String?,
      shareSubTitle: json['shareSubTitle'] as String?,
      shareTitle: json['shareTitle'] as String?,
      shareUrl: json['shareUrl'] as String?,
      timestamp: json['timestamp'] as String?,
    );

Map<String, dynamic> _$AttrCustomFeatureDetailToJson(
        AttrCustomFeatureDetail instance) =>
    <String, dynamic>{
      'eventType': _$EventTypeEnumEnumMap[instance.eventType],
      'nav_url': instance.nav_url,
      'is_jump': _$IsJumpEnumEnumMap[instance.is_jump],
      'jump_logic': _$JumpLogicEnumEnumMap[instance.jump_logic],
      'jump_environment':
          _$JumpEnvironmentEnumEnumMap[instance.jump_environment],
      'miniProId': instance.miniProId,
      'originId': instance.originId,
      'functionType': _$FunctionTypeEnumEnumMap[instance.functionType],
      'phoneNumber': instance.phoneNumber,
      'pageType': _$PageTypeEnumEnumMap[instance.pageType],
      'shareTitle': instance.shareTitle,
      'shareSubTitle': instance.shareSubTitle,
      'shareImg': instance.shareImg,
      'shareImgName': instance.shareImgName,
      'shareUrl': instance.shareUrl,
      'beginEnvir': instance.beginEnvir,
      'timestamp': instance.timestamp,
    };

const _$EventTypeEnumEnumMap = {
  EventTypeEnum.jumpLink: '1',
  EventTypeEnum.function: '4',
  EventTypeEnum.linkDictionary: '5',
};

const _$FunctionTypeEnumEnumMap = {
  FunctionTypeEnum.phoneNumber: '3',
  FunctionTypeEnum.share: '5',
};

const _$IsJumpEnumEnumMap = {
  IsJumpEnum.directJump: '0',
  IsJumpEnum.notDirectJump: '1',
};

const _$JumpEnvironmentEnumEnumMap = {
  JumpEnvironmentEnum.zhijiaApp: '0',
  JumpEnvironmentEnum.sanyiniaoWechatMiniProgram: '1',
  JumpEnvironmentEnum.otherAppMiniProgram: '2',
  JumpEnvironmentEnum.sanyiniaoApp: '3',
  JumpEnvironmentEnum.zhijiaWechatMiniProgram: '4',
  JumpEnvironmentEnum.douyinMiniProgram: '5',
};

const _$JumpLogicEnumEnumMap = {
  JumpLogicEnum.specificEnvironmentJump: '0',
};

const _$PageTypeEnumEnumMap = {
  PageTypeEnum.H5: 0,
  PageTypeEnum.wechatMiniProgram: 1,
};
