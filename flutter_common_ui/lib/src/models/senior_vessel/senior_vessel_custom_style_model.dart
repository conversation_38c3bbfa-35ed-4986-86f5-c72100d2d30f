import 'package:flutter_common_ui/src/models/common_models/background_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'senior_vessel_custom_style_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class SeniorVesselCustomStyleModel {
  ///  [background] 组件背景
  final BackgroundModel? background;
  SeniorVesselCustomStyleModel({this.background});

  factory SeniorVesselCustomStyleModel.fromJson(Map<String, dynamic> json) =>
      _$SeniorVesselCustomStyleModelFromJson(json);
  Map<String, dynamic> toJson() => _$SeniorVesselCustomStyleModelToJson(this);
}
