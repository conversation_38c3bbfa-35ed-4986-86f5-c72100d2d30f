import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/family/family_info_model.dart';
import 'package:flutter_common_ui/src/models/live/live_visual_model.dart';
import 'package:flutter_common_ui/src/models/menu/menu_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/models/text/text_model.dart';
import 'package:flutter_common_ui/src/models/card_navigate/card_navigate_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'senior_vessel_model.g.dart';

/// 动态容器model
@JsonSerializable(explicitToJson: true, anyMap: true)
class SeniorVesselModel extends BaseModel {
  /// [type] 组件类型 在父[CommonZJ]中已经定义，这里定义的原因是[SeniorVesselModel.fromJson] 没有调用父[CommonZJ.fromJson]
  @override
  final ComponentType? type;

  /// [content] 动态容器里面包含组件放置
  final AttrVesselContent? content;

  /// [style] 组件样式 在父[CommonZJ]中已经定义，这里定义的原因是[SeniorVesselModel.fromJson] 没有调用父[CommonZJ.fromJson]
  @override
  final StyleInfoModel? style;

  /// [] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  SeniorVesselModel(
      {this.type,
      this.content,
      this.style,
      this.customFeature,
      String? alias,
      num? aliasCode,
      String? aliasName,
      String? aliasUnit})
      : super(
          style: style,
          customFeature: customFeature,
          type: type,
          alias: alias,
          aliasCode: aliasCode,
          aliasName: aliasName,
          aliasUnit: aliasUnit,
        );
  factory SeniorVesselModel.fromJson(Map<String, dynamic> json) =>
      _$SeniorVesselModelFromJson(json);
  Map<String, dynamic> toJson() => _$SeniorVesselModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrVesselContent {
  /// [vesselType] 容器类型
  final VesselTypeEnum? vesselType;

  /// [vesselWidget] 容器组件
  final AttrVesselWidget? vesselWidget;

  AttrVesselContent({this.vesselType, this.vesselWidget});

  factory AttrVesselContent.fromJson(Map<String, dynamic> json) =>
      _$AttrVesselContentFromJson(json);
  Map<String, dynamic> toJson() => _$AttrVesselContentToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrVesselWidget {
  /// [currentStatusId] 当前状态Id
  String? currentStatusId;

  /// [name] 名称
  String? name;

  /// [statusWarehouseCollection] 动态容器的状态列表，包含初始和上滑
  List<AttrStatusWareHouse>? statusWarehouseCollection;

  AttrVesselWidget(
    this.currentStatusId,
    this.name,
    this.statusWarehouseCollection,
  );

  factory AttrVesselWidget.fromJson(Map<String, dynamic> json) =>
      _$AttrVesselWidgetFromJson(json);
  Map<String, dynamic> toJson() => _$AttrVesselWidgetToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrStatusWareHouse {
  /// [modeSelect]动态容器模式，只有这一种，自由拖拽模式 freeDragMode
  VesselModeEnum? modeSelect;

  /// [name] 动态容器的状态名称
  String? name;

  /// [uniqueId] 动态容器的Id
  String? uniqueId;

  /// [widgetList] 这个里面放置动态容器包含的组件的配置
  @JsonKey(
    toJson: _widgetListToJson,
    fromJson: _widgetListFromJson,
  )
  List<dynamic>? widgetList;

  AttrStatusWareHouse(
    this.modeSelect,
    this.name,
    this.uniqueId,
    this.widgetList,
  );

  factory AttrStatusWareHouse.fromJson(Map<String, dynamic> json) =>
      _$AttrStatusWareHouseFromJson(json);
  Map<String, dynamic> toJson() => _$AttrStatusWareHouseToJson(this);

  static List<dynamic>? _widgetListFromJson(List<dynamic>? jsonList) {
    List<dynamic> list = [];
    if (jsonList?.length == null) {
      return null;
    }
    jsonList!.forEach((json) {
      switch (json?['type']) {
        // 可以包含7类子组件：轮播、图片、文本、内容、菜单、直播、家庭
        case ComponentName.picture:
          list.add(
              PictureModel.fromJson(json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.carousel:
          list.add(
              CarouselModel.fromJson(json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.text:
          list.add(
              TextModel.fromJson(json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.menu:
          list.add(
              MenuModel.fromJson(json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.liveBroadcast:
          list.add(LiveVisualModel.fromJson(
              json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.family:
          list.add(FamilyInfoModel.fromJson(
              json is Map<String, dynamic> ? json : {}));
          break;
        case ComponentName.cardNav:
          list.add(CardNavigateModel.fromJson(
              json is Map<String, dynamic> ? json : {}));
          break;
        // TODO 内容组件添加
        default:
          break;
      }
    });
    return list;
  }

  static List<dynamic>? _widgetListToJson(List<dynamic>? list) {
    return list?.length == null
        ? null
        : list!.map((item) {
            return item?.toJson() ?? {};
          }).toList();
  }
}

/// 容器类型
enum VesselTypeEnum {
  /// [floor] 楼层类型
  @JsonValue(0)
  floor,

  /// [persistent] 吸顶类型
  @JsonValue(1)
  persistent,
}

/// 容器模式
enum VesselModeEnum {
  /// 自由拖拽
  @JsonValue('freeDragMode')
  freeDragMode,
}

/// 容器状态
enum VesselStatusEnum {
  /// 初始状态 0
  @JsonValue(0)
  initial,

  /// 上滑状态 1
  @JsonValue(1)
  slideUp,
}
