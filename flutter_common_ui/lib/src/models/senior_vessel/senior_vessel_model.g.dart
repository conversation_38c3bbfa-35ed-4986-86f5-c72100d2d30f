// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'senior_vessel_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SeniorVesselModel _$SeniorVesselModelFromJson(Map json) => SeniorVesselModel(
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      content: json['content'] == null
          ? null
          : AttrVesselContent.fromJson(
              Map<String, dynamic>.from(json['content'] as Map)),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
    );

Map<String, dynamic> _$SeniorVesselModelToJson(SeniorVesselModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'type': _$ComponentTypeEnumMap[instance.type],
      'content': instance.content?.toJson(),
      'style': instance.style?.toJson(),
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

AttrVesselContent _$AttrVesselContentFromJson(Map json) => AttrVesselContent(
      vesselType:
          $enumDecodeNullable(_$VesselTypeEnumEnumMap, json['vesselType']),
      vesselWidget: json['vesselWidget'] == null
          ? null
          : AttrVesselWidget.fromJson(
              Map<String, dynamic>.from(json['vesselWidget'] as Map)),
    );

Map<String, dynamic> _$AttrVesselContentToJson(AttrVesselContent instance) =>
    <String, dynamic>{
      'vesselType': _$VesselTypeEnumEnumMap[instance.vesselType],
      'vesselWidget': instance.vesselWidget?.toJson(),
    };

const _$VesselTypeEnumEnumMap = {
  VesselTypeEnum.floor: 0,
  VesselTypeEnum.persistent: 1,
};

AttrVesselWidget _$AttrVesselWidgetFromJson(Map json) => AttrVesselWidget(
      json['currentStatusId'] as String?,
      json['name'] as String?,
      (json['statusWarehouseCollection'] as List<dynamic>?)
          ?.map((e) =>
              AttrStatusWareHouse.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$AttrVesselWidgetToJson(AttrVesselWidget instance) =>
    <String, dynamic>{
      'currentStatusId': instance.currentStatusId,
      'name': instance.name,
      'statusWarehouseCollection':
          instance.statusWarehouseCollection?.map((e) => e.toJson()).toList(),
    };

AttrStatusWareHouse _$AttrStatusWareHouseFromJson(Map json) =>
    AttrStatusWareHouse(
      $enumDecodeNullable(_$VesselModeEnumEnumMap, json['modeSelect']),
      json['name'] as String?,
      json['uniqueId'] as String?,
      AttrStatusWareHouse._widgetListFromJson(json['widgetList'] as List?),
    );

Map<String, dynamic> _$AttrStatusWareHouseToJson(
        AttrStatusWareHouse instance) =>
    <String, dynamic>{
      'modeSelect': _$VesselModeEnumEnumMap[instance.modeSelect],
      'name': instance.name,
      'uniqueId': instance.uniqueId,
      'widgetList': AttrStatusWareHouse._widgetListToJson(instance.widgetList),
    };

const _$VesselModeEnumEnumMap = {
  VesselModeEnum.freeDragMode: 'freeDragMode',
};
