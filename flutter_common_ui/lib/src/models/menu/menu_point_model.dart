import 'package:json_annotation/json_annotation.dart';
part 'menu_point_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuPointModel {
  final List<MenuPointItemModel>? menuItemList;
  MenuPointModel({this.menuItemList});
  factory MenuPointModel.fromJson(Map<String, dynamic> json) =>
      _$MenuPointModelFromJson(json);
  Map<String, dynamic> toJson() => _$MenuPointModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuPointItemModel {
  final bool? isShowDot;
  final String? itemName;
  MenuPointItemModel({this.isShowDot, this.itemName});
  factory MenuPointItemModel.fromJson(Map<String, dynamic> json) =>
      _$MenuPointItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$MenuPointItemModelToJson(this);
}
