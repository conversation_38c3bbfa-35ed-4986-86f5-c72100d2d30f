// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menu_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MenuModel _$MenuModelFromJson(Map json) => MenuModel(
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      attr: json['attr'] == null
          ? null
          : MenuAttr.fromJson(Map<String, dynamic>.from(json['attr'] as Map)),
    );

Map<String, dynamic> _$MenuModelToJson(MenuModel instance) => <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

MenuAttr _$MenuAttrFromJson(Map json) => MenuAttr(
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      style: json['style'] as num?,
      icon: json['icon'] as String?,
      fontSize: json['fontSize'] as num?,
      fontWeight:
          $enumDecodeNullable(_$TextWeightTypeEnumMap, json['fontWeight']),
      color: json['color'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['color'] as Map)),
      list: (json['list'] as List<dynamic>?)
          ?.map((e) =>
              MenuItemModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$MenuAttrToJson(MenuAttr instance) => <String, dynamic>{
      'trackInfo': instance.trackInfo?.toJson(),
      'style': instance.style,
      'icon': instance.icon,
      'fontSize': instance.fontSize,
      'fontWeight': _$TextWeightTypeEnumMap[instance.fontWeight],
      'color': instance.color?.toJson(),
      'list': instance.list?.map((e) => e.toJson()).toList(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

MenuItemModel _$MenuItemModelFromJson(Map json) => MenuItemModel(
      id: MenuItemModel._menuItemModelFromJson(json['id']),
      icon: json['icon'] as String?,
      name: json['name'] as String?,
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      logic: json['logic'] as int?,
      interfaceType: json['interfaceType'] as int?,
      dataSource: json['dataSource'] as int?,
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      cornerMark: json['cornerMark'] == null
          ? null
          : AttrCornerMark.fromJson(
              Map<String, dynamic>.from(json['cornerMark'] as Map)),
      materialName: json['materialName'] as String?,
      isShowDot: json['isShowDot'] as bool?,
      materialTrack: json['materialTrack'] as String?,
    );

Map<String, dynamic> _$MenuItemModelToJson(MenuItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'icon': instance.icon,
      'name': instance.name,
      'customfeature': instance.customFeature?.toJson(),
      'logic': instance.logic,
      'interfaceType': instance.interfaceType,
      'dataSource': instance.dataSource,
      'trackInfo': instance.trackInfo?.toJson(),
      'cornerMark': instance.cornerMark?.toJson(),
      'materialName': instance.materialName,
      'isShowDot': instance.isShowDot,
      'materialTrack': instance.materialTrack,
    };
