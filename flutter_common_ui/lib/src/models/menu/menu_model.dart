import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'menu_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuModel extends BaseModel {
  /// 组件自己的属性
  @override
  final MenuAttr? attr;

  /// 组件自己的样式
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[PictureModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [customfeature] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  MenuModel({
    String? alias,
    num? aliasCode,
    String? aliasName,
    String? aliasUnit,
    String? group,
    this.type,
    this.style,
    this.customFeature,
    this.attr,
  }) : super(
            type: type,
            alias: alias,
            aliasCode: aliasCode,
            aliasName: aliasName,
            aliasUnit: aliasUnit,
            group: group,
            style: style,
            customFeature: customFeature,
            attr: attr);
  factory MenuModel.fromJson(Map<String, dynamic> json) =>
      _$MenuModelFromJson(json);

  Map<String, dynamic> toJson() => _$MenuModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuAttr extends Attr {
  /// 埋点信息
  TrackInfoModel? trackInfo;

  /// 图标
  num? style;

  /// 菜单图标
  String? icon;

  /// 菜单项名称字号
  num? fontSize;

  /// 菜单项名称字重
  TextWeightType? fontWeight;

  /// 菜单项名称颜色
  ColorModel? color;

  /// 菜单项
  List<MenuItemModel>? list;

  MenuAttr(
      {this.trackInfo,
      this.style,
      this.icon,
      this.fontSize,
      this.fontWeight,
      this.color,
      this.list});
  factory MenuAttr.fromJson(Map<String, dynamic> json) =>
      _$MenuAttrFromJson(json);
  Map<String, dynamic> toJson() => _$MenuAttrToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class MenuItemModel {
  /// 每一菜单项的唯一标识，使用时间戳
  @JsonKey(
    fromJson: _menuItemModelFromJson,
  )
  String? id;

  /// 图标
  String? icon;

  /// 菜单名称
  String? name;

  /// 绑定事件
  /// [customFeature] 每个卡片的事件，会被赋值当接口返回了数据
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  /// 展示逻辑 角标类型为红点时该字段生效
  int? logic;

  /**
   * 接口类型
   * 展示逻辑选逻辑2时该字段生效
   * 1: APP缓存接口 2: 云端接口
   */
  int? interfaceType;
  /**
   * 数据源
   * 展示逻辑选逻辑2时该字段生效
   * 当接口类型为 APP缓存接口时，选项只有 1:扫一扫引导页
   * 当接口类型为 云端服务接口时，选项只有 2:售后评价
   * 1: 扫一扫引导页 2:售后评价 
   */
  // 1: 扫一扫引导页   2: 售后评价  3: 消息中心未读提示
  int? dataSource;

  // 埋点信息
  TrackInfoModel? trackInfo;

  /// [cornerMark]角标类型 0: 无 1: 红点 2: 动态文字
  AttrCornerMark? cornerMark;

  /// [materialName]业务库增加 物料名称
  String? materialName;

  /// [isShowDot]业务库增加 小红点是否显示
  bool? isShowDot;

  /// [materialTrack]二级页埋点信息（物料库配置）
  String? materialTrack;

  MenuItemModel(
      {this.id,
      this.icon,
      this.name,
      this.customFeature,
      this.logic,
      this.interfaceType,
      this.dataSource,
      this.trackInfo,
      this.cornerMark,
      this.materialName,
      this.isShowDot,
      this.materialTrack});
  factory MenuItemModel.fromJson(Map<String, dynamic> json) =>
      _$MenuItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$MenuItemModelToJson(this);

  static String? _menuItemModelFromJson(dynamic id) {
    return id.toString();
  }

  // MenuItemModel _menuItemModelFromJson(Map<String, dynamic> json) {
  //   return MenuItemModel(
  //     id: json['id'] as String?,
  //     icon: json['icon'] as String?,
  //     name: json['name'] as String?,
  //     customFeature: json['customfeature'] == null
  //         ? null
  //         : CustomFeatureModel.fromJson(
  //             Map<String, dynamic>.from(json['customfeature'] as Map)),
  //     logic: json['logic'] as int?,
  //     interfaceType: json['interfaceType'] as int?,
  //     dataSource: json['dataSource'] as int?,
  //     trackInfo: json['trackInfo'] == null
  //         ? null
  //         : TrackInfoModel.fromJson(
  //             Map<String, dynamic>.from(json['trackInfo'] as Map)),
  //     cornerMark: json['cornerMark'] == null
  //         ? null
  //         : AttrCornerMark.fromJson(
  //             Map<String, dynamic>.from(json['cornerMark'] as Map)),
  //   );
  // }
}
