// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menu_point_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MenuPointModel _$MenuPointModelFromJson(Map json) => MenuPointModel(
      menuItemList: (json['menuItemList'] as List<dynamic>?)
          ?.map((e) =>
              MenuPointItemModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$MenuPointModelToJson(MenuPointModel instance) =>
    <String, dynamic>{
      'menuItemList': instance.menuItemList?.map((e) => e.toJson()).toList(),
    };

MenuPointItemModel _$MenuPointItemModelFromJson(Map json) => MenuPointItemModel(
      isShowDot: json['isShowDot'] as bool?,
      itemName: json['itemName'] as String?,
    );

Map<String, dynamic> _$MenuPointItemModelToJson(MenuPointItemModel instance) =>
    <String, dynamic>{
      'isShowDot': instance.isShowDot,
      'itemName': instance.itemName,
    };
