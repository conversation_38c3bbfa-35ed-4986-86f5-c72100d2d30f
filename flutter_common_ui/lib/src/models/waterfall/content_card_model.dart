import 'common_models.dart';

// 内容卡片类型

class ContentCardModel {
  String? title; // 内容标题
  String? createDate; // 发布时间
  String? contentId; //内容id
  List<String>? coverUrls; // 封面图
  num? likeCount; // 点赞数

  String? contentType; //内容类型（article=图文；picture=图片；video=小视频 ; live=直播）
  List<String>? imageUrls; //内容图片
  String? videoUrl; // 视频链接
  ContentCardUser? user; // 作者信息
  bool? likeFlag; // 是否点赞

  String? liveId; //直播内容id
  String? liveStatus; // 直播内容状态（1. 直播中 、2预告 、3回放、4已结束）
  // List<num> sort; // 非精选tab下拉刷新需要传递的参数
  List<ContentCardTopic>? topics; // 话题
  String? cityCode; // 城市编码

  String? cityName; // 城市名
  num? commentCount; // 评论数
  num? favoriteCount; // 收藏数
  String? classCode; // 频道分类编码
  // num liveViewersNum; // 直播观看人数

  // num recommendFrom; // 推荐渠道（1来自推荐 2来自运营）
  String? coverUrlInfo; // 封面图宽高信息
  String? imageUrlInfo; // 内容图宽高信息
  // ContentCardGoods goods; // 商品
  // ContentCardScenes scenes; // 场景

  num? coverShowType; // 文章类型封面图显示方式：1、单小图 2、三联图 3、无封面（这种不特殊处理，后端不应返回） 4、单大图
  String? productBrand; // 产品品牌 (常见故障类型使用)
  String? productCategoryCode; // 产品大类code
  String? productCategoryName; // 产品大类名称 (常见故障类型使用)
  String? contentIdBeEncrypted; //DES加密后的内容id

  String? jumpUrl; //跳转url

  ContentCardModel.fromMap(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    likeCount = safeGet<num>(json['likeCount']);
    title = safeGet<String>(json['title']);
    createDate = safeGet<String>(json['createDate']);
    contentId = safeGet<String>(json['contentId']);
    contentType = safeGet<String>(json['contentType']);
    if (json['coverUrls'] != null) {
      coverUrls = <String>[];
      safeGet<List<String>>(json['coverUrls'])?.forEach((String v) {
        coverUrls!.add(v);
      });
    }
    if (json['imageUrls'] != null) {
      imageUrls = <String>[];
      safeGet<List<String>>(json['imageUrls'])?.forEach((String v) {
        imageUrls!.add(v);
      });
    }
    videoUrl = safeGet<String>(json['videoUrl']);
    user = ContentCardUser.fromMap(safeGet<Map<String, dynamic>>(json['user']));
    likeFlag = safeGet<bool>(json['likeFlag']);
    liveId = safeGet<String>(json['liveId']);
    liveStatus = safeGet<String>(json['liveStatus']);
    if (json['topics'] != null) {
      this.topics = <ContentCardTopic>[];
      safeGet<List<dynamic>>(json['topics'])?.forEach((dynamic v) {
        topics!.add(ContentCardTopic.fromMap(v as Map<String, dynamic>));
      });
    }
    cityCode = safeGet<String>(json['cityCode']);
    cityName = safeGet<String>(json['cityName']);
    commentCount = safeGet<num>(json['commentCount']);
    favoriteCount = safeGet<num>(json['favoriteCount']);
    classCode = safeGet<String>(json['classCode']);
    coverUrlInfo = safeGet<String>(json['coverUrlInfo']);
    imageUrlInfo = safeGet<String>(json['imageUrlInfo']);
    coverShowType = safeGet<num>(json['coverShowType']);
    productBrand = safeGet<String>(json['productBrand']);
    productCategoryCode = safeGet<String>(json['productCategoryCode']);
    productCategoryName = safeGet<String>(json['productCategoryName']);
    contentIdBeEncrypted = safeGet<String>(json['contentIdBeEncrypted']);
    jumpUrl = safeGet<String>(json['jumpUrl']);
  }

  ContentCardModel.fromJson(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    likeCount = safeGet<num>(json['likeCount']);
    title = safeGet<String>(json['title']);
    createDate = safeGet<String>(json['createDate']);
    contentId = safeGet<String>(json['contentId']);
    contentType = safeGet<String>(json['contentType']);
    if (json['coverUrls'] != null) {
      coverUrls = <String>[];
      safeGet<List<String>>(json['coverUrls'])?.forEach((String v) {
        coverUrls!.add(v);
      });
    }
    if (json['imageUrls'] != null) {
      imageUrls = <String>[];
      safeGet<List<String>>(json['imageUrls'])?.forEach((String v) {
        imageUrls!.add(v);
      });
    }
    videoUrl = safeGet<String>(json['videoUrl']);
    user = ContentCardUser.fromMap(safeGet<Map<String, dynamic>>(json['user']));
    likeFlag = safeGet<bool>(json['likeFlag']);
    liveId = safeGet<String>(json['liveId']);
    liveStatus = safeGet<String>(json['liveStatus']);
    if (json['topics'] != null) {
      this.topics = <ContentCardTopic>[];
      safeGet<List<dynamic>>(json['topics'])?.forEach((dynamic v) {
        topics!.add(ContentCardTopic.fromMap(v as Map<String, dynamic>));
      });
    }
    cityCode = safeGet<String>(json['cityCode']);
    cityName = safeGet<String>(json['cityName']);
    commentCount = safeGet<num>(json['commentCount']);
    favoriteCount = safeGet<num>(json['favoriteCount']);
    classCode = safeGet<String>(json['classCode']);
    coverUrlInfo = safeGet<String>(json['liveStatus']);
    imageUrlInfo = safeGet<String>(json['imageUrlInfo']);
    coverShowType = safeGet<num>(json['coverShowType']);
    productBrand = safeGet<String>(json['productBrand']);
    productCategoryCode = safeGet<String>(json['productCategoryCode']);
    productCategoryName = safeGet<String>(json['productCategoryName']);
    contentIdBeEncrypted = safeGet<String>(json['contentIdBeEncrypted']);
    jumpUrl = safeGet<String>(json['jumpUrl']);
  }

  // jsonDecode(jsonStr) 方法中会调用实体类的这个方法。如果实体类中没有这个方法，会报错。
  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['title'] = this.title;
    map['createDate'] = this.createDate;
    map['contentId'] = this.contentId;
    map['coverUrls'] = this.coverUrls;
    map['likeCount'] = this.likeCount;

    map['contentType'] = this.contentType;
    map['imageUrls'] = this.imageUrls;
    map['videoUrl'] = this.videoUrl;
    map['user'] = this.user;
    map['likeFlag'] = this.likeFlag;

    map['liveId'] = this.liveId;
    map['liveStatus'] = this.liveStatus;
    map['topics'] = this.topics;
    map['cityCode'] = this.cityCode;

    map['cityName'] = this.cityName;
    map['commentCount'] = this.commentCount;
    map['favoriteCount'] = this.favoriteCount;
    map['classCode'] = this.classCode;

    map['coverUrlInfo'] = this.coverUrlInfo;
    map['imageUrlInfo'] = this.imageUrlInfo;

    map['coverShowType'] = this.coverShowType;
    map['productBrand'] = this.productBrand;
    map['productCategoryCode'] = this.productCategoryCode;
    map['productCategoryName'] = this.productCategoryName;
    map['contentIdBeEncrypted'] = this.contentIdBeEncrypted;

    map['jumpUrl'] = this.jumpUrl;
    return map;
  }
}
