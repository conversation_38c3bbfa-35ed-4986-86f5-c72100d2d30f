// 内容卡片中的用户类型

import 'package:flutter_common_ui/src/utils/home_common_util.dart';

final safeGet = CommonUtil.safeGet;

class ContentCardUser {
  bool? followFlag; // 是否关注
  String? icon; //用户头像
  String? nickname; //用户昵称
  String? userId; // 用户id
  String? idsUserId;
  num? userType; // 用户类型 1：UGC 2 PGC 3:智家号
  List<String>? userLable; // 用户标签，如优秀创作者

  ContentCardUser.fromMap(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    followFlag = safeGet<bool>(json['followFlag']);
    icon = safeGet<String>(json['icon']);
    nickname = safeGet<String>(json['nickname']);
    userId = safeGet<String>(json['userId']);
    idsUserId = safeGet<String>(json['idsUserId']);
    userType = safeGet<num>(json['userType']);
    if (json['userLable'] != null) {
      userLable = <String>[];
      safeGet<List<String>>(json['userLable'])?.forEach((String v) {
        userLable!.add(v);
      });
    }
  }

  ContentCardUser.fromJson(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    followFlag = safeGet<bool>(json['followFlag']);
    icon = safeGet<String>(json['icon']);
    nickname = safeGet<String>(json['nickname']);
    userId = safeGet<String>(json['userId']);
    idsUserId = safeGet<String>(json['idsUserId']);
    userType = safeGet<num>(json['userType']);
    if (json['userLable'] != null) {
      userLable = <String>[];
      safeGet<List<String>>(json['userLable'])?.forEach((String v) {
        userLable!.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['followFlag'] = followFlag;
    map['icon'] = icon;
    map['nickname'] = nickname;
    map['userId'] = userId;
    map['idsUserId'] = idsUserId;
    map['userType'] = userType;
    map['userLable'] = userLable;
    return map;
  }
}

// 频道标签类型
class WaterfallSecondTabBar {
  String? code;
  String? image;
  String? name;

  WaterfallSecondTabBar.fromMap(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    code = safeGet<String>(json['code']);
    name = safeGet<String>(json['name']);
    image = safeGet<String>(json['image']);
  }

  WaterfallSecondTabBar.fromJson(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    code = safeGet<String>(json['code']);
    name = safeGet<String>(json['name']);
    image = safeGet<String>(json['image']);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['code'] = code;
    map['image'] = image;
    map['name'] = name;
    return map;
  }

  WaterfallSecondTabBar({this.code, this.image, this.name});

  @override
  String toString() {
    return 'ChannelLabel{code: $code, image: $image, name: $name}';
  }
}

// 内容卡片中的话题类型
class ContentCardTopic {
  num? id;
  String? name;

  ContentCardTopic.fromMap(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    id = safeGet<num>(json['id']);
    name = safeGet<String>(json['name']);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['id'] = id;
    map['name'] = name;
    return map;
  }

  @override
  String toString() {
    return 'ContentCardTopic{id: $id, name: $name}';
  }
}

// 内容卡片中的商品类型
class ContentCardGoods {
  String? goodsName; // 商品名称
  String? goodsUrl; // 商品跳转url
  String? goodsId; // 商品id
  String? goodsPicUrl; // 商品图片url
  String? goodsDesc; // 商品描述
  String? price; // 商品价钱

  ContentCardGoods(
      {this.goodsName,
      this.goodsUrl,
      this.goodsId,
      this.goodsPicUrl,
      this.goodsDesc,
      this.price});

  ContentCardGoods.fromMap(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    goodsName = safeGet<String>(json['goodsName']);
    goodsUrl = safeGet<String>(json['goodsUrl']);
    goodsId = safeGet<String>(json['goodsId']);
    goodsPicUrl = safeGet<String>(json['goodsPicUrl']);
    goodsDesc = safeGet<String>(json['goodsDesc']);
    price = safeGet<String>(json['price']);
  }

  ContentCardGoods.fromJson(Map<String, dynamic>? json) {
    if (json == null || json is! Map) {
      return;
    }
    goodsName = safeGet<String>(json['goodsName']);
    goodsUrl = safeGet<String>(json['goodsUrl']);
    goodsId = safeGet<String>(json['goodsId']);
    goodsPicUrl = safeGet<String>(json['goodsPicUrl']);
    goodsDesc = safeGet<String>(json['goodsDesc']);
    price = safeGet<String>(json['price']);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['goodsName'] = goodsName;
    map['goodsUrl'] = goodsUrl;
    map['goodsId'] = goodsId;
    map['goodsPicUrl'] = goodsPicUrl;
    map['goodsDesc'] = goodsDesc;
    map['price'] = price;
    return map;
  }

  @override
  String toString() {
    return 'ContentCardGoods{goodsName: $goodsName, goodsUrl: $goodsUrl, goodsId: $goodsId, goodsPicUrl: $goodsPicUrl, goodsDesc: $goodsDesc, price: $price}';
  }
}

// 内容卡片中的场景类型
class ContentCardScenes {
  String? sceneName; //场景名称
  String? sceneUrl; //场景跳转连接
  String? scenePicUrl; //场景图片url
  String? sceneDesc; //场景描述

  ContentCardScenes.fromMap(Map<String, dynamic> json) {
    if (json is! Map) {
      return;
    }
    sceneName = safeGet<String>(json['sceneName']);
    sceneUrl = safeGet<String>(json['sceneUrl']);
    scenePicUrl = safeGet<String>(json['scenePicUrl']);
    sceneDesc = safeGet<String>(json['sceneDesc']);
  }

  ContentCardScenes.fromJson(Map<String, dynamic> json) {
    if (json is! Map) {
      return;
    }
    sceneName = safeGet<String>(json['sceneName']);
    sceneUrl = safeGet<String>(json['sceneUrl']);
    scenePicUrl = safeGet<String>(json['scenePicUrl']);
    sceneDesc = safeGet<String>(json['sceneDesc']);
  }

  Map<String, dynamic> toJson() {
    Map<String, dynamic> map = <String, dynamic>{};
    map['sceneName'] = sceneName;
    map['sceneUrl'] = sceneUrl;
    map['scenePicUrl'] = scenePicUrl;
    map['sceneDesc'] = sceneDesc;
    return map;
  }

  ContentCardScenes(
      {this.sceneName, this.sceneUrl, this.scenePicUrl, this.sceneDesc});

  @override
  String toString() {
    return 'ContentCardScenes{sceneName: $sceneName, sceneUrl: $sceneUrl, scenePicUrl: $scenePicUrl, sceneDesc: $sceneDesc}';
  }
}
