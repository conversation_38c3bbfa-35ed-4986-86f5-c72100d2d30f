/*
 * @Author: ma<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-15 17:20:15
 * @description: 分类目录可视化数据模型
 */
import 'package:flutter_common_ui/src/models/common_models/background_model.dart';
import 'package:flutter_common_ui/src/models/common_models/color_model.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/padding_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/models/text/text_manage_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'category_model.g.dart';

/// 分类目录组件的信息
@JsonSerializable(explicitToJson: true, anyMap: true)
class CategoryModel {
  /// 属性
  CatrgoryAttrInfo? attr;

  /// 组件样式
  final StyleInfoModel? style;

  /// [customfeature] 绑定事件
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customfeature;

  CategoryModel({this.style, this.customfeature});

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);
}

/// 分类目录属性的信息
@JsonSerializable(explicitToJson: true, anyMap: true)
class CatrgoryAttrInfo {
  /// [trackInfo] 埋点信息
  TrackInfoModel? trackInfo;

  /// [cateStyle] 切换样式 -- 滚动scroll ， 横向切换 horizontal
  final String? cateStyle;

  /// [navBar] 导航栏
  NavBarInfo? navBar;

  /// [defaultSelect] 默认分类选中项(设为默认)默认为 0，表示第一个
  final int? defaultSelect;

  /// [sticky] 是否吸顶
  final bool? sticky;

  /// [spacing] 分类名称间距
  final double? spacing;

  /// [padding] 内边距
  PaddingModel? padding;

  /// [categoryList] 包含的分类数据
  List<CategoryListInfo>? categoryList;

  CatrgoryAttrInfo(
      {this.trackInfo,
      this.cateStyle,
      this.defaultSelect,
      this.spacing,
      this.sticky});
  factory CatrgoryAttrInfo.fromJson(Map<String, dynamic> json) =>
      _$CatrgoryAttrInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CatrgoryAttrInfoToJson(this);
}

/// 导航栏的信息
@JsonSerializable(explicitToJson: true, anyMap: true)
class NavBarInfo {
  /// 导航栏布局  left左对齐 center居中等分
  final String? display;

  /// [height] 导航栏高度
  double? height;

  /// [selectFontSize] 导航栏选中文字大小
  double? selectFontSize;

  /// [selectFontWeight] 导航栏选中文字字重
  TextWeightType? selectFontWeight;

  /// [selectFontColor] 导航栏默认选中文字颜色
  ColorModel? selectFontColor;

  /// [defaultFontSize] 导航栏默认文字大小
  double? defaultFontSize;

  /// [defaultFontWeight] 导航栏默认文字字重
  TextWeightType? defaultFontWeight;

  /// [defaultFontColor] 导航栏默认文字颜色
  ColorModel? defaultFontColor;

  /// [navRowType] 导航条类型
  BackgroundModel? navRowType;

  /// [navRowRadius] 导航条圆角
  RadiusModel? navRowRadius;

  /// [navRowShadow] 导航条阴影
  ShadowModel? navRowShadow;
  NavBarInfo(
      {this.display,
      this.height,
      this.selectFontSize,
      this.selectFontWeight,
      this.defaultFontColor,
      this.defaultFontSize,
      this.defaultFontWeight,
      this.navRowRadius,
      this.navRowShadow,
      this.navRowType,
      this.selectFontColor});
  factory NavBarInfo.fromJson(Map<String, dynamic> json) =>
      _$NavBarInfoFromJson(json);

  Map<String, dynamic> toJson() => _$NavBarInfoToJson(this);
}

/// 包含的分类数据
@JsonSerializable(explicitToJson: true, anyMap: true)
class CategoryListInfo {
  /// [cateName] 标题
  final String? cateName;

  /// [defaultImgSrc] 每个分类默认图片地址
  final String? defaultImgSrc;

  /// [selectedImgSrc] 分类点击后切换的图片地址
  final String? selectedImgSrc;

  /// [vesselWidget] 是数组，里面放的是分类一到四的数据
  /// 分类一数据，后面的分类数据以此类推
  VesselWidgetInfo? vesselWidget;
  CategoryListInfo({this.cateName, this.defaultImgSrc, this.selectedImgSrc});

  factory CategoryListInfo.fromJson(Map<String, dynamic> json) =>
      _$CategoryListInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryListInfoToJson(this);
}

/// 分类一的数据
@JsonSerializable(explicitToJson: true, anyMap: true)
class VesselWidgetInfo {
  /// [trackInfo] 埋点信息（分类目录具体某个分类埋点信息）
  TrackInfoModel? trackInfo;

  /// [autoWidget]  具体信息
  AutoWidgetInfo? autoWidget;

  /// [type] 分类容器的类型
  final String? type;

  VesselWidgetInfo({this.trackInfo, this.autoWidget, this.type});

  factory VesselWidgetInfo.fromJson(Map<String, dynamic> json) =>
      _$VesselWidgetInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VesselWidgetInfoToJson(this);
}

/// 分类一的数据
@JsonSerializable(explicitToJson: true, anyMap: true)
class AutoWidgetInfo {
  /// [aliasCode] 分类id
  final num? aliasCode;

  /// [style] 分类一的样式
  StyleInfoModel? style;

  /// [content] 内容
  ContentInfo? content;

  AutoWidgetInfo({this.aliasCode, this.style, this.content});

  factory AutoWidgetInfo.fromJson(Map<String, dynamic> json) =>
      _$AutoWidgetInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AutoWidgetInfoToJson(this);
}

/// content内容
@JsonSerializable(explicitToJson: true, anyMap: true)
class ContentInfo {
  /// [style] 分类一的样式
  VesselWidgetDetail? vesselWidget;

  ContentInfo({this.vesselWidget});

  factory ContentInfo.fromJson(Map<String, dynamic> json) =>
      _$ContentInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ContentInfoToJson(this);
}

/// VesselWidgetDetail
@JsonSerializable(explicitToJson: true, anyMap: true)
class VesselWidgetDetail {
  /// 当前的状态id
  final String? currentStatusId;

  /// 名称
  final String? name;

  /// 实际取这里的数据
  List<StatusWarehouseCollection>? statusWarehouseCollection;

  VesselWidgetDetail(
      {this.currentStatusId, this.name, this.statusWarehouseCollection});

  factory VesselWidgetDetail.fromJson(Map<String, dynamic> json) =>
      _$VesselWidgetDetailFromJson(json);

  Map<String, dynamic> toJson() => _$VesselWidgetDetailToJson(this);
}

/// StatusWarehouseCollection
@JsonSerializable(explicitToJson: true, anyMap: true)
class StatusWarehouseCollection {
  /// 说明是freeDragMode自由拖拽模式还是floorPileMode楼层堆叠模式
  /// 定位方法会不一样
  final String? modeSelect;

  /// 分类目录下的动态容器的名字
  final String? name;

  /// 唯一标识
  final String? uniqueId;

  List<PictureModel>? widgetList;

  StatusWarehouseCollection(
      {this.modeSelect, this.name, this.uniqueId, this.widgetList});

  factory StatusWarehouseCollection.fromJson(Map<String, dynamic> json) =>
      _$StatusWarehouseCollectionFromJson(json);

  Map<String, dynamic> toJson() => _$StatusWarehouseCollectionToJson(this);
}
