// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryModel _$CategoryModelFromJson(Map json) => CategoryModel(
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customfeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
    )..attr = json['attr'] == null
        ? null
        : CatrgoryAttrInfo.fromJson(
            Map<String, dynamic>.from(json['attr'] as Map));

Map<String, dynamic> _$CategoryModelToJson(CategoryModel instance) =>
    <String, dynamic>{
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'customfeature': instance.customfeature?.toJson(),
    };

CatrgoryAttrInfo _$CatrgoryAttrInfoFromJson(Map json) => CatrgoryAttrInfo(
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      cateStyle: json['cateStyle'] as String?,
      defaultSelect: json['defaultSelect'] as int?,
      spacing: (json['spacing'] as num?)?.toDouble(),
      sticky: json['sticky'] as bool?,
    )
      ..navBar = json['navBar'] == null
          ? null
          : NavBarInfo.fromJson(
              Map<String, dynamic>.from(json['navBar'] as Map))
      ..padding = json['padding'] == null
          ? null
          : PaddingModel.fromJson(
              Map<String, dynamic>.from(json['padding'] as Map))
      ..categoryList = (json['categoryList'] as List<dynamic>?)
          ?.map((e) =>
              CategoryListInfo.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList();

Map<String, dynamic> _$CatrgoryAttrInfoToJson(CatrgoryAttrInfo instance) =>
    <String, dynamic>{
      'trackInfo': instance.trackInfo?.toJson(),
      'cateStyle': instance.cateStyle,
      'navBar': instance.navBar?.toJson(),
      'defaultSelect': instance.defaultSelect,
      'sticky': instance.sticky,
      'spacing': instance.spacing,
      'padding': instance.padding?.toJson(),
      'categoryList': instance.categoryList?.map((e) => e.toJson()).toList(),
    };

NavBarInfo _$NavBarInfoFromJson(Map json) => NavBarInfo(
      display: json['display'] as String?,
      height: (json['height'] as num?)?.toDouble(),
      selectFontSize: (json['selectFontSize'] as num?)?.toDouble(),
      selectFontWeight: $enumDecodeNullable(
          _$TextWeightTypeEnumMap, json['selectFontWeight']),
      defaultFontColor: json['defaultFontColor'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['defaultFontColor'] as Map)),
      defaultFontSize: (json['defaultFontSize'] as num?)?.toDouble(),
      defaultFontWeight: $enumDecodeNullable(
          _$TextWeightTypeEnumMap, json['defaultFontWeight']),
      navRowRadius: json['navRowRadius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['navRowRadius'] as Map)),
      navRowShadow: json['navRowShadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['navRowShadow'] as Map)),
      navRowType: json['navRowType'] == null
          ? null
          : BackgroundModel.fromJson(
              Map<String, dynamic>.from(json['navRowType'] as Map)),
      selectFontColor: json['selectFontColor'] == null
          ? null
          : ColorModel.fromJson(
              Map<String, dynamic>.from(json['selectFontColor'] as Map)),
    );

Map<String, dynamic> _$NavBarInfoToJson(NavBarInfo instance) =>
    <String, dynamic>{
      'display': instance.display,
      'height': instance.height,
      'selectFontSize': instance.selectFontSize,
      'selectFontWeight': _$TextWeightTypeEnumMap[instance.selectFontWeight],
      'selectFontColor': instance.selectFontColor?.toJson(),
      'defaultFontSize': instance.defaultFontSize,
      'defaultFontWeight': _$TextWeightTypeEnumMap[instance.defaultFontWeight],
      'defaultFontColor': instance.defaultFontColor?.toJson(),
      'navRowType': instance.navRowType?.toJson(),
      'navRowRadius': instance.navRowRadius?.toJson(),
      'navRowShadow': instance.navRowShadow?.toJson(),
    };

const _$TextWeightTypeEnumMap = {
  TextWeightType.normalWeight: 400,
  TextWeightType.boldWeight: 500,
  TextWeightType.bolderWeight: 600,
};

CategoryListInfo _$CategoryListInfoFromJson(Map json) => CategoryListInfo(
      cateName: json['cateName'] as String?,
      defaultImgSrc: json['defaultImgSrc'] as String?,
      selectedImgSrc: json['selectedImgSrc'] as String?,
    )..vesselWidget = json['vesselWidget'] == null
        ? null
        : VesselWidgetInfo.fromJson(
            Map<String, dynamic>.from(json['vesselWidget'] as Map));

Map<String, dynamic> _$CategoryListInfoToJson(CategoryListInfo instance) =>
    <String, dynamic>{
      'cateName': instance.cateName,
      'defaultImgSrc': instance.defaultImgSrc,
      'selectedImgSrc': instance.selectedImgSrc,
      'vesselWidget': instance.vesselWidget?.toJson(),
    };

VesselWidgetInfo _$VesselWidgetInfoFromJson(Map json) => VesselWidgetInfo(
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      autoWidget: json['autoWidget'] == null
          ? null
          : AutoWidgetInfo.fromJson(
              Map<String, dynamic>.from(json['autoWidget'] as Map)),
      type: json['type'] as String?,
    );

Map<String, dynamic> _$VesselWidgetInfoToJson(VesselWidgetInfo instance) =>
    <String, dynamic>{
      'trackInfo': instance.trackInfo?.toJson(),
      'autoWidget': instance.autoWidget?.toJson(),
      'type': instance.type,
    };

AutoWidgetInfo _$AutoWidgetInfoFromJson(Map json) => AutoWidgetInfo(
      aliasCode: json['aliasCode'] as num?,
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      content: json['content'] == null
          ? null
          : ContentInfo.fromJson(
              Map<String, dynamic>.from(json['content'] as Map)),
    );

Map<String, dynamic> _$AutoWidgetInfoToJson(AutoWidgetInfo instance) =>
    <String, dynamic>{
      'aliasCode': instance.aliasCode,
      'style': instance.style?.toJson(),
      'content': instance.content?.toJson(),
    };

ContentInfo _$ContentInfoFromJson(Map json) => ContentInfo(
      vesselWidget: json['vesselWidget'] == null
          ? null
          : VesselWidgetDetail.fromJson(
              Map<String, dynamic>.from(json['vesselWidget'] as Map)),
    );

Map<String, dynamic> _$ContentInfoToJson(ContentInfo instance) =>
    <String, dynamic>{
      'vesselWidget': instance.vesselWidget?.toJson(),
    };

VesselWidgetDetail _$VesselWidgetDetailFromJson(Map json) => VesselWidgetDetail(
      currentStatusId: json['currentStatusId'] as String?,
      name: json['name'] as String?,
      statusWarehouseCollection:
          (json['statusWarehouseCollection'] as List<dynamic>?)
              ?.map((e) => StatusWarehouseCollection.fromJson(
                  Map<String, dynamic>.from(e as Map)))
              .toList(),
    );

Map<String, dynamic> _$VesselWidgetDetailToJson(VesselWidgetDetail instance) =>
    <String, dynamic>{
      'currentStatusId': instance.currentStatusId,
      'name': instance.name,
      'statusWarehouseCollection':
          instance.statusWarehouseCollection?.map((e) => e.toJson()).toList(),
    };

StatusWarehouseCollection _$StatusWarehouseCollectionFromJson(Map json) =>
    StatusWarehouseCollection(
      modeSelect: json['modeSelect'] as String?,
      name: json['name'] as String?,
      uniqueId: json['uniqueId'] as String?,
      widgetList: (json['widgetList'] as List<dynamic>?)
          ?.map(
              (e) => PictureModel.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
    );

Map<String, dynamic> _$StatusWarehouseCollectionToJson(
        StatusWarehouseCollection instance) =>
    <String, dynamic>{
      'modeSelect': instance.modeSelect,
      'name': instance.name,
      'uniqueId': instance.uniqueId,
      'widgetList': instance.widgetList?.map((e) => e.toJson()).toList(),
    };
