// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'carousel_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CarouselModel _$CarouselModelFromJson(Map json) => CarouselModel(
      type: $enumDecodeNullable(_$ComponentTypeEnumMap, json['type']),
      attr: json['attr'] == null
          ? null
          : CarouselAttr.fromJson(
              Map<String, dynamic>.from(json['attr'] as Map)),
      style: json['style'] == null
          ? null
          : StyleInfoModel.fromJson(
              Map<String, dynamic>.from(json['style'] as Map)),
      customFeature: json['customfeature'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customfeature'] as Map)),
      alias: json['alias'] as String?,
      aliasCode: json['aliasCode'] as num?,
      aliasName: json['aliasName'] as String?,
      aliasUnit: json['aliasUnit'] as String?,
      group: json['group'] as String?,
    );

Map<String, dynamic> _$CarouselModelToJson(CarouselModel instance) =>
    <String, dynamic>{
      'alias': instance.alias,
      'aliasCode': instance.aliasCode,
      'aliasName': instance.aliasName,
      'aliasUnit': instance.aliasUnit,
      'group': instance.group,
      'attr': instance.attr?.toJson(),
      'style': instance.style?.toJson(),
      'type': _$ComponentTypeEnumMap[instance.type],
      'customfeature': instance.customFeature?.toJson(),
    };

const _$ComponentTypeEnumMap = {
  ComponentType.picture: 'picture',
  ComponentType.cardNav: 'cardNav',
  ComponentType.content: 'content',
  ComponentType.carousel: 'carousel',
  ComponentType.category: 'category',
  ComponentType.seniorVessel: 'seniorvessel',
  ComponentType.family: 'family',
  ComponentType.weather: 'weather',
  ComponentType.liveBroadcast: 'live_broadcast',
  ComponentType.msNotify: 'msNotify',
  ComponentType.text: 'text_syn',
  ComponentType.menu: 'menu',
};

CarouselAttr _$CarouselAttrFromJson(Map json) => CarouselAttr(
      style: $enumDecodeNullable(_$CarouselStyleEnumEnumMap, json['style']),
      singleNorms: json['singleNorms'] == null
          ? null
          : AttrSingleCarouselLayout.fromJson(
              Map<String, dynamic>.from(json['singleNorms'] as Map)),
      dataSource: $enumDecodeNullable(
          _$CarouselDataSourceEnumEnumMap, json['dataSource']),
      trackInfo: json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      content: (json['content'] as List<dynamic>?)
          ?.map((e) =>
              AttrCarouseItem.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList(),
      radius: json['radius'] == null
          ? null
          : RadiusModel.fromJson(
              Map<String, dynamic>.from(json['radius'] as Map)),
      shadow: json['shadow'] == null
          ? null
          : ShadowModel.fromJson(
              Map<String, dynamic>.from(json['shadow'] as Map)),
      gutter: (json['gutter'] as num?)?.toDouble(),
      resourceBit: json['resourceBit'] as String?,
      emptyScheme: json['emptyScheme'] == null
          ? null
          : EmptySchemeModel.fromJson(
              Map<String, dynamic>.from(json['emptyScheme'] as Map)),
    );

Map<String, dynamic> _$CarouselAttrToJson(CarouselAttr instance) =>
    <String, dynamic>{
      'style': _$CarouselStyleEnumEnumMap[instance.style],
      'singleNorms': instance.singleNorms?.toJson(),
      'dataSource': _$CarouselDataSourceEnumEnumMap[instance.dataSource],
      'trackInfo': instance.trackInfo?.toJson(),
      'content': instance.content?.map((e) => e.toJson()).toList(),
      'radius': instance.radius?.toJson(),
      'shadow': instance.shadow?.toJson(),
      'gutter': instance.gutter,
      'resourceBit': instance.resourceBit,
      'emptyScheme': instance.emptyScheme?.toJson(),
    };

const _$CarouselStyleEnumEnumMap = {
  CarouselStyleEnum.standard: 1,
  CarouselStyleEnum.multiple: 2,
  CarouselStyleEnum.splash: 3,
};

const _$CarouselDataSourceEnumEnumMap = {
  CarouselDataSourceEnum.custom: 1,
  CarouselDataSourceEnum.recommend: 2,
};

AttrSingleCarouselLayout _$AttrSingleCarouselLayoutFromJson(Map json) =>
    AttrSingleCarouselLayout(
      maxRows: json['maxRows'] as int?,
      rowSize: (json['rowSize'] as num?)?.toDouble(),
      sliderType: $enumDecodeNullable(
          _$CarouselSlideTypeEnumEnumMap, json['sliderType']),
    );

Map<String, dynamic> _$AttrSingleCarouselLayoutToJson(
        AttrSingleCarouselLayout instance) =>
    <String, dynamic>{
      'maxRows': instance.maxRows,
      'rowSize': instance.rowSize,
      'sliderType': _$CarouselSlideTypeEnumEnumMap[instance.sliderType],
    };

const _$CarouselSlideTypeEnumEnumMap = {
  CarouselSlideTypeEnum.scrollHorizontal: 1,
  CarouselSlideTypeEnum.swipe: 2,
};

AttrCarouseItem _$AttrCarouseItemFromJson(Map json) => AttrCarouseItem(
      json['detailsUrl'] as String?,
      (json['entryIntoForceTime'] as List<dynamic>?)
          ?.map((e) => e as int?)
          .toList(),
      json['id'] as int?,
      (json['liveBroadcast'] as List<dynamic>?)?.map((e) => e as int?).toList(),
      json['pictureUrl'] as String?,
      (json['region'] as List<dynamic>?)?.map((e) => e as String?).toList(),
      json['title'] as String?,
      $enumDecodeNullable(_$AttrCarouselTypeEnumEnumMap, json['type']),
      json['shareUrl'] as String?,
      json['planId'] as String?,
      json['materialName'] as String?,
      (json['dataAttributionList'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : AttrDataAttributionModel.fromJson(
                  Map<String, dynamic>.from(e as Map)))
          .toList(),
      json['trackInfo'] == null
          ? null
          : TrackInfoModel.fromJson(
              Map<String, dynamic>.from(json['trackInfo'] as Map)),
      json['customFeatureModel'] == null
          ? null
          : CustomFeatureModel.fromJson(
              Map<String, dynamic>.from(json['customFeatureModel'] as Map)),
      json['attrCornerMark'] == null
          ? null
          : AttrCornerMark.fromJson(
              Map<String, dynamic>.from(json['attrCornerMark'] as Map)),
      json['isShowDot'] as bool?,
    );

Map<String, dynamic> _$AttrCarouseItemToJson(AttrCarouseItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$AttrCarouselTypeEnumEnumMap[instance.type],
      'pictureUrl': instance.pictureUrl,
      'title': instance.title,
      'entryIntoForceTime': instance.entryIntoForceTime,
      'detailsUrl': instance.detailsUrl,
      'liveBroadcast': instance.liveBroadcast,
      'region': instance.region,
      'shareUrl': instance.shareUrl,
      'planId': instance.planId,
      'materialName': instance.materialName,
      'dataAttributionList':
          instance.dataAttributionList?.map((e) => e?.toJson()).toList(),
      'trackInfo': instance.trackInfo?.toJson(),
      'customFeatureModel': instance.customFeatureModel?.toJson(),
      'attrCornerMark': instance.attrCornerMark?.toJson(),
      'isShowDot': instance.isShowDot,
    };

const _$AttrCarouselTypeEnumEnumMap = {
  AttrCarouselTypeEnum.picture: 1,
  AttrCarouselTypeEnum.text: 2,
};

AttrDataAttributionModel _$AttrDataAttributionModelFromJson(Map json) =>
    AttrDataAttributionModel(
      microCode: json['microCode'] as String?,
      microValue: json['microValue'] as String?,
      industryValue: json['industryValue'] as String?,
      industryCode: json['industryCode'] as String?,
    );

Map<String, dynamic> _$AttrDataAttributionModelToJson(
        AttrDataAttributionModel instance) =>
    <String, dynamic>{
      'microCode': instance.microCode,
      'microValue': instance.microValue,
      'industryCode': instance.industryCode,
      'industryValue': instance.industryValue,
    };
