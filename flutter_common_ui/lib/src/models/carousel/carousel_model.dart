import 'package:flutter_common_ui/src/models/common_models/attr_corner_mark_model.dart';
import 'package:flutter_common_ui/src/models/common_models/base_model.dart';
import 'package:flutter_common_ui/src/models/common_models/component_type_enum.dart';
import 'package:flutter_common_ui/src/models/common_models/custom_feature_model.dart';
import 'package:flutter_common_ui/src/models/common_models/empty_scheme_model.dart';
import 'package:flutter_common_ui/src/models/common_models/radius_model.dart';
import 'package:flutter_common_ui/src/models/common_models/shadow_model.dart';
import 'package:flutter_common_ui/src/models/common_models/style_info_model.dart';
import 'package:flutter_common_ui/src/models/common_models/track_info_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'carousel_model.g.dart';

/// 轮播数据模型
@JsonSerializable(explicitToJson: true, anyMap: true)
class CarouselModel extends BaseModel {
  /// [attr] 轮播的配置数据
  @override
  final CarouselAttr? attr;

  /// [style] 组件样式 在父[BaseModel]中已经定义，这里定义的原因是[CarouselModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final StyleInfoModel? style;

  /// [type] 组件类型 在父[BaseModel]中已经定义，这里定义的原因是[CarouselModel.fromJson] 没有调用父[BaseModel.fromJson]
  @override
  final ComponentType? type;

  /// [] 绑定事件
  @override
  @JsonKey(name: 'customfeature')
  final CustomFeatureModel? customFeature;

  CarouselModel({
    this.type,
    this.attr,
    this.style,
    this.customFeature,
    String? alias,
    num? aliasCode,
    String? aliasName,
    String? aliasUnit,
    String? group,
  }) : super(
          attr: attr,
          style: style,
          customFeature: customFeature,
          type: type,
          alias: alias,
          aliasCode: aliasCode,
          aliasName: aliasName,
          aliasUnit: aliasUnit,
          group: group,
        );
  factory CarouselModel.fromJson(Map<String, dynamic> json) =>
      _$CarouselModelFromJson(json);
  Map<String, dynamic> toJson() => _$CarouselModelToJson(this);
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class CarouselAttr extends Attr {
  /// [style] 轮播样式
  final CarouselStyleEnum? style;

  /// [singleNorms] 单页轮播项规格
  final AttrSingleCarouselLayout? singleNorms;

  /// [dataSource] 数据源
  final CarouselDataSourceEnum? dataSource;

  /// [trackInfo] 埋点信息
  final TrackInfoModel? trackInfo;

  /// [content] 轮播内容项
  final List<AttrCarouseItem>? content;

  /// [radius] 轮播项图片圆角
  final RadiusModel? radius;

  /// [shadow]轮播项阴影
  final ShadowModel? shadow;

  /// [gutter] 轮播项间距
  final double? gutter;

  /// [resourceBit] 选择轮播资源位
  final String? resourceBit;

  /// [emptyScheme] 轮播空数据处理逻辑
  final EmptySchemeModel? emptyScheme;

  CarouselAttr(
      {this.style,
      this.singleNorms,
      this.dataSource,
      this.trackInfo,
      this.content,
      this.radius,
      this.shadow,
      this.gutter,
      this.resourceBit,
      this.emptyScheme});

  factory CarouselAttr.fromJson(Map<String, dynamic> json) =>
      _$CarouselAttrFromJson(json);
  Map<String, dynamic> toJson() => _$CarouselAttrToJson(this);
}

/// 单页轮播项规格
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrSingleCarouselLayout {
  /// [maxRows] 最大行数，选项包括：1行；默认选择1行；
  final int? maxRows;

  ///  [rowSize] 单页每行最大展示个数，选项包括：1、2、2.5、3；
  /// 样式为‘标准轮播’时，默认选择1个
  /// 样式为‘一页多个’时，选项包括2，2.5，3，默认为为2.5
  final double? rowSize;

  /// [sliderType] 滑动设置，包括：横向拖拽滑动、横向整屏滑动
  final CarouselSlideTypeEnum? sliderType;
  AttrSingleCarouselLayout({this.maxRows, this.rowSize, this.sliderType});

  factory AttrSingleCarouselLayout.fromJson(Map<String, dynamic> json) =>
      _$AttrSingleCarouselLayoutFromJson(json);
  Map<String, dynamic> toJson() => _$AttrSingleCarouselLayoutToJson(this);
}

/// 轮播内容项
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrCarouseItem {
  /// [id] 轮播广告ID
  int? id;
  // 轮播类型
  /// [type] 1-图片轮播 2-文字轮播
  AttrCarouselTypeEnum? type;

  /// [pictureUrl] 图片地址
  String? pictureUrl;

  /// [title] 内容标题
  String? title;

  /// 生效时间
  List<int?>? entryIntoForceTime;

  /// [detailsUrl] 详情地址/详情图片/详情视频/直播详情地址
  String? detailsUrl;

  /// [liveBroadcast] 直播时段
  List<int?>? liveBroadcast;

  /// [region] 适用地域 100000--全国  选择省只有省的regioncode
  List<String?>? region;

  /// [shareUrl] (业务层添加的) 分享链接（有值：分享；没值：不能分享）
  final String? shareUrl;

  /// [planId] (业务层添加的) 运营计划id
  final String? planId;

  /// [materialName] (业务层添加的) 物料名称
  final String? materialName;

  /// [dataAttributionList] (业务层添加的) 拓展属性列表，打点相关的值
  final List<AttrDataAttributionModel?>? dataAttributionList;

  /// [trackInfo] 埋点配置
  final TrackInfoModel? trackInfo;

  /// [customFeatureModel] 事件绑定，轮播没有，兼容了卡片导航
  ///
  final CustomFeatureModel? customFeatureModel;

  /// [attrCornerMark] 角标配置
  final AttrCornerMark? attrCornerMark;

  /// [isShowDot] 小红点是否显示 (业务层添加的) 轮播没有，兼容了卡片导航
  final bool? isShowDot;

  AttrCarouseItem(
      this.detailsUrl,
      this.entryIntoForceTime,
      this.id,
      this.liveBroadcast,
      this.pictureUrl,
      this.region,
      this.title,
      this.type,
      this.shareUrl,
      this.planId,
      this.materialName,
      this.dataAttributionList,
      this.trackInfo,
      this.customFeatureModel,
      this.attrCornerMark,
      this.isShowDot);

  // 获取城市code集合
  getCityCodeList() {}
  factory AttrCarouseItem.fromJson(Map<String, dynamic> json) =>
      _$AttrCarouseItemFromJson(json);
  Map<String, dynamic> toJson() => _$AttrCarouseItemToJson(this);
}

/// 拓展属性列表，打点相关的值
@JsonSerializable(explicitToJson: true, anyMap: true)
class AttrDataAttributionModel {
  /// [microCode] 小微code
  String? microCode;

  /// [microValue]  小微名称
  String? microValue;

  /// [industryCode] 产业code
  String? industryCode;

  /// [industryValue] 产业名称
  String? industryValue;

  AttrDataAttributionModel(
      {this.microCode, this.microValue, this.industryValue, this.industryCode});

  factory AttrDataAttributionModel.fromJson(Map<String, dynamic> json) =>
      _$AttrDataAttributionModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttrDataAttributionModelToJson(this);
}

/// 轮播样式类型
enum CarouselStyleEnum {
  /// [standard] 标准轮播
  @JsonValue(1)
  standard,

  /// [multiple] 一页多个
  @JsonValue(2)
  multiple,

  /// [splash] 闪现轮播
  @JsonValue(3)
  splash,
}

/// 轮播滑动样式
/// 选项包括：1 横向拖拽滑动、2 横向整屏滑动
/// 默认为横向拖拽滑动
enum CarouselSlideTypeEnum {
  /// [1] 横向拖拽滑动
  @JsonValue(1)
  scrollHorizontal,

  /// 横向整屏滑动
  @JsonValue(2)
  swipe,
}

/// 数据源类型
/// 1-自定义 2-轮播资源位库
enum CarouselDataSourceEnum {
  /// [1] 自定义
  @JsonValue(1)
  custom,

  /// 轮播资源位库
  @JsonValue(2)
  recommend,
}

/// 轮播类型
enum AttrCarouselTypeEnum {
  /// [picture] 1-图片轮播
  @JsonValue(1)
  picture,

  /// [text] 2-文字轮播
  @JsonValue(2)
  text,
}
