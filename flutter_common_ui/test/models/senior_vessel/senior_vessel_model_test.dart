import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SeniorVesselModel.fromJson test', () {
    Map<String, dynamic> json = {
      "customfeature": {"eventHandler": "", "eventParams": null},
      "sourcemap": {},
      "style": {
        "attr": {
          "width": 375,
          "height": 152,
          "children": [],
          "top": 0,
          "left": 0,
          "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0},
          "opacity": 1,
          "background": {
            "type": "color",
            "color": {
              "value": "rgba(255, 255, 255, 0)",
              "isGradient": false,
              "gradient": {}
            },
            "image": {
              "size": [375, 152]
            }
          },
          "border": {
            "style": "unset",
            "width": 0,
            "color": {"value": "#000", "isGradient": false, "gradient": {}}
          },
          "radius": {"lt": 0, "rt": 0, "rb": 0, "lb": 0},
          "shadow": {
            "color": {"value": "#000", "isGradient": false, "gradient": {}},
            "X": 0,
            "Y": 0,
            "blur": 0
          },
          "filter": {
            "brightness": 0,
            "contrast": 0,
            "saturate": 0,
            "grayscale": 0,
            "sepia": 0,
            "hue-rotate": 0,
            "hue-rotate-unit": "deg",
            "invert": 0
          }
        }
      },
      "content": {
        "vesselType": 1,
        "vesselWidget": {
          "currentStatusId": "nrbmunvldipv",
          "statusWarehouseCollection": [
            {
              "name": "初始状态",
              "uniqueId": "nrmyxa4ymwly",
              "widgetList": [
                {
                  "customfeature": {"eventHandler": "", "eventParams": null},
                  "sourcemap": {},
                  "style": {
                    "attr": {
                      "width": 245,
                      "height": 105,
                      "background": {
                        "type": "color",
                        "image": {
                          "url": "",
                          "repeat": "no-repeat",
                          "position": "",
                          "size": [245, 105]
                        },
                        "color": {
                          "value": "rgba(255,255,255,0)",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "top": 13,
                      "left": 0,
                      "opacity": 1,
                      "border": {
                        "style": "unset",
                        "width": 0,
                        "color": {
                          "value": "#000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0},
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "shadow": {
                        "color": {
                          "value": "#000000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        },
                        "X": 0,
                        "Y": 0,
                        "blur": 0,
                        "spread": 0
                      },
                      "children": [],
                      "filter": {
                        "blur": 0,
                        "brightness": 0,
                        "contrast": 0,
                        "saturate": 0,
                        "grayscale": 0,
                        "sepia": 0,
                        "hue-rotate": 0,
                        "hue-rotate-unit": "deg",
                        "invert": 0
                      }
                    },
                    "children": []
                  },
                  "content":
                      "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046716734.jpeg",
                  "type": "picture",
                  "picName": "download.jpeg",
                  "alias": "图片pkcy631677046643710",
                  "aliasUnit": "picture2ltciv1677046643710",
                  "group": "未分组",
                  "isPopSenior": false,
                  "notOperation": false,
                  "orientationmodel": {
                    "left": 0,
                    "top": 13,
                    "rotate": 0,
                    "width": 245,
                    "height": 105,
                    "zIndex": 1,
                    "nzMax": 375
                  },
                  "aliasName": "图片pkcy63",
                  "aliasCode": 1677046643710,
                  "lotterydraw": {},
                  "taskSet": {},
                  "attr": {
                    "sourceMap": [
                      {
                        "id": 1677046717264,
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046716734.jpeg",
                        "imgUrlList": [],
                        "ratio": "",
                        "customfeature": {
                          "eventParams": {},
                          "eventHandler": "",
                          "currentEventIndex": 0
                        },
                        "trackInfo": {
                          "click": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          },
                          "exposure": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          }
                        }
                      }
                    ],
                    "ratio": "4.16666667:1",
                    "ratioIn": "2.06111111:1",
                    "mode": 1,
                    "modeIn": "",
                    "isRadiusAssociated": false,
                    "bannerStyle": {
                      "gutter": 4,
                      "gutterIn": 4,
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "borderRadius": 0
                    },
                    "borderInfo": {
                      "style": "unset",
                      "width": 0,
                      "color": {
                        "value": "#000",
                        "isGradient": false,
                        "gradient": {
                          "type": "linear",
                          "value":
                              "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                          "stop": [
                            ["#FF0000", 0],
                            ["#0000FF", 0.8]
                          ],
                          "angle": 75,
                          "direction": "circle at center"
                        }
                      }
                    },
                    "filter": {"blur": 0}
                  },
                  "bannerSet": {},
                  "notStreching": false,
                  "disableWidth": false
                }
              ],
              "isEdit": false,
              "modeSelect": "freeDragMode",
              "panelVesselInfo": {}
            },
            {
              "name": "上滑状态",
              "uniqueId": "nrbmunvldipv",
              "widgetList": [
                {
                  "customfeature": {"eventHandler": "", "eventParams": null},
                  "sourcemap": {},
                  "style": {
                    "attr": {
                      "width": 340,
                      "height": 85,
                      "background": {
                        "type": "color",
                        "image": {
                          "url": "",
                          "repeat": "no-repeat",
                          "position": "",
                          "size": [340, 85]
                        },
                        "color": {
                          "value": "rgba(255,255,255,0)",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "top": 38,
                      "left": 8,
                      "opacity": 1,
                      "border": {
                        "style": "unset",
                        "width": 0,
                        "color": {
                          "value": "#000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0},
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "shadow": {
                        "color": {
                          "value": "#000000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        },
                        "X": 0,
                        "Y": 0,
                        "blur": 0,
                        "spread": 0
                      },
                      "children": [],
                      "filter": {
                        "blur": 0,
                        "brightness": 0,
                        "contrast": 0,
                        "saturate": 0,
                        "grayscale": 0,
                        "sepia": 0,
                        "hue-rotate": 0,
                        "hue-rotate-unit": "deg",
                        "invert": 0
                      }
                    },
                    "children": []
                  },
                  "content":
                      "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046825423.jpeg",
                  "type": "picture",
                  "picName": "download (1).jpeg",
                  "alias": "图片djk83d1677046815232",
                  "aliasUnit": "picturecx452r1677046815232",
                  "group": "未分组",
                  "isPopSenior": false,
                  "notOperation": false,
                  "orientationmodel": {
                    "left": 8,
                    "top": 38,
                    "rotate": 0,
                    "width": 340,
                    "height": 85,
                    "zIndex": 1,
                    "nzMax": 375
                  },
                  "aliasName": "图片djk83d",
                  "aliasCode": 1677046815232,
                  "lotterydraw": {},
                  "taskSet": {},
                  "attr": {
                    "sourceMap": [
                      {
                        "id": 1677046825936,
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046825423.jpeg",
                        "imgUrlList": [],
                        "ratio": "",
                        "customfeature": {
                          "eventParams": {},
                          "eventHandler": "",
                          "currentEventIndex": 0
                        },
                        "trackInfo": {
                          "click": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          },
                          "exposure": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          }
                        }
                      }
                    ],
                    "ratio": "4.16666667:1",
                    "ratioIn": "2.06111111:1",
                    "mode": 1,
                    "modeIn": "",
                    "isRadiusAssociated": false,
                    "bannerStyle": {
                      "gutter": 4,
                      "gutterIn": 4,
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "borderRadius": 0
                    },
                    "borderInfo": {
                      "style": "unset",
                      "width": 0,
                      "color": {
                        "value": "#000",
                        "isGradient": false,
                        "gradient": {
                          "type": "linear",
                          "value":
                              "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                          "stop": [
                            ["#FF0000", 0],
                            ["#0000FF", 0.8]
                          ],
                          "angle": 75,
                          "direction": "circle at center"
                        }
                      }
                    },
                    "filter": {"blur": 0}
                  },
                  "bannerSet": {},
                  "notStreching": false,
                  "disableWidth": false
                }
              ],
              "isEdit": false,
              "modeSelect": "freeDragMode",
              "panelVesselInfo": {}
            }
          ],
          "name": "动态容器1677046613462",
          "repertoryStatusWarehouse": {
            "nrmyxa4ymwly": {
              "name": "初始状态",
              "uniqueId": "nrmyxa4ymwly",
              "widgetList": [
                {
                  "customfeature": {"eventHandler": "", "eventParams": null},
                  "sourcemap": {},
                  "style": {
                    "attr": {
                      "width": 245,
                      "height": 105,
                      "background": {
                        "type": "color",
                        "image": {
                          "url": "",
                          "repeat": "no-repeat",
                          "position": "",
                          "size": [245, 105]
                        },
                        "color": {
                          "value": "rgba(255,255,255,0)",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "top": 13,
                      "left": 0,
                      "opacity": 1,
                      "border": {
                        "style": "unset",
                        "width": 0,
                        "color": {
                          "value": "#000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0},
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "shadow": {
                        "color": {
                          "value": "#000000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        },
                        "X": 0,
                        "Y": 0,
                        "blur": 0,
                        "spread": 0
                      },
                      "children": [],
                      "filter": {
                        "blur": 0,
                        "brightness": 0,
                        "contrast": 0,
                        "saturate": 0,
                        "grayscale": 0,
                        "sepia": 0,
                        "hue-rotate": 0,
                        "hue-rotate-unit": "deg",
                        "invert": 0
                      }
                    },
                    "children": []
                  },
                  "content":
                      "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046716734.jpeg",
                  "type": "picture",
                  "picName": "download.jpeg",
                  "alias": "图片pkcy631677046643710",
                  "aliasUnit": "picture2ltciv1677046643710",
                  "group": "未分组",
                  "isPopSenior": false,
                  "notOperation": false,
                  "orientationmodel": {
                    "left": 0,
                    "top": 13,
                    "rotate": 0,
                    "width": 245,
                    "height": 105,
                    "zIndex": 1,
                    "nzMax": 375
                  },
                  "aliasName": "图片pkcy63",
                  "aliasCode": 1677046643710,
                  "lotterydraw": {},
                  "taskSet": {},
                  "attr": {
                    "sourceMap": [
                      {
                        "id": 1677046717264,
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046716734.jpeg",
                        "imgUrlList": [],
                        "ratio": "",
                        "customfeature": {
                          "eventParams": {},
                          "eventHandler": "",
                          "currentEventIndex": 0
                        },
                        "trackInfo": {
                          "click": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          },
                          "exposure": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          }
                        }
                      }
                    ],
                    "ratio": "4.16666667:1",
                    "ratioIn": "2.06111111:1",
                    "mode": 1,
                    "modeIn": "",
                    "isRadiusAssociated": false,
                    "bannerStyle": {
                      "gutter": 4,
                      "gutterIn": 4,
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "borderRadius": 0
                    },
                    "borderInfo": {
                      "style": "unset",
                      "width": 0,
                      "color": {
                        "value": "#000",
                        "isGradient": false,
                        "gradient": {
                          "type": "linear",
                          "value":
                              "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                          "stop": [
                            ["#FF0000", 0],
                            ["#0000FF", 0.8]
                          ],
                          "angle": 75,
                          "direction": "circle at center"
                        }
                      }
                    },
                    "filter": {"blur": 0}
                  },
                  "bannerSet": {},
                  "notStreching": false,
                  "disableWidth": false
                }
              ],
              "isEdit": false,
              "modeSelect": "freeDragMode",
              "panelVesselInfo": {}
            },
            "nrbmunvldipv": {
              "name": "上滑状态",
              "uniqueId": "nrbmunvldipv",
              "widgetList": [
                {
                  "customfeature": {"eventHandler": "", "eventParams": null},
                  "sourcemap": {},
                  "style": {
                    "attr": {
                      "width": 340,
                      "height": 85,
                      "background": {
                        "type": "color",
                        "image": {
                          "url": "",
                          "repeat": "no-repeat",
                          "position": "",
                          "size": [340, 85]
                        },
                        "color": {
                          "value": "rgba(255,255,255,0)",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "top": 38,
                      "left": 8,
                      "opacity": 1,
                      "border": {
                        "style": "unset",
                        "width": 0,
                        "color": {
                          "value": "#000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        }
                      },
                      "padding": {"top": 0, "right": 0, "bottom": 0, "left": 0},
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "shadow": {
                        "color": {
                          "value": "#000000",
                          "isGradient": false,
                          "gradient": {
                            "type": "linear",
                            "value":
                                "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                            "stop": [
                              ["#FF0000", 0],
                              ["#0000FF", 0.8]
                            ],
                            "angle": 75,
                            "direction": "circle at center"
                          }
                        },
                        "X": 0,
                        "Y": 0,
                        "blur": 0,
                        "spread": 0
                      },
                      "children": [],
                      "filter": {
                        "blur": 0,
                        "brightness": 0,
                        "contrast": 0,
                        "saturate": 0,
                        "grayscale": 0,
                        "sepia": 0,
                        "hue-rotate": 0,
                        "hue-rotate-unit": "deg",
                        "invert": 0
                      }
                    },
                    "children": []
                  },
                  "content":
                      "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046825423.jpeg",
                  "type": "picture",
                  "picName": "download (1).jpeg",
                  "alias": "图片djk83d1677046815232",
                  "aliasUnit": "picturecx452r1677046815232",
                  "group": "未分组",
                  "isPopSenior": false,
                  "notOperation": false,
                  "orientationmodel": {
                    "left": 8,
                    "top": 38,
                    "rotate": 0,
                    "width": 340,
                    "height": 85,
                    "zIndex": 1,
                    "nzMax": 375
                  },
                  "aliasName": "图片djk83d",
                  "aliasCode": 1677046815232,
                  "lotterydraw": {},
                  "taskSet": {},
                  "attr": {
                    "sourceMap": [
                      {
                        "id": 1677046825936,
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046825423.jpeg",
                        "imgUrlList": [],
                        "ratio": "",
                        "customfeature": {
                          "eventParams": {},
                          "eventHandler": "",
                          "currentEventIndex": 0
                        },
                        "trackInfo": {
                          "click": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          },
                          "exposure": {
                            "code": "",
                            "attr": {"hyperlink": false, "site_number": false}
                          }
                        }
                      }
                    ],
                    "ratio": "4.16666667:1",
                    "ratioIn": "2.06111111:1",
                    "mode": 1,
                    "modeIn": "",
                    "isRadiusAssociated": false,
                    "bannerStyle": {
                      "gutter": 4,
                      "gutterIn": 4,
                      "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
                      "borderRadius": 0
                    },
                    "borderInfo": {
                      "style": "unset",
                      "width": 0,
                      "color": {
                        "value": "#000",
                        "isGradient": false,
                        "gradient": {
                          "type": "linear",
                          "value":
                              "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,#42B983 80%)",
                          "stop": [
                            ["#FF0000", 0],
                            ["#0000FF", 0.8]
                          ],
                          "angle": 75,
                          "direction": "circle at center"
                        }
                      }
                    },
                    "filter": {"blur": 0}
                  },
                  "bannerSet": {},
                  "notStreching": false,
                  "disableWidth": false
                }
              ],
              "isEdit": false,
              "modeSelect": "freeDragMode",
              "panelVesselInfo": {}
            }
          }
        }
      },
      "type": "seniorvessel",
      "alias": "容器trnbi11677046613461",
      "aliasUnit": "seniorvesseltrnbi11677046613461",
      "group": "未分组",
      "isPopSenior": false,
      "notOperation": false,
      "orientationmodel": {
        "left": 0,
        "top": 0,
        "rotate": 0,
        "width": 375,
        "height": 152,
        "zIndex": 1,
        "nzMax": 375
      },
      "aliasName": "容器trnbi1",
      "aliasCode": 1677046613461,
      "lotterydraw": {},
      "taskSet": {},
      "attr": {},
      "bannerSet": {},
      "notStreching": false,
      "disableWidth": false,
      "uniqueId": "nre3v38a805l"
    };

    test(
        'SeniorVesselModel.fromJson() should return SeniorVesselModel instance ',
        () {
      SeniorVesselModel s = SeniorVesselModel.fromJson(json);
      expect(s.content?.vesselType, VesselTypeEnum.persistent);
      expect(
          s.content?.vesselWidget?.statusWarehouseCollection?[0].name, '初始状态');
      expect(
          s.content?.vesselWidget?.statusWarehouseCollection?[1].name, '上滑状态');
    });
  });
}
