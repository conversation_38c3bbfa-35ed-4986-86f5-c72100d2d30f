import 'package:flutter_common_ui/src/models/carousel/carousel_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CarouselModel.fromJson test', () {
    Map<String, dynamic> json = {
      "customfeature": {"eventHandler": "", "eventParams": null},
      "sourcemap": {},
      "style": {
        "attr": {
          "padding": {"top": 0, "right": 24, "bottom": 0, "left": 24},
          "left": 0,
          "top": 0,
          "width": 375,
          "height": 211,
          "opacity": 1,
          "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
          "background": {
            "type": "color",
            "image": {
              "url": "",
              "repeat": "no-repeat",
              "position": "center",
              "size": [375, 211]
            },
            "color": {
              "value": "rgba(255,255,255,0)",
              "isGradient": false,
              "gradient": {
                "type": "linear",
                "value":
                    "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%, rgba(255,255,255,0) 80%)",
                "stop": [
                  ["rgba(255,255,255,0)", 0],
                  ["rgba(255,255,255,0)", 0.8]
                ],
                "angle": 75,
                "direction": "circle at center"
              }
            }
          },
          "border": {
            "style": "unset",
            "width": 0,
            "color": {
              "value": "rgba(0,0,0,1)",
              "isGradient": false,
              "gradient": {
                "type": "linear",
                "value":
                    "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,rgba(255,255,255,0) 80%)",
                "stop": [
                  ["rgba(255,255,255,0)", 0],
                  ["rgba(255,255,255,0)", 0.8]
                ],
                "angle": 75,
                "direction": "circle at center"
              }
            }
          },
          "shadow": {
            "color": {
              "value": "rgba(255,255,255,0)",
              "isGradient": false,
              "gradient": {
                "type": "linear",
                "value":
                    "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,rgba(255,255,255,0) 80%)",
                "stop": [
                  ["rgba(255,255,255,0)", 0],
                  ["rgba(255,255,255,0)", 0.8]
                ],
                "angle": 75,
                "direction": "circle at center"
              }
            },
            "X": 0,
            "Y": 0,
            "blur": 0,
            "spread": 0
          },
          "filter": {
            "blur": 0,
            "brightness": 0,
            "contrast": 0,
            "saturate": 0,
            "grayscale": 0,
            "sepia": 0,
            "hue-rotate": 0,
            "hue-rotate-unit": "deg",
            "invert": 0
          }
        }
      },
      "type": "carousel",
      "alias": "轮播pqdg6e1677587862839",
      "aliasUnit": "carouselpqdg6e1677587862839",
      "group": "未分组",
      "isPopSenior": false,
      "notOperation": false,
      "orientationmodel": {
        "width": 375,
        "height": 211,
        "left": 0,
        "top": 0,
        "zIndex": 1,
        "rotate": 0,
        "opacity": 100,
        "nzMax": 375,
        "zTop": 0
      },
      "aliasName": "轮播pqdg6e",
      "aliasCode": 1677587862839,
      "lotterydraw": {},
      "taskSet": {},
      "attr": {
        "style": 1,
        "singleNorms": {"maxRows": 1, "rowSize": 1, "sliderType": 1},
        "dataSource": 2,
        "trackInfo": {
          "click": {
            "code": "",
            "attr": {
              "hyperlink": false,
              "content_title": false,
              "site_number": false,
              "content_attribution_id": false,
              "content_attribution_name": false,
              "microCode": false,
              "microName": false,
              "material_id": false,
              "material_name": false,
              "operation_plan_id": false
            }
          },
          "exposure": {
            "code": "",
            "attr": {
              "hyperlink": false,
              "content_title": false,
              "site_number": false,
              "content_attribution_id": false,
              "content_attribution_name": false,
              "microCode": false,
              "microName": false,
              "material_id": false,
              "material_name": false,
              "operation_plan_id": false
            }
          }
        },
        "content": [
          {
            "id": 10527,
            "type": 1,
            "pictureUrl":
                "https://ys-oss-zjrs.haier.net/content/img/2023022417411011788185.jpg",
            "title": "面向全员",
            "subTitle": "",
            "entryIntoForceTime": [1677231683000, 1679650883000],
            "contentType": 1,
            "detailsUrl":
                "https://ys-oss-zjrs.haier.net/content/video/2023022814433313389135.mp4",
            "detailName": "",
            "liveBroadcast": [1677566670000, 1677912270000],
            "region": ["100000"]
          },
          {
            "id": 10528,
            "type": 1,
            "pictureUrl":
                "https://ys-oss-zjrs.haier.net/content/img/202302241741507628588.jpg",
            "title": "面向人群包",
            "subTitle": "",
            "entryIntoForceTime": [1677231727000, 1679650927000],
            "contentType": 1,
            "detailsUrl": "",
            "detailName": "",
            "liveBroadcast": [],
            "region": ["100000"]
          }
        ],
        "radius": {"lt": 0, "rt": 0, "lb": 0, "rb": 0},
        "shadow": {
          "color": {
            "value": "rgba(0,0,0,1)",
            "isGradient": false,
            "gradient": {
              "type": "linear",
              "value":
                  "linear-gradient(75deg, rgba(255,132,0, 0.5) 0%,rgb(255,0,130) 50%,rgba(255,255,255,0) 80%)",
              "stop": [
                ["rgba(255,255,255,0)", 0],
                ["rgba(255,255,255,0)", 0.8]
              ],
              "angle": 75,
              "direction": "circle at center"
            }
          },
          "X": 0,
          "Y": 0,
          "blur": 0,
          "spread": 0
        },
        "gutter": 0,
        "resourceBit": "B0324",
        "resourceBitName": "测试CDP人群包~勿动"
      },
      "bannerSet": {},
      "notStreching": false,
      "disableWidth": false,
      "uniqueId": "nr868tuwgg4t"
    };

    test(
        'CarouselModel.fromJson() should return standard and recommend CarouselModel instance ',
        () {
      CarouselModel carousel = CarouselModel.fromJson(json);
      expect(carousel.attr?.style, CarouselStyleEnum.standard);
      expect(carousel.attr?.dataSource, CarouselDataSourceEnum.recommend);
    });
  });
}
