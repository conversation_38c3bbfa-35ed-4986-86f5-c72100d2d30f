/*
 * @Author: maxia<PERSON>n <EMAIL>
 * @Date: 2023-03-30 13:34:59
 * @description: 
 */
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PictureModel.fromJson test', () {
    Map<String, dynamic> json = {
      "attr": {
        "mode": 1,
      },
      "style": {
        "attr": {"width": 375}
      }
    };

    test('PictureModel.fromJson() should return PictureModel instance ', () {
      PictureModel p = PictureModel.fromJson(json);
      expect(p.attr?.mode, 1);
    });
  });
}
