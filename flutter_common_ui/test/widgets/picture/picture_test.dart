/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-30 13:34:59
 * @description: 
 */
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/src/models/picture/picture_model.dart';
import 'package:flutter_common_ui/src/widgets/picture/picture.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('picture return 1 picture', (tester) async {
    const Map<String, dynamic> json = {
      "attr": {
        "mode": 1,
        "sourceMap": [
          {
            "img":
                'https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1664360438998.jpg',
          }
        ]
      },
      "style": {
        "attr": {"width": 375, "height": 100}
      }
    };
    final pictureWidget =
        Picture(config: PictureModel.fromJson(json), data: null);
    await tester.pumpWidget(MaterialApp(home: Scaffold(body: pictureWidget)));
    final imageFinder = find.byType(Image);
    expect(imageFinder, findsOneWidget);
    Image image = imageFinder.evaluate().single.widget as Image;
    expect((image.image as NetworkImage).url,
        'https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1664360438998.jpg');
  });

  testWidgets('picture return 2 pictures', (tester) async {
    const Map<String, dynamic> json = {
      "attr": {
        "mode": 2,
        "sourceMap": [
          {
            "img":
                'https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1664360438998.jpg',
          },
          {
            "img": 'https://photo.tuchong.com/14649482/f/601672690.jpg',
          }
        ]
      },
      "style": {
        "attr": {"width": 375, "height": 100}
      }
    };
    final pictureWidget =
        Picture(config: PictureModel.fromJson(json), data: null);
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(body: pictureWidget),
    ));
    final imageFinders = find.byType(Image);
    expect(imageFinders, findsNWidgets(2));
    Image image = imageFinders.evaluate().toList()[1].widget as Image;
    expect((image.image as NetworkImage).url,
        'https://photo.tuchong.com/14649482/f/601672690.jpg');
  });
}
