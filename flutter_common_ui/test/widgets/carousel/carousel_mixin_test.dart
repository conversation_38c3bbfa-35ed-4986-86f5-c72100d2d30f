// /*
//  * @Author: maxiaodan <EMAIL>
//  * @Date: 2023-03-30 13:34:59
//  * @description: 
//  */
// import 'package:flutter_common_ui/src/widgets/carousel/carousel_mixin.dart';
// import 'package:flutter_test/flutter_test.dart';

// void main() {
//   CarouselMixin _carouselMixin = CarouselMixin();

//   test('isActiveCarousel should return false when cityCode is not in region',
//       () async {
//     expect(
//         _carouselMixin.isActiveCarousel(
//             ['110000'], [1677600000000, 2529676800000], '610100'),
//         false);
//   });

//   test(
//       'isActiveCarousel should return false when entryIntoForceTime is from 2023-02 to 2023-03',
//       () async {
//     expect(
//         _carouselMixin.isActiveCarousel(
//             ['100000'], [1675180800000, 1677600000000], '610100'),
//         false);
//   });

//   test(
//       'isActiveCarousel should return true when entryIntoForceTime is 2023-03 to 2050-03 and regin is countryCode',
//       () async {
//     expect(
//         _carouselMixin.isActiveCarousel(
//             ['100000'], [1677600000000, 2529676800000], '610100'),
//         true);
//   });

//   test(
//       'isActiveCarousel should return true when entryIntoForceTime is 2023-01 to 2050-03 and regin include cityCode',
//       () async {
//     expect(
//         _carouselMixin.isActiveCarousel(
//             ['610100'], [1677600000000, 2529676800000], '610100'),
//         true);
//   });

//   test(
//       'isActiveCarousel should return true when entryIntoForceTime is 2023-01 to 2050-03 and regin include cityCode',
//       () async {
//     expect(
//         _carouselMixin.isActiveCarousel(
//             ['610100'], [1677600000000, 2529676800000], '610100'),
//         true);
//   });

//   test(
//       'isActiveCarousel should return true when entryIntoForceTime is 2023-01 to 2050-03 and cityCode is empty str',
//       () async {
//     expect(
//         _carouselMixin
//             .isActiveCarousel(['10000'], [1677600000000, 2529676800000], ''),
//         true);
//   });
// }
