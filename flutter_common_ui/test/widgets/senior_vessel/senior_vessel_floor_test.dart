/*
 * @Author: maxiaodan <EMAIL>
 * @Date: 2023-03-30 13:34:59
 * @description: 
 */
import 'package:flutter_common_ui/src/models/senior_vessel/senior_vessel_model.dart';
import 'package:flutter_common_ui/src/widgets/senior_vessel/senior_vessel_floor.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('seniorVesselFloor  return instance', (tester) async {
    Map<String, dynamic> json = {
      "type": "seniorvessel",
      "style": {
        "attr": {
          "width": 375,
          "height": 152,
        }
      },
      "content": {
        "vesselType": 0,
        "vesselWidget": {
          "statusWarehouseCollection": [
            {
              "name": "初始状态",
              "widgetList": [
                {
                  "type": "picture",
                  "attr": {
                    "sourceMap": [
                      {
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046941091.jpeg",
                      },
                      {
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046965619.jpeg",
                      },
                      {
                        "img":
                            "https://ys-oss-zjrs.haier.net/content/visulaEditor/img/1677046953920.jpeg",
                      }
                    ],
                    "mode": 6,
                  },
                }
              ]
            }
          ]
        }
      }
    };

    final seniorVessel =
        SeniorVesselFloor(config: SeniorVesselModel.fromJson(json));
    await tester.pumpWidget(seniorVessel);
    expect(find.byWidget(seniorVessel), findsOneWidget);
  });
}
