name: flutter_main
description: A new Flutter package project.
version: 0.7.8
author: <EMAIL>
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3

environment:
  sdk: ">=3.3.1 <4.0.0"
  flutter: ">=3.19.3"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  path_provider: 2.1.2
  redux: 5.0.0
  flutter_redux: 0.8.2
  flutter_screenutil: 5.0.0+2
  convert: 3.1.1
  crypto: 3.0.1
  lottie: 2.6.0

  login:
    hosted:
      name: login
      url: http://**************:8083
    version: ">=0.1.0"

  bind_scan:
    hosted:
      name: bind_scan
      url: http://**************:8083
    version: ">=1.0.0"

  plugin_test_page:
    hosted:
      name: plugin_test_page
      url: http://**************:8083
    version: ">=1.1.0+**********"

  app_mine:
    version: ">=0.0.1"
    hosted:
      name: app_mine
      url: http://**************:8083

  eshop:
    hosted:
      name: eshop
      url: http://**************:8083
    version: ">=0.2.19"

  smart_home:
    hosted:
      name: smart_home
      url: http://**************:8083
    version: ">=9.8.0"

  whole_house_air:
    hosted:
      name: whole_house_air
      url: http://**************:8083
      version: ">=0.0.1"

  ugc:
    hosted:
      name: ugc
      url: http://**************:8083
    version: ">=7.10.0"

  scan:
    hosted:
      name: scan
      url: http://**************:8083
    version: ">=0.0.1"

  photo:
    hosted:
      name: photo
      url: http://**************:8083
    version: ">=8.2.0+2024041801"

  uppermission:
    version: ">=2.1.0+2023040701"
    hosted:
      name: uppermission
      url: http://**************:8083

  camera_camera:
    hosted:
      name: camera_camera
      url: http://**************:8083
    version: ">=2.0.1"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=1.2.0+2023040701"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.3.0+2023032901"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=1.3.0+2023032901"

  plugin_device:
    hosted:
      name: plugin_device
      url: http://**************:8083
    version: ">=4.3.0+2023091501"

  family:
    hosted:
      name: family
      url: http://**************:8083
    version: ">=0.4.0+2023032901"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.3.0+2023032906"

  Appinfos:
    hosted:
      name: Appinfos
      url: http://**************:8083
    version: ">=1.1.0+2023040701"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=2.6.0+2023091501"

  upsystem:
    hosted:
      name: upsystem
      url: http://**************:8083
    version: ">=1.2.0+2023040701"

  uplusai:
    hosted:
      name: uplusai
      url: http://**************:8083
    version: ">=1.1.0+2023040702"

  umeng:
    hosted:
      name: umeng
      url: http://**************:8083
    version: ">=0.2.0+2023040705"

  fullscreen_player:
    hosted:
      name: fullscreen_player
      url: http://**************:8083
    version: ">=1.0.1"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=1.1.0+2023032901"

  location:
    hosted:
      name: location
      url: http://**************:8083
    version: ">=2.2.0+2023040701"

  video_player:
    hosted:
      name: video_player
      url: http://**************:8083
    version: ">=2.0.1"

  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: ">=1.1.0+2023032901"

  video_player_platform_interface:
    hosted:
      name: video_player_platform_interface
      url: http://**************:8083
    version: ">=3.0.1"

  app_service:
    version: ">=0.0.1"
    hosted:
      name: app_service
      url: http://**************:8083

  uimessage:
    hosted:
      name: uimessage
      url: http://**************:8083
    version: ">=8.3.0+2024010501"

  main_business:
    hosted:
      name: main_business
      url: http://**************:8083
    version: ">=8.1.0+2023091501"

  function_toggle:
    hosted:
      name: function_toggle
      url: http://**************:8083
    version: ">=1.0.0"

  about_us:
    hosted:
      name: about_us
      url: http://**************:8083
    version: ">=1.0.0"

  flutter_common_ui:
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
    version: ">=10.0.0"

dev_dependencies:
  flutter_test:
    sdk: flutter
  extended_image_library: 4.0.2
  http_client_helper: 3.0.0
  url_launcher: 6.2.4
  url_launcher_android: 6.0.37
  path_provider: 2.1.2

dependency_overrides:
  extended_image_library: 4.0.2
  http_client_helper: 3.0.0
  url_launcher: 6.2.4
  url_launcher_android: 6.0.37
  path_provider: 2.1.2
# For information on the generic Dart part of this file, see the
# following page here: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - resources/
    - resources/images/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
