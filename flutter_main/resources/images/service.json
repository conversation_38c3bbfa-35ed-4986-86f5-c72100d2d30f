{"v": "5.12.2", "fr": 60, "ip": 0, "op": 37, "w": 72, "h": 72, "nm": "服务tab渐变", "ddd": 0, "assets": [{"id": "image_0", "w": 72, "h": 72, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "服务选中-内", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [-4]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 31, "s": [1]}, {"t": 36, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [37.553, -37.906, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [47.5, -45, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [97.674, 97.674, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.27, -0.82]], "o": [[0.42, 1.23], [0, 0]], "v": [[-1.695, -1.719], [1.695, 1.721]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 16, "s": [0]}, {"t": 25, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 16, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "服务选中-外描边", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"t": 12, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36.003, 36.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 6, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [110, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [95, 95, 100]}, {"t": 30, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "sy": [{"c": {"a": 0, "k": [0.082352943718, 0.631372570992, 0.972549021244, 1], "ix": 2}, "o": {"a": 0, "k": 25, "ix": 3}, "a": {"a": 0, "k": 90, "ix": 5}, "s": {"a": 0, "k": 3, "ix": 8}, "d": {"a": 0, "k": 3, "ix": 6}, "ch": {"a": 0, "k": 0, "ix": 7}, "bm": {"a": 0, "k": 5, "ix": 1}, "no": {"a": 0, "k": 0, "ix": 9}, "lc": {"a": 0, "k": 1, "ix": 10}, "ty": 1, "nm": "投影"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 2.58], [-2.7, 0], [-0.88, -1.21], [-0.34, 0.47], [-1.69, 0], [0, -3.06], [1.84, -2.14], [1.77, -0.85], [0.4, 0.2], [1.83, 2.14]], "o": [[0, -3.06], [1.69, 0], [0.34, 0.47], [0.88, -1.21], [2.7, 0], [0, 2.58], [-1.83, 2.14], [-0.41, 0.2], [-1.78, -0.85], [-1.83, -2.14]], "v": [[-9.671, -3.33], [-4.671, -8.75], [-0.701, -6.99], [0.699, -6.99], [4.669, -8.75], [9.669, -3.33], [6.519, 3.93], [0.639, 8.6], [-0.631, 8.6], [-6.521, 3.93]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 1, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 1, 1, 0.5, 0.59, 0.81, 1, 1, 0.18, 0.62, 1], "ix": 8}}, "s": {"a": 0, "k": [0, -9.5], "ix": 4}, "e": {"a": 0, "k": [0, 9.5], "ix": 5}, "t": 1, "lc": 2, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 13}, "bm": 0, "nm": "Grad", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [310, 310], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 6, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "服务选中-外", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"t": 12, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36.003, 36.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 6, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [110, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [95, 95, 100]}, {"t": 30, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "sy": [{"c": {"a": 0, "k": [0.082352943718, 0.631372570992, 0.972549021244, 1], "ix": 2}, "o": {"a": 0, "k": 25, "ix": 3}, "a": {"a": 0, "k": 90, "ix": 5}, "s": {"a": 0, "k": 3, "ix": 8}, "d": {"a": 0, "k": 3, "ix": 6}, "ch": {"a": 0, "k": 0, "ix": 7}, "bm": {"a": 0, "k": 5, "ix": 1}, "no": {"a": 0, "k": 0, "ix": 9}, "lc": {"a": 0, "k": 1, "ix": 10}, "ty": 1, "nm": "投影"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 2.58], [-2.7, 0], [-0.88, -1.21], [-0.34, 0.47], [-1.69, 0], [0, -3.06], [1.84, -2.14], [1.77, -0.85], [0.4, 0.2], [1.83, 2.14]], "o": [[0, -3.06], [1.69, 0], [0.34, 0.47], [0.88, -1.21], [2.7, 0], [0, 2.58], [-1.83, 2.14], [-0.41, 0.2], [-1.78, -0.85], [-1.83, -2.14]], "v": [[-9.671, -3.33], [-4.671, -8.75], [-0.701, -6.99], [0.699, -6.99], [4.669, -8.75], [9.669, -3.33], [6.519, 3.93], [0.639, 8.6], [-0.631, 8.6], [-6.521, 3.93]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "bm": 0, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.545, 0.82, 1, 0.5, 0.273, 0.663, 1, 1, 0, 0.506, 1], "ix": 9}}, "s": {"a": 0, "k": [0, 9.5], "ix": 5}, "e": {"a": 0, "k": [0, -9.5], "ix": 6}, "t": 1, "nm": "Gragient", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 6, "op": 600, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "服务投影.png", "cl": "png", "parent": 4, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 6, "s": [0]}, {"t": 12, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.003, -1.895, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [36, 36, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [96, 96, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "服务默认-内", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-8.842, 6.048, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [97.674, 97.674, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.42, 1.23]], "o": [[-1.27, -0.82], [0, 0]], "v": [[1.695, 1.721], [-1.695, -1.719]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.600000023842, 0.600000023842, 0.600000023842, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 6, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 7, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "服务默认-外", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36.003, 36.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 6, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 15, "s": [110, 110, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [95, 95, 100]}, {"t": 30, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 2.58], [-2.7, 0], [-0.88, -1.21], [-0.34, 0.47], [-1.69, 0], [0, -3.06], [1.84, -2.14], [1.77, -0.85], [0.4, 0.2], [1.83, 2.14]], "o": [[0, -3.06], [1.69, 0], [0.34, 0.47], [0.88, -1.21], [2.7, 0], [0, 2.58], [-1.83, 2.14], [-0.41, 0.2], [-1.78, -0.85], [-1.83, -2.14]], "v": [[-9.671, -3.33], [-4.671, -8.75], [-0.701, -6.99], [0.699, -6.99], [4.669, -8.75], [9.669, -3.33], [6.519, 3.93], [0.639, 8.6], [-0.631, 8.6], [-6.521, 3.93]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.600000023842, 0.600000023842, 0.600000023842, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "路径", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 7, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "外框", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [24, 24], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.847058832645, 0.847058832645, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "ct": 1, "bm": 0}], "markers": [], "props": {}}