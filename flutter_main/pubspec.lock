# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  Appinfos:
    dependency: "direct main"
    description:
      name: Appinfos
      sha256: "90d097849f09db32035aca34693d8c5fa5cf9a1af39af94a791682200d44a563"
      url: "http://**************:8083"
    source: hosted
    version: "1.2.0+2024012901"
  about_us:
    dependency: "direct main"
    description:
      name: about_us
      sha256: "5caa8df78e5ff39bb899c43876a6267e17e00bb3d9b87cf6a315dfee692e212c"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025030801"
  abtest:
    dependency: transitive
    description:
      name: abtest
      sha256: dbd50c33fdeefc48a7dbf61cb392d73dde09e5dff92c7d76bc61cc06d2578b53
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025033101"
  annotation:
    dependency: transitive
    description:
      name: annotation
      sha256: e7aa3a0746fcca2d30d8f2eb834e6d7a32371658ca41736ba44b243f1bdb5026
      url: "http://**************:8083"
    source: hosted
    version: "0.0.11"
  app_info:
    dependency: transitive
    description:
      name: app_info
      sha256: "6424c96c162796e5e2f14e4a07900ade5bf107130a2c8d4647febf9bf0d148c0"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024060301"
  app_mine:
    dependency: "direct main"
    description:
      name: app_mine
      sha256: "2179bef5482a5891d18d0d2c292cdd9db99fa405c3debad2db75e7fa9e127112"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025031101"
  app_service:
    dependency: "direct main"
    description:
      name: app_service
      sha256: "56dc86d37d865be3eb27e8ac55ac11cf0d36c8cfdd287f2907978ad4ba773f5c"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025030501"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "20071638cbe4e5964a427cfa0e86dce55d060bc7d82d56f3554095d7239a8765"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.2"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.7.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.11.0"
  auto_orientation:
    dependency: transitive
    description:
      name: auto_orientation
      sha256: cd56bb59b36fa54cc28ee254bc600524f022a4862f31d5ab20abd7bb1c54e678
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  badges:
    dependency: transitive
    description:
      name: badges
      sha256: "37535560bc887e4a02c58cd78bf50f5d84c31dd70d6f71cba71b7cae1ae11f39"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  bind_scan:
    dependency: "direct main"
    description:
      name: bind_scan
      sha256: "55329c9a8b79b71f4598b5d431f474dd0a9d5fdf1fab42e94ce438126d7fc2dd"
      url: "http://**************:8083"
    source: hosted
    version: "1.0.0+2025052201"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "28ea9690a8207179c319965c13cd8df184d5ee721ae2ce60f398ced1219cea1f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "9e90e78ae72caa874a323d78fa6301b3fb8fa7ea76a8f96dc5b5bf79f283bf2f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "205d6a9f1862de34b93184f22b9d2d94586b2f05c581d546695e3d8f6a805cd7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: fbd2d6349ac6c3d2d1efe4ff04bb2dfb58696be2522a05d9599285e254633cc8
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024082301"
  camera_camera:
    dependency: "direct main"
    description:
      name: camera_camera
      sha256: "3e0187da0d39b83506cbc1a832fa36b991ccd368246e0bc4f5104328172389d9"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024122401"
  card_swiper:
    dependency: transitive
    description:
      name: card_swiper
      sha256: "0c94c538f47be1dab52d018d4900a7046b4cb0700dc7f95b8628da89d1212b35"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.4"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.18.0"
  color_models:
    dependency: transitive
    description:
      name: color_models
      sha256: "3683f0a461570161ca22b7d3e1765b2ce2bce3534db14e00d5ee458d0287abac"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.3"
  connectivity:
    dependency: transitive
    description:
      name: connectivity
      sha256: a8e91263cf3e25fb5cc95e19dfde4999e32a648ac3b9e8a558a28165731678f8
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.6"
  connectivity_for_web:
    dependency: transitive
    description:
      name: connectivity_for_web
      sha256: "01a390c1d5adc2ed1fa1f52d120c07fe9fd01166a93f965a832fd6cfc0ea6482"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0+1"
  connectivity_macos:
    dependency: transitive
    description:
      name: connectivity_macos
      sha256: "51ae08d5162eca9669b9d8951ed83ce19c5355a81149f94e4dee2740beb93628"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1+2"
  connectivity_platform_interface:
    dependency: transitive
    description:
      name: connectivity_platform_interface
      sha256: "2d82e942df9d49f29a24bb07fb5ce085d4a53e47818c62364d2b6deb9e0d7a8e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  convert:
    dependency: "direct main"
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: cf75650c66c0316274e21d7c43d3dea246273af5955bd94e8184837cd577575c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: "1989d917fbe8e6b39806207df5a3fdd3d816cbd090fac2ce26fb45e9a71476e5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  cx_player:
    dependency: transitive
    description:
      name: cx_player
      sha256: "54918e3284b6c70713283200c99c8dae75a516fa3c416e24383049fcaa2616e1"
      url: "http://**************:8083"
    source: hosted
    version: "0.0.1+2025042108"
  date_format:
    dependency: transitive
    description:
      name: date_format
      sha256: a48254e60bdb7f1d5a15cac7f86e37491808056c0a99dbdc850841def4754ddc
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.9"
  device_utils:
    dependency: transitive
    description:
      name: device_utils
      sha256: e078845d6805f7f6199392ae9068926e26df12d8435331386b4a56269d53d8d0
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024121001"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: ce75a1b40947fea0a0e16ce73337122a86762e38b982e1ccb909daa3b9bc4197
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.3.2"
  easy_refresh:
    dependency: transitive
    description:
      name: easy_refresh
      sha256: "486e30abfcaae66c0f2c2798a10de2298eb9dc5e0bb7e1dba9328308968cae0c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.0"
  eshop:
    dependency: "direct main"
    description:
      name: eshop
      sha256: "8190816e7fc880bee22c6ba0b0e500a90144534869c45777c0a4b6074e186e25"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025031101"
  eshop_utils:
    dependency: transitive
    description:
      name: eshop_utils
      sha256: d4da8ca57bcb4818eea582a3661fc439dee52c0ff36b2cc31235bf5eb0a49390
      url: "http://**************:8083"
    source: hosted
    version: "0.0.7+2024071801"
  eshop_widgets:
    dependency: transitive
    description:
      name: eshop_widgets
      sha256: f3fe84fc8b899a8084ad749dc6b3a5b985e7f1dce7031f5af4ddfd694ee3d997
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025021401"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "44baa799834f4c803921873e7446a2add0f3efa45e101a054b1f0ab9b95f8edc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  extended_image:
    dependency: transitive
    description:
      name: extended_image
      sha256: d7f091d068fcac7246c4b22a84b8dac59a62e04d29a5c172710c696e67a22f94
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.2.0"
  extended_image_library:
    dependency: "direct dev"
    description:
      name: extended_image_library
      sha256: a7cc0270299589ba12b21152abd8ac7287ac8e1997c7ce1a26c337bac4429208
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  extended_nested_scroll_view:
    dependency: transitive
    description:
      name: extended_nested_scroll_view
      sha256: "835580d40c2c62b448bd14adecd316acba469ba61f1510ef559d17668a85e777"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  family:
    dependency: "direct main"
    description:
      name: family
      sha256: b8be1417e58209b1b4cf754a345e26e156a3c5d53bf6a4b2c2934e7cd9cac822
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024121001"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.1"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: ceff65d74d907b1b772e22cf04daad60fb472461638977d9fae8b00a63e01e3d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.3"
  flutter_color_models:
    dependency: transitive
    description:
      name: flutter_color_models
      sha256: "8454198ba9a82e533452d0764f1df3d6c3ccc2b33e18eb1f2fedc7ae4a5c43be"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.3+2"
  flutter_common_ui:
    dependency: "direct main"
    description:
      name: flutter_common_ui
      sha256: b9484090cab422677f540a823df22d4a3e29aa1e8e1286a4a37c4d9070e2b1cc
      url: "http://**************:8083"
    source: hosted
    version: "10.0.0+2025052101"
  flutter_displaymode:
    dependency: transitive
    description:
      name: flutter_displaymode
      sha256: "42c5e9abd13d28ed74f701b60529d7f8416947e58256e6659c5550db719c57ef"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_image:
    dependency: transitive
    description:
      name: flutter_native_image
      sha256: "2a0d53e50bdd6ddc681cb2697554971d6b4bd986fdc0f66fb75fd73da7877be4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.6"
  flutter_picker:
    dependency: transitive
    description:
      name: flutter_picker
      sha256: "5bda0e7beca43bbcbd12423aa713374ba3fac3e464d74e11a942f2ccb1799c77"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  flutter_redux:
    dependency: "direct main"
    description:
      name: flutter_redux
      sha256: "8985fd9a4f4016be6acc058818b7bc4cd43f65d134e6507a6e017ac5b499cd82"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.2"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "03714dd992a90453f2eb48c08d1caa0769d366ee5dc647cf9d23c5b320dab2cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.0+2"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: bd4c27ac82bfece6b9dce68b23063f894cefbd9cb3af98743e488c01141f5461
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.0"
  flutter_staggered_grid_view:
    dependency: transitive
    description:
      name: flutter_staggered_grid_view
      sha256: f0b6d8c0fa7b4b444985cdde68492c0138a4fb6fc57a641b24cb234b7ee0f5c4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: d39e7f95621fc84376bc0f7d504f05c3a41488c562f4a8ad410569127507402c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.9"
  flutter_swiper_null_safety:
    dependency: transitive
    description:
      name: flutter_swiper_null_safety
      sha256: "5a855e0080d035c08e82f8b7fd2f106344943a30c9ab483b2584860a2f22eaaf"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "2f9c4d3f4836421f7067a28f8939814597b27614e021da9d63e5d3fb6e212d25"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.2.1"
  fullscreen_player:
    dependency: "direct main"
    description:
      name: fullscreen_player
      sha256: "49448fe080952a04c8d170052008def26e1937289c396753692d909934d16ea6"
      url: "http://**************:8083"
    source: hosted
    version: "9.0.0+2024091001"
  function_toggle:
    dependency: "direct main"
    description:
      name: function_toggle
      sha256: dc37aa26fb1339efec37be8867944a25af1500820c6c45ae53dcbf19993011ac
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024053102"
  gif:
    dependency: transitive
    description:
      name: gif
      sha256: ade95694f1471da737922806818ffade2814d1d7f8d10af38ebcf36ace012bc0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  gradient_borders:
    dependency: transitive
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  gyflut:
    dependency: transitive
    description:
      name: gyflut
      sha256: "1510eea0c83c62731069b14482751d5d95e977ddd72a3dfc3fbb1f62cdd393c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.6"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "759d1a329847dd0f39226c688d3e06a6b8679668e350e2891a6474f8b4bb8525"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  http_client_helper:
    dependency: "direct dev"
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  image_crop:
    dependency: transitive
    description:
      name: image_crop
      sha256: "9a299c15dedcef2637469ff7d6321751fa21404f95cdbb67a80c75db335fc31c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0"
  image_editor:
    dependency: transitive
    description:
      name: image_editor
      sha256: "9877a057b0cd2fafcd9a3dce5279948bd850d53ce76231a83c9678a2c9f186e9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: "93d2f5c8b636f862775dd62a9ec20d09c8272598daa02f935955a4640e1844ee"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.18.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.8.1"
  keframe:
    dependency: transitive
    description:
      name: keframe
      sha256: a84add1826c54cd68e6095dbcc00cdd84fd4d97820712bfc8e1d06f41b0bfac3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "78eb209deea09858f5269f5a5b02be4049535f568c07b275096836f01ea323fa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "10.0.0"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: b46c5e37c19120a8a01918cfaf293547f47269f7cb4b0058f21531c2465d6ef0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: a597f72a664dbd293f3bfc51f9ba69816f84dcd403cdac7066cb3f6003f3ab47
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  library_widgets:
    dependency: transitive
    description:
      name: library_widgets
      sha256: "46153a10c5c7b08fbdb85618e80049ac4af2187a9a3215d026b150e614f8f210"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024080102"
  liveforuplus:
    dependency: transitive
    description:
      name: liveforuplus
      sha256: "630a86f472abd4eb32d4e77c40cc2e9ede7f808f71aac0f33e884d98744bd185"
      url: "http://**************:8083"
    source: hosted
    version: "1.0.21"
  location:
    dependency: "direct main"
    description:
      name: location
      sha256: "236eb396cdef30f7649f29e395ed38da3d77d66baa4d191c66c5e53e20e91f8b"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024060301"
  log:
    dependency: "direct main"
    description:
      name: log
      sha256: a38d0b2df4d7feec05c3e7ecc2d1eb021999d9bf79649705a8efe0eb0d41b6ad
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024102901"
  login:
    dependency: "direct main"
    description:
      name: login
      sha256: "940d8c3783f21d84a9c808c9f6861c10747a373926d141bd25200099df06da70"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025061701"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: b8bdd54b488c54068c57d41ae85d02808da09e2bee8b8dd1f59f441e7efa60cd
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.6.0"
  main_business:
    dependency: "direct main"
    description:
      name: main_business
      sha256: "75b6d7defc48ff663d5bd4a307f1f507965daf38aa18bdb1167cb5ece26cce16"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011501"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "0e0a020085b65b6083975e499759762399b4475f766c21668c4ecca34ea74e5a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.0"
  memoize:
    dependency: transitive
    description:
      name: memoize
      sha256: "51481d328c86cbdc59711369179bac88551ca0556569249be5317e66fc796cac"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  message:
    dependency: "direct main"
    description:
      name: message
      sha256: "3f785efcb3096a994ff0bf06ae000e12bd4022bda9834cfa893ae9df48959160"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010901"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: d584fa6707a52763a52446f02cc621b077888fb63b93bbcb1143a7be5a0c0c04
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.0"
  multiengines:
    dependency: transitive
    description:
      name: multiengines
      sha256: e02ba9886c6cbbe011c37705c92f23d0515a21f53c101f619cbf60895c3aa66f
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025030701"
  native_device_orientation:
    dependency: transitive
    description:
      name: native_device_orientation
      sha256: ea0b70eb34a2484e51888e64381d9aaefc10fce45f1b029d737a873f9dbf164c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  num_utilities:
    dependency: transitive
    description:
      name: num_utilities
      sha256: "170695bcafd17a19ee3a060325e7e10fb35bb878c8bcb3e6f5691dde1462c0ae"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.5"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: b27217933eeeba8ff24845c34003b003b2b22151de3c908d0e679e8fe1aa078b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: a248d8146ee5983446bf03ed5ea8f6533129a12b11f12057ad1b4a67a2b3b41d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.4"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.1"
  personal_information:
    dependency: transitive
    description:
      name: personal_information
      sha256: cc6baa0f83ae8b719027d15208c76d2af6d9d0057c57c60ebc2df3ca367aedd5
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+**********"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.2"
  photo:
    dependency: "direct main"
    description:
      name: photo
      sha256: "4f0463104598638383eeb20cf90955c36cafb5ffa78e88941dc4ddf9c3735d6c"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025031101"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: "11bfda6b2c821062e6b655107e75f758e359b7160f95c2240a72034184a74243"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010201"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.14.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.6"
  plugin_device:
    dependency: "direct main"
    description:
      name: plugin_device
      sha256: "9f5e68cc954903045f8cf068292677c1b7cf1a8fbcfad5de6066200a059519c5"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025031101"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: c384b19bf8d317b3d1576327203cdc95f96bf5f109ab63ab72690fe32fdb0d3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  plugin_test_page:
    dependency: "direct main"
    description:
      name: plugin_test_page
      sha256: b60a94ec00ad90bbf16b8260ad4d103663bca510a7290a8e4644123c581a233f
      url: "http://**************:8083"
    source: hosted
    version: "1.1.0+2024012501"
  plugin_upgrade:
    dependency: transitive
    description:
      name: plugin_upgrade
      sha256: "0a3d7eb55aa17c0b1e8aaceb121b58f74242399c4361c25c0fdbaf822238b97a"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025021703"
  plugin_usdk:
    dependency: transitive
    description:
      name: plugin_usdk
      sha256: "4994171010d7c966bc04bec97ff2a5bd3e3d8ca4b2c49b8d281c260ceb0e45c2"
      url: "http://**************:8083"
    source: hosted
    version: "1.4.0+**********"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.9.1"
  powers:
    dependency: transitive
    description:
      name: powers
      sha256: "389ba222d4264655ecd3ffa461921bc707a07e4597b98831d21dd516eed6f496"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0+2"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: cdbe7530b12ecd9eb455bdaa2fcb8d4dad22e80b8afb4798b41479d5ce26847f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.5"
  pullToRefreshNew:
    dependency: transitive
    description:
      name: pullToRefreshNew
      sha256: "8f151794ecde9f141a866a175346f66d9738bf4f62ebb06921f53054f9f94699"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+**********"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: "5e592c348a6c528fb8deb7cc7d85a7097ce65bf2349121ad004d1fc5d5905eaa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  r_scan:
    dependency: transitive
    description:
      name: r_scan
      sha256: "1e23d78603a187b22cb61448c0f1a7dd958f70cfbef30c0b360a0cce6b2100dd"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+**********"
  redux:
    dependency: "direct main"
    description:
      name: redux
      sha256: "1e86ed5b1a9a717922d0a0ca41f9bf49c1a587d50050e9426fc65b14e85ec4d7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.0.0"
  reorderable_grid_view:
    dependency: transitive
    description:
      name: reorderable_grid_view
      sha256: e36c6229a97105a10c79e15ab4b9b14ee9f6c488574ff2be9e858c82af47cda6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.8"
  reorderable_plus:
    dependency: transitive
    description:
      name: reorderable_plus
      sha256: f5bad1d92fa42788d142c8ca12bf3844d2ce4d47f913376667a5ad533d53eab0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.2"
  reselect:
    dependency: transitive
    description:
      name: reselect
      sha256: "7b01878d91ab22148001bd04b0da9577444d27636d940110524772ac664f5b88"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.0"
  resource:
    dependency: transitive
    description:
      name: resource
      sha256: ad9e564c343ec8285a9db1874d4821ff771fd9c7783c4813a810bb4ce634b8f5
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011401"
  retrofit:
    dependency: transitive
    description:
      name: retrofit
      sha256: "13a2865c0d97da580ea4e3c64d412d81f365fd5b26be2a18fca9582e021da37a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.7"
  scan:
    dependency: "direct main"
    description:
      name: scan
      sha256: "88544548b99c1ed2a4eacb82316782cb48c13f159a3c4603da8f7d6cd0b77142"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025030702"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.8"
  setting:
    dependency: transitive
    description:
      name: setting
      sha256: b4edf5ea6ec8b35793529bd717b4edb97330def8b50954eb7748aa60a84db0df
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025030801"
  share:
    dependency: transitive
    description:
      name: share
      sha256: "19e449024801bf677ebcac5a0a23c2a9ae879e350264ec3bae69bb2f5f210e55"
      url: "http://**************:8083"
    source: hosted
    version: "1.3.2+2024112801"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "78528fd87d0d08ffd3e69551173c026e8eacc7b7079c82eb6a77413957b7e394"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.20"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "1ee8bf911094a1b592de7ab29add6f826a7331fb854273d55918693d5364a1f2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "0a8a893bf4fd1152f93fec03a415d11c27c74454d96e2318a7ac38dd18683ab7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "7b15ffb9387ea3e237bb7a66b8a23d2147663d391cafc5c8f37b2e7b4bde5d21"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliver_tools:
    dependency: transitive
    description:
      name: sliver_tools
      sha256: eae28220badfb9d0559207badcbbc9ad5331aac829a88cb0964d330d2a4636a6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.12"
  smart_home:
    dependency: "direct main"
    description:
      name: smart_home
      sha256: "288ab5a915ffe404be20bfba0d8a74a443584244519d351b0068b7c047c9ffeb"
      url: "http://**************:8083"
    source: hosted
    version: "10.3.0+2025071504"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: a43e5a27235518c03ca238e7b4732cf35eabe863a369ceba6cbefa537a66f16d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.3+1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "3da423ce7baf868be70e2c0976c28a1bb2f73644268b7ffa7d2e08eab71f16a4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.5.4"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.1"
  storage:
    dependency: "direct main"
    description:
      name: storage
      sha256: "72f30da3d1ecce24c32ac3eb073a39223ad149c56171cd32795e340a98aa43fa"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024061701"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ed464977cb26a1f41537e177e190c67223dbd9f4f683489b6ab2e5d211ec564e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  syn_dotted_border:
    dependency: transitive
    description:
      name: syn_dotted_border
      sha256: "5ab5ad633da77fc1986c99c7a0c6367827822254f52fcb844b042fc091ce0ba6"
      url: "http://**************:8083"
    source: hosted
    version: "3.4.0+2024011901"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.1"
  trace:
    dependency: "direct main"
    description:
      name: trace
      sha256: "6cbd27024a7b93826d8c06bbee3888551983ff86bb054c990ff8fc6a962e9ef3"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011401"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  ugc:
    dependency: "direct main"
    description:
      name: ugc
      sha256: e9b6dea2cd49058d22ea9a032c8b3fe3f6f61c9a7f15b68f5cb0fb27f7f8d430
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024082702"
  uimessage:
    dependency: "direct main"
    description:
      name: uimessage
      sha256: "9fcb3d6f684aa7c30a7779e2bfabb1b77ae1a4fd33055dba83cc8909184302c8"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024112802"
  umeng:
    dependency: "direct main"
    description:
      name: umeng
      sha256: f220583b0dfd3f1f021ae2194ae38bc61ba4227ad39e2851fb45bc4e3acd2046
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025011401"
  uplusai:
    dependency: "direct main"
    description:
      name: uplusai
      sha256: "3d2cddc120baf3067311fd01a4018e7bf475a09d8cb23f88aaec1a764a9c3bc1"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024052301"
  uplustrace:
    dependency: "direct main"
    description:
      name: uplustrace
      sha256: "66feb5f0fb2e891a0877f5ba629c02914ba77000e6afb88c565397c245a7abad"
      url: "http://**************:8083"
    source: hosted
    version: "1.2.0+2024012601"
  uppermission:
    dependency: "direct main"
    description:
      name: uppermission
      sha256: "2abdc0f3987c37e0298444642c74fff0674b71f6168526ace9fd77d60fba993a"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024060301"
  upservice:
    dependency: transitive
    description:
      name: upservice
      sha256: "36f7f44b42c31573b42cc8191a97875a269ee1e8f8d5f849be4739b16b77e76f"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024121801"
  upsystem:
    dependency: "direct main"
    description:
      name: upsystem
      sha256: ccb9000e4b3adf375b737880530883e44ed665e3aa6b324018e12c3a2d1ec16f
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024102302"
  url_launcher:
    dependency: "direct dev"
    description:
      name: url_launcher
      sha256: c512655380d241a337521703af62d2c122bf7b77a46ff7dd750092aa9433499c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.4"
  url_launcher_android:
    dependency: "direct dev"
    description:
      name: url_launcher_android
      sha256: "78cb6dea3e93148615109e58e42c35d1ffbf5ef66c44add673d0ab75f12ff3af"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.37"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "16a513b6c12bb419304e72ea0ae2ab4fed569920d1c7cb850263fe3acc824626"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.2"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "4aca1e060978e19b2998ee28503f40b5ba6226819c2b5e3e4d1821e8ccd92198"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: fff0932192afeedf63cdd50ecbb1bc825d31aed259f02bb8dba0f3b729a5e88b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.3"
  user:
    dependency: "direct main"
    description:
      name: user
      sha256: "6624ebaa103a97e773e953d2d2104beaafa19fc3aff531560a65dc40fae38197"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024121001"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.1"
  vdn:
    dependency: "direct main"
    description:
      name: vdn
      sha256: beb1d1db62af4fdf05f383c4ab18ff08765943d78bf83341ae19935f5eb742f0
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025012301"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "32c3c684e02f9bc0afb0ae0aa653337a2fe022e8ab064bcd7ffda27a74e288e3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: c86987475f162fadff579e7320c7ddda04cd2fdeffbe1129227a85d9ac9e03da
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "12faff3f73b1741a36ca7e31b292ddeb629af819ca9efe9953b70bd63fc8cd81"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "21dac219ecef5588a58c27d493d4969f911d8fdc0e3d2247165d7cafa1dbd3e3"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010201"
  video_player_platform_interface:
    dependency: "direct main"
    description:
      name: video_player_platform_interface
      sha256: "33107b16b90d8a7fafd00e17983090ece6daa3e49f5b6dba63fa7380ac2bfae5"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2025010202"
  videoview:
    dependency: transitive
    description:
      name: videoview
      sha256: "5f4c020f84d9494415871912ab43bb783759bce9b2406d4678dd63f20032b58a"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024091101"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: b3d56ff4341b8f182b96aceb2fa20e3dcb336b9f867bc0eafc0de10f1048e957
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "13.0.0"
  wash_device_manager:
    dependency: transitive
    description:
      name: wash_device_manager
      sha256: "77afb961f0c76a0d533308138db9e497d91bd7083e4ada913a7435de461c9310"
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024122803"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "0b7fd4a0bbc4b92641dbf20adfd7e3fd1398fe17102d94b674234563e110088a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.2"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "4188706108906f002b3a293509234588823c8c979dc83304e229ff400c996b05"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "3a969ddcc204a3e34e863d204b29c0752716f78b6f9cc8235083208d268a4ccd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  whole_house_air:
    dependency: "direct main"
    description:
      name: whole_house_air
      sha256: "0294dc1d61534c0845b5f1fe7129e1a9ff0e9df84ee8b9ea75e820b143e1e90a"
      url: "http://**************:8083"
    source: hosted
    version: "9.3.0+2025031101"
  whole_house_music:
    dependency: transitive
    description:
      name: whole_house_music
      sha256: bb6b77f9f4e0c8e7d0912d682e2a737cb887c98389fc93e493e0d1124d804473
      url: "http://**************:8083"
    source: hosted
    version: "999.999.999+2024122601"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.5.0"
sdks:
  dart: ">=3.3.1 <4.0.0"
  flutter: ">=3.19.3"
