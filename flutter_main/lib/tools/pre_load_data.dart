import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:Appinfos/Appinfos.dart';
import 'package:Appinfos/AppinfosModel.dart';
import 'package:family/family.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_main/config/const_string.dart';
import 'package:flutter_main/utils/log_util.dart';
import 'package:message/message.dart';
import 'package:path_provider/path_provider.dart';
import 'package:user/user.dart';

import '../model/mourning_mode_model.dart';
import '../store/action/bottom_bar_actions.dart';
import '../store/bottom_bar_store.dart';
import '../utils/common_util.dart';
import 'mourning_mode_manager.dart';

class PreLoadData {
  static ServerEnv _serverEvn = ServerEnv.UNKNOWN;

  ///当前应用appId
  static AppInfoModel? _appInfoModel;

  static BuildContext? context;

  ///当前用户的个人邀请码
  static String? userInviteCode;

  ///当前家庭下是否有设备（此时已为登录状态）
  static bool isDeviceUser = false;

  /// 是否登录状态
  static bool isLogin = false;

  static int currentIndex = 0;
  static int preIndex = 0;
  //初始化控制器
  static PageController? pageController;

  // 启动参数
  static Map<String, String> startArgs = <String, String>{};
  static List<String>? argList;

  //预加载子任务
  static Future<void> getAppInfoTask() async {
    try {
      // init _appInfoModel
      _appInfoModel = await AppInfoPlugin.getAppInfo();
      // init _serverEnv
      _serverEvn = _fromType(_appInfoModel!.env);
    } catch (error) {
      LogUtil.i('_initGuideShown exception $error', tag: 'flutter_main');
    }
    return;
  }

  ///异步加载所需native资源
  static void init() {
    LogUtil.i('preloadDatas init start', tag: 'flutter_main');
    User.initCachedUserData();
    Family.initCachedFamilyData();
    LogUtil.i('preloadDatas', tag: 'flutter_main');
  }

  // 主页面初始化
  static void initForMainPage() {
    LogUtil.i('initForMainPage with args: $argList', tag: 'flutter_main');
    PreLoadData.handleArgsForStart(argList);
    Message.initCacheMsgListByName('flutter_package_main_jump_tabbar');
    isDeviceUser = startArgs['device_user'] == 'true';
    isLogin = startArgs['is_logined'] == 'true';
    PreLoadData.getXiaoUStatus();
    PreLoadData.initTabIndex();
    PreLoadData.initMourningStatus();
  }

  // 处理启动参数
  static void handleArgsForStart(List<String>? args) {
    LogUtil.i('handleArgsForStart before: $args', tag: 'flutter_main');
    if (args == null) {
      LogUtil.i('handleArgsForStart args is null', tag: 'flutter_main');
      return;
    }
    // 跳过第一个URL参数
    for (int i = 1; i < args.length; i += 2) {
      // 只遍历存储的key，紧跟的数据为其对应的value
      startArgs[args[i]] = args[i + 1];
    }
    LogUtil.i('handleArgsForStart after: $startArgs', tag: 'flutter_main');
  }

  // 初始化APP启动进入哪一个页面
  static void initTabIndex() {
    LogUtil.d(
        'PreLoadData init isDeviceUser: ${PreLoadData.isDeviceUser} | isLogin: ${PreLoadData.isLogin}',
        tag: 'flutter_main');
    if (!PreLoadData.isLogin) {
      // 未登录默认进智家
      currentIndex = 0;
      preIndex = 0;
    } else if (!PreLoadData.isDeviceUser) {
      // 已登录非网器用户
      currentIndex = 1;
      preIndex = 1;
    } else {
      currentIndex = 0;
      preIndex = 0;
    }
    pageController = PageController(initialPage: currentIndex);
    bottomBarStore.dispatch(UpdateCurrentIndexAction(currentIndex));
  }

  // 初始化哀悼模式状态
  static void initMourningStatus() {
    final bool mourningStatus =
        startArgs['uplus_mourning_mode_enable_status'] == 'true';
    if (mourningStatus) {
      // 哀悼模式生效时
      final String str = startArgs['uplus_mourning_mode_status_string'] ?? '';
      if (str.isEmpty) {
        return;
      }
      LogUtil.d('PreLoadData initMourningStatus: $str', tag: 'flutter_main');
      final Map<String, dynamic> map =
          FlutterMainUtils.convertType<Map<dynamic, dynamic>>(
              json.decode(str), <String, dynamic>{}).cast<String, dynamic>();
      final MourningModeModel mourningModeModel =
          MourningModeModel.fromJSON(map);
      MourningModeManager.handleModeEvent(mourningModeModel);
    }
  }

  static void getXiaoUStatus() {
    final bool serverFlag =
        startArgs['voice_assistant_server_switch'] == 'true';
    LogUtil.d('PreLoadData getXiaoUStatus server key flag is $serverFlag ',
        tag: 'flutter_main');

    bottomBarStore.dispatch(UpdateXiaoUStatusAction(serverFlag));
  }

  static Future<void> cleanPhoto() async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final Directory director = Directory('${appDir.path}/flutter-images');
    if (!director.existsSync()) {
      return;
    }

    final Stream<FileSystemEntity> entityList =
        director.list(recursive: false, followLinks: false);
    await for (final File entity in entityList.cast()) {
      LogUtil.d('FileSystemEntity ${entity.path} ${entity.lastModifiedSync()}',
          tag: 'flutter_main');
      final DateTime today = DateTime.now();
      final DateTime sevenDaysAgo = today.subtract(const Duration(days: 7));
      if (sevenDaysAgo.compareTo(entity.lastModifiedSync()) >= 0) {
        await entity.delete();
      }
    }
  }

  static AppInfoModel? getAppInfo() {
    return _appInfoModel;
  }

  static ServerEnv getServerEnv() {
    return _serverEvn;
  }

  static ServerEnv _fromType(String env) {
    if (env == '生产') {
      return ServerEnv.SHENGCHAN;
    } else if (env == '验收') {
      return ServerEnv.YANSHOU;
    } else {
      return ServerEnv.UNKNOWN;
    }
  }
}
