class ConstString {
  /// 缓存路径
  static const String FILE_DIR = '/cache_file_dir/';

  /// 哀悼模式缓存key
  static const String MOURNING_MODE_KEY = 'uplus_mourning_mode_status';
  static const String LOCAL_MOURNING_MODE_KEY =
      'uplus_mourning_mode_status_string';

  /// 小优入口相关key
  static const String XIAO_U_SERVER_KEY = 'voice_assistant_server_switch';
  static const String XIAO_U_DETAIL_PAGE_KEY = 'enter_voice_assistant_page';

  /// tab切换gio打点
  static const List<String> TRACES_GIO_FOR_TAB_SWITCH = <String>[
    'MB10113', // 智家
    'MB10112', // 服务
    'MB10115', // 商城
    'MB10116', // 我的
  ];

  // 智家app-回到顶部，小火箭点击上报点位
  static const String TRACE_GIO_FOR_BACK_TOP_CLICK = 'MB35568';

  static const String NETWORK_ERROR_TIP = '网络不可用';

  /// 用户通过设置的账号与安全注销成功
  static const String KEY_USER_ACCOUNT_CANCELED =
      'user_account_cancelled_from_setting';

  /// 冒泡通用类型
  static const String U_API_TYPE_ACTIVE_SERVICE_MSG = 'ACTIVE_SERVICE_MSG';

  /// 弹窗展示点位
  static const String TRACE_GIO_FOR_ALERT_DIALOG_SHOW = 'MB35768';

  /// 弹窗点击点位
  static const String TRACE_GIO_FOR_ALERT_DIALOG_DETAIL = 'MB35770';

  /// 我的Tab小红点缓存Key
  static const String APP_MINE_RED_POINT_KEY = 'app_mine_red_point_status';
}

///环境enum
enum ServerEnv { SHENGCHAN, YANSHOU, UNKNOWN }

///语音智能卡片类型
enum CardType { CARD_WEATHER, CARD_DEVICE, CARD_SKILL, CART_UNKNOWN }
