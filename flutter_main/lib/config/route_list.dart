import 'package:about_us/aboutUs.dart';
import 'package:app_mine/birthday_info/family_birthday.dart';
import 'package:app_mine/gpt_center/gpt_center_widget.dart';
import 'package:app_mine/screens/pocket/pocket_widget.dart';
import 'package:app_mine/screens/purse/purse_widget.dart';
import 'package:bind_scan/bind_scan.dart';
import 'package:camera_camera/camera_camera.dart';
import 'package:flutter/material.dart';
import 'package:fullscreen_player/fullscreen_player.dart';
import 'package:liveforuplus/live.dart';
import 'package:login/login.dart';
import 'package:multiengines/route_manager.dart';
import 'package:nested/nested.dart';
import 'package:personal_information/pages/address.dart';
import 'package:personal_information/pages/name.dart';
import 'package:personal_information/pages/newAddress.dart';
import 'package:personal_information/pages/nickname.dart';
import 'package:personal_information/personalInformation.dart';
import 'package:personal_information/viewmodels/address_info_view_model.dart';
import 'package:personal_information/viewmodels/address_list_view_model.dart';
import 'package:personal_information/viewmodels/controller_input_view_model.dart';
import 'package:personal_information/viewmodels/current_location_view_model.dart';
import 'package:personal_information/viewmodels/personal_address_view_model.dart';
import 'package:personal_information/viewmodels/show_input_view_model.dart';
import 'package:personal_information/viewmodels/user_info_view_model.dart';
import 'package:personal_information/viewmodels/user_input_view_model.dart';
import 'package:personal_information/viewmodels/user_name_view_model.dart';
import 'package:personal_information/viewmodels/user_nickname_view_model.dart';
import 'package:photo/photo.dart';
import 'package:provider/provider.dart';
import 'package:scan/pages/security_migration.dart';
import 'package:scan/scan.dart';
import 'package:setting/setting.dart';
import 'package:smart_home/device/device_guide/device_guide_widget.dart';
import 'package:smart_home/scene/scene_manage/scene_manage_page.dart';
import 'package:smart_home/whole_house/device_consumables/widgets/consumables_detail_page.dart';
import 'package:ugc/pages/selectPublishType/selectPublishType.dart';
import 'package:whole_house_air/presentation/single_house_air.dart';
import 'package:smart_home/device/aggregation/aggregation_setting/aggregation_setting_sheet.dart';
import 'package:whole_house_air/wholehouseair.dart';

import '../pages/main_index_new.dart';
import '../utils/log_util.dart';

class RouteList {
  static String tag = 'route_list';
  static Map<String, RouteFunction> routes = <String, RouteFunction>{
    'flutter://index.html': (Map<String, String> args) async => MainIndexPage(),
    'flutter://setting.html': (Map<String, String> args) async {
      return const Setting();
    },
    'flutter://appmine/aboutUs': (Map<String, String> args) async {
      return AboutUs();
    },
    'flutter://smarthome/settings': (Map<String, String> args) async {
      return AggregationSettingSheet(
        familyId: FlutterModuleUtils.convertType<String>(args['familyId'], ''),
        roomId: FlutterModuleUtils.convertType<String>(args['roomId'], ''),
      );
    },
    'flutter://appmine/gptCenter': (Map<String, String> args) async {
      return const GptCenterWidget();
    },
    'flutter://myPause.html': (Map<String, String> args) async {
      return const Purse();
    },
    'flutter://myCard.html': (Map<String, String> args) async {
      return const Pocket();
    },
    'flutter://deviceGuide.html': (Map<String, String> args) async {
      return DeviceGuideWidget(params: args);
    },
    'flutter://xiaou/familyBirthdayInfo': (Map<String, String> args) async {
      return const FamilyBirthday();
    },
    'flutter://photocheck': (Map<String, String> params) async {
      final String fromScanStr = '${params['isFromScan']}';
      return gotoPhotoCheck(
        isFromScan: fromScanStr == 'true',
        maxSelected: int.parse(
            FlutterModuleUtils.convertType<String>(params['max'], '1')),
        needClip: params['cropImage'] != null
            ? (params['cropImage'] == 'true' ? 1 : 0)
            : 0,
        appType:
            FlutterModuleUtils.convertType<String?>(params['appType'], null),
        typeMode: params['typeMode'] != null
            ? int.parse(
                FlutterModuleUtils.convertType<String>(params['typeMode'], '0'))
            : 0,
        singleChoice: setSingleChoice(params, false),
        originalImage: params['showOriginalBtn'] == 'true',
        isOriginalCheck: getOriginal(params),
        videoLimitSecond: params['videoLimitSecond'] != null
            ? int.parse(FlutterModuleUtils.convertType<String>(
                params['videoLimitSecond'], '60'))
            : 60,
        allFileSizeByteLimit: params['allFileSizeByteLimit'] != null
            ? int.parse(FlutterModuleUtils.convertType<String>(
                params['allFileSizeByteLimit'], '${1000 * 1024 * 1024}'))
            : 1000 * 1024 * 1024,
        clipRatios: params['clipPicParam'] != null
            ? getClipRatiosFromString(FlutterModuleUtils.convertType<String?>(
                params['clipPicParam'], null))
            : null,
        singleFileSizeByteLimit: params['singleFileSizeByteLimit'] != null
            ? int.parse(FlutterModuleUtils.convertType<String>(
                params['singleFileSizeByteLimit'], '${1000 * 1024 * 1024}'))
            : 1000 * 1024 * 1024,
        scanQRCode: FlutterModuleUtils.convertType<String>(
            params['scanQRCode'], 'false'),
      );
    },
    'flutter://cameraentry': (Map<String, String> params) async {
      LogUtil.i('---camera---inputparams:$params', tag: tag);
      final String boolStr = '${params['saveToPhotoAlbum']}';
      final String fromScanStr = '${params['isFromScan']}';
      return gotoCamera(
        clipPicParam: FlutterModuleUtils.convertType<String?>(
            params['clipPicParam'], null),
        isFromScan: fromScanStr == 'true',
        destinationType: params['destinationType'] != null
            ? int.parse(FlutterModuleUtils.convertType<String>(
                params['destinationType'], '1'))
            : 1,
        quality: params['quality'] != null
            ? int.parse(FlutterModuleUtils.convertType<String>(
                params['quality'], '100'))
            : 100,
        saveToPhotoAlbum: boolStr == 'true',
      );
    },
    'flutter://bindscan': (Map<String, String> params) async {
      return gotoBindQrScan(params: params);
    },
    'flutter://qrscan': (Map<String, String> params) async {
      return gotoQrScan(
          params: FlutterModuleUtils.convertType<Map<dynamic, dynamic>?>(
                  params, null)
              ?.cast<String, dynamic>());
    },
    'flutter://qrGeneralScan': (Map<String, String> params) async {
      return gotoQrGeneralScan(
        scanTitle:
            FlutterModuleUtils.convertType<String?>(params['scanTitle'], null),
        scanContent: FlutterModuleUtils.convertType<String?>(
            params['scanContent'], null),
        scanError:
            FlutterModuleUtils.convertType<String?>(params['scanError'], null),
        scanRules:
            FlutterModuleUtils.convertType<String?>(params['scanRules'], null),
        btn1_Title:
            FlutterModuleUtils.convertType<String?>(params['btn1_Title'], null),
        btn1_Link:
            FlutterModuleUtils.convertType<String?>(params['btn1_Link'], null),
        btn2_Title:
            FlutterModuleUtils.convertType<String?>(params['btn2_Title'], null),
        btn2_Link:
            FlutterModuleUtils.convertType<String?>(params['btn2_Link'], null),
        isShowAlbum: params['showAlbum'] == '1',
        highLightContent: FlutterModuleUtils.convertType<String>(
            params['highLightContent'], ''),
        showDefaultIcon: FlutterModuleUtils.convertType<String>(
            params['showDefaultIcon'], 'false'),
      );
    },
    'flutter://fullscreen.player/videoPlayer':
        (Map<String, String> params) async {
      return gotoVideoPlayer(
          params: FlutterModuleUtils.convertType<Map<dynamic, dynamic>>(
              params, <String, dynamic>{}).cast<String, dynamic>());
    },
    'flutter://personalInfo.html': (Map<String, String> params) async {
      return PersonalInformation();
    },
    'flutter://personal/nickname': (Map<String, String> params) async {
      return MultiProvider(
        providers: <SingleChildWidget>[
          ChangeNotifierProvider<UserNicknameViewModel>(
              create: (BuildContext context) => UserNicknameViewModel()),
          ChangeNotifierProvider<UserInputViewModel>(
              create: (BuildContext context) => UserInputViewModel()),
        ],
        child: NickNamePage(
          nickname:
              FlutterModuleUtils.convertType<String>(params['nickname'], ''),
          mobile: FlutterModuleUtils.convertType<String>(params['mobile'], ''),
        ),
      );
    },
    'flutter://personal/name': (Map<String, String> params) async {
      return MultiProvider(
        providers: <SingleChildWidget>[
          ChangeNotifierProvider<UserNameViewModel>(
              create: (BuildContext context) => UserNameViewModel()),
          ChangeNotifierProvider<UserInputViewModel>(
              create: (BuildContext context) => UserInputViewModel()),
        ],
        child: NamePage(
          name: FlutterModuleUtils.convertType<String>(params['name'], ''),
        ),
      );
    },
    'flutter://personal/address': (Map<String, String> params) async {
      return MultiProvider(
        providers: <SingleChildWidget>[
          ChangeNotifierProvider<PersonalAddressViewModel>(
              create: (BuildContext context) => PersonalAddressViewModel()),
          ChangeNotifierProvider<CurrentLocationViewModel>(
              create: (BuildContext context) => CurrentLocationViewModel()),
        ],
        child: AddressPage(),
      );
    },
    'flutter://personal/newaddress': (Map<String, String> params) async {
      return MultiProvider(
        providers: <SingleChildWidget>[
          ChangeNotifierProvider<AddressInfoViewModel>(
              create: (BuildContext context) => AddressInfoViewModel()),
          ChangeNotifierProvider<AddressInputViewModel>(
              create: (BuildContext context) => AddressInputViewModel()),
          ChangeNotifierProvider<UserInfoViewModel>(
              create: (BuildContext context) => UserInfoViewModel()),
          ChangeNotifierProvider<ControllerInputViewModel>(
              create: (BuildContext context) => ControllerInputViewModel()),
          ChangeNotifierProvider<AddressListViewModel>(
              create: (BuildContext context) => AddressListViewModel()),
        ],
        child: NewAddressPage(
          title: '新建地址',
          provinceName: FlutterModuleUtils.convertType<String>(
              params['provinceName'], ''),
          cityName:
              FlutterModuleUtils.convertType<String>(params['cityName'], ''),
          countyName:
              FlutterModuleUtils.convertType<String>(params['countyName'], ''),
          addressId: '',
          isDefault: '',
          receiverMobile: '',
          receiverName: '',
          provinceId: '',
          cityId: '',
          countyId: '',
          line1: '',
          line2: '',
        ),
      );
    },
    'flutter://personal/editaddress': (Map<String, String> params) async {
      return MultiProvider(
        providers: <SingleChildWidget>[
          ChangeNotifierProvider<AddressInfoViewModel>(
              create: (BuildContext context) => AddressInfoViewModel()),
          ChangeNotifierProvider<AddressInputViewModel>(
              create: (BuildContext context) => AddressInputViewModel()),
          ChangeNotifierProvider<UserInfoViewModel>(
              create: (BuildContext context) => UserInfoViewModel()),
          ChangeNotifierProvider<ControllerInputViewModel>(
              create: (BuildContext context) => ControllerInputViewModel()),
          ChangeNotifierProvider<AddressListViewModel>(
              create: (BuildContext context) => AddressListViewModel()),
        ],
        child: NewAddressPage(
          title: '编辑地址',
          addressId:
              FlutterModuleUtils.convertType<String>(params['addressId'], ''),
          isDefault:
              FlutterModuleUtils.convertType<String>(params['isDefault'], ''),
          receiverMobile: FlutterModuleUtils.convertType<String>(
              params['receiverMobile'], ''),
          receiverName: FlutterModuleUtils.convertType<String>(
              params['receiverName'], ''),
          provinceName: FlutterModuleUtils.convertType<String>(
              params['provinceName'], ''),
          provinceId:
              FlutterModuleUtils.convertType<String>(params['provinceId'], ''),
          cityName:
              FlutterModuleUtils.convertType<String>(params['cityName'], ''),
          cityId: FlutterModuleUtils.convertType<String>(params['cityId'], ''),
          countyName:
              FlutterModuleUtils.convertType<String>(params['countyName'], ''),
          countyId:
              FlutterModuleUtils.convertType<String>(params['countyId'], ''),
          line1: FlutterModuleUtils.convertType<String>(params['line1'], ''),
          line2: FlutterModuleUtils.convertType<String>(params['line2'], ''),
        ),
      );
    },
    'flutter://scan/migration': (Map<String, String> params) async {
      return SecurityMigration(map: params);
    },
    'flutter://zjzblive.html': (Map<String, String> params) async {
      return LiveListView(
        ugcIsResume: true,
        isLiveTab: true,
        source: 'vdn',
      );
    },
    'flutter://cameraVideo': (Map<String, String> params) async {
      return goToVideo(
        maxVideoTime: FlutterModuleUtils.convertType<String>(
            params['maxVideoTime'], '180'),
        saveToPhotoAlbum: FlutterModuleUtils.convertType<String>(
            params['saveToPhotoAlbum'], '0'),
        chooseHighQuality: FlutterModuleUtils.convertType<String>(
            params['chooseHighQuality'], '0'),
      );
    },
    'flutter://WholeHouseAir.html': (Map<String, String> params) async {
      return WholeHouseAir(appbarHeight: 44, appbarChangeHeight: 0);
    },
    'flutter://singleWholeHouseAir': (Map<String, String> params) async {
      return SingleHouseAir(
        params: params,
      );
    },
    'flutter://slelectPublishType': (Map<String, String> params) async {
      return SelectPublishType(
        params: params,
      );
    },
    'flutter://smart_home/scene_manage': (Map<String, String> params) async {
      return SceneManagePage(
        familyId:
            FlutterModuleUtils.convertType<String>(params['familyId'], ''),
        roomId: FlutterModuleUtils.convertType<String>(params['roomId'], ''),
      );
    },
    'flutter://verification': (Map<String, String> params) async {
      // 登录页面的路由处理
      return const LoginPage();
    },
    'flutter://smart_home/consumable_detail_page':
        (Map<String, String> params) async {
      return ConsumablesDetailPage(
        deviceId:
            FlutterModuleUtils.convertType<String>(params['deviceId'], ''),
        consumableCode: FlutterModuleUtils.convertType<String>(
            params['consumableCode'], ''),
      );
    },
  };
}

class FlutterModuleUtils {
  /// 类型转化公共方法
  static T convertType<T>(dynamic arg, T defaultValue) {
    return arg is T ? arg : defaultValue;
  }
}
