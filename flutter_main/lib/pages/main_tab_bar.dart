import 'dart:io';

import 'package:badges/badges.dart' as badges;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_main/store/bottom_bar_store.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:message/message.dart';
import 'package:redux/redux.dart';
import 'package:uplustrace/uplustrace.dart';

import '../store/state/bottom_bar_state.dart';
import '../store/viewmodel/bottom_bar_viewmodel.dart';

class MainTabBar extends StatefulWidget {
  const MainTabBar({
    super.key,
    required this.onClick,
  });
  final ValueChanged<int> onClick;

  @override
  State<MainTabBar> createState() => _MainTabBarState();

  @override
  StatefulElement createElement() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      UplusTrace.trackFinishColdBoot();
      // 通知native启动
      Message.send('flutter_package_did_launch');
    });
    return super.createElement();
  }
}

class _MainTabBarState extends State<MainTabBar> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      const ImageProvider imageProvider = AssetImage(
        'resources/images/backtop_png.png',
        package: 'flutter_main',
      );
      imageProvider.resolve(ImageConfiguration.empty).addListener(
            ImageStreamListener((ImageInfo image, bool synchronousCall) {},
                onError: (dynamic err, StackTrace? stackTrace) {}),
          );
    });
  }

  BottomNavigationBarItem _buildBarItem(
    BarItemViewModel itemsVM,
    int index,
  ) {
    final BottomNavigationBarItem item = BottomNavigationBarItem(
      label: itemsVM.navText,
      icon: Stack(
        clipBehavior: Clip.none,
        children: <Widget>[
          SizedBox(
            width: Platform.isAndroid ? 24 : 24.w,
            height: Platform.isAndroid ? 24 : 24.w,
            child: Image.asset(
              itemsVM.unselectedImage,
              fit: BoxFit.cover,
              gaplessPlayback: true,
              package: 'flutter_main',
            ),
          ),
          if (index == 3 && itemsVM.showRedPoint) _buildBarItemRedPoint()
        ],
      ),
      activeIcon: Stack(
        clipBehavior: Clip.none,
        alignment: Alignment.center,
        children: <Widget>[
          SizedBox(
            width: Platform.isAndroid ? 24 : 24.w,
            height: Platform.isAndroid ? 24 : 24.w,
            child: itemsVM.needAnimation
                ? Lottie.asset(itemsVM.selectedImage,
                    fit: BoxFit.cover,
                    package: 'flutter_main',
                    repeat: false, errorBuilder: (_, __, ___) {
                    return Image.asset(
                      itemsVM.selectedImage,
                      fit: BoxFit.cover,
                      gaplessPlayback: true,
                      package: 'flutter_main',
                    );
                  })
                : Image.asset(
                    itemsVM.selectedImage,
                    fit: BoxFit.cover,
                    gaplessPlayback: true,
                    package: 'flutter_main',
                  ),
          ),
          if (index == 3 && itemsVM.showRedPoint) _buildBarItemRedPoint()
        ],
      ),
    );
    return item;
  }

  List<BottomNavigationBarItem> _buildBarItemList(
      BottomBarViewModel vm, int passiveCount) {
    final List<BottomNavigationBarItem> tabItems = <BottomNavigationBarItem>[];
    final List<BarItemViewModel> itemsVMList = vm.barItemVMList;
    tabItems
      ..add(_buildBarItem(itemsVMList[0], 0))
      ..add(_buildBarItem(itemsVMList[1], 1));
    if (vm.showXiaoU) {
      tabItems.add(
        BottomNavigationBarItem(
          label: '小优',
          icon: Stack(
            clipBehavior: Clip.none,
            children: <Widget>[
              SizedBox(
                width: Platform.isAndroid ? 24 : 24.w,
                height: Platform.isAndroid ? 24 : 24.w,
                child: Image.asset(
                  'resources/images/xiao_u_icon.webp',
                  package: 'flutter_main',
                  gaplessPlayback: true,
                  fit: BoxFit.cover,
                ),
              ),
              if (passiveCount > 0) _buildRedPoint(passiveCount)
            ],
          ),
        ),
      );
    }
    tabItems
      ..add(_buildBarItem(itemsVMList[2], 2))
      ..add(_buildBarItem(itemsVMList[3], 3));
    return tabItems;
  }

  Widget _buildBarItemRedPoint() {
    return Positioned(
      top: Platform.isAndroid ? -2 : -2.w,
      right: Platform.isAndroid ? -2 : -2.w,
      width: Platform.isAndroid ? 8 : 8.w,
      height: Platform.isAndroid ? 8 : 8.w,
      child: badges.Badge(
        toAnimate: false,
        elevation: 0,
        shape: badges.BadgeShape.square,
        borderRadius:
            BorderRadius.all(Radius.circular(Platform.isAndroid ? 8 : 8.w)),
        padding: EdgeInsets.symmetric(
          horizontal: Platform.isAndroid ? 2 : 2.w,
          vertical: Platform.isAndroid ? 1 : 1.w,
        ),
        badgeColor: const Color(0xFFED2856),
        badgeContent: Text(
          ' ',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: Platform.isAndroid ? 8 : 8.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRedPoint(int count) {
    return Positioned(
      top: Platform.isAndroid ? -3.5 : -3.5.w,
      right: count > 9
          ? (Platform.isAndroid ? -10.5 : -10.5.w)
          : (Platform.isAndroid ? -3.5 : -3.5.w),
      width: count > 9
          ? (Platform.isAndroid ? 22 : 22.w)
          : (Platform.isAndroid ? 14 : 14.w),
      height: Platform.isAndroid ? 14 : 14.w,
      child: badges.Badge(
        toAnimate: false,
        elevation: 0,
        shape: badges.BadgeShape.square,
        borderRadius:
            BorderRadius.all(Radius.circular(Platform.isAndroid ? 8 : 8.w)),
        padding: EdgeInsets.symmetric(
          horizontal: Platform.isAndroid ? 2 : 2.w,
          vertical: Platform.isAndroid ? 1 : 1.w,
        ),
        badgeColor: const Color(0xFFED2856),
        badgeContent: Text(
          '$count', // $count
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: Platform.isAndroid ? 8 : 8.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar(BottomBarViewModel vm) {
    // 登录状态切换
    // 确保系统断言 assert(0 <= currentIndex && currentIndex < items.length), 能够校验通过
    final List<BottomNavigationBarItem> barItemList =
        _buildBarItemList(vm, vm.passiveCount);
    if (bottomBarStore.state.currentIndex >= 0 &&
        bottomBarStore.state.currentIndex < barItemList.length) {
      // 转化index
      final int idx = bottomBarStore.state.showXiaoU &&
              bottomBarStore.state.currentIndex > 1
          ? bottomBarStore.state.currentIndex + 1
          : bottomBarStore.state.currentIndex;
      return Theme(
        data: ThemeData(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
        ),
        child: BottomNavigationBar(
          items: barItemList,
          elevation: 0.0,
          enableFeedback: false,
          backgroundColor: Colors.white,
          selectedFontSize: Platform.isAndroid ? 10.0 : 10.0.w,
          unselectedFontSize: Platform.isAndroid ? 10.0 : 10.0.w,
          selectedItemColor: AppSemanticColors.item.primary,
          unselectedItemColor: AppSemanticColors.item.secWeaken,
          type: BottomNavigationBarType.fixed,
          onTap: widget.onClick,
          currentIndex: idx,
        ),
      );
    }
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    return StoreConnector<BottomBarState, BottomBarViewModel>(
      distinct: true,
      converter: (Store<BottomBarState> store) {
        return BottomBarViewModel.fromStore(store);
      },
      builder: (BuildContext context, BottomBarViewModel bottomBarViewModel) {
        return _buildBottomNavigationBar(bottomBarViewModel);
      },
    );
  }
}
