import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_mine/app_mine.dart';
import 'package:app_service/app_service.dart';
import 'package:connectivity/connectivity.dart';
import 'package:eshop/eshop.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_main/store/viewmodel/consume_data_viewmodel.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:main_business/main_business.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:plugin_upgrade/plugin_upgrade.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/smart_home.dart';
import 'package:storage/storage.dart';
import 'package:trace/trace.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';
import 'package:vdn/vdn.dart';

import '../config/api_url.dart';
import '../config/const_string.dart';
import '../model/active_info_model.dart';
import '../model/passive_count_model.dart';
import '../model/push_msg_model.dart';
import '../service/common_request.dart';
import '../service/http_service.dart';
import '../store/action/bottom_bar_actions.dart';
import '../store/bottom_bar_store.dart';
import '../store/state/bottom_bar_state.dart';
import '../store/viewmodel/alarm_data_viewmodel.dart';
import '../tools/pre_load_data.dart';
import '../tools/show_toast.dart';
import '../utils/common_util.dart';
import '../utils/log_util.dart';
import '../utils/request_env.dart';
import '../utils/throttle_util.dart';
import '../widget/xiao_u_bubble_widget.dart';
import '../widget/xiao_u_diaolog_widget.dart';
import 'main_tab_bar.dart';

class Homes extends StatefulWidget {
  // const Homes({super.key});

  @override
  State<Homes> createState() => _HomesState();
}

class _HomesState extends State<Homes>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  List<GlobalKey> _childrenKeys = <GlobalKey>[];

  List<Widget> _children = <Widget>[];

  ///跳转tab需要传递的参数
  dynamic targetTabParams;

  StreamSubscription<dynamic>? _jumpTabSub;

  /// app启动后转到后台过，防止启动的时候收到app转前台推送，小优接口无用调用
  bool appEverPaused = false;

  ///监听用户注销的事件回调
  void Function(String, String)? accountCancelledCallback;
  StreamSubscription<ConnectivityResult>? _networkListener;

  ///是否可见
  bool tabPageContainerVisible = true;

  /// 小优冒泡数据是否请求过
  bool xiaoUBubbleDataChecked = false;

  /// 是否启动时调用过 onPageShow
  bool onPageShowForInit = true;

  /// 是否收到退出登录消息
  bool logoutStatus = false;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    LogUtil.i(
        'home didChangeAppLifecycleState $state | onPageShowForInit $onPageShowForInit | logoutStatus: $logoutStatus | index: ${PreLoadData.currentIndex}',
        tag: 'flutter_main');
    switch (state) {
      case AppLifecycleState.resumed:
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': PreLoadData.currentIndex});
        if (logoutStatus) {
          logoutStatus = false;
          return;
        }
        if (!onPageShowForInit) {
          onPageShow();
        }
        break;
      case AppLifecycleState.hidden:
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': -1});
        onPageShowForInit = false;
        onPageHide();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.paused:
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': -1});
        onPageShowForInit = false;
        break;
      case AppLifecycleState.inactive:
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': -1});
        break;
    }
  }

  void onPageShow() {
    LogUtil.i('home onPageShow', tag: 'flutter_main');
    tabPageContainerVisible = true;

    if (_childrenKeys.isEmpty) {
      return;
    }
    LogUtil.d('home_tab_page_appear', tag: 'flutter_main');
    _getXiaoURedPoint();
    try {
      //bugFix ZHIJIAAPP-31776
      if (PreLoadData.currentIndex !=
          PreLoadData.pageController?.page?.round()) {
        _currentIndexInfoPrint(
            'PreLoadData.currentIndex not equal to _pagecontroller.page.round:',
            null);
        if (PreLoadData.pageController != null &&
            PreLoadData.pageController!.positions.isNotEmpty) {
          PreLoadData.pageController!.jumpToPage(PreLoadData.currentIndex);
        } else {
          Future<void>.delayed(const Duration(milliseconds: 100), () {
            if (PreLoadData.pageController != null &&
                PreLoadData.pageController!.positions.isNotEmpty) {
              PreLoadData.pageController!.jumpToPage(PreLoadData.currentIndex);
            } else {
              LogUtil.d(
                  'onPageShow delayed jumpToPage failed: positions is empty',
                  tag: 'flutter_main');
            }
          });
        }
      }
    } catch (e) {
      LogUtil.e('onPageShow _pageController jumpToPage failed:$e',
          tag: 'flutter_main');
    }

    //回调当前被选tab的didAppear
    final dynamic currentWidget =
        _childrenKeys[PreLoadData.currentIndex].currentState;
    try {
      currentWidget.didAppear(targetTabParams);
      targetTabParams = null;
    } catch (err) {
      LogUtil.e('didAppear failed for $err', tag: 'flutter_main');
    }
  }

  void onPageHide() {
    LogUtil.i('home onPageHide', tag: 'flutter_main');
    tabPageContainerVisible = false;

    if (_childrenKeys.isEmpty) {
      return;
    }
    LogUtil.d('home_tab_page_disappear', tag: 'flutter_main');
    //回调当前被选tab的didDisappear
    final dynamic currentWidget =
        _childrenKeys[PreLoadData.currentIndex].currentState;
    try {
      if (currentWidget != null) {
        currentWidget.didDisappear();
      }
    } catch (err) {
      LogUtil.e('didDisappear failed for $err', tag: 'flutter_main');
    }
  }

  @override
  void initState() {
    super.initState();
    LogUtil.i(
        'home_tab_page initState | currentIndex: ${PreLoadData.currentIndex}',
        tag: 'flutter_main');
    _childrenKeys = <GlobalKey>[
      GlobalKey(),
      GlobalKey(),
      GlobalKey(),
      GlobalKey(),
    ];
    _children = <Widget>[
      SmartHome(
        key: _childrenKeys[0],
        mainParamsMap: PreLoadData.startArgs,
        isFirstScreen: PreLoadData.currentIndex == 0,
      ),
      AppService(
        key: _childrenKeys[1],
        isLogin: PreLoadData.isLogin,
        isFirstScreen: PreLoadData.currentIndex == 1,
      ),
      EshopHome(
        key: _childrenKeys[2],
      ),
      AppMine(
        key: _childrenKeys[3],
      ),
    ];
    Future<void>.delayed(const Duration(milliseconds: 300), () {
      final dynamic currentWidget =
          _childrenKeys[PreLoadData.currentIndex].currentState;
      try {
        currentWidget.didAppear(targetTabParams);
        targetTabParams = null;
      } catch (err) {
        LogUtil.e('home_tab_page initState didAppear failed for $err',
            tag: 'flutter_main');
      }
      WidgetsBinding.instance.addObserver(this);
    });

    _initDatas();
  }

  void _initDatas() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LogUtil.d(
          'HomeTabPage->initState()->_initDatas()->WidgetsBinding.instance.addPostFrameCallback',
          tag: 'flutter_main');
      _handleDelayedEvent();
    });
  }

  void _showAppUpgrade() {
    User.getOauthData().then((OauthData oauthData) {
      UpgradePlugin.checkAvailableVersionWithCallback(oauthData.uc_user_id);
    }).catchError((dynamic err) {
      UpgradePlugin.checkAvailableVersionWithCallback('');
      LogUtil.i('_showAppUpgrade User.getOauthData error for $err',
          tag: 'flutter_main');
    });
  }

  void _getXiaoURedPoint() {
    // 更新小红点状态
    Storage.getTemporaryStorage(ConstString.XIAO_U_DETAIL_PAGE_KEY)
        .then((String value) {
      LogUtil.i('getTemporaryStorage enter_voice_assistant_page for $value',
          tag: 'flutter_main');
      if (value.isNotEmpty) {
        final dynamic obj = json.decode(value);
        final Map<String, dynamic> map =
            FlutterMainUtils.convertType<Map<String, dynamic>>(
                obj, <String, dynamic>{}).cast<String, dynamic>();
        if (map['value'] == 'true') {
          LogUtil.i(
              'getTemporaryStorage enter_voice_assistant_page value is true',
              tag: 'flutter_main');
          _notifyUnreadMsg();
          Storage.setTemporaryStorage(
              ConstString.XIAO_U_DETAIL_PAGE_KEY, 'false');
        }
      }
    });
  }

  // 统一处理延时逻辑
  void _handleDelayedEvent() {
    Future<void>.delayed(const Duration(milliseconds: 1000), () async {
      // 获取AppInfo，供后续接口请求使用
      await PreLoadData.getAppInfoTask();
      _registerListeners();
      Message.send(
          'syn_launchView_hide'); // 修复ios app启动时Flutter初始化白屏问题-native加了一个遮罩，flutter延迟500ms通知遮罩消失
      Message.send('show_privacy_alert_notification');
      CommonRequest.requestModeInfo();
      PreLoadData.cleanPhoto();
      if (tabPageContainerVisible) {
        LogUtil.d('首tab 可见', tag: 'flutter_main');
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': PreLoadData.currentIndex});

        /// 进首页调插件弹升级弹框
        _showAppUpgrade();
      } else {
        LogUtil.d('首tab 不可见', tag: 'flutter_main');
        Message.send('flutter_package_maintab_switch',
            msgData: <String, int>{'messageData': -1});
      }
      final LoginStatus? loginStatus = User.getLoginStatusSync();
      LogUtil.d('_handleDelayedEvent loginStatus $loginStatus',
          tag: 'flutter_main');

      if (loginStatus == null || !loginStatus.isLogin) {
        return;
      }
      CommonRequest.requestXiaoUEntranceStatus();
      _getPersonalizedRecommendSwitchState();
      // 初始化请求防打扰开关状态
      MainBusiness.initDialogStatus();
      LogUtil.d('_handleDelayedEvent _initXiaoUDataForLoginState',
          tag: 'flutter_main');
      _initXiaoUDataForLoginState();
      _fetchRedPointStatus();
    });
  }

  void _registerListeners() {
    // 监听我的页面小红点
    Storage.addNodeListner(ConstString.APP_MINE_RED_POINT_KEY,
        (String key, String type) {
      LogUtil.i('Storage.addNodeListner app_mine_red_point_status callback',
          tag: 'flutter_main');
      _fetchRedPointStatus();
    });
    // 监听家庭切换更新小红点
    Message.listen<UserCurrentFamilyChangeMessage>(
        (UserCurrentFamilyChangeMessage event) {
      LogUtil.i('XiaoU3 log: listen to user current family changed.',
          tag: 'flutter_main');
      _notifyUnreadMsg();
    });

    /// User,family,device信息刷新完成
    Message.listen<UserRefreshCompletedMessage>(
        (UserRefreshCompletedMessage event) {
      Future<void>.delayed(const Duration(milliseconds: 1500), () {
        LogUtil.i('XiaoU3 log: listen to UserRefreshCompletedMessage changed.',
            tag: 'flutter_main');
        _initXiaoUDataForLoginState();
      });
    });
    Message.listen<UserTokenRefreshedMessage>(
        (UserTokenRefreshedMessage event) {
      ///用户数据刷新完成
      LogUtil.d('first register update user token after refresh complete.',
          tag: 'flutter_main');
      _fetchRedPointStatus();

      CommonRequest.requestXiaoUEntranceStatus();
      _getPersonalizedRecommendSwitchState();

      // 初始化请求防打扰开关状态
      MainBusiness.initDialogStatus();

      LogUtil.i('XiaoU3 log: UserTokenRefreshedMessage', tag: 'flutter_main');
    });

    accountCancelledCallback = (String key, String type) async {
      LogUtil.d('account cancel for first register $key $type',
          tag: 'flutter_main');
      try {
        if (ConstString.KEY_USER_ACCOUNT_CANCELED == key && 'update' == type) {
          // 删除注销状态本地存储
          Storage.deleteTemporaryStorage('user_account_cancelled_from_setting');
        }
      } catch (e) {
        LogUtil.e('account cancel for first register failed: $e',
            tag: 'flutter_main');
        Storage.deleteTemporaryStorage('user_account_cancelled_from_setting');
      }
    };

    ///用户注销
    Storage.addDataListener(
        ConstString.KEY_USER_ACCOUNT_CANCELED, accountCancelledCallback!);

    _networkListener = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      LogUtil.d('Connectivity().onConnectivityChanged: $result',
          tag: 'flutter_main');
      // 网络更新，重新获取个性化开关
      if (result != ConnectivityResult.none) {
        _getPersonalizedRecommendSwitchState();
      }
    });

    _jumpTabSub = Message.listenWithName('flutter_package_main_jump_tabbar',
        (dynamic data) {
      LogUtil.d('flutter_package_main_jump_tabbar: $data', tag: 'flutter_main');
      try {
        final String tempStr = FlutterMainUtils.convertType<String>(data, '');
        final Map<String, dynamic>? dataMap =
            FlutterMainUtils.convertType<Map<dynamic, dynamic>?>(
                    json.decode(tempStr), null)
                ?.cast<String, dynamic>();
        if (dataMap is Map<String, dynamic>) {
          final String currentIndex = FlutterMainUtils.convertType<String>(
              dataMap['flutter_package_main_jump_tabbar_index'], '');
          final int? idx = int.tryParse(currentIndex);
          _currentIndexInfoPrint('tab jump sub info with index:', idx);

          if (idx == null) {
            return;
          }
          // 不转化索引，native 携带index参数：0 1 2 3
          final int index = idx;

          dataMap.remove('flutter_package_main_jump_tabbar_index');
          if (dataMap.isNotEmpty) {
            targetTabParams = dataMap;
          } else {
            targetTabParams = null;
          }

          //控制器跳转页面
          if (index != PreLoadData.pageController?.page?.round()) {
            if (PreLoadData.pageController != null &&
                PreLoadData.pageController!.positions.isNotEmpty) {
              PreLoadData.pageController!.jumpToPage(index);
            } else {
              Future<void>.delayed(const Duration(milliseconds: 100), () {
                if (PreLoadData.pageController != null &&
                    PreLoadData.pageController!.positions.isNotEmpty) {
                  PreLoadData.pageController!.jumpToPage(index);
                } else {
                  LogUtil.d(
                      'main_jump_tabbar delayed jumpToPage failed: positions is empty',
                      tag: 'flutter_main');
                }
              });
            }
            final bool curIndexUpdate = PreLoadData.currentIndex != index;

            if (curIndexUpdate) {
              PreLoadData.preIndex = bottomBarStore.state.currentIndex;
              PreLoadData.currentIndex = index;
              bottomBarStore
                  .dispatch(UpdateCurrentIndexAction(PreLoadData.currentIndex));
            }

            if (_childrenKeys.isNotEmpty) {
              final dynamic preWidget =
                  _childrenKeys[PreLoadData.preIndex].currentState;
              try {
                preWidget.didDisappear();
              } catch (err) {
                LogUtil.i('didAppear failed for $err', tag: 'flutter_main');
              }
            }
          }
          if (_childrenKeys.isEmpty) {
            return;
          }
          //回调当前被选tab的didAppear
          final dynamic currentWidget =
              _childrenKeys[PreLoadData.currentIndex].currentState;
          try {
            if (targetTabParams != null) {
              currentWidget.didAppear(targetTabParams);
              targetTabParams = null;
            }
          } catch (err) {
            targetTabParams = null;
          }
        }
      } catch (e) {
        LogUtil.e('flutter_package_main_jump_tabbar failed: $e',
            tag: 'flutter_main');
      }
    });

    /// 延时增加监听事件
    UIMessage.sub<ShowBackTopMessage>((ShowBackTopMessage event) async {
      LogUtil.d(
          'HomeTabPage backTop show msg: ${event.tabIndex}, ${event.showBackTop}',
          tag: 'flutter_main');
      // 根据tabIndex和showBackTop更新对应tab的显示状态
      _refreshTabIconsByBackTopMsg(event.tabIndex, event.showBackTop);
    });
    // 监听App进入前台，更新小红点
    Message.listen<AppResumeMessage>((AppResumeMessage event) {
      LogUtil.i('XiaoU3 log: listen to app resumed event $appEverPaused.',
          tag: 'flutter_main');
      if (appEverPaused) {
        _notifyUnreadMsg();
      }
    });

    Message.listen<AppPauseMessage>((AppPauseMessage event) {
      LogUtil.i('XiaoU3 log: listen to app paused event.', tag: 'flutter_main');
      appEverPaused = true;
    });

    ///推送消息监听
    Message.listen<PushMessage>((PushMessage event) async {
      final Map<String, dynamic> messageMap = event.messageMap;
      LogUtil.i(
          'XiaoU3 log: home tab page handle push msg here with $messageMap.',
          tag: 'flutter_main');
      final PushMsgData pushMsgData = PushMsgData.fromJson(messageMap);
      final PushApi? pushApiData = pushMsgData.pushBody?.pushExtData?.pushApi;
      final String curApiType = pushApiData?.apiType ?? '';
      if (curApiType != ConstString.U_API_TYPE_ACTIVE_SERVICE_MSG) {
        LogUtil.i(
            'XiaoU3 log: api type $curApiType is not active service, ignore.',
            tag: 'flutter_main');
        return;
      }

      LogUtil.i('XiaoU3 log: api type correct, handle bubble logic.',
          tag: 'flutter_main');
      final LoginStatus? loginStatus = User.getLoginStatusSync();

      LogUtil.d('XiaoU3 log: handle push msg with $loginStatus',
          tag: 'flutter_main');
      if (loginStatus != null && loginStatus.isLogin) {
        final PushParams? pushParams = pushApiData?.pushParams;
        // 1. 更新冒泡数据
        if (pushParams is PushParams) {
          final String message = pushParams.bubbleText;
          final String jumpUrl = pushParams.bubbleLink;
          final String cardType = pushParams.cardType;
          final String clickEventId = pushParams.clickEventId;
          final String showEventId = pushParams.showEventId;
          final String currentTime = DateTime.now().toString();
          final Map<String, String> data = <String, String>{
            'message': message,
            'keywords': '',
            'jumpUrl': jumpUrl,
            'currentTime': currentTime,
            'cardType': cardType,
            'clickEventId': clickEventId,
            'showEventId': showEventId,
          };
          final String dataString = json.encode(data);
          final ConsumeData consumeData = ConsumeData.fromJson(data);

          LogUtil.i(
              'XiaoU3 log: showXiaoU - ${bottomBarStore.state.showXiaoU} && alarmDatas.isEmpty ${bottomBarStore.state.alarmDatasState.alarmDatas.isEmpty}',
              tag: 'flutter_main');
          if (bottomBarStore.state.showXiaoU &&
              bottomBarStore.state.alarmDatasState.alarmDatas.isEmpty) {
            // 展示小优且 弹窗不展示时，进行冒泡
            final UserInfo userInfo = await User.getUserInfo();
            final String userId = userInfo.userId;
            // 冒泡开关
            final String consumeSwitchKey =
                'voice_switch_consumable_key_$userId';
            final bool consumeSwitch = await Storage.getBooleanValue(
                consumeSwitchKey,
                defaultValue: true);
            LogUtil.d('UpdateConsumeDataAction consumeSwitch: $consumeSwitch',
                tag: 'flutter_main');
            if (consumeSwitch) {
              bottomBarStore.dispatch(UpdateConsumeDataAction(consumeData));
              LogUtil.i(
                  'XiaoU3 log: _updateBubbleData by push notify with $dataString',
                  tag: 'flutter_main');
            }
          }

          // 2. 更新小红点
          _notifyUnreadMsg();
        }
      }
    });

    ///登出消息监听
    Message.listen<UserLogoutMessage>((UserLogoutMessage event) {
      LogUtil.d('UserLogoutMessage callback', tag: 'flutter_main');
      logoutStatus = true;
      // 重置冒泡数据接口请求标志位
      xiaoUBubbleDataChecked = false;

      // 退出登录，清除网器用户标识及邀请码
      PreLoadData.userInviteCode = null;

      Storage.putStringValue('uplus_personalized_recommend', '1');

      // 用户退出登录，注销监听, 消除小红点
      _removeListenerToXiaoU();

      // 退出登录时，不显示我的Tab小红点
      bottomBarStore.dispatch(UpdateAppMineRedPointAction(false));
    });
  }

  /// 获取我的Tab小红点显示状态
  void _fetchRedPointStatus() {
    Storage.getBooleanValue(ConstString.APP_MINE_RED_POINT_KEY,
            defaultValue: false)
        .then((bool value) {
      LogUtil.i('_fetchRedPointStatus value: $value', tag: 'flutter_main');
      bottomBarStore.dispatch(UpdateAppMineRedPointAction(value));
    }).catchError((dynamic err) {
      LogUtil.e('_fetchRedPointStatus err: $err', tag: 'flutter_main');
    });
  }

  /// 初始化(登录情况下)小优相关接口和开关的调用方法
  void _initXiaoUDataForLoginState() {
    if (!bottomBarStore.state.showXiaoU) {
      LogUtil.i('XiaoU3 log: _initXiaoUDataForLoginState showXiaoU is false',
          tag: 'flutter_main');
      return;
    }
    _notifyBubbleData();
  }

  /// 查询并通知native小红点数据
  Future<void> _notifyUnreadMsg() async {
    LogUtil.i(
        'XiaoU3 log: _updateUnreadMsg bottomBarStore.state.showXiaoU: ${bottomBarStore.state.showXiaoU}',
        tag: 'flutter_main');
    final LoginStatus? loginStatus = User.getLoginStatusSync();
    LogUtil.i('XiaoU3 log: _updateUnreadMsg getLoginStatusSync: $loginStatus',
        tag: 'flutter_main');
    // 确保已登录
    if (loginStatus != null &&
        loginStatus.isLogin &&
        bottomBarStore.state.showXiaoU) {
      // 展示小优
      final PassiveCountModel? model =
          await CommonRequest.requestPassiveCount();
      LogUtil.i('XiaoU3 log: _updateUnreadMsg witdh $model',
          tag: 'flutter_main');
      if (model is PassiveCountModel) {
        final int count = model.data?.count ?? 0;

        final UserInfo userInfo = await User.getUserInfo();
        final String userId = userInfo.userId;
        // 小红点开关
        final String unreadMsgSwitchKey = 'voice_switch_handle_key_$userId';
        final bool unreadMsgSwitch = await Storage.getBooleanValue(
            unreadMsgSwitchKey,
            defaultValue: true);
        LogUtil.d('UpdatePassiveCountAction unreadMsgSwitch: $unreadMsgSwitch',
            tag: 'flutter_main');
        if (unreadMsgSwitch) {
          bottomBarStore.dispatch(UpdatePassiveCountAction(count));
          Storage.setTemporaryStorage('xiaou_badge_value', count.toString());
          LogUtil.i('XiaoU3 log: _updateUnreadMsg notify with $count',
              tag: 'flutter_main');
        }
      }
    }
  }

  /// 查询并通知native冒泡数据
  Future<void> _notifyBubbleData() async {
    LogUtil.i(
        'XiaoU3 log: _updateBubbleData bubble data showXiaoU: ${bottomBarStore.state.showXiaoU}',
        tag: 'flutter_main');
    if (xiaoUBubbleDataChecked) {
      LogUtil.i(
          'XiaoU3 log: _updateBubbleData bubble data xiaoUBubbleDataChecked: $xiaoUBubbleDataChecked',
          tag: 'flutter_main');
      return;
    }
    xiaoUBubbleDataChecked = true;
    final ActiveInfoModel? activeInfoModel =
        await CommonRequest.requestActiveInfo();
    LogUtil.i('XiaoU3 log: _updateBubbleData with $activeInfoModel',
        tag: 'flutter_main');
    if (activeInfoModel == null) {
      return;
    }

    final UserInfo userInfo = await User.getUserInfo();
    final String userId = userInfo.userId;
    LogUtil.d('UpdateAlarmAndConsumeAction userId: $userId',
        tag: 'flutter_main');
    // 弹窗开关
    final String alarmSwitchKey = 'voice_switch_warn_key_$userId';
    // 冒泡开关
    final String consumeSwitchKey = 'voice_switch_consumable_key_$userId';
    // 小红点开关
    final String unreadMsgSwitchKey = 'voice_switch_handle_key_$userId';
    final bool alarmSwitch =
        await Storage.getBooleanValue(alarmSwitchKey, defaultValue: true);
    LogUtil.d('UpdateAlarmAndConsumeAction alarmSwitch: $alarmSwitch',
        tag: 'flutter_main');
    final bool consumeSwitch =
        await Storage.getBooleanValue(consumeSwitchKey, defaultValue: true);
    LogUtil.d('UpdateAlarmAndConsumeAction consumeSwitch: $consumeSwitch',
        tag: 'flutter_main');
    final bool unreadMsgSwitch =
        await Storage.getBooleanValue(unreadMsgSwitchKey, defaultValue: true);
    LogUtil.d('UpdateAlarmAndConsumeAction unreadMsgSwitch: $unreadMsgSwitch',
        tag: 'flutter_main');

    if (!alarmSwitch) {
      activeInfoModel.data.errorList.clear();
    }

    if (!consumeSwitch) {
      activeInfoModel.data.consumeList.clear();
    }

    if (!unreadMsgSwitch) {
      activeInfoModel.data.passiveCount = 0;
    }

    bottomBarStore.dispatch(UpdateAlarmAndConsumeAction(activeInfoModel.data));
  }

  /// 关闭小优弹窗
  Future<void> _hideErrorDialog() async {
    LogUtil.i('XiaoU3 log: _hideErrorDialog', tag: 'flutter_main');
    bottomBarStore.dispatch(CloseAlarmAction());
  }

  void _currentIndexInfoPrint(String tagInfo, int? index) {
    LogUtil.d(
        '$tagInfo $index;'
        ' current index info:'
        ' ${PreLoadData.currentIndex}//${PreLoadData.preIndex}//${PreLoadData.pageController?.page?.round()}',
        tag: 'flutter_main');
  }

  void _refreshTabIconsByBackTopMsg(int? index, bool? showFlag) {
    LogUtil.i('refresh tab by back top msg $index, show flag $showFlag',
        tag: 'flutter_main');
    if (index is! int || showFlag is! bool) {
      return;
    }

    if (bottomBarStore.state.barItemsState.barItemsVM[index].showBackTop ==
        showFlag) {
      return;
    }

    bottomBarStore.dispatch(
      UpdateShowBackTopAction(
        index,
        showFlag,
      ),
    );
  }

  Widget _currentPage() {
    return PageView.builder(
      physics: const NeverScrollableScrollPhysics(), //禁用左右滑动
      itemBuilder: (BuildContext context, int index) {
        if (index == PreLoadData.currentIndex) {
          return _children[PreLoadData.currentIndex];
        }
        return null;
      },
      itemCount: _children.length,
      controller: PreLoadData.pageController,
    );
  }

  //返回顶部要显示需要的条件
  bool _getBackTopCondition(int index) {
    final bool flag = PreLoadData.currentIndex == index &&
        bottomBarStore.state.barItemsState.barItemsVM[index].showBackTop;
    LogUtil.i(
        '_getBackTopCondition index:$index, isShowBacktop: ${bottomBarStore.state.barItemsState.barItemsVM[index].showBackTop},',
        tag: 'flutter_main');
    return flag;
  }

  void _clickTabToBackTop(int index) {
    final bool showFlag = _getBackTopCondition(index);
    if (showFlag) {
      _backToTopRefresh(index);
      _traceForBackTopClick(index);
    }
  }

  //智家app-回到顶部点位上报：点击小火箭点位
  void _traceForBackTopClick(int index) {
    LogUtil.i('trace for back click for tab $index', tag: 'flutter_main');
    final Map<String, Object?> variable = <String, Object?>{
      'value': index == 0 ? '智家' : '商城'
    };
    Trace.traceEventWithVariable(
        eventId: ConstString.TRACE_GIO_FOR_BACK_TOP_CLICK, variable: variable);
  }

  Future<void> _backToTopRefresh(int index) async {
    LogUtil.i('send msg to tab $index back to top', tag: 'flutter_main');
    // 处理商城页面回到顶部
    UIMessage.fireEvent(BackTopRefreshMessage(tabIndex: index));
  }

  // 展示小优时，转化底Tab索引
  int convertIndex(int idx) {
    LogUtil.d(
        'convertIndex with index: $idx | showXiaoU: ${bottomBarStore.state.showXiaoU}',
        tag: 'flutter_main');
    return bottomBarStore.state.showXiaoU && idx > 1 ? idx - 1 : idx;
  }

  void onTabTapped(int index) {
    LogUtil.d('flutter tab tapped with index: $index', tag: 'flutter_main');
    // 通知点击事件
    _hideErrorDialog();
    UIMessage.fireEvent(
      HomeTabTapEvent(
        tabTapIndex: convertIndex(index),
      ),
    );
    if (bottomBarStore.state.showXiaoU && index == 2) {
      LogUtil.d('flutter tab tapped with bottomBarStore.state.showXiaoU = true',
          tag: 'flutter_main');
      Connectivity().checkConnectivity().then((ConnectivityResult result) {
        if (result == ConnectivityResult.none) {
          showToast(ConstString.NETWORK_ERROR_TIP);
        } else {
          Debounce.run(() {
            Trace.traceEventWithVariable(
              eventId: 'MB35767',
              variable: <String, dynamic>{
                'value': '首页底TAB',
                'status': bottomBarStore.state.passiveCount.toString(),
              },
            );
            Vdn.goToPage(
                'mpaas://HomeGPT?needAuthLogin=1&checkGuestMode=1&page_anim=anim_pop');
          });
        }
      });
      LogUtil.d('flutter tab tapped with bottomBarStore.state.showXiaoU return',
          tag: 'flutter_main');
      return;
    }

    index = convertIndex(index);

    if (index == PreLoadData.currentIndex) {
      _clickTabToBackTop(index);
      return;
    }
    _handleTabSwitchEvent(index);
  }

  // 处理tab切换相关事件
  void _handleTabSwitchEvent(int index) {
    Message.send('flutter_package_maintab_switch',
        msgData: <String, int>{'messageData': index});

    //控制器跳转页面
    try {
      if (PreLoadData.pageController != null &&
          PreLoadData.pageController!.positions.isNotEmpty) {
        PreLoadData.pageController!.jumpToPage(index);
      } else {
        Future<void>.delayed(const Duration(milliseconds: 100), () {
          if (PreLoadData.pageController != null &&
              PreLoadData.pageController!.positions.isNotEmpty) {
            PreLoadData.pageController!.jumpToPage(index);
          } else {
            LogUtil.d('click tab delayed jumpToPage failed: positions is empty',
                tag: 'flutter_main');
          }
        });
      }
    } catch (e) {
      LogUtil.e('click tab jumpToPage failed:$e', tag: 'flutter_main');
    }

    final bool curIndexUpdate = PreLoadData.currentIndex != index;
    if (curIndexUpdate) {
      PreLoadData.preIndex = bottomBarStore.state.currentIndex;
      PreLoadData.currentIndex = index;
      bottomBarStore
          .dispatch(UpdateCurrentIndexAction(PreLoadData.currentIndex));
    }

    //当索引变化后,向当前widget调用didAppear、上一个widget调用didDisappear
    final Map<String, dynamic> params = <String, dynamic>{'fromTabClick': true};
    final dynamic preWidget = _childrenKeys[PreLoadData.preIndex].currentState;
    final dynamic currentWidget = _childrenKeys[index].currentState;
    try {
      preWidget.didDisappear();
    } catch (err) {
      LogUtil.i('didAppear failed for $err', tag: 'flutter_main');
    }
    try {
      //currentWidget此时可能是null
      if (currentWidget == null) {
        //延迟100毫秒再次尝试读取
        Future<void>.delayed(const Duration(milliseconds: 100), () {
          final dynamic currentWidget = _childrenKeys[index].currentState;
          try {
            currentWidget.didAppear(params);
          } catch (error) {
            LogUtil.i('didAppear failed for $error', tag: 'flutter_main');
          }
          targetTabParams = null;
        });
      } else {
        currentWidget.didAppear(params);
        targetTabParams = null;
      }
    } catch (err) {
      targetTabParams = null;
    }

    _gioTrackForTabSwitch(index);
  }

  ///TAB切换gio打点
  void _gioTrackForTabSwitch(int index) {
    try {
      final String traceGio = ConstString.TRACES_GIO_FOR_TAB_SWITCH[index];
      LogUtil.d('_gioTrackForTabSwitch with $traceGio, cur index: $index',
          tag: 'flutter_main');
      Trace.traceEvent(eventId: traceGio);
    } catch (e) {
      LogUtil.e('_gioTrackForTabSwitch exception $e', tag: 'flutter_main');
    }
  }

  //板块标题有更新，需要根据个性化开关
  Future<void> _getPersonalizedRecommendSwitchState() async {
    final Map<String, dynamic> paramMap = <String, dynamic>{};
    paramMap['switchType'] = 2; //2：个人个性化推荐
    try {
      final UserInfo userInfo = await User.getUserInfo();
      paramMap['switchKey'] = userInfo.userId; //个性化推荐和生活服务以用户为维度传用户中心userId
      if (userInfo.userId.isEmpty) {
        return; //不登录获取不到userId，不存储个性化开关值
      }
    } catch (e) {
      LogUtil.e(
          '_getPersonalizedRecommendSwitchState User.getUserInfo() err:$e',
          tag: 'flutter_main');
    }

    HttpService.post(
      RequestUtil.getSerEnv(),
      ApiUrl.LIFESERVICE_SWITCH_QUERY,
      SignType.md5,
      param: paramMap,
      headers: <String, String>{'Content-Type': 'application/json'},
    ).then((Map<String, dynamic>? value) async {
      if (value is! Map ||
          value!['retCode'] != '00000' ||
          value['data'] is! Map) {
        final String value =
            await Storage.getStringValue('uplus_personalized_recommend');
        if (value == '') {
          Storage.putStringValue('uplus_personalized_recommend', '0');
        }
        LogUtil.d(
            '_getPersonalizedRecommendSwitchState request failed value:$value',
            tag: 'flutter_main');
      } else {
        final Map<String, dynamic> dataMap =
            FlutterMainUtils.convertType<Map<dynamic, dynamic>>(
                value['data'], <String, dynamic>{}).cast<String, dynamic>();
        final String? lStatus = FlutterMainUtils.convertType<String?>(
            dataMap['status'], null); //状态(0-打开,1-关闭)
        if (lStatus == '1') {
          Storage.putStringValue('uplus_personalized_recommend', '1');
        } else {
          Storage.putStringValue('uplus_personalized_recommend', '0');
        }
        LogUtil.d('_getPersonalizedRecommendSwitchState request success:$value',
            tag: 'flutter_main');
      }
    }).catchError((dynamic err) async {
      LogUtil.d('_getPersonalizedRecommendSwitchState request error:$err',
          tag: 'flutter_main');
      String value =
          await Storage.getStringValue('uplus_personalized_recommend');
      if (value == '') {
        Storage.putStringValue('uplus_personalized_recommend', '0');
      }
    });
  }

  /// 移除耗材，小红点开关的监听
  Future<void> _removeListenerToXiaoU() async {
    LogUtil.d(
        'XiaoU3 log: remove listener to consumeable and unread msg switch',
        tag: 'flutter_main');
    if (bottomBarStore.state.showXiaoU) {
      LogUtil.i('XiaoU3 log: logout bottomBarStore.state.showXiaoU true',
          tag: 'flutter_main');

      // 隐藏小红点消失
      bottomBarStore.dispatch(UpdatePassiveCountAction(0));
      Storage.setTemporaryStorage('xiaou_badge_value', '0');
    }
  }

  /// 构建小优对话框
  Widget _buildXiaoUDialog() {
    return StoreConnector<BottomBarState, AlarmDataListViewModel>(
      distinct: true,
      converter: (Store<BottomBarState> store) {
        return AlarmDataListViewModel.fromStore(store.state.alarmDatasState);
      },
      builder: (BuildContext context,
          AlarmDataListViewModel alarmDataListViewModel) {
        final bool showDialog =
            alarmDataListViewModel.alarmDataListVM.isNotEmpty;
        LogUtil.i('XiaoU3 log: _buildXiaoUDialog with showDialog: $showDialog',
            tag: 'flutter_main');
        return showDialog
            ? XiaoUDialogWidget(
                dialogDataList: alarmDataListViewModel.alarmDataListVM,
              )
            : Container();
      },
    );
  }

  // 构造小优冒泡
  Widget _buildXiaoUBubble() {
    return StoreConnector<BottomBarState, ConsumeDataViewModel>(
      distinct: true,
      converter: (Store<BottomBarState> store) {
        return ConsumeDataViewModel(
          store.state.consumeDataState.message,
          store.state.consumeDataState.jumpUrl,
        );
      },
      builder:
          (BuildContext context, ConsumeDataViewModel consumeDataViewModel) {
        LogUtil.i(
            'XiaoU3 log: _buildXiaoUBubble with ${consumeDataViewModel.message} | ${consumeDataViewModel.jumpUrl}',
            tag: 'flutter_main');
        final bool showBubble = consumeDataViewModel.message.isNotEmpty &&
            consumeDataViewModel.jumpUrl.isNotEmpty;
        return showBubble
            ? XiaoUBubbleWidget(
                message: consumeDataViewModel.message,
                jumpUrl: consumeDataViewModel.jumpUrl,
              )
            : Container();
      },
    );
  }

  // 构建主页
  Widget _buildHomeTabPage() {
    return Stack(
      children: <Widget>[
        Scaffold(
          drawerEdgeDragWidth: 0.0,
          extendBody: true,
          backgroundColor: Colors.white,
          bottomNavigationBar: MainTabBar(
            onClick: onTabTapped,
          ),
          body: Stack(
            children: <Widget>[
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                child: _currentPage(),
              ),
              _buildXiaoUDialog(),
            ],
          ),
        ),
        _buildXiaoUBubble(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (Platform.isAndroid) {
      /// Android状态栏设置为透明
      const SystemUiOverlayStyle systemUiOverlayStyle =
          SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    }

    return ScreenUtilInit(
      designSize: const Size(375, 667),
      builder: () => _buildHomeTabPage(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    PreLoadData.pageController?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _jumpTabSub?.cancel();
    _networkListener?.cancel();
    Message.setCacheMessage('flutter_package_main_jump_tabbar', true);
    if (accountCancelledCallback != null) {
      Storage.removeDataListener(
          ConstString.KEY_USER_ACCOUNT_CANCELED, accountCancelledCallback!);
    }
  }
}
