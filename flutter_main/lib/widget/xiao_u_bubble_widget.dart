import 'package:connectivity/connectivity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:trace/trace.dart';
import 'package:vdn/vdn.dart';

import '../config/const_string.dart';
import '../store/action/bottom_bar_actions.dart';
import '../store/bottom_bar_store.dart';
import '../tools/show_toast.dart';
import '../utils/log_util.dart';
import '../utils/throttle_util.dart';

class XiaoUBubbleWidget extends StatefulWidget {
  const XiaoUBubbleWidget({
    super.key,
    required this.message,
    required this.jumpUrl,
  });

  final String message;
  final String jumpUrl;

  @override
  State<XiaoUBubbleWidget> createState() => _XiaoUBubbleWidgetState();
}

class _XiaoUBubbleWidgetState extends State<XiaoUBubbleWidget>
    with SingleTickerProviderStateMixin {
  AnimationController? _controller;
  Animation<double>? _animation;
  double _opacity = 0.1;

  @override
  void initState() {
    super.initState();
    // 页面曝光埋点
    _traceBubbleEvent(false);

    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
      lowerBound: 0.0,
      upperBound: 1.0,
    )
      ..addListener(() {
        setState(() {
          _opacity = _controller?.value ?? 0.1;
        });
      })
      ..addStatusListener((AnimationStatus status) {
        if (status == AnimationStatus.dismissed) {
          LogUtil.i('status is AnimationStatus.dismissed', tag: 'flutter_main');
          _opacity = 0.1;
          bottomBarStore.dispatch(CloseConsumeAction());
        }
      });

    _animation = CurvedAnimation(
      parent: _controller!,
      curve: Curves.easeOutCubic,
    );

    _controller?.forward();
    // 延时5秒关闭冒泡
    _closeBubble();
  }

  void _closeBubble() {
    Future<void>.delayed(const Duration(seconds: 5), () {
      _reverseBubble();
    });
  }

  void _reverseBubble() {
    if (mounted) {
      _controller?.reverse();
    }
  }

  void _traceBubbleEvent(bool forClickEvent) {
    // 小优耗材冒泡展示点位
    // 展示点位：MB35771；点击点位：MB35772
    Trace.traceEventWithVariable(
      eventId: forClickEvent ? 'MB35772' : 'MB35771',
      variable: <String, String>{
        'value': 'NEWXIAOU_CONSUMABLE_MSG',
      },
    );
  }

  Widget _buildBubbleWidget() {
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                padding: EdgeInsets.symmetric(
                  vertical: 14.w,
                  horizontal: 12.w,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(12.0.w)),
                  color: const Color.fromRGBO(34, 131, 226, 1),
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      offset: const Offset(0, 8), // 设置阴影位移量
                      blurRadius: 16, // 设置模糊半径
                      spreadRadius: 0, // 设置扩散半径
                      color: const Color(0x00000000)
                          .withOpacity(0.12), // 设置阴影颜色及不透明度
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.only(
                        right: 8.w,
                      ),
                      child: SizedBox(
                        width: 16.w,
                        height: 16.w,
                        child: Image.asset(
                          'resources/images/xiao_u_bubble_notify.webp',
                          package: 'flutter_main',
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        // 点击跳转
                        Connectivity()
                            .checkConnectivity()
                            .then((ConnectivityResult result) {
                          if (result == ConnectivityResult.none) {
                            showToast(ConstString.NETWORK_ERROR_TIP);
                          } else {
                            Debounce.run(() {
                              // 1.关闭
                              _reverseBubble();
                              // 2.跳转
                              _traceBubbleEvent(true);
                              Vdn.goToPage(widget.jumpUrl);
                            });
                          }
                        });
                      },
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: 208.w,
                          maxHeight: 44.w,
                          minWidth: 107.w,
                          minHeight: 20.w,
                        ),
                        child: Text(
                          widget.message,
                          textAlign: TextAlign.start,
                          softWrap: true,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: const Color.fromRGBO(255, 255, 255, 1),
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            decoration: TextDecoration.none,
                          ),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        _reverseBubble();
                      },
                      child: Container(
                        padding: EdgeInsets.only(
                          left: 8.w,
                        ),
                        child: SizedBox(
                          width: 16.w,
                          height: 16.w,
                          child: Image.asset(
                            'resources/images/xiao_u_bubble_close.png',
                            package: 'flutter_main',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: -5.5.w,
          left: 0,
          right: 0,
          child: Center(
            child: Image.asset(
              'resources/images/xiao_u_bubble_arrow.png',
              package: 'flutter_main',
              fit: BoxFit.cover,
              width: 11.w,
              height: 6.w,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: kBottomNavigationBarHeight.w +
          MediaQuery.of(context).padding.bottom.w,
      left: 0,
      right: 0,
      child: ScaleTransition(
        scale: _animation!,
        alignment: Alignment.bottomCenter,
        child: AnimatedOpacity(
          opacity: _opacity,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeOutCubic,
          child: _buildBubbleWidget(),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
    _controller = null;
  }
}
