import 'dart:async';

import 'package:redux/redux.dart';

import '../../utils/log_util.dart';
import '../state/bottom_bar_state.dart';

Timer? _timer;

final List<Middleware<BottomBarState>> bottomBarMiddleware =
    <Middleware<BottomBarState>>[
  BottomBarMiddleware().call,
];

class BottomBarMiddleware implements MiddlewareClass<BottomBarState> {
  @override
  dynamic call(
      Store<BottomBarState> store, dynamic action, NextDispatcher next) async {
    LogUtil.d('BottomBarMiddleware start action is: $action',
        tag: 'flutter_main');

    next(action);
  }
}
