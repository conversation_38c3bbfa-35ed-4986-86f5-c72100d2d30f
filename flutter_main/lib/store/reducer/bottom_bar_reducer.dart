import 'package:storage/storage.dart';

import '../../model/active_info_model.dart';
import '../action/bottom_bar_actions.dart';
import '../state/bottom_bar_state.dart';
import '../viewmodel/bottom_bar_viewmodel.dart';

/// 是否启动调用
bool _forInit = true;

BottomBarState bottomBarReducer(BottomBarState state, dynamic action) {
  final BottomBarState newState = BottomBarState.deepCopy(state);
  if (action is UpdateXiaoUStatusAction) {
    newState.showXiaoU = action.showXiaoU;
  } else if (action is UpdateMourningStatusAction) {
    newState.showMourning = action.showMourning;
  } else if (action is UpdatePassiveCountAction) {
    newState.passiveCount = action.passiveCount;
  } else if (action is UpdateShowBackTopAction) {
    _updateShowBackTopReducer(newState, action);
  } else if (action is UpdateCurrentIndexAction) {
    _updateCurrentIndexReducer(newState, action);
  } else if (action is UpdateAlarmAndConsumeAction) {
    // 接口请求数据更新弹窗、冒泡
    _updateAlarmAndConsumeReducer(newState, action);
  } else if (action is UpdateConsumeDataAction) {
    // 推送数据更新冒泡
    _updateConsumeDataReducer(newState, action);
  } else if (action is CloseAlarmAction) {
    // 关闭弹窗
    newState.alarmDatasState.clearState();
  } else if (action is CloseConsumeAction) {
    // 关闭冒泡
    newState.consumeDataState.jumpUrl = '';
    newState.consumeDataState.message = '';
  } else if (action is UpdateAppMineRedPointAction) {
    // 更新我的Tab 小红点
    _updateAppMineRedPointReducer(newState, action);
  }
  return newState;
}

void _updateShowBackTopReducer(
    BottomBarState newState, UpdateShowBackTopAction action) {
  final int idx = action.index;
  final BarItemsState barItemsState = newState.barItemsState;
  barItemsState.barItemsVM[idx].showBackTop = action.showBackTop;
  barItemsState.barItemsVM[idx].navText =
      action.showBackTop ? '回到顶部' : tabIcons[idx]['navName']!;
  const String backTopImage = 'resources/images/backtop_png.png';
  final String selectedImage = tabIcons[idx]['selected_png']!;
  barItemsState.barItemsVM[idx].selectedImage =
      action.showBackTop ? backTopImage : selectedImage;
  barItemsState.barItemsVM[idx].needAnimation = false;
}

void _updateCurrentIndexReducer(
    BottomBarState newState, UpdateCurrentIndexAction action) {
  final int index = action.index;
  newState.currentIndex = index;
  if (_forInit) {
    _forInit = false;
    return;
  }
  for (int i = 0; i < newState.barItemsState.barItemsVM.length; i++) {
    final BarItemState barItemState = newState.barItemsState.barItemsVM[i];
    if (i == index && barItemState.showBackTop) {
      // 选中状态且处于回到顶部
      barItemState.navText = '回到顶部';
      const String backTopImage = 'resources/images/backtop_png.png';
      barItemState.selectedImage = backTopImage;
      barItemState.needAnimation = false;
    } else {
      barItemState.navText = tabIcons[i]['navName']!;
      final String selectedImage = tabIcons[i]['selected_webp']!;
      barItemState.selectedImage = selectedImage;
      barItemState.needAnimation = true;
    }
  }
}

void _updateAppMineRedPointReducer(
    BottomBarState newState, UpdateAppMineRedPointAction action) {
  // 更新我的Tab 小红点
  newState.barItemsState.barItemsVM[3].showRedPoint = action.showRedPoint;
}

void _updateAlarmAndConsumeReducer(
    BottomBarState newState, UpdateAlarmAndConsumeAction action) {
  final List<ErrorData> errorList = action.activeData.errorList;
  final List<ConsumeData> consumeDataList = action.activeData.consumeList;
  if (errorList.isNotEmpty && consumeDataList.isNotEmpty) {
    // 弹窗、冒泡都存在，优先展示弹窗
    consumeDataList.clear();
  }

  if (errorList.isNotEmpty) {
    newState.alarmDatasState.clearState();
    errorList.forEach((ErrorData element) {
      newState.alarmDatasState.alarmDatas.add(
        AlarmDataState(
          element.deviceName,
          element.deviceLocation,
          element.errorTitle,
          element.errorSolution,
          element.devicePicture,
          element.buttonMessage,
          element.buttonUrl,
          element.deviceCode,
        ),
      );
    });
  }

  if (consumeDataList.isNotEmpty) {
    newState.consumeDataState.message = consumeDataList.first.message;
    newState.consumeDataState.jumpUrl = consumeDataList.first.jumpUrl;
  }

  newState.passiveCount = action.activeData.passiveCount;
  Storage.setTemporaryStorage(
      'xiaou_badge_value', newState.passiveCount.toString());
}

void _updateConsumeDataReducer(
    BottomBarState newState, UpdateConsumeDataAction action) {
  final ConsumeData consumeData = action.consumeData;
  newState.consumeDataState.message = consumeData.message;
  newState.consumeDataState.jumpUrl = consumeData.jumpUrl;
}
