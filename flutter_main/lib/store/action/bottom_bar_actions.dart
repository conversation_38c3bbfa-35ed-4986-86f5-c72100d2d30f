import '../../model/active_info_model.dart';

class UpdateXiaoUStatusAction {
  UpdateXiaoUStatusAction(this.showXiaoU);

  bool showXiaoU = false;

  @override
  String toString() {
    return 'UpdateXiaoUStatusAction{showXiaoU: $showXiaoU}';
  }
}

class UpdateMourningStatusAction {
  UpdateMourningStatusAction(this.showMourning);

  bool showMourning = false;

  @override
  String toString() {
    return 'UpdateMourningStatusAction{showMourning: $showMourning}';
  }
}

class UpdatePassiveCountAction {
  UpdatePassiveCountAction(this.passiveCount);

  int passiveCount = 0;

  @override
  String toString() {
    return 'UpdatePassiveCountAction{passiveCount: $passiveCount}';
  }
}

class UpdateCurrentIndexAction {
  UpdateCurrentIndexAction(int idx) {
    index = idx;
  }

  int index = 0;

  @override
  String toString() {
    return 'UpdateCurrentIndexAction{index: $index}';
  }
}

class UpdateShowBackTopAction {
  UpdateShowBackTopAction(this.index, this.showBackTop);

  bool showBackTop = false;
  int index = 0;

  @override
  String toString() {
    return 'UpdateShowBackTopAction{index: $index, showBackTop: $showBackTop}';
  }
}

class UpdateAppMineRedPointAction {
  UpdateAppMineRedPointAction(this.showRedPoint);

  bool showRedPoint = false;

  @override
  String toString() {
    return 'UpdateAppMineRedPointAction{showRedPoint: $showRedPoint}';
  }
}

// 接口请求后更新告警弹窗、冒泡
class UpdateAlarmAndConsumeAction {
  UpdateAlarmAndConsumeAction(this.activeData);

  ActiveData activeData;

  @override
  String toString() {
    return 'UpdateAlarmAndConsumeAction{activeData: $activeData}';
  }
}

// 推送消息更新冒泡
class UpdateConsumeDataAction {
  UpdateConsumeDataAction(this.consumeData);

  ConsumeData consumeData;

  @override
  String toString() {
    return 'UpdateConsumeDataAction{consumeData: $consumeData}';
  }
}

// 关闭告警弹窗
class CloseAlarmAction {
  @override
  String toString() {
    return 'CloseAlarmAction{}';
  }
}

// 关闭冒泡
class CloseConsumeAction {
  @override
  String toString() {
    return 'CloseConsumeAction{}';
  }
}
