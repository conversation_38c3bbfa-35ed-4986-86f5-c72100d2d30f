import 'package:flutter/foundation.dart';
import 'package:redux/redux.dart';

import '../state/bottom_bar_state.dart';
import 'list_hashcode.dart';

final List<Map<String, String>> tabIcons = <Map<String, String>>[
  <String, String>{
    'navName': '智家',
    'selected_png': 'resources/images/smart_selected.png',
    'selected_webp': 'resources/images/smart.json',
    'unselected': 'resources/images/smart_unselected.png',
  },
  <String, String>{
    'navName': '服务',
    'selected_png': 'resources/images/service_selected.png',
    'selected_webp': 'resources/images/service.json',
    'unselected': 'resources/images/service_unselected.png',
  },
  <String, String>{
    'navName': '商城',
    'selected_png': 'resources/images/eshop_selected.png',
    'selected_webp': 'resources/images/eshop.json',
    'unselected': 'resources/images/eshop_unselected.png',
  },
  <String, String>{
    'navName': '我的',
    'selected_png': 'resources/images/mine_selected.png',
    'selected_webp': 'resources/images/mine.json',
    'unselected': 'resources/images/mine_unselected.png',
  },
];

class BottomBarViewModel {
  BottomBarViewModel(
    this.passiveCount,
    this.currentIdx,
    this.showXiaoU,
    this.barItemVMList,
  );

  BottomBarViewModel.fromStore(Store<BottomBarState> store) {
    passiveCount = store.state.passiveCount;
    currentIdx = store.state.currentIndex;
    showXiaoU = store.state.showXiaoU;
    barItemVMList.clear();
    store.state.barItemsState.barItemsVM.forEach((BarItemState element) {
      barItemVMList.add(
        BarItemViewModel(
          element.navText,
          element.selectedImage,
          element.unselectedImage,
          element.showRedPoint,
          element.needAnimation,
        ),
      );
    });
  }

  int passiveCount = 0;
  int currentIdx = 0;
  bool showXiaoU = false;
  List<BarItemViewModel> barItemVMList = <BarItemViewModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BottomBarViewModel &&
          runtimeType == other.runtimeType &&
          passiveCount == other.passiveCount &&
          currentIdx == other.currentIdx &&
          showXiaoU == other.showXiaoU &&
          listEquals(barItemVMList, other.barItemVMList);

  @override
  int get hashCode =>
      currentIdx.hashCode ^
      passiveCount.hashCode ^
      showXiaoU.hashCode ^
      listHashCode(barItemVMList);

  @override
  String toString() {
    return 'BottomBarViewModel{currentIdx: $currentIdx, passiveCount: $passiveCount, showXiaoUStatus: $showXiaoU, barItemVMList: $barItemVMList}';
  }
}

class BarItemViewModel {
  BarItemViewModel(
    this.navText,
    this.selectedImage,
    this.unselectedImage,
    this.showRedPoint,
    this.needAnimation,
  );

  String navText = '';
  String selectedImage = '';
  String unselectedImage = '';
  bool showRedPoint = false;
  bool needAnimation = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BarItemViewModel &&
          runtimeType == other.runtimeType &&
          navText == other.navText &&
          selectedImage == other.selectedImage &&
          unselectedImage == other.unselectedImage &&
          showRedPoint == other.showRedPoint &&
          needAnimation == other.needAnimation;

  @override
  int get hashCode =>
      navText.hashCode ^
      selectedImage.hashCode ^
      unselectedImage.hashCode ^
      showRedPoint.hashCode ^
      needAnimation.hashCode;

  @override
  String toString() {
    return 'BarItemViewModel{navText: $navText, selectedImage: $selectedImage, unselectedImage: $unselectedImage, showRedPoint: $showRedPoint, needAnimation: $needAnimation}';
  }
}

class MourningStatusViewModel {
  MourningStatusViewModel(this.showMourning);

  bool showMourning = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MourningStatusViewModel &&
          runtimeType == other.runtimeType &&
          showMourning == other.showMourning;

  @override
  int get hashCode => showMourning.hashCode;

  @override
  String toString() {
    return 'MourningStatusViewModel{showMourning: $showMourning,}';
  }
}

class XiaoUBubbleViewModel {
  XiaoUBubbleViewModel(this.showBubble);

  bool showBubble = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is XiaoUBubbleViewModel &&
          runtimeType == other.runtimeType &&
          showBubble == other.showBubble;

  @override
  int get hashCode => showBubble.hashCode;

  @override
  String toString() {
    return 'XiaoUBubbleViewModel{showBubble: $showBubble,}';
  }
}
