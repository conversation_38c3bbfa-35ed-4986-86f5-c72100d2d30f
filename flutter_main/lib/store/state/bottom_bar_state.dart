import '../viewmodel/bottom_bar_viewmodel.dart';

class BottomBarState {
  BottomBarState();

  BottomBarState.deepCopy(BottomBarState state)
      : showXiaoU = state.showXiaoU,
        showMourning = state.showMourning,
        passiveCount = state.passiveCount,
        currentIndex = state.currentIndex,
        barItemsState = BarItemsState.deepCopy(state.barItemsState),
        alarmDatasState = AlarmDatasState.deepCopy(state.alarmDatasState),
        consumeDataState = ConsumeDataState.deepCopy(state.consumeDataState);

  bool showXiaoU = false;
  bool showMourning = false;
  int passiveCount = 0;
  int currentIndex = 0;
  BarItemsState barItemsState = BarItemsState();
  AlarmDatasState alarmDatasState = AlarmDatasState();
  ConsumeDataState consumeDataState = ConsumeDataState();
}

class BarItemsState {
  BarItemsState() {
    // 初始化底Tab数据
    tabIcons.forEach((Map<String, String> element) {
      final BarItemState barItemState = BarItemState(
        false,
        element['navName']!,
        element['selected_png']!,
        element['unselected']!,
        false,
        false,
      );
      barItemsVM.add(barItemState);
    });
  }

  BarItemsState.deepCopy(BarItemsState state)
      : barItemsVM = List<BarItemState>.generate(
          state.barItemsVM.length,
          (int index) {
            final BarItemState barItemState = state.barItemsVM[index];
            return BarItemState.deepCopy(
              barItemState,
            );
          },
        );

  List<BarItemState> barItemsVM = <BarItemState>[];

  void clearState() {
    barItemsVM.clear();
  }
}

class BarItemState {
  BarItemState(
    this.showBackTop,
    this.navText,
    this.selectedImage,
    this.unselectedImage,
    this.showRedPoint,
    this.needAnimation,
  );

  BarItemState.deepCopy(BarItemState state)
      : showBackTop = state.showBackTop,
        navText = state.navText,
        selectedImage = state.selectedImage,
        unselectedImage = state.unselectedImage,
        showRedPoint = state.showRedPoint,
        needAnimation = state.needAnimation;

  bool showBackTop = false;
  String navText = '';
  String selectedImage = '';
  String unselectedImage = '';
  bool showRedPoint = false;
  bool needAnimation = false;
}

/// 告警弹窗
class AlarmDatasState {
  AlarmDatasState();

  AlarmDatasState.deepCopy(AlarmDatasState state)
      : alarmDatas =
            List<AlarmDataState>.generate(state.alarmDatas.length, (int index) {
          final AlarmDataState alarmDataState = state.alarmDatas[index];
          return AlarmDataState.deepCopy(alarmDataState);
        });

  List<AlarmDataState> alarmDatas = <AlarmDataState>[];

  void clearState() {
    alarmDatas.clear();
  }
}

class AlarmDataState {
  AlarmDataState(
    this.deviceName,
    this.deviceLocation,
    this.errorTitle,
    this.errorSolution,
    this.devicePicture,
    this.buttonMessage,
    this.buttonUrl,
    this.deviceCode,
  );

  AlarmDataState.deepCopy(AlarmDataState state)
      : deviceName = state.deviceName,
        deviceLocation = state.deviceLocation,
        errorTitle = state.errorTitle,
        errorSolution = state.errorSolution,
        devicePicture = state.devicePicture,
        buttonMessage = state.buttonMessage,
        buttonUrl = state.buttonUrl,
        deviceCode = state.deviceCode;

  String deviceName = '';
  String deviceLocation = '';
  String errorTitle = '';
  String errorSolution = '';
  String devicePicture = '';
  String buttonMessage = '';
  String buttonUrl = '';
  String deviceCode = '';
}

/// 耗材冒泡
class ConsumeDataState {
  ConsumeDataState();

  ConsumeDataState.deepCopy(ConsumeDataState state)
      : message = state.message,
        jumpUrl = state.jumpUrl;

  String message = '';
  String jumpUrl = '';
}
