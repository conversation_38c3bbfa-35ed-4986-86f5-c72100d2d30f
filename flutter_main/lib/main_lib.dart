library flutter_main;

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_main/model/common_context.dart';
import 'package:flutter_main/utils/common_localizations_delegate.dart';
import 'package:flutter_main/utils/log_util.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:smart_home/common/smart_home_route_manager.dart';

import 'store/bottom_bar_store.dart';
import 'store/state/bottom_bar_state.dart';
import 'store/viewmodel/bottom_bar_viewmodel.dart';
import 'tools/pre_load_data.dart';

// ignore: must_be_immutable
class MyApp extends StatelessWidget {
  static Completer<dynamic>? waitingCompleter;
  BuildContext? refcontext;
  final Widget child;

  MyApp({
    super.key,
    required this.child,
    required List<String>? args,
  }) {
    PreLoadData.argList = args;
    PreLoadData.init();
    waitingCompleter = new Completer<dynamic>();
    //图片内存缓存限制200M
    PaintingBinding.instance.imageCache.maximumSizeBytes = 1024 * 1024 * 200;
  }

  Widget appBuilder(Widget home) {
    return MaterialApp(
      title: '海尔智家',
      builder: (BuildContext context, Widget? widget) {
        return MediaQuery(
          //设置文字大小不随系统设置改变
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: widget!,
        );
      },
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: false,
        //设置导航栏颜色
        appBarTheme: AppBarTheme.of(refcontext!).copyWith(
          color: Colors.white,
          toolbarTextStyle: const TextTheme(
                  titleLarge: TextStyle(color: Colors.black, fontSize: 17))
              .bodyMedium,
          titleTextStyle: const TextTheme(
                  titleLarge: TextStyle(color: Colors.black, fontSize: 17))
              .titleLarge,
        ),
      ),
      home: home,
      // ignore: missing_return
      localeResolutionCallback:
          (Locale? deviceLocale, Iterable<Locale> supportedLocales) {
        LogUtil.d('deviceLocale: $deviceLocale', tag: 'MyApp');
        if (deviceLocale != null) {
          CommonContext().language = deviceLocale.toString();
        }
        return;
      },
      // locale:const Locale('zh','CN'),      //指定简体中文
      localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
        // 本地化代理
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        CommonLocalizationsDelegate(),
      ],
      supportedLocales: const <Locale>[
        // const Locale('en', 'US'), // 美国英语
        Locale('zh', 'CN'), // 中文简体
        //其它Locales
      ],
      navigatorObservers: <NavigatorObserver>[SmartHomeRouteManager.observer],
    );
  }

  @override
  Widget build(BuildContext context) {
    this.refcontext = context;

    if (Platform.isAndroid) {
      /// Android状态栏设置为透明
      SystemUiOverlayStyle systemUiOverlayStyle =
          const SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    }

    return ScreenUtilInit(
      designSize: const Size(375, 667),
      builder: () => StoreProvider<BottomBarState>(
        store: bottomBarStore,
        child: StoreConnector<BottomBarState, MourningStatusViewModel>(
          distinct: true,
          converter: (Store<BottomBarState> store) {
            final bool flag = store.state.showMourning;
            return MourningStatusViewModel(flag);
          },
          builder: (BuildContext context,
              MourningStatusViewModel mourningStatusViewModel) {
            return mourningStatusViewModel.showMourning
                ? ColorFiltered(
                    colorFilter: const ColorFilter.mode(
                      Colors.grey,
                      BlendMode.hue,
                    ),
                    child: appBuilder(child),
                  )
                : appBuilder(child);
          },
        ),
      ),
    );
  }
}
