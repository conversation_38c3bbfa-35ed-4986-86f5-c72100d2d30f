import 'package:app_mine/screens/purse/purse_presenter.dart';
import 'package:app_mine/screens/purse/store/purse_action.dart';
import 'package:app_mine/screens/purse/widgets/PurseList.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/second_page_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

/// 我的钱包
class Purse extends StatefulWidget {
  const Purse({super.key});

  @override
  State<Purse> createState() => _PurseState();
}

class _PurseState extends State<Purse> {
  /// 我的钱包presenter
  final PursePresenter _presenter = PursePresenter();
  @override
  void initState() {
    super.initState();
    /// 初始化数据
    _presenter.initPageData();
  }

  @override
  void dispose() {
    /// 清空数据
    appMineStore.dispatch(UpdatePurseAction(null));
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StoreProvider<AppMineState>(
      store: appMineStore,
      child: const SecondPageScaffold(
          title: Constant.purseTitle, child: PurseList()),
    );
  }
}
