import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:redux/redux.dart';

/// 我的钱包 viewModel
class PurseViewModel {
  PurseViewModel({
    this.purseList,
  });

  List<ConfigItemModel?>? purseList;

  /// 组装viewModel的数据
  static PurseViewModel fromStore(Store<AppMineState> store) {
    return PurseViewModel(
      purseList: store.state.purseState.purseList,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurseViewModel &&
          runtimeType == other.runtimeType &&
           listEquals(purseList, other.purseList);

  @override
  int get hashCode => listHashCode(purseList ?? <ConfigItemModel?>[]);

  @override
  String toString() {
    return 'PurseViewModel{purseList: $purseList}';
  }
}
