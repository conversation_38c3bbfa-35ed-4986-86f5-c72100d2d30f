import 'dart:convert';
import 'dart:io';

import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:app_mine/screens/purse/models/config_response_model.dart';
import 'package:app_mine/screens/purse/store/purse_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:resource/resource.dart';

/// 我的钱包数据处理
class PursePresenter {
  PursePresenter();

  // 我的钱包
  static List<Map<String, String>> purseDefaultList = <Map<String, String>>[
    <String, String>{
      'title': '我的卡包',
      'comeFrom': 'MB10256',
      'detailAddress': '${Constant.pocketUrl}?needAuthLogin=1',
      'titleImage': 'assets/images/purse-card.png',
      'subtitle': '',
    },
    <String, String>{
      'title': '我的资产',
      'comeFrom': 'MB10255',
      'detailAddress': Constant.myAssetsUrl,
      'titleImage': 'assets/images/purse-assets.png',
      'subtitle': '',
    },
  ];

  /// 初始化页面数据
  Future<void> initPageData() async {
    /// 钱包先使用本地兜底数据
    _userDefaultPurseData();


    /// 获取动态化配置文件
    _retrieveDynamicConfigFile();
  }

  /// 获取动态化配置文件
  Future<void> _retrieveDynamicConfigFile() async {
    try {
      await Resource.getCommonRes(
          Constant.purseConfigName, 'configAPP', _getCommonResCallback);
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_retrieveDynamicConfigFile err: $err');
    }
  }

  /// 获取配置文件接口回调信息
  Future<void> _getCommonResCallback(Map<dynamic, dynamic> response) async {
    try {
      final ConfigResponseModel configResponseModel =
          ConfigResponseModel.fromJson(response);

      if (configResponseModel.retCode == '000000') {
        if (configResponseModel.retData?.path == null) {
          return;
        }

        // 读取存储到指定文件中的数据
        final File _file = File(configResponseModel.retData!.path);
        final String jsonString = await _file.readAsString();

        /// 解析字符，获得我的钱包数据
        _getPurseDataFromFileString(jsonString);
      }
    } catch (err) {
      // 处理异常情况，使用本地json作为最后保障，可不做任何处理，默认使用本地配置
      DevLogger.error(
          tag: Constant.packageName, msg: '_getCommonResCallback err: $err');
    }
  }

  /// 解析字符，获得我的钱包数据
  void _getPurseDataFromFileString(String jsonString) {
    /// 我的钱包数据
    List<ConfigItemModel?>? _purseData;

    try {
      /// 解析json
      final ConfigFileDataModel configFileDataModel =
          ConfigFileDataModel.fromJson(
              jsonDecode(jsonString) as Map<String, dynamic>);
      if (configFileDataModel.data != null) {
        /// 循环找钱包配置数据
        for (final ConfigFileDataItemModel? item in configFileDataModel.data!) {
          if (item?.id == Constant.purseResourceId &&
              item?.specialContentList != null &&
              item!.specialContentList!.isNotEmpty) {
            /// 我的钱包更新
            _purseData = item.specialContentList;
            appMineStore.dispatch(UpdatePurseAction(item.specialContentList));
          } 
          /// 钱包找到了，退出循环
          if (_purseData?.length != null) {
            break;
          }
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_getPurseDataFromFileString err: $err');
    }
  }

  /// 我的钱包使用本地兜底数据
  void _userDefaultPurseData() {
    final List<ConfigItemModel> _purseData = purseDefaultList
        .map<ConfigItemModel>(
            (Map<String, String> item) => ConfigItemModel.fromJson(item))
        .toList();
    appMineStore.dispatch(UpdatePurseAction(_purseData));
  }
}
