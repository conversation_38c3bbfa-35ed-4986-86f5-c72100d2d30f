import 'package:app_mine/screens/purse/models/config_file_data_model.dart';

/// 更新我的钱包
class UpdatePurseAction {
  UpdatePurseAction(this.purseList);

  /// [purseList] 我的钱包列表数据
  List<ConfigItemModel?>? purseList;
}

/// 查询token并且vdn跳转
class GoToPageWithTokenAction {
  GoToPageWithTokenAction({required this.ysUrl, required this.url});

  /// [ysUrl] 验收url
  final String ysUrl;

  /// [url] 生产url
  final String url;
}
