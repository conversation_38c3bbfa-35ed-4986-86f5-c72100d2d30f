import 'package:app_mine/screens/purse/store/purse_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> purseReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdatePurseAction>(_updatePurseReducer).call,
];

/// 更新我的钱包reducer
AppMineState _updatePurseReducer(
    AppMineState state, UpdatePurseAction action) {    
    state.purseState.purseList = action.purseList;
    return state;
}
