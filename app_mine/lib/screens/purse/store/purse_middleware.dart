import 'package:app_mine/screens/purse/store/purse_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/util.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

final List<Middleware<AppMineState>> purseMiddlewares =
    <Middleware<AppMineState>>[
  TypedMiddleware<AppMineState, GoToPageWithTokenAction>(goToPageWithTokenMiddleware).call
];

void goToPageWithTokenMiddleware(
  Store<AppMineState> store,
  GoToPageWithTokenAction action,
  NextDispatcher next,
) {
  final OauthData? oauthData = User.getOauthDataSync();
  if (oauthData?.user_center_access_token != null) {
    final String url =
        _combineUrlWithToken(action.url, oauthData!.user_center_access_token);
    if (action.ysUrl.isNotEmpty) {
      final String ysUrl = _combineUrlWithToken(
          action.ysUrl, oauthData.user_center_access_token);
      goToPageByEnv(ysUrl, url);
    } else {
      goToPage(url);
    }
  } else {
    goToPageByEnv(action.ysUrl, action.url);
  }
}

/// 在url后面拼接token=abc参数
String _combineUrlWithToken(String url, String token) {
  if (url.contains('?')) {
    return '$url&token=$token';
  }
  return '$url?token=$token';
}
