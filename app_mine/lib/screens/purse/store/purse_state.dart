import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

/// 我的钱包
class PurseState {
  PurseState({this.purseList});

  /// 钱包列表
  List<ConfigItemModel?>? purseList;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurseState &&
          runtimeType == other.runtimeType &&
          listEquals(purseList, other.purseList);

  @override
  int get hashCode => listHashCode(purseList ?? <ConfigItemModel?>[]);

  @override
  String toString() {
    return 'PurseState{purseList: $purseList}';
  }
}
