import 'package:app_mine/screens/purse/purse_view_model.dart';
import 'package:app_mine/screens/purse/widgets/ListItem.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';

/// 我的钱包列表
class PurseList extends StatelessWidget {
  const PurseList({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, PurseViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            PurseViewModel.fromStore(store),
        builder: (BuildContext context, PurseViewModel vm) {
          if (vm.purseList == null || vm.purseList!.isEmpty) {
            return const SizedBox.shrink();
          }
          return ListView.separated(
              itemBuilder: (BuildContext context, int index) {
                return ListItem(
                    name: vm.purseList![index]?.name ?? '',
                    icon: vm.purseList![index]?.icon ?? '',
                    url: vm.purseList![index]?.url ?? '',
                    eventId: vm.purseList![index]?.eventId ?? '',
                    needToken: vm.purseList![index]?.subtitle == 'needToken');
              },
              separatorBuilder: (BuildContext context, int index) {
                return const _Separator();
              },
              itemCount: vm.purseList!.length);
        });
  }
}

/// ListView的列表分隔符
class _Separator extends StatelessWidget {
  const _Separator({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      height: 0.5.w,
      child: Container(
          margin: EdgeInsets.only(left: 16.w),
          height: 0.5.w,
          color: const Color.fromRGBO(242, 242, 242, 1)),
    );
  }
}
