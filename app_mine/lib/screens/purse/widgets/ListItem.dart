import 'package:app_mine/screens/purse/store/purse_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/arrow_right.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 列表项
class ListItem extends StatelessWidget {
  const ListItem(
      {super.key,
      required this.name,
      required this.icon,
      required this.url,
      required this.needToken,
      required this.eventId});

  /// [name] 标题
  final String name;

  /// [icon] 图标
  final String icon;

  /// [url] 图标
  final String url;

  /// [needToken] 是否需要token
  final bool needToken;

  /// [eventId] 事件id
  final String eventId;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        gioTrack(eventId);
        if (name == Constant.zhiJiaPurse) {
          appMineStore.dispatch(GoToPageWithTokenAction(
              ysUrl: Constant.zhiJiaPurseYsUrl, url: url));
        } else {
          if (needToken) {
            appMineStore.dispatch(GoToPageWithTokenAction(ysUrl: '', url: url));
          } else {
            goToPage(url);
          }
        }
      },
      child: Container(
        height: 52.w,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          children: <Widget>[
            _Icon(icon: icon),
            _Title(name: name),
            const Expanded(
              child: SizedBox.shrink(),
            ),
            const ArrowRight(),
          ],
        ),
      ),
    );
  }
}

/// 标题
class _Title extends StatelessWidget {
  const _Title({
    super.key,
    required this.name,
  });

  final String name;

  @override
  Widget build(BuildContext context) {
    return Text(
      name,
      style: TextStyle(
        fontSize: 15.sp,
        fontWeight: FontWeight.w400,
        height: 1.3,
        color: const Color.fromRGBO(0, 0, 0, 0.8),
      ),
    );
  }
}

/// 图标
class _Icon extends StatelessWidget {
  const _Icon({
    super.key,
    required this.icon,
  });

  final String icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        right: 12.w,
      ),
      child: icon.startsWith('http')
          ? CommonNetWorkImage(
              url: icon,
              width: 24.w,
              height: 24.w,
              fit: BoxFit.cover,
              errorWidget: const _Placeholder(),
              placeHolder: const _Placeholder(),
            )
          : Image.asset(
              icon,
              package: Constant.packageName,
              width: 24.w,
              height: 24.w,
              fit: BoxFit.cover
            ),
    );
  }
}

/// 默认占位图
class _Placeholder extends StatelessWidget {
  const _Placeholder({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 24.w,
      height: 24.w,
      decoration:
          const BoxDecoration(color: Color(0xFFF5F5F5), shape: BoxShape.circle),
    );
  }
}
