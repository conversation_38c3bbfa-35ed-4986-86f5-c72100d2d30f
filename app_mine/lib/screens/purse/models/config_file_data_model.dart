import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

part 'config_file_data_model.g.dart';

/// 配置文件数据model
@JsonSerializable(explicitToJson: true, anyMap: true)
class ConfigFileDataModel {
  ConfigFileDataModel({
    this.data,
  });

  final List<ConfigFileDataItemModel?>? data;

  factory ConfigFileDataModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ConfigFileDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileDataModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConfigFileDataModel &&
          runtimeType == other.runtimeType &&
          listEquals(data, other.data);

  @override
  int get hashCode => listHashCode(data ?? <ConfigFileDataItemModel?>[]);

  @override
  String toString() {
    return 'ConfigFileDataModel{data: $data}';
  }
}

@JsonSerializable(explicitToJson: true, anyMap: true)
class ConfigFileDataItemModel {
  ConfigFileDataItemModel({
    required this.id,
    this.specialContentList,
  });
  /// [id]
  @JsonKey(defaultValue: 0)
  final int id;

  /// [specialContentList] 内容列表
  final List<ConfigItemModel?>? specialContentList;

  factory ConfigFileDataItemModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ConfigFileDataItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileDataItemModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConfigFileDataItemModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          listEquals(specialContentList, other.specialContentList);

  @override
  int get hashCode => id.hashCode ^ listHashCode(specialContentList ?? <ConfigFileDataItemModel?>[]);

  @override
  String toString() {
    return 'ConfigFileDataItemModel{id: $id, specialContentList: $specialContentList}';
  }
}

/// 配置项model
@JsonSerializable(explicitToJson: true, anyMap: true)
class ConfigItemModel {
  const ConfigItemModel({
    required this.name,
    required this.eventId,
    required this.url,
    required this.icon,
    required this.subtitle,
  });

  /// [name] 名称
  @JsonKey(name: 'title', defaultValue: '')
  final String name;

  /// [eventId] 事件id
  @JsonKey(name: 'comeFrom', defaultValue: '')
  final String eventId;

  /// [url] 跳转url
  @JsonKey(name: 'detailAddress', defaultValue: '')
  final String url;

  /// [icon] 图标
  @JsonKey(name: 'titleImage', defaultValue: '')
  final String icon;

  /// [subtitle] == 'needToken' 表示需要token
  @JsonKey(defaultValue: '')
  final String subtitle;

  factory ConfigItemModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ConfigItemModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigItemModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConfigItemModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          eventId == other.eventId &&
          url == other.url &&
          icon == other.icon &&
          subtitle == other.subtitle;

  @override
  int get hashCode =>
      name.hashCode ^
      eventId.hashCode ^
      url.hashCode ^
      icon.hashCode ^
      subtitle.hashCode;

  @override
  String toString() {
    return 'ConfigItemModel{name: $name, eventId: $eventId, url: $url, icon: $icon, subtitle: $subtitle}';
  }
}
