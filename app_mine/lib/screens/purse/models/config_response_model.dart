import 'package:json_annotation/json_annotation.dart';

part 'config_response_model.g.dart';

/// 配置文件响应model
@JsonSerializable(explicitToJson: true, anyMap: true)
class ConfigResponseModel {
  ConfigResponseModel({
    required this.retCode,
    required this.retInfo,
    this.retData,
  });

  @JsonKey(defaultValue: '')
  final String? retCode;

  @JsonKey(defaultValue: '')
  final String? retInfo;

  final ConfigFileModel? retData;

  factory ConfigResponseModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ConfigResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigResponseModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConfigResponseModel &&
          runtimeType == other.runtimeType &&
          retCode == other.retCode &&
          retInfo == other.retInfo &&
          retData == other.retData;

  @override
  int get hashCode => retCode.hashCode ^ retInfo.hashCode ^ retData.hashCode;

  @override
  String toString() {
    return 'ConfigResponseModel{retCode: $retCode, retInfo: $retInfo, retData: $retData}';
  }
}

/// 配置文件model
@JsonSerializable(explicitToJson: true, anyMap: true)
class ConfigFileModel {
  ConfigFileModel({
    required this.path,
  });

  /// [path] 文件路径
  @JsonKey(defaultValue: '')
  final String path;

  factory ConfigFileModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ConfigFileModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigFileModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ConfigFileModel &&
          runtimeType == other.runtimeType &&
          path == other.path;

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() {
    return 'ConfigFileModel{path: $path}';
  }
}
