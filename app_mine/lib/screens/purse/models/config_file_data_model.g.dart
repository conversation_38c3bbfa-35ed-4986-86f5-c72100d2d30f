// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'config_file_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConfigFileDataModel _$ConfigFileDataModelFromJson(Map json) =>
    ConfigFileDataModel(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) =>
              e == null ? null : ConfigFileDataItemModel.fromJson(e as Map))
          .toList(),
    );

Map<String, dynamic> _$ConfigFileDataModelToJson(
        ConfigFileDataModel instance) =>
    <String, dynamic>{
      'data': instance.data?.map((e) => e?.toJson()).toList(),
    };

ConfigFileDataItemModel _$ConfigFileDataItemModelFromJson(Map json) =>
    ConfigFileDataItemModel(
      id: json['id'] as int? ?? 0,
      specialContentList: (json['specialContentList'] as List<dynamic>?)
          ?.map((e) => e == null ? null : ConfigItemModel.fromJson(e as Map))
          .toList(),
    );

Map<String, dynamic> _$ConfigFileDataItemModelToJson(
        ConfigFileDataItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'specialContentList':
          instance.specialContentList?.map((e) => e?.toJson()).toList(),
    };

ConfigItemModel _$ConfigItemModelFromJson(Map json) => ConfigItemModel(
      name: json['title'] as String? ?? '',
      eventId: json['comeFrom'] as String? ?? '',
      url: json['detailAddress'] as String? ?? '',
      icon: json['titleImage'] as String? ?? '',
      subtitle: json['subtitle'] as String? ?? '',
    );

Map<String, dynamic> _$ConfigItemModelToJson(ConfigItemModel instance) =>
    <String, dynamic>{
      'title': instance.name,
      'comeFrom': instance.eventId,
      'detailAddress': instance.url,
      'titleImage': instance.icon,
      'subtitle': instance.subtitle,
    };
