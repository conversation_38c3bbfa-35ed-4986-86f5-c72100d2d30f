import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';

/// 我的卡包
class PocketState {
  PocketState({this.pocketList});

  /// 卡包列表
  List<ConfigItemModel?>? pocketList;


  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PocketState &&
          runtimeType == other.runtimeType &&
          listEquals(pocketList, other.pocketList);

  @override
  int get hashCode => listHashCode(pocketList ?? <ConfigItemModel?>[]);

  @override
  String toString() {
    return 'PocketState{pocketList: $pocketList}';
  }
}
