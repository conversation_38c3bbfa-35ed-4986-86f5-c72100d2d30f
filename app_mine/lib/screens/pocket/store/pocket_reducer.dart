import 'package:app_mine/screens/pocket/store/pocket_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> pocketReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdatePocketAction>(_updatePocketReducer).call,
];

/// 更新我的卡包reducer
AppMineState _updatePocketReducer(
    AppMineState state, UpdatePocketAction action) {    
    state.pocketState.pocketList = action.pocketList;
    return state;
}
