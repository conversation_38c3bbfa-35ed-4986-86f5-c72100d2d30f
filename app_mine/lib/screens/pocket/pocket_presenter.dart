import 'dart:convert';
import 'dart:io';

import 'package:app_mine/screens/pocket/store/pocket_action.dart';
import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:app_mine/screens/purse/models/config_response_model.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:resource/resource.dart';

/// 我的卡包数据处理
class PocketPresenter {
  PocketPresenter();

  /// 我的卡包
  static List<Map<String, String>> pocketDefaultList = <Map<String, String>>[
    <String, String>{
      'title': '食联生态卡',
      'comeFrom': '',
      'detailAddress': Constant.foodEcologicalCodeUrl,
      'titleImage': 'assets/images/eat_card.png',
      'subtitle': '',
    }
  ];

  /// 初始化页面数据
  Future<void> initPageData() async {

    /// 卡包先使用本地兜底数据
    _userDefaultPocketData();

    /// 获取动态化配置文件
    _retrieveDynamicConfigFile();
  }

  /// 获取动态化配置文件
  Future<void> _retrieveDynamicConfigFile() async {
    try {
      await Resource.getCommonRes(
          Constant.purseConfigName, 'configAPP', _getCommonResCallback);
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_retrieveDynamicConfigFile err: $err');
    }
  }

  /// 获取配置文件接口回调信息
  Future<void> _getCommonResCallback(Map<dynamic, dynamic> response) async {
    try {
      final ConfigResponseModel configResponseModel =
          ConfigResponseModel.fromJson(response);

      if (configResponseModel.retCode == '000000') {
        if (configResponseModel.retData?.path == null) {
          return;
        }

        // 读取存储到指定文件中的数据
        final File _file = File(configResponseModel.retData!.path);
        final String jsonString = await _file.readAsString();

        /// 解析字符，我的卡包数据
        _getPocketDataFromFileString(jsonString);
      }
    } catch (err) {
      // 处理异常情况，使用本地json作为最后保障，可不做任何处理，默认使用本地配置
      DevLogger.error(
          tag: Constant.packageName, msg: '_getCommonResCallback err: $err');
    }
  }

  /// 解析字符，我的卡包数据
  void _getPocketDataFromFileString(String jsonString) {
   
    /// 我的卡包数据
    List<ConfigItemModel?>? _pocketData;

    try {
      /// 解析json
      final ConfigFileDataModel configFileDataModel =
          ConfigFileDataModel.fromJson(
              jsonDecode(jsonString) as Map<String, dynamic>);
      if (configFileDataModel.data != null) {
        /// 循环找卡包的配置数据
        for (final ConfigFileDataItemModel? item in configFileDataModel.data!) {
          if (item?.id == Constant.pocketResourceId &&
              item?.specialContentList != null &&
              item!.specialContentList!.isNotEmpty) {
            _pocketData = item.specialContentList;
            appMineStore.dispatch(UpdatePocketAction(item.specialContentList));
          }

          /// 卡包找到了，退出循环
          if (_pocketData?.length != null) {
            break;
          }
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_getPurseDataFromFileString err: $err');
    }
  }

  /// 我的卡包使用本地兜底数据
  void _userDefaultPocketData() {
    final List<ConfigItemModel> _pocketData = pocketDefaultList
        .map<ConfigItemModel>(
            (Map<String, String> item) => ConfigItemModel.fromJson(item))
        .toList();
    appMineStore.dispatch(UpdatePocketAction(_pocketData));
  }
}
