import 'package:app_mine/screens/purse/models/config_file_data_model.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:redux/redux.dart';

/// 我的卡包 viewModel
class PocketViewModel {
  PocketViewModel({
    this.pocketList,
  });

  List<ConfigItemModel?>? pocketList;

  /// 组装viewModel的数据
  static PocketViewModel fromStore(Store<AppMineState> store) {
    return PocketViewModel(
      pocketList: store.state.pocketState.pocketList,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PocketViewModel &&
          runtimeType == other.runtimeType &&
           listEquals(pocketList, other.pocketList);

  @override
  int get hashCode => listHashCode(pocketList ?? <ConfigItemModel?>[]);

  @override
  String toString() {
    return 'PocketViewModel{pocketList: $pocketList}';
  }
}
