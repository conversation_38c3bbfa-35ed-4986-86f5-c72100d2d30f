import 'package:app_mine/screens/pocket/pocket_presenter.dart';
import 'package:app_mine/screens/pocket/store/pocket_action.dart';
import 'package:app_mine/screens/pocket/widgets/pocket_list.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/second_page_scaffold.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';

/// 我的卡包
class Pocket extends StatefulWidget {
  const Pocket({super.key});

  @override
  State<Pocket> createState() => _PocketState();
}

class _PocketState extends State<Pocket> {
  /// 我的卡包presenter
  final PocketPresenter _presenter = PocketPresenter();
  @override
  void initState() {
    super.initState();

    /// 初始化数据
    _presenter.initPageData();
  }

  @override
  void dispose() {
    appMineStore.dispatch(UpdatePocketAction(null));
    super.dispose();
  }

 @override
  Widget build(BuildContext context) {
    return StoreProvider<AppMineState>(
      store: appMineStore,
      child: const SecondPageScaffold(
          title: Constant.pocketTitle, child: PocketList()),
    );
  }
}
