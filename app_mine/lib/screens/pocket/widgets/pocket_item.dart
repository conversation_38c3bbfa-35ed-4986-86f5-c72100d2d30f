import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PocketItem extends StatelessWidget {
  const PocketItem({
    super.key,
    required this.name,
    required this.icon,
    required this.url,
  });

  /// [name] 标题
  final String name;

  /// [icon] 图标
  final String icon;

  /// [url] 图标
  final String url;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        goToPage(url);
      },
      child: Container(
        alignment: Alignment.centerLeft,
        height: 120.w,
        decoration: icon.isEmpty
            ? BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
                color: const Color(0xFFF5F5F5),
              )
            : BoxDecoration(
                borderRadius: BorderRadius.circular(12.w),
                image: DecorationImage(
                  fit: BoxFit.fill,
                  image: icon.startsWith('http')
                      ? NetworkImage(
                          icon,
                        ) as ImageProvider
                      : AssetImage(
                          icon,
                          package: Constant.packageName,
                        ),
                ),
              ),
        child: _Title(name: name),
      ),
    );
  }
}

class _Title extends StatelessWidget {
  const _Title({
    super.key,
    required this.name,
  });

  final String name;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: 64.w,
      ),
      child: Text(
        name,
        style: TextStyle(
          fontSize: 20.sp,
          color: Colors.white,
          height: 1.5,
        ),
      ),
    );
  }
}
