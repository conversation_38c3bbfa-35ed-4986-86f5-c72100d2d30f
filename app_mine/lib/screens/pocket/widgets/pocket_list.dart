import 'package:app_mine/screens/pocket/pocket_view_model.dart';
import 'package:app_mine/screens/pocket/widgets/pocket_item.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';

/// 我的卡包列表
class PocketList extends StatelessWidget {
  const PocketList({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, PocketViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            PocketViewModel.fromStore(store),
        builder: (BuildContext context, PocketViewModel vm) {
          if (vm.pocketList == null || vm.pocketList!.isEmpty) {
            return const SizedBox.shrink();
          }
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: ListView.separated(
                itemBuilder: (BuildContext context, int index) {
                  return PocketItem(
                      name: vm.pocketList![index]?.name ?? '',
                      icon: vm.pocketList![index]?.icon ?? '',
                      url: vm.pocketList![index]?.url ?? '');
                },
                separatorBuilder: (BuildContext context, int index) {
                  return const _Separator();
                },
                itemCount: vm.pocketList!.length),
          );
        });
  }
}

/// ListView的列表分隔符
class _Separator extends StatelessWidget {
  const _Separator({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      height: 0.5.w,
      child: Container(
          margin: EdgeInsets.only(left: 16.w),
          height: 0.5.w,
          color: const Color.fromRGBO(242, 242, 242, 1)),
    );
  }
}
