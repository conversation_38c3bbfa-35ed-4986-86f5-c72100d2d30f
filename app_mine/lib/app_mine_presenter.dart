import 'dart:async';

import 'package:app_mine/badges/models/badges_response_model.dart';
import 'package:app_mine/badges/store/badges_action.dart';
import 'package:app_mine/current_family/switch_query_model.dart';
import 'package:app_mine/device_user_popup/store/device_user_popup_action.dart';
import 'package:app_mine/list_menu/store/menu_action.dart';
import 'package:app_mine/service/service.dart';
import 'package:app_mine/store/mine_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/user_info/models/additional_user_info_response_model.dart';
import 'package:app_mine/user_info/store/user_action.dart';
import 'package:app_mine/utils/app_info.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:plugin_device/model/device_info_model.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:plugin_upgrade/plugin_upgrade.dart';
import 'package:storage/storage.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import 'badges/models/badges_model.dart';
import 'current_family/store/family_action.dart';

class AppMinePresenter {
  AppMinePresenter();

  StreamSubscription<UserLoginSuccess>? _userLoginSuccessListener;
  StreamSubscription<UserInfoRefreshedMessage>? _userInfoRefreshedListener;
  StreamSubscription<CurrentFamilyUpdateComplete>? _curFamilyListener;
  StreamSubscription<UserLogoutMessage>? _userLogoutListener;
  StreamSubscription<NetworkStatus>? _networkListener;

  /// 页面停留时长
  int pageStayTime = 0;

  Future<void> initAppData(
      {bool? initState, bool? refresh, bool? networkChange}) async {
    final LoginStatus? loginStatus = _getLoginStatus();
    if (initState ?? false) {
      /// 初始化状态，查询app版本号
      _getAppInfo();
    }

    /// 查询app版本更新，关于我们是否显示NEW标
    _getAboutUsShowNew();

    // 未登录不查询用户信息
    if (loginStatus?.isLogin != true) {
      return;
    }

    if (refresh ?? false) {
      User.refreshUser().then((void value) {
        _getCurrentFamily(refresh: true);
      });
    } else {
      _getCurrentFamily();
    }

    _getUserInfo();

    /// 登录后请求服务端数据
    _requestDataWhenLogin();

    /// 查询网器用户弹窗是否弹出过，本地缓存
    _getDeviceUserPopupShowStorage();
  }

  /// 查询app版本号
  Future<void> _getAppInfo() async {
    await AppInfo.getAppInfo();

    /// state中版本号为空，则更新版本号
    if (AppInfo.appVersion.isNotEmpty &&
        appMineStore.state.menuState.appVersion.isEmpty) {
      appMineStore.dispatch(UpdateAppVersionAction(AppInfo.appVersion));
    }
  }

  /// 登录后请求的数据
  Future<void> _requestDataWhenLogin() async {
    final OauthData? oauthData = User.getOauthDataSync();
    DevLogger.info(
        tag: Constant.packageName,
        msg: 'getOauthDataSync oauthData: $oauthData');
    if (oauthData != null) {
      /// 查询用户附加信息整合(会员等级)
      _getAdditionalUserInfo();

      /// 查询小红点
      _getBadgesData();

      ///查询是否展示家庭升级标签
      _queryUpgradeBadge(oauthData.uc_user_id);
    }
  }

  /// 查询小红点
  Future<void> _getBadgesData() async {
    final BadgesResponseModel? badgesResponseModel =
        await Service().getBadges();
    if (badgesResponseModel != null && badgesResponseModel.badges != null) {
      final BadgesModel badgesModel = badgesResponseModel.badges!;
      final bool settingBadge = badgesModel.deviceNeedUpgrade;

      /// 消息小红点
      final MessageCenterUnreadItem? msgUnReadItem =
          badgesModel.msgcenterUnreadNum?.firstWhere(
              (MessageCenterUnreadItem? msg) =>
                  msg?.msgNums != null && msg!.msgNums > 0,
              orElse: () => null);
      bool messageBadge = false;
      if (msgUnReadItem != null) {
        messageBadge = true;
      } else {
        messageBadge = false;
      }

      final bool mineRedPoint = settingBadge || messageBadge;
      Storage.putBooleanValue(Constant.mineRedPointSp, mineRedPoint);
      appMineStore.dispatch(UpdateBadgesAction(badgesResponseModel.badges!));
    }
  }

  /// 查询用户附加信息整合(会员等级)
  Future<void> _getAdditionalUserInfo() async {
    final AdditionalUserInfoResponseModel? additionalUserInfoResponseModel =
        await Service().getAdditionalUserInfo(<String, dynamic>{
      ///品牌对应的拼写
      'brandName': 'zjst',
    });
    if (additionalUserInfoResponseModel?.userInfo != null) {
      appMineStore.dispatch(UpdateAdditionalUserInfoAction(
          additionalUserInfoResponseModel!.userInfo!));
    }
  }

  /// 关于我们是否显示NEW标：有版本更新则显示, 一个小红点，需要查2个Storage, 一个plugin
  Future<void> _getAboutUsShowNew({bool? usePlugin}) async {
    try {
      String newVersion = '';
      if (usePlugin ?? false) {
        newVersion = await _getAppUpdatePlugin();
      } else {
        newVersion = await _getAppUpdateStorage();
      }

      /// 如果有新版本，和本地存储的曝光版本号比较
      if (newVersion.isNotEmpty) {
        final String exposureVersion =
            await Storage.getStringValue(Constant.appVersionExposureKey);

        /// 如果新版本和曝光版本不一致，更新NEW标显示，否则不显示NEW
        appMineStore.dispatch(
            UpdateAppVersionBadgeAction(newVersion != exposureVersion));
      } else {
        /// 没有新版本，则去掉NEW标
        if (appMineStore.state.menuState.versionBadge) {
          appMineStore.dispatch(UpdateAppVersionBadgeAction(false));
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: '_getAboutUsShowNew err: $err');
    }
  }

  /// 更新的版本号(通过storage获取), 调用UpgradePlugin.checkFullVersion的时候会写入Storage提示更新的版本号
  Future<String> _getAppUpdateStorage() async {
    try {
      final String newVersion =
          await Storage.getStringValue(Constant.appNewVersionStorageKey);
      return newVersion;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getAppUpdateStorage err: $err');
      return '';
    }
  }

  /// 版本更新小红点（通过原生插件获取，登录登出调用此方法）
  Future<String> _getAppUpdatePlugin() async {
    try {
      final bool hasNewVersion = await UpgradePlugin.checkFullVersion(false);
      if (hasNewVersion) {
        return _getAppUpdateStorage();
      }
      return '';
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getAppUpdatePlugin err: $err');
      return '';
    }
  }

  /// 查询网器用户弹窗是否弹出过
  Future<void> _getDeviceUserPopupShowStorage() async {
    try {
      final bool showed = await Storage.getBooleanValue(
          Constant.deviceUserPopupShowedStorageKey);

      /// 设置网器用户弹窗是否显示过
      appMineStore.dispatch(UpdateDeviceUserPopupShowedAction(showed));
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getDeviceUserPopupShow err: $err');
    }
  }

  void addPluginListeners() {
    /// 监听添加前先确保都移除了
    removePluginListeners();

    /// 用户信息刷新完成
    _userInfoRefreshedListener = Message.listen<UserInfoRefreshedMessage>(
        (UserInfoRefreshedMessage event) {
      DevLogger.info(
          tag: Constant.packageName, msg: 'UserInfoRefreshedMessage callback');
      _getUserInfo();
    });

    /// 当前家庭变化监听
    _curFamilyListener = UIMessage.sub<CurrentFamilyUpdateComplete>(
        (CurrentFamilyUpdateComplete event) {
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'CurrentFamilyUpdateComplete callback');
      _getCurrentFamily();
    });

    /// 网络变化监听
    _networkListener = Network
        .onConnectivityChanged
        .listen((IsOnline onValue) async {
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'onConnectivityChanged callback:$result');

      /// 更新网络状态
      appMineStore
          .dispatch(UpdateNetworkAction(onValue.isOnline));
      if (onValue.isOnline) {
        /// 重新查询数据
        initAppData(networkChange: true);
        appMineStore.dispatch(UpdateRefreshAction());
      }
    });
  }

  /// 持久监听，伴随页面整个生命周期,包括用户登录、登出监听
  void addPersistentPluginListener() {
    /// 监听添加前先确保都移除了
    removePersistentPluginListeners();

    /// 用户登录成功监听，此时OauthData也已经刷新
    _userLoginSuccessListener =
        UIMessage.sub<UserLoginSuccess>((UserLoginSuccess event) {
      DevLogger.info(
          tag: Constant.packageName, msg: '_userLoginSuccessListener callback');
      final LoginStatus? loginStatus = _getLoginStatus();

      /// 登录后，查询用户基本信息
      if (loginStatus?.isLogin ?? false) {
        _getUserInfo();

        _getAboutUsShowNew(usePlugin: true);
        _requestDataWhenLogin();
      }
    });

    /// 退出登陆监听
    _userLogoutListener =
        Message.listen<UserLogoutMessage>((UserLogoutMessage event) {
      DevLogger.info(
          tag: Constant.packageName, msg: 'UserLogoutMessage callback');

      /// 退出登录查询关于我们NEW标
      _getAboutUsShowNew(usePlugin: true);

      appMineStore.dispatch(UpdateLoginStatusAction(false));
    });
  }

  /// 同步查询登录状态
  LoginStatus? _getLoginStatus() {
    try {
      final LoginStatus? loginStatus = User.getLoginStatusSync();
      if (loginStatus != null) {
        appMineStore.dispatch(UpdateLoginStatusAction(loginStatus.isLogin));
        DevLogger.info(
            tag: Constant.packageName,
            msg: '_getLoginStatus_isLogin:${loginStatus.isLogin}');
      }
      return loginStatus;
    } catch (error) {
      DevLogger.error(
          tag: Constant.packageName, msg: '_getLoginStatus error:$error');
      return null;
    }
  }

  /// 同步查询当前家庭级
  Future<void> _getCurrentFamily({bool? refresh}) async {
    try {
      final FamilyModel currentFamily = await Family.getCurrentFamily();
      DevLogger.info(
          tag: Constant.packageName,
          msg:
              '_getCurrentFamily data: $currentFamily memberType：${currentFamily.memberType}');

      appMineStore.dispatch(UpdateCurrentFamilyInfoAction(currentFamily));

      /// 根据当前家庭查询网器用户和设备数量
      if (currentFamily.familyId.isNotEmpty) {
        /// 下拉刷新时，要刷新一下设备列表
        if (refresh ?? false) {
          await UpDevicePlugin.updateDeviceList(immediate: true);
        }
        _getDeviceUserAndDeviceCount(currentFamily.familyId);
      }
    } catch (error) {
      DevLogger.error(
          tag: Constant.packageName, msg: '_getCurrentFamily error:$error');
    }
  }

  /// 查询网器用户和设备数量
  Future<void> _getDeviceUserAndDeviceCount(String familyId) async {
    try {
      final Map<String, DeviceInfoModel> deviceInfoMap =
          await UpDevicePlugin.getDeviceInfoMap(familyId);
      //排除附件设备
      deviceInfoMap.removeWhere(
          (String key, DeviceInfoModel value) => value.deviceRole == '3');

      /// 移除聚合卡片
      deviceInfoMap.removeWhere((String key, DeviceInfoModel value) =>
          key.contains(Constant.aggregationIdPrefix));

      //排除共享设备
      deviceInfoMap.removeWhere(
          (String key, DeviceInfoModel value) => value.isSharedDevice);

      /// 设备个数要排除附件及聚合卡片
      final int deviceCount = deviceInfoMap.length;
      //移除非网器设备
      deviceInfoMap.removeWhere(
          (String key, DeviceInfoModel value) => value.netType != 'device');
      final bool deviceUser = deviceInfoMap.isNotEmpty;
      appMineStore.dispatch(UpdateDeviceAction(deviceCount, deviceUser));
    } catch (error) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_getDeviceUserAndDeviceCount error:$error');
    }
  }

  /// 异步查询用户基本信息
  Future<void> _getUserInfo() async {
    try {
      final UserInfo userInfo = await User.getUserInfo();
      DevLogger.info(tag: Constant.packageName, msg: '_getUserInfo :$userInfo');
      appMineStore.dispatch(UpdateUserInfoAction(userInfo));
    } catch (error) {
      DevLogger.error(
          tag: Constant.packageName, msg: '_getUserInfo error:$error');
    }
  }

  /// 移除监听
  void removePluginListeners() {
    _userInfoRefreshedListener?.cancel();
    _curFamilyListener?.cancel();
    _networkListener?.cancel();
  }

  /// 移除持久监听，包括用户登录、登出
  void removePersistentPluginListeners() {
    _userLoginSuccessListener?.cancel();
    _userLogoutListener?.cancel();
  }

  Future<void> _queryUpgradeBadge(String ucUserId) async {
    final SwitchQueryResponseModel? switchQueryResponseModel =
        await Service().familyCardUpgradeSwitchQuery(<String, dynamic>{
      'switchType': Constant.upgradeBadgeSwitchType,
      'switchKey': ucUserId,
    });
    if (switchQueryResponseModel != null &&
        switchQueryResponseModel.statusModel != null &&
        switchQueryResponseModel.statusModel?.status != null) {
      appMineStore.dispatch(UpdateUpgradeBadgeVisibleAction(
          switchQueryResponseModel.statusModel?.status == Constant.statusOpen));
    }
  }
}
