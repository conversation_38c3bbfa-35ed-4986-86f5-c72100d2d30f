import 'package:app_mine/current_family/store/family_middleware.dart';
import 'package:app_mine/device_user_popup/store/device_user_popup_middleware.dart';
import 'package:app_mine/gpt_center/store/gpt_center_middleware.dart';
import 'package:app_mine/list_menu/store/menu_middleware.dart';
import 'package:app_mine/score_popup/store/score_pop_up_middle_ware.dart';
import 'package:app_mine/screens/purse/store/purse_middleware.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Middleware<AppMineState>> appMineMiddleware =
    <Middleware<AppMineState>>[
  GptCenterMiddleware().call,
  FamilyMiddleware().call,
  ...menuMiddlewares,
  ...deviceUserPopupMiddlewares,
  ...scorePopupMiddlewares,
  ...purseMiddlewares,
];
