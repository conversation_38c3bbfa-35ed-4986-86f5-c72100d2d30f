import 'package:app_mine/badges/store/badges_reducer.dart';
import 'package:app_mine/current_family/store/family_reducer.dart';
import 'package:app_mine/device_user_popup/store/device_user_popup_reducer.dart';
import 'package:app_mine/gpt_center/store/gpt_center_reducer.dart';
import 'package:app_mine/list_menu/store/menu_reducer.dart';
import 'package:app_mine/score_popup/store/score_pop_up_reducer.dart';
import 'package:app_mine/screens/pocket/store/pocket_reducer.dart';
import 'package:app_mine/screens/purse/store/purse_reducer.dart';
import 'package:app_mine/store/mine_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/user_info/store/user_reducer.dart';
import 'package:redux/redux.dart';

import '../navbar/store/navbar_reducer.dart';

final Reducer<AppMineState> appMineReducer =
    combineReducers(<AppMineState Function(AppMineState state, dynamic action)>[
  TypedReducer<AppMineState, UpdateLoginStatusAction>(_loginReducer).call,
  TypedReducer<AppMineState, UpdateRefreshAction>(_refreshReducer).call,
  TypedReducer<AppMineState, UpdateNetworkAction>(_networkReducer).call,
  ...currentFamilyReducer,
  ...userInfoReducer,
  ...menuStateReducer,
  ...gptCenterStateReducer,
  ...badgesReducer,
  ...deviceUserPopupReducer,
  ...purseReducer,
  ...pocketReducer,
  ...scorePopupReducer,
  ...navbarReducer,
]);

/// [isLogin] 登录reducer
AppMineState _loginReducer(AppMineState state, UpdateLoginStatusAction action) {
  state.isLogin = action.isLogin;
  if (!action.isLogin) {
    state.clear();
  }
  return state;
}

/// refresh reducer
AppMineState _refreshReducer(AppMineState state, UpdateRefreshAction action) {
  state.refreshCount += 1;
  return state;
}

AppMineState _networkReducer(AppMineState state, UpdateNetworkAction action) {
  state.netAvailable = action.netAvailable;
  return state;
}
