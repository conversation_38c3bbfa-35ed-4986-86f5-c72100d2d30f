import 'package:app_mine/badges/store/badges_state.dart';
import 'package:app_mine/device_user_popup/store/device_user_popup_state.dart';
import 'package:app_mine/gpt_center/store/gpt_center_state.dart';
import 'package:app_mine/list_menu/store/menu_state.dart';
import 'package:app_mine/score_popup/store/score_pop_up_state.dart';
import 'package:app_mine/screens/pocket/store/pocket_state.dart';
import 'package:app_mine/screens/purse/store/purse_state.dart';
import 'package:app_mine/user_info/store/user_state.dart';

import '../current_family/store/family_state.dart';
import '../navbar/store/navbar_state.dart';

class AppMineState {
  bool isLogin = false;

  bool netAvailable = true;

  /// [refreshCount] 刷新次数，下拉及网络恢复的时候更新
  int refreshCount = 0;
  CurrentFamilyState currentFamilyState = CurrentFamilyState();
  UserInfoState userInfoState = UserInfoState();
  MenuState menuState = MenuState();
  GptCenterState gptCenterState = GptCenterState();
  BadgesState badgeState = BadgesState();
  NavBarState navBarState = NavBarState();

  /// 网器用户state
  DeviceUserState deviceUserState = DeviceUserState();

  /// 我的钱包state
  PurseState purseState = PurseState();

  /// 我的卡包state
  PocketState pocketState = PocketState();

  /// 五星随手评
  ScorePopupState scorePopupState = ScorePopupState();

  void clear() {
    isLogin = false;
    currentFamilyState.clear();
    userInfoState.clear();
    badgeState.clear();
    deviceUserState.clear();
    scorePopupState.clear();
  }
}
