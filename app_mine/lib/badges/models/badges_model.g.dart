// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'badges_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BadgesModel _$BadgesModelFromJson(Map json) => BadgesModel(
      deviceNeedUpgrade: json['deviceNeedUpgrade'] as bool? ?? false,
      msgcenterUnreadNum: (json['msgcenterUnreadNum'] as List<dynamic>?)
          ?.map((e) => e == null
              ? null
              : MessageCenterUnreadItem.fromJson(
                  Map<String, dynamic>.from(e as Map)))
          .toList(),
      workOrderTodoCount: json['workOrderTodoCount'] == null
          ? null
          : WorkOrderTodoCount.fromJson(
              Map<String, dynamic>.from(json['workOrderTodoCount'] as Map)),
    );

Map<String, dynamic> _$BadgesModelToJson(BadgesModel instance) =>
    <String, dynamic>{
      'deviceNeedUpgrade': instance.deviceNeedUpgrade,
      'msgcenterUnreadNum':
          instance.msgcenterUnreadNum?.map((e) => e?.toJson()).toList(),
      'workOrderTodoCount': instance.workOrderTodoCount?.toJson(),
    };

MessageCenterUnreadItem _$MessageCenterUnreadItemFromJson(
        Map<String, dynamic> json) =>
    MessageCenterUnreadItem(
      businessType: json['businessType'] as int? ?? 1,
      msgNums: json['msgNums'] as int? ?? 0,
    );

Map<String, dynamic> _$MessageCenterUnreadItemToJson(
        MessageCenterUnreadItem instance) =>
    <String, dynamic>{
      'businessType': instance.businessType,
      'msgNums': instance.msgNums,
    };

WorkOrderTodoCount _$WorkOrderTodoCountFromJson(Map<String, dynamic> json) =>
    WorkOrderTodoCount(
      paigongCount: json['paigongCount'] as String? ?? '0',
      fuwuCount: json['fuwuCount'] as String? ?? '0',
      pingjiaCount: json['pingjiaCount'] as String? ?? '0',
    );

Map<String, dynamic> _$WorkOrderTodoCountToJson(WorkOrderTodoCount instance) =>
    <String, dynamic>{
      'paigongCount': instance.paigongCount,
      'fuwuCount': instance.fuwuCount,
      'pingjiaCount': instance.pingjiaCount,
    };
