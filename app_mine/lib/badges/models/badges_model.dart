import 'package:device_utils/compare/compare.dart';
import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

/// 接口请求小红点信息整合(设备升级、用户中心消息、工单、问题反馈)

part 'badges_model.g.dart';

@JsonSerializable(explicitToJson: true, anyMap: true)
class BadgesModel {
  BadgesModel({
    required this.deviceNeedUpgrade,
    this.msgcenterUnreadNum,
    this.workOrderTodoCount,
  });

  /// [deviceNeedUpgrade] 是否有设备需要升级，有:true, 无:false
  @JsonKey(defaultValue: false)
  bool deviceNeedUpgrade = false;

  /// 消息中心未读消息
  List<MessageCenterUnreadItem?>? msgcenterUnreadNum;

  /// 工单未读消息数(我的服务)
  WorkOrderTodoCount? workOrderTodoCount;

  factory BadgesModel.fromJson(Map<dynamic, dynamic> json) =>
      _$BadgesModelFromJson(json);

  Map<String, dynamic> toJson() => _$BadgesModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BadgesModel &&
          runtimeType == other.runtimeType &&
          deviceNeedUpgrade == other.deviceNeedUpgrade &&
          listEquals(msgcenterUnreadNum, other.msgcenterUnreadNum) &&
          workOrderTodoCount == other.workOrderTodoCount;

  @override
  int get hashCode =>
      deviceNeedUpgrade.hashCode ^
      listHashCode(msgcenterUnreadNum ?? <MessageCenterUnreadItem?>[]) ^
      workOrderTodoCount.hashCode;

  @override
  String toString() {
    return 'BadgesModel{deviceNeedUpgrade: $deviceNeedUpgrade, msgcenterUnreadNum: $msgcenterUnreadNum, workOrderTodoCount: $workOrderTodoCount}';
  }
}

/// 消息中心未读消息
@JsonSerializable(explicitToJson: true)
class MessageCenterUnreadItem {
  MessageCenterUnreadItem({required this.businessType, required this.msgNums});

  /// [businessType] 业务类型
  @JsonKey(defaultValue: 1)
  int businessType;

  /// [msgNums] 未读消息数
  @JsonKey(defaultValue: 0)
  int msgNums;

  factory MessageCenterUnreadItem.fromJson(Map<String, dynamic> json) =>
      _$MessageCenterUnreadItemFromJson(json);

  Map<String, dynamic> toJson() => _$MessageCenterUnreadItemToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MessageCenterUnreadItem &&
          runtimeType == other.runtimeType &&
          businessType == other.businessType &&
          msgNums == other.msgNums;

  @override
  int get hashCode => businessType.hashCode ^ msgNums.hashCode;

  @override
  String toString() {
    return 'MessageCenterUnreadItem{businessType: $businessType, msgNums: $msgNums}';
  }
}

/// 工单未读消息数(我的服务)
@JsonSerializable(explicitToJson: true)
class WorkOrderTodoCount {
  WorkOrderTodoCount({
    required this.paigongCount,
    required this.fuwuCount,
    required this.pingjiaCount,
  });

  /// 派工工单
  @JsonKey(defaultValue: '0')
  String paigongCount;

  /// 服务工单
  @JsonKey(defaultValue: '0')
  String fuwuCount;

  /// 待评价工单
  @JsonKey(defaultValue: '0')
  String pingjiaCount;

  factory WorkOrderTodoCount.fromJson(Map<String, dynamic> json) =>
      _$WorkOrderTodoCountFromJson(json);

  Map<String, dynamic> toJson() => _$WorkOrderTodoCountToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is WorkOrderTodoCount &&
          runtimeType == other.runtimeType &&
          paigongCount == other.paigongCount &&
          fuwuCount == other.fuwuCount &&
          pingjiaCount == other.pingjiaCount;

  @override
  int get hashCode =>
      paigongCount.hashCode ^ fuwuCount.hashCode ^ pingjiaCount.hashCode;

  @override
  String toString() {
    return 'WorkOrderTodoCount{paigongCount: $paigongCount, fuwuCount: $fuwuCount, pingjiaCount: $pingjiaCount}';
  }
}
