import 'package:app_mine/badges/models/badges_model.dart';
import 'package:app_mine/badges/store/badges_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> badgesReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateBadgesAction>(_updateBadgesReducer).call,
];

/// 更新我的(设置、消息、我的建议、我的服务)小红点reducer
AppMineState _updateBadgesReducer(
    AppMineState state, UpdateBadgesAction action) {
  /// 设置小红点
  state.badgeState.settingBadge = action.badgesModel.deviceNeedUpgrade;

  /// 消息小红点
  final MessageCenterUnreadItem? msgUnReadItem = action
      .badgesModel.msgcenterUnreadNum
      ?.firstWhere((MessageCenterUnreadItem? msg) =>
          msg?.msgNums != null && msg!.msgNums > 0, orElse: () => null);
  if (msgUnReadItem != null) {
    state.badgeState.messageBadge = true;
  } else {
    state.badgeState.messageBadge = false;
  }
  
  return state;
}
