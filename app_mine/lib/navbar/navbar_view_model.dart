import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

import '../utils/constant.dart';

/// 导航栏 viewModel
class NavBarViewModel {
  NavBarViewModel({
    required this.isLogin,
    required this.settingBadge,
    required this.messageBadge,
    this.nickname = '',
    this.nicknameOpacity = 0.0,
  });

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [nickname] 昵称
  final String nickname;

  ///  [settingBadge] 设置小红点
  bool settingBadge;

  /// [messageBadge] 消息小红点
  bool messageBadge;

  /// [nicknameOpacity] 昵称透明度
  double nicknameOpacity;

  /// 组装viewModel的数据
  static NavBarViewModel fromStore(Store<AppMineState> store) {
    return NavBarViewModel(
      isLogin: store.state.isLogin,
      nickname: store.state.userInfoState.nickName,
      settingBadge: store.state.badgeState.settingBadge,
      messageBadge: store.state.badgeState.messageBadge,
      nicknameOpacity: store.state.navBarState.scrollOffset <=
              Constant.minScrollOffset
          ? 0
          : (store.state.navBarState.scrollOffset - Constant.minScrollOffset) /
              Constant.scrollOffsetDelta,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NavBarViewModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          nickname == other.nickname &&
          settingBadge == other.settingBadge &&
          messageBadge == other.messageBadge &&
          nicknameOpacity == other.nicknameOpacity;

  @override
  int get hashCode =>
      isLogin.hashCode ^
      nickname.hashCode ^
      settingBadge.hashCode ^
      messageBadge.hashCode ^
      nicknameOpacity.hashCode;

  @override
  String toString() {
    return 'NavBarViewModel{isLogin: $isLogin, nickname: $nickname, settingBadge: $settingBadge, messageBadge: $messageBadge, nicknameOpacity: $nicknameOpacity}';
  }
}
