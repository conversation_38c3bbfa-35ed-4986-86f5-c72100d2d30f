import 'package:redux/redux.dart';

import '../../store/mine_state.dart';
import '../../utils/constant.dart';
import 'navbar_action.dart';

final List<Reducer<AppMineState>> navbarReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateScrollOffsetAction>(
          _updateScrollOffsetReducer)
      .call,
];

/// 更新顶部上滑距离，最大64
AppMineState _updateScrollOffsetReducer(
    AppMineState state, UpdateScrollOffsetAction action) {
  state.navBarState.scrollOffset =
      action.scrollOffset > Constant.maxScrollOffset
          ? Constant.maxScrollOffset
          : action.scrollOffset;
  return state;
}
