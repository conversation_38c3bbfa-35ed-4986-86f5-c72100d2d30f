import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/my_badge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show goToPage, gioTrack;

/// 顶部导航右边的操作按钮 设置、消息
class RightActionsWidget extends StatelessWidget {
  const RightActionsWidget(
      {super.key, required this.settingBadge, required this.messageBadge});
  final bool settingBadge;
  final bool messageBadge;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        /// 设置
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            gioTrack(Constant.settingsClickGio);
            goToPage(Constant.settingsUrl);
          },
          child: settingBadge
              ? const MyBadge(child: Settings())
              : const Settings(),
        ),
    
        const SizedBox(width: 20),
    
        /// 消息
        GestureDetector(
          onTap: () {
            gioTrack(Constant.messageCenterClickGio);
            goToPage(Constant.messageCenterUrl);
          },
          child: messageBadge
              ? const MyBadge(child: MessageCenter())
              : const MessageCenter(),
        ),

        const SizedBox(width: 4),
      ],
    );
  }
}

class MessageCenter extends StatelessWidget {
  const MessageCenter({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset('assets/images/message.webp',
        width: 24,
        height: 24,
        fit: BoxFit.cover,
        package: Constant.packageName);
  }
}

class Settings extends StatelessWidget {
  const Settings({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset('assets/images/setting.webp',
        width: 24,
        height: 24,
        fit: BoxFit.cover,
        package: Constant.packageName);
  }
}
