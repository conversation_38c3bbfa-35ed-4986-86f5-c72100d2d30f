import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import '../store/mine_state.dart';
import '../user_info/widgets/nickname.dart';
import 'navbar_view_model.dart';
import 'widgets/right_actions_widget.dart';

class NavBarWidget extends StatelessWidget {
  const NavBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    /// 状态栏高度
    final double _paddingTop = MediaQuery.of(context).padding.top;
    return AnnotatedRegion<SystemUiOverlayStyle>(
         value: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Padding(
            padding: EdgeInsets.only(top: _paddingTop),
            child: StoreConnector<AppMineState, NavBarViewModel>(
                distinct: true,
                converter: (Store<AppMineState> store) =>
                    NavBarViewModel.fromStore(store),
                builder: (BuildContext context, NavBarViewModel vm) {
                  return Container(
                      height: 44,
                      color: Colors.transparent,
                      padding: const EdgeInsets.only(left: 18, right: 16),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            // 昵称
                            Opacity(
                              opacity: vm.nicknameOpacity,
                              child: Nickname(
                                  isLogin: vm.isLogin, nickname: vm.nickname),
                            ),

                            // 设置、消息
                            RightActionsWidget(
                                settingBadge: vm.settingBadge,
                                messageBadge: vm.messageBadge),
                          ]));
                })));
  }
}
