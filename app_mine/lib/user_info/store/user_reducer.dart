import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/user_info/store/user_action.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> userInfoReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateUserInfoAction>(_updateUserInfoReducer).call,
  TypedReducer<AppMineState, UpdateAdditionalUserInfoAction>(
          _updateAdditionalUserInfoReducer)
      .call,
  TypedReducer<AppMineState, UpdateUserPrizeAction>(_updateUserPrizeReducer)
      .call,
];

/// 更新用户基本信息reducer
AppMineState _updateUserInfoReducer(
    AppMineState state, UpdateUserInfoAction action) {
  final String nickName =
      _getDisplayName(action.userInfo.nickname, action.userInfo.mobile);
  state.userInfoState.avatarUrl = action.userInfo.avatarUrl;
  state.userInfoState.nickName = nickName;
  state.userInfoState.mobile = action.userInfo.mobile;
  return state;
}

/// 更新用户附加信息(会员等级)reducer
AppMineState _updateAdditionalUserInfoReducer(
    AppMineState state, UpdateAdditionalUserInfoAction action) {
  state.userInfoState.additionalUserInfo = action.additionalUserInfo;
  return state;
}

/// 更新用户奖品reducer
AppMineState _updateUserPrizeReducer(
    AppMineState state, UpdateUserPrizeAction action) {
  state.userInfoState.priceVal = action.priceVal;
  return state;
}

/// 获取用户名称显示文案
String _getDisplayName(String nickName, String mobile) {
  if (nickName.isNotEmpty) {
    return nickName;
  } else if (mobile.isNotEmpty) {
    return mobile.replaceFirst(RegExp(r'\d{4}'), '****', 3);
  } else {
    return '';
  }
}
