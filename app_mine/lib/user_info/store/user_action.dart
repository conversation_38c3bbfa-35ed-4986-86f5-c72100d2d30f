import 'package:app_mine/user_info/models/additional_user_info.dart';
import 'package:user/modle/user_info_model.dart';

abstract class UpdateUserAction {}
/// 更新用户基本信息action
class UpdateUserInfoAction implements UpdateUserAction {
  UserInfo userInfo;
  UpdateUserInfoAction(this.userInfo);
}

/// 更新用户附加信息action(会员等级)
class UpdateAdditionalUserInfoAction implements UpdateUserAction {
  UpdateAdditionalUserInfoAction(this.additionalUserInfo);

  AdditionalUserInfo additionalUserInfo;
}

/// 更新用户奖品action
class UpdateUserPrizeAction implements UpdateUserAction {
  UpdateUserPrizeAction(this.priceVal);

  /// 用户奖品
  String priceVal;
}
