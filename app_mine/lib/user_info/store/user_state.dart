import 'package:app_mine/user_info/models/additional_user_info.dart';
import 'package:app_mine/utils/constant.dart';

/// 用户信息State
class UserInfoState {
  /// [nickname] 昵称
  String nickName = '';

  /// [avatarUrl] 头像
  String avatarUrl = '';

  /// [mobile] 电话
  String mobile = '';

  /// [userInfo] 用户附加信息 (会员等级)
  AdditionalUserInfo additionalUserInfo = AdditionalUserInfo(
      vipFlag: false,
      vipLevelName: '',
      );
  
  /// [priceVal] 用户奖品
  String priceVal = Constant.doubleHyphenPlaceholder;

  void clear() {
    nickName = '';
    avatarUrl = '';
    mobile = '';
    additionalUserInfo.clear();
    priceVal = Constant.doubleHyphenPlaceholder;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserInfoState &&
          runtimeType == other.runtimeType &&
          nickName == other.nickName &&
          avatarUrl == other.avatarUrl &&
          mobile == other.mobile &&
          additionalUserInfo == other.additionalUserInfo &&
          priceVal == other.priceVal;

  @override
  int get hashCode =>
      nickName.hashCode ^
      avatarUrl.hashCode ^
      mobile.hashCode ^
      additionalUserInfo.hashCode ^
      priceVal.hashCode;

  @override
  String toString() {
    return 'UserInfoState{nickName: $nickName, avatarUrl: $avatarUrl, mobile: $mobile, additionalUserInfo: $additionalUserInfo, priceVal: $priceVal}';
  }
}
