import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/user_info/models/additional_user_info.dart';
import 'package:redux/redux.dart';

import '../utils/constant.dart';

/// 用户信息+登录
class UserViewModel {
  UserViewModel(
      {required this.isLogin,
      required this.userInfoViewModel,
      this.opacity = 1.0});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [userInfoViewModel] 用户基本信息及(会员等级)model
  final UserInfoViewModel userInfoViewModel;

  /// [opacity] 透明度
  final double opacity;

  static UserViewModel fromStore(Store<AppMineState> store) {
    return UserViewModel(
        isLogin: store.state.isLogin,
        userInfoViewModel: UserInfoViewModel.fromStore(
          store,
        ),
        opacity: 1 -
            (store.state.navBarState.scrollOffset <= Constant.minScrollOffset
                ? 0
                : ((store.state.navBarState.scrollOffset -
                        Constant.minScrollOffset) /
                    Constant.scrollOffsetDelta)));
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserViewModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          userInfoViewModel == other.userInfoViewModel &&
          opacity == other.opacity;

  @override
  int get hashCode =>
      isLogin.hashCode ^ userInfoViewModel.hashCode ^ opacity.hashCode;

  @override
  String toString() {
    return 'UserViewModel{isLogin: $isLogin, userInfoViewModel: $userInfoViewModel, opacity: $opacity}';
  }
}

/// 用户信息model
class UserInfoViewModel {
  UserInfoViewModel(
      {required this.vipFlag,
      required this.deviceUser,
      required this.nickName,
      required this.avatarUrl,
      required this.vipLevelName});

  /// [vipFlag] 是否vip，true是，false不是
  bool vipFlag = false;

  /// [deviceUser] 是否网器用户
  bool deviceUser = false;

  /// [nickname] 昵称
  String nickName = '';

  /// [avatarUrl] 头像
  String avatarUrl = '';

  /// [vipLevelName] 会员等级名称，如"V1"
  String vipLevelName = '';

  static UserInfoViewModel fromStore(Store<AppMineState> store) {
    final AdditionalUserInfo additionalUserInfo =
        store.state.userInfoState.additionalUserInfo;
    return UserInfoViewModel(
      vipFlag: additionalUserInfo.vipFlag,
      vipLevelName: additionalUserInfo.vipLevelName,
      deviceUser: store.state.currentFamilyState.deviceUser,
      nickName: store.state.userInfoState.nickName,
      avatarUrl: store.state.userInfoState.avatarUrl,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserInfoViewModel &&
          runtimeType == other.runtimeType &&
          vipFlag == other.vipFlag &&
          deviceUser == other.deviceUser &&
          nickName == other.nickName &&
          avatarUrl == other.avatarUrl &&
          vipLevelName == other.vipLevelName;

  @override
  int get hashCode =>
      vipFlag.hashCode ^
      deviceUser.hashCode ^
      nickName.hashCode ^
      avatarUrl.hashCode ^
      vipLevelName.hashCode;

  @override
  String toString() {
    return 'UserInfoViewModel{vipFlag: $vipFlag, deviceUser: $deviceUser, nickName: $nickName, avatarUrl: $avatarUrl, vipLevelName: $vipLevelName}';
  }
}
