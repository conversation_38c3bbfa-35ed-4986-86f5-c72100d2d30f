import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

import '../../../widget_common/bold_text_style.dart';

class Nickname extends StatelessWidget {
  const Nickname(
      {super.key,
      required this.isLogin,
      required this.nickname,
      this.canClick = false});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [nickname] 昵称
  final String nickname;

  /// [canClick] 能否点击
  final bool canClick;

  @override
  Widget build(BuildContext context) {
    // 已登录，没有获得昵称，展示空
    if (isLogin && nickname.isEmpty) {
      return const SizedBox.shrink();
    }
    if (canClick) {
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          gioTrack(Constant.personalInfoClickGio);
          goToPage(isLogin ? Constant.personalInfoUrl : Constant.loginUrl);
        },
        child: Row(children: <Widget>[
          _TextWidget(
            isLogin: isLogin,
            nickname: nickname,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 6.0),
            child: Image.asset(
              'assets/images/arrow.webp',
              package: Constant.packageName,
              width: 12,
              height: 12,
            ),
          )
        ]),
      );
    }

    return _TextWidget(
      isLogin: isLogin,
      nickname: nickname,
    );
  }
}

class _TextWidget extends StatelessWidget {
  const _TextWidget({super.key, required this.isLogin, required this.nickname});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [nickname] 昵称
  final String nickname;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      /// iPhoneX/8 等375的手机上未登录显示了..., 这里将最大宽度限制为180
      constraints: BoxConstraints(maxWidth: isLogin ? 160 : 180),
      child: Text(isLogin ? nickname : '登录开启智慧生活',
          maxLines: 1,
          style: TextStyle(
              fontSize: 20,
              color: AppSemanticColors.item.primary,
              fontFamilyFallback: fontFamilyFallback(),
              fontWeight: FontWeight.w500,
              overflow: TextOverflow.ellipsis)),
    );
  }
}
