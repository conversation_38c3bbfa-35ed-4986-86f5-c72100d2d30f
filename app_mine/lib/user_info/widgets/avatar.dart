import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/avatar_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show goToPage, gioTrack;

/// 用户头像
class Avatar extends StatelessWidget {
  const Avatar(
      {super.key,
      required this.isLogin,
      required this.vipFlag,
      required this.avatarUrl,
      required this.deviceUser});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [vipFlag] 是否vip，true是，false不是
  final bool vipFlag;

  /// [avatarUrl] 头像
  final String avatarUrl;

  /// [deviceUser] 是否网器用户
  final bool deviceUser;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          gioTrack(Constant.personalInfoClickGio);
          goToPage(isLogin ? Constant.personalInfoUrl : Constant.loginUrl);
        },
        child: Stack(
          alignment: Alignment.center,
          clipBehavior: Clip.none,
          children: <Widget>[
            /// 头像主图
            AvatarImage(
              avatarUrl: avatarUrl,
              width: 64,
            ),

            /// 网器用户的头像挂件
            if (deviceUser) const Pendant(),

            /// vip的皇冠
            if (vipFlag) const VipIcon(),
          ],
        ));
  }
}

/// vip的皇冠
class VipIcon extends StatelessWidget {
  const VipIcon({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: -3,
      child: Image.asset(
        'assets/images/vip_plus.webp',
        width: 16,
        height: 16,
        package: Constant.packageName,
        fit: BoxFit.cover,
      ),
    );
  }
}

/// 网器用户的头像挂件
class Pendant extends StatelessWidget {
  const Pendant({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: 64,
        height: 64,
        child: Image.asset(
          'assets/images/device_user_pendant.png',
          package: Constant.packageName,
          fit: BoxFit.fill,
        ));
  }
}
