import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:gradient_borders/gradient_borders.dart';

import '../../utils/constant.dart';
import '../../utils/util.dart';
import 'nickname.dart';

class UserName extends StatelessWidget {
  const UserName(
      {super.key,
      required this.isLogin,
      required this.nickName,
      required this.vipLevelName});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [nickname] 昵称
  final String nickName;

  /// [vipLevelName] 会员等级名称，如"V1"
  final String vipLevelName;

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisAlignment:
            isLogin ? MainAxisAlignment.spaceBetween : MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Nickname(isLogin: isLogin, nickname: nickName, canClick: true),
          Visibility(
            visible: isLogin,
            child: Row(
              children: <Widget>[
                Label(
                    name: '智家会员$vipLevelName',
                    jumpYsUrl: Constant.vipLevelYsUrl,
                    jumpUrl: Constant.vipLevelUrl,
                    gioKey: Constant.vipLevelClickGio),
                const SizedBox(width: 12),
                const Label(
                    name: '会员俱乐部',
                    jumpYsUrl: Constant.vipPubYsUrl,
                    jumpUrl: Constant.vipPubUrl,
                    gioKey: Constant.vipPubClickGio),
              ],
            ),
          )
        ]);
  }
}

class Label extends StatelessWidget {
  const Label(
      {super.key,
      required this.name,
      required this.jumpYsUrl,
      required this.jumpUrl,
      required this.gioKey});

  final String name;
  final String jumpYsUrl;
  final String jumpUrl;
  final String gioKey;

  @override
  Widget build(BuildContext context) {
    return PressableOverlayWithTapWidget(
      overlayClick: () {
        gioTrack(gioKey);
        goToPageByEnv(jumpYsUrl, jumpUrl);
      },
      borderRadius: BorderRadius.circular(24),
      child: Container(
          alignment: Alignment.center,
          height: 27,
          decoration: BoxDecoration(
              color: AppSemanticColors.container.notice,
              border: const GradientBoxBorder(
                gradient: LinearGradient(
                  // -8度
                  begin: Alignment(-0.1, -0.8),
                  end: Alignment(0.1, 0.8),
                  colors: <Color>[
                  Color.fromRGBO(255, 255, 255, 1),
                  Color.fromRGBO(255, 255, 255, 0),
                  Color.fromRGBO(255, 255, 255, 0),
                  Color.fromRGBO(255, 255, 255, 1)
                ], stops: <double>[
                  0,
                  .4,
                  .6,
                  1
                ]),
              ),
              borderRadius: BorderRadius.circular(24)),
          padding: const EdgeInsets.symmetric(horizontal: 11),
          child: Text(name,
              style: const TextStyle(
                  fontSize: 11, height: 15 / 11, fontWeight: FontWeight.w400))),
    );
  }
}
