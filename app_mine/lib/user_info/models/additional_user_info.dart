import 'package:json_annotation/json_annotation.dart';

part 'additional_user_info.g.dart';

/// 接口请求的用户附加信息(是否vip, 会员等级名称)
@JsonSerializable(explicitToJson: true, anyMap: true)
class AdditionalUserInfo {
  AdditionalUserInfo(
      {required this.vipFlag,
      required this.vipLevelName});

  /// [vipFlag] 是否vip，true是，false不是
  @JsonKey(defaultValue: false)
  bool vipFlag;

  /// [vipLevelName] 会员等级名称，如"V1"
  @JsonKey(defaultValue: '')
  String vipLevelName;

  factory AdditionalUserInfo.fromJson(Map<dynamic, dynamic> json) =>
      _$AdditionalUserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$AdditionalUserInfoToJson(this);

  void clear() {
    vipFlag = false;
    vipLevelName = '';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AdditionalUserInfo &&
          runtimeType == other.runtimeType &&
          vipFlag == other.vipFlag &&
          vipLevelName == other.vipLevelName;

  @override
  int get hashCode =>
      vipFlag.hashCode ^
      vipLevelName.hashCode;

  @override
  String toString() {
    return 'AdditionalUserInfo{vipFlag: $vipFlag, vipLevelName: $vipLevelName}';
  }
}
