import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/user_info/user_view_model.dart';
import 'package:app_mine/user_info/widgets/avatar.dart';
import 'package:app_mine/user_info/widgets/user_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

/// 用户信息widget
class UserInfoWidget extends StatelessWidget {
  const UserInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, UserViewModel>(
      distinct: true,
      converter: (Store<AppMineState> store) => UserViewModel.fromStore(store),
      builder: (BuildContext context, UserViewModel vm) {
        return Opacity(
          opacity: vm.opacity,
          child: Padding(
            padding: const EdgeInsets.only(top: 4, bottom: 34, left: 28),
            child: <PERSON><PERSON><PERSON><PERSON>(
              height: 64,
              child: Row(children: <Widget>[
                /// 头像
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Avatar(
                      isLogin: vm.isLogin,
                      vipFlag: vm.userInfoViewModel.vipFlag,
                      avatarUrl: vm.userInfoViewModel.avatarUrl,
                      deviceUser: vm.userInfoViewModel.deviceUser),
                ),
                UserName(
                    isLogin: vm.isLogin,
                    nickName: vm.userInfoViewModel.nickName,
                    vipLevelName: vm.userInfoViewModel.vipLevelName)
              ]),
            ),
          ),
        );
      },
    );
  }
}
