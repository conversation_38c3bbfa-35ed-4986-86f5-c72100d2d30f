import 'package:app_mine/list_menu/menu_view_model.dart';
import 'package:app_mine/list_menu/store/menu_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/utils/util.dart';
import 'package:app_mine/widget_common/arrow_right.dart';
import 'package:app_mine/widget_common/my_badge.dart';
import 'package:app_mine/widget_common/new_badge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show AppSemanticColors, PressableOverlayWithTapWidget, gioTrack, goToPage;
import 'package:visibility_detector/visibility_detector.dart';

class MenuItem extends StatelessWidget {
  const MenuItem({super.key, required this.menuItem});

  /// [menuItem] 菜单项
  final MenuViewModel menuItem;

  @override
  Widget build(BuildContext context) {
    return PressableOverlayWithTapWidget(
      overlayClick: () {
        gioTrack(menuItem.gioClick);

        /// 关于我们需要记录点击事件，写入提示更新的app版本，去掉NEW标
        if (menuItem.title == Constant.aboutUsTitle) {
          /// 判断当前是否有NEW标，有的话再写入缓存
          if (menuItem.badge) {
            appMineStore.dispatch(WriteAppNewVersionAction());
          }
        }
        menuItem.jumpYsUrl.isNotEmpty
            ? goToPageByEnv(menuItem.jumpYsUrl, menuItem.jumpUrl)
            : goToPage(menuItem.jumpUrl);
      },
      child: SizedBox(
        height: 60,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                TitleWithNewBadge(menuItem: menuItem),
                Tail(menuItem: menuItem)
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 带New标的标题
class TitleWithNewBadge extends StatelessWidget {
  const TitleWithNewBadge({super.key, required this.menuItem});

  /// [menuItem] 菜单项
  final MenuViewModel menuItem;

  /// 曝光埋点
  void onVisibilityChanged(VisibilityInfo info) {
    if (info.visibleFraction > 0) {
      gioTrack(Constant.aboutUsExposureGio);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (menuItem.title == Constant.aboutUsTitle) {
      return VisibilityDetector(
        key: const ValueKey<String>('mine_about_use'),
        onVisibilityChanged: onVisibilityChanged,
        child: Row(
          children: <Widget>[
            Title(title: menuItem.title, image: menuItem.image),
            Visibility(
                visible: menuItem.badge,
                child: const Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: NewBadge(),
                ))
          ],
        ),
      );
    }
    return Title(title: menuItem.title, image: menuItem.image);
  }
}

/// 尾部，包含subTitle、小红点、右箭头
class Tail extends StatelessWidget {
  const Tail({
    super.key,
    required this.menuItem,
  });

  final MenuViewModel menuItem;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        /// 副标题
        Visibility(
          visible: menuItem.subTitle.isNotEmpty,
          child: SubTitle(
            subTitle: menuItem.subTitle,
          ),
        ),

        /// 小红点, 关于我们是NEW标
        Visibility(
          visible: menuItem.badge && menuItem.title != Constant.aboutUsTitle,
          child: const MyBadge(padding: EdgeInsets.all(3)),
        ),

        const SizedBox(width: 4),

        /// 右箭头
        const ArrowRight(),
      ],
    );
  }
}

/// 标题，如我的服务，我的交易
class Title extends StatelessWidget {
  const Title({super.key, required this.title, required this.image});

  /// [title] 标题
  final String title;
  final String image;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Image.asset(
          image,
          width: 28,
          height: 28,
          package: Constant.packageName,
          fit: BoxFit.cover,
        ),
        const SizedBox(
          width: 12,
        ),
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}

/// 副标题，我的交易后面的简短说明
class SubTitle extends StatelessWidget {
  const SubTitle({super.key, required this.subTitle});

  final String subTitle;

  @override
  Widget build(BuildContext context) {
    return Text(subTitle,
        style: TextStyle(
          fontSize: 14,
          color: AppSemanticColors.item.secWeaken,
          fontWeight: FontWeight.w400,
        ));
  }
}
