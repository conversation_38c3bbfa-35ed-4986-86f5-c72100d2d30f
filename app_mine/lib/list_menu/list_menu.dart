import 'package:app_mine/list_menu/widgets/menu_item.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import 'menu_view_model.dart';

class ListMenuWidget extends StatelessWidget {
  const ListMenuWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, MenuBlocksViewModel>(
      distinct: true,
      converter: (Store<AppMineState> store) =>
          MenuBlocksViewModel.fromStore(store),
      builder: (BuildContext context, MenuBlocksViewModel viewModel) {
        return Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, bottom: 5),
          child: Column(
            children: viewModel.menuBlocks
                .map((ListMenuViewModel block) =>
                    _MenuBlock(menuList: block.menuList))
                .toList(),
          ),
        );
      },
    );
  }
}

class _MenuBlock extends StatelessWidget {
  const _MenuBlock({super.key, required this.menuList});

  final List<MenuViewModel> menuList;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(22),
        child: Container(
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(22)),
          child: Column(
            children: menuList
                .map((MenuViewModel menu) => MenuItem(menuItem: menu))
                .toList(),
          ),
        ),
      ),
    );
  }
}
