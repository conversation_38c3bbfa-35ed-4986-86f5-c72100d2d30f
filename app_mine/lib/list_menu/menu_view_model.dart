import 'package:plugin_device/utils/util_diff.dart';
import 'package:redux/redux.dart';

import '../store/mine_state.dart';
import '../utils/constant.dart';
import 'store/menu_state.dart';

class MenuBlocksViewModel {
  List<ListMenuViewModel> menuBlocks = <ListMenuViewModel>[];

  MenuBlocksViewModel(this.menuBlocks);

  static MenuBlocksViewModel fromStore(Store<AppMineState> store) {
    final MenuState menuState = store.state.menuState;
    final String familyId = store.state.currentFamilyState.familyId;
    final ListMenuViewModel partOneItems = ListMenuViewModel(<MenuViewModel>[
      MenuViewModel(
          image: 'assets/images/icon_gift.webp',
          title: '活动中心',
          subTitle: '',
          badge: false,
          jumpYsUrl: Constant.activityCenterYsUrl,
          jumpUrl: Constant.activityCenterUrl,
          gioClick: Constant.activityCenterGioKey),
      MenuViewModel(
          image: 'assets/images/icon_bag.webp',
          title: '我的权益',
          subTitle: '积分/卡券/红包',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.myRightsUrl,
          gioClick: Constant.myRightsGioKey),
      MenuViewModel(
          image: 'assets/images/icon_rich.webp',
          title: '我的交易',
          subTitle: '订单/购物车/收藏等',
          badge: false,
          jumpYsUrl: Constant.tradeYsUrl,
          jumpUrl: Constant.tradeUrl,
          gioClick: Constant.tradeGio)
    ]);

    final ListMenuViewModel partTwoItems = ListMenuViewModel(<MenuViewModel>[
      MenuViewModel(
          image: 'assets/images/icon_heart.webp',
          title: '我的服务',
          subTitle: '',
          badge: false,
          jumpYsUrl: Constant.serverYsUrl,
          jumpUrl: Constant.serverUrl,
          gioClick: Constant.serverGio),
      MenuViewModel(
          image: 'assets/images/icon_scene.webp',
          title: '我的场景',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl:
              'mpaas://scene_list?familyId=$familyId&needAuthLogin=1#/myscenelist',
          gioClick: Constant.sceneGio),
      MenuViewModel(
          image: 'assets/images/icon_consumable.webp',
          title: '设备耗材',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.consumablesUrl,
          gioClick: Constant.consumablesGio),
      MenuViewModel(
          image: 'assets/images/icon_energy_saving.webp',
          title: '设备节能',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.energySavingUrl,
          gioClick: Constant.energySavingGio),
      MenuViewModel(
          image: 'assets/images/icon_share.webp',
          title: '设备共享',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.shareUrl,
          gioClick: Constant.shareGio),
      MenuViewModel(
          image: 'assets/images/icon_connect.webp',
          title: '第三方平台设备',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.otherDeviceUrl,
          gioClick: Constant.otherDeviceGio),
    ]);

    final ListMenuViewModel partThreeItems = ListMenuViewModel(<MenuViewModel>[
      MenuViewModel(
          image: 'assets/images/icon_service.webp',
          title: '在线客服',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.onlineServerUrl,
          gioClick: Constant.onlineServerGio),
      MenuViewModel(
          image: 'assets/images/icon_msg.webp',
          title: '我的建议',
          subTitle: '',
          badge: false,
          jumpYsUrl: '',
          jumpUrl: Constant.suggestUrl,
          gioClick: Constant.suggestGio),
      MenuViewModel(
          image: 'assets/images/icon_about.webp',
          title: Constant.aboutUsTitle,
          subTitle: menuState.appVersion,
          badge: menuState.versionBadge,
          jumpYsUrl: '',
          jumpUrl: Constant.aboutUsUrl,
          gioClick: Constant.aboutUsGio)
    ]);

    final List<ListMenuViewModel> menuBlocks = <ListMenuViewModel>[
      partOneItems,
      partTwoItems,
      partThreeItems
    ];
    return MenuBlocksViewModel(menuBlocks);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MenuBlocksViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(menuBlocks, other.menuBlocks);

  @override
  int get hashCode => listHashCode(menuBlocks);

  @override
  String toString() {
    return 'MenuBlocksViewModel{menuBlocks: $menuBlocks}';
  }
}

class ListMenuViewModel {
  List<MenuViewModel> menuList = <MenuViewModel>[];

  ListMenuViewModel(this.menuList);

  @override
  String toString() {
    return 'ListMenuViewModel{menuList: $menuList}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ListMenuViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(menuList, other.menuList);

  @override
  int get hashCode => listHashCode(menuList);
}

class MenuViewModel {
  String image;
  bool badge = false;
  String jumpYsUrl = '';
  String jumpUrl = '';
  String title = '';
  String subTitle = '';
  String gioClick = '';

  MenuViewModel(
      {required this.image,
      required this.title,
      required this.subTitle,
      required this.badge,
      required this.jumpYsUrl,
      required this.jumpUrl,
      required this.gioClick});

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MenuViewModel &&
          runtimeType == other.runtimeType &&
          image == other.image &&
          badge == other.badge &&
          jumpYsUrl == other.jumpYsUrl &&
          jumpUrl == other.jumpUrl &&
          title == other.title &&
          subTitle == other.subTitle &&
          gioClick == other.gioClick;

  @override
  int get hashCode =>
      image.hashCode ^
      badge.hashCode ^
      jumpYsUrl.hashCode ^
      jumpUrl.hashCode ^
      title.hashCode ^
      subTitle.hashCode ^
      gioClick.hashCode;

  @override
  String toString() {
    return 'MenuViewModel{badge: $badge, jumpYsUrl: $jumpYsUrl, jumpUrl: $jumpUrl, title: $title, subTitle: $subTitle, gioClick: $gioClick}';
  }
}
