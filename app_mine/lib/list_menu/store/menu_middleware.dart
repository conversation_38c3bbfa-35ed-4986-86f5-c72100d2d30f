import 'package:app_mine/list_menu/store/menu_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';

final List<Middleware<AppMineState>> menuMiddlewares =
    <Middleware<AppMineState>>[
  TypedMiddleware<AppMineState, WriteAppNewVersionAction>(
          writeAppNewVersionMiddleware)
      .call
];

/// 写入app提示更新的版本号middleware
void writeAppNewVersionMiddleware(
  Store<AppMineState> store,
  WriteAppNewVersionAction action,
  NextDispatcher next,
) {
  _writeAppNewVersionWhenJumpToAboutUs(store);
}

/// 写入app提示更新的版本号
Future<void> _writeAppNewVersionWhenJumpToAboutUs(
    Store<AppMineState> store) async {
  try {
    /// 先查询网络，无网的时候不会跳转到关于我们页面，所以不会写入appNewVersion
    final IsOnline isOnline =
        await Network.isOnline();
    if (isOnline.isOnline) {
      /// 如果关于我们NEW标展示，则记录已经点击曝光过，等下次didAppear的时候，查询该曝光的版本号，去掉NEW标
      if (store.state.menuState.versionBadge) {
        /// 查询缓存中的提示用户更新的appVersion
        final String appNewVersion =
            await Storage.getStringValue(Constant.appNewVersionStorageKey);
        if (appNewVersion.isNotEmpty) {
          await Storage.putStringValue(
              Constant.appVersionExposureKey, appNewVersion);
          DevLogger.info(
              tag: Constant.packageName,
              msg:
                  '_writeAppNewVersionWhenJumpToAboutUs appNewVersion: $appNewVersion');
        }
      }
    }
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName,
        msg: '_writeAppNewVersionWhenJumpToAboutUs err: $err');
  }
}
