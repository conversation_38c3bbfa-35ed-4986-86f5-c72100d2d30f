class MenuState {
  /// [appVersion] app版本号
  String appVersion = '';

  /// [versionBadge] 版本升级提示
  bool versionBadge = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MenuState &&
          runtimeType == other.runtimeType &&
          appVersion == other.appVersion &&
          versionBadge == other.versionBadge;

  @override
  int get hashCode => appVersion.hashCode ^ versionBadge.hashCode;

  @override
  String toString() {
    return 'MenuState{appVersion: $appVersion, versionBadge: $versionBadge}';
  }
}
