import 'package:app_mine/list_menu/store/menu_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> menuStateReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateAppVersionAction>(_updateAppVersionReducer)
      .call,
  TypedReducer<AppMineState, UpdateAppVersionBadgeAction>(
          _updateAppVersionBadgeReducer)
      .call,
];

/// 更新App版本
AppMineState _updateAppVersionReducer(
    AppMineState state, UpdateAppVersionAction action) {
  state.menuState.appVersion =
      action.appVersion.isNotEmpty ? 'V${action.appVersion}' : '';
  return state;
}

/// 更新App版本的提示
AppMineState _updateAppVersionBadgeReducer(
    AppMineState state, UpdateAppVersionBadgeAction action) {
  state.menuState.versionBadge = action.versionBadge;
  return state;
}
