// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rest_client.dart';

// **************************************************************************
// RetrofitGenerator
// **************************************************************************

// ignore_for_file: unnecessary_brace_in_string_interps,no_leading_underscores_for_local_identifiers

class _RestClient implements RestClient {
  _RestClient(
    this._dio, {
    this.baseUrl,
  });

  final Dio _dio;

  String? baseUrl;

  @override
  Future<BirthdayResponseModel> getBirthdayListDataApi(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BirthdayResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/aiAssistant/virtual/listUser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = BirthdayResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<UhomeResponseModel> delFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UhomeResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/aiAssistant/virtual/batchDeleteUser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = UhomeResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<UhomeResponseModel> addFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UhomeResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/aiAssistant/virtual/addUser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = UhomeResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<UhomeResponseModel> editFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UhomeResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/aiAssistant/virtual/updateUser',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = UhomeResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<AdditionalUserInfoResponseModel> getUserAdditionalDataApi(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<AdditionalUserInfoResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/minetab/user/vipInfo/query',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = AdditionalUserInfoResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<BadgesResponseModel> getBadgesApi() async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    const Map<String, dynamic>? _data = null;
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<BadgesResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/minetab/redpoint/info',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = BadgesResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<UhomeResponseModel> fiveStarPraise(
      Map<String, dynamic> requestMap) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{};
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UhomeResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/fiveStarPraise/save',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = UhomeResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<UhomeResponseModel> familyCardUpgradeSwitchOperate(
    Map<String, dynamic> requestMap,
    String appVersion,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'appVersion': appVersion};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio
        .fetch<Map<String, dynamic>>(_setStreamType<UhomeResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/switch/operate',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = UhomeResponseModel.fromJson(_result.data!);
    return value;
  }

  @override
  Future<SwitchQueryResponseModel> familyCardUpgradeSwitchQuery(
    Map<String, dynamic> requestMap,
    String appVersion,
  ) async {
    final _extra = <String, dynamic>{};
    final queryParameters = <String, dynamic>{};
    final _headers = <String, dynamic>{r'appVersion': appVersion};
    _headers.removeWhere((k, v) => v == null);
    final _data = <String, dynamic>{};
    _data.addAll(requestMap);
    final _result = await _dio.fetch<Map<String, dynamic>>(
        _setStreamType<SwitchQueryResponseModel>(Options(
      method: 'POST',
      headers: _headers,
      extra: _extra,
    )
            .compose(
              _dio.options,
              '/api-gw/zjBaseServer/switch/query',
              queryParameters: queryParameters,
              data: _data,
            )
            .copyWith(
                baseUrl: _combineBaseUrls(
              _dio.options.baseUrl,
              baseUrl,
            ))));
    final value = SwitchQueryResponseModel.fromJson(_result.data!);
    return value;
  }

  RequestOptions _setStreamType<T>(RequestOptions requestOptions) {
    if (T != dynamic &&
        !(requestOptions.responseType == ResponseType.bytes ||
            requestOptions.responseType == ResponseType.stream)) {
      if (T == String) {
        requestOptions.responseType = ResponseType.plain;
      } else {
        requestOptions.responseType = ResponseType.json;
      }
    }
    return requestOptions;
  }

  String _combineBaseUrls(
    String dioBaseUrl,
    String? baseUrl,
  ) {
    if (baseUrl == null || baseUrl.trim().isEmpty) {
      return dioBaseUrl;
    }

    final url = Uri.parse(baseUrl);

    if (url.isAbsolute) {
      return url.toString();
    }

    return Uri.parse(dioBaseUrl).resolveUri(url).toString();
  }
}
