import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:app_mine/badges/models/badges_response_model.dart';
import 'package:app_mine/birthday_info/birthday_model.dart';
import 'package:app_mine/current_family/switch_query_model.dart';
import 'package:app_mine/service/rest_client.dart';
import 'package:app_mine/user_info/models/additional_user_info_response_model.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';
import 'package:upservice/model/uhome_response_model.dart';

class Service {
  static final Service _instance = Service._internal();

  /// 私有的命名构造函数
  Service._internal();

  /// 工厂构造函数
  factory Service() => _instance;

  Future<RestClient> _getClient() async {
    return RestClient(
      UhomeDio().dio,
    );
  }

  /// 查询用户附加数据
  Future<AdditionalUserInfoResponseModel?> getAdditionalUserInfo(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();

      final AdditionalUserInfoResponseModel res =
          await client.getUserAdditionalDataApi(requestMap);
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'service_getAdditionalUserInfo res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: 'service_getAdditionalUserInfo err: $err');
      return null;
    }
  }

  Future<BirthdayResponseModel?> getBirthdayList(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();

      final BirthdayResponseModel res =
          await client.getBirthdayListDataApi(requestMap);
      DevLogger.info(
          tag: Constant.packageName, msg: 'service_getBirthdayList res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'service_getBirthdayList err: $err');
      return null;
    }
  }

  /// 添加家人生日
  Future<UhomeResponseModel?> addFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();

      final UhomeResponseModel res = await client.addFamilyBirthday(requestMap);
      DevLogger.info(
          tag: Constant.packageName, msg: 'addFamilyBirthday res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'addFamilyBirthday err: $err');
      return null;
    }
  }

  /// 删除家人生日
  Future<UhomeResponseModel?> delFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();

      final UhomeResponseModel res = await client.delFamilyBirthday(requestMap);
      DevLogger.info(
          tag: Constant.packageName, msg: 'delFamilyBirthday res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'delFamilyBirthday err: $err');
      return null;
    }
  }

  /// 编辑家人生日
  Future<UhomeResponseModel?> editFamilyBirthday(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();

      final UhomeResponseModel res =
          await client.editFamilyBirthday(requestMap);
      DevLogger.info(
          tag: Constant.packageName, msg: 'editFamilyBirthday res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'editFamilyBirthday err: $err');
      return null;
    }
  }

  /// 查询小红点
  Future<BadgesResponseModel?> getBadges() async {
    try {
      final RestClient client = await _getClient();

      final BadgesResponseModel res = await client.getBadgesApi();
      DevLogger.info(
          tag: Constant.packageName, msg: 'service_getBadges res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'service_getBadges err: $err');
      return null;
    }
  }

  /// 五星好评提交, 点击拒绝、关闭按钮时调用
  Future<UhomeResponseModel?> fiveStarPraise(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();
      final UhomeResponseModel res = await client.fiveStarPraise(requestMap);
      DevLogger.info(
          tag: Constant.packageName, msg: 'fiveStarPraise res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'fiveStarPraise err: $err');
      return null;
    }
  }

  static String _appVersion = '';

  static Future<String> getAppVersion() async {
    if (_appVersion.isEmpty) {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _appVersion = appInfo.appVersion;
    }
    return _appVersion;
  }

  /// 打开/关闭开关
  Future<UhomeResponseModel?> familyCardUpgradeSwitchOperate(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();
      final String appVersion = await getAppVersion();
      final UhomeResponseModel res =
          await client.familyCardUpgradeSwitchOperate(requestMap, appVersion);
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'familyCardUpgradeSwitchOperate res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: 'familyCardUpgradeSwitchOperate err: $err');
      return null;
    }
  }

  /// 查询开关状态
  Future<SwitchQueryResponseModel?> familyCardUpgradeSwitchQuery(
      Map<String, dynamic> requestMap) async {
    try {
      final RestClient client = await _getClient();
      final String appVersion = await getAppVersion();
      final SwitchQueryResponseModel res =
          await client.familyCardUpgradeSwitchQuery(requestMap, appVersion);
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'familyCardUpgradeSwitchQuery res: $res');
      return res;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: 'familyCardUpgradeSwitchQuery err: $err');
      return null;
    }
  }
}
