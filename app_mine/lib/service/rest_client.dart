import 'package:app_mine/badges/models/badges_response_model.dart';
import 'package:app_mine/birthday_info/birthday_model.dart';
import 'package:app_mine/current_family/switch_query_model.dart';
import 'package:app_mine/user_info/models/additional_user_info_response_model.dart';
import 'package:dio/dio.dart' hide Headers;
import 'package:retrofit/retrofit.dart';
import 'package:upservice/model/uhome_response_model.dart';

part 'rest_client.g.dart';

@RestApi()
abstract class RestClient {
  factory RestClient(Dio dio, {String baseUrl}) = _RestClient;

  /// 获取用户附加信息整合(会员等级)
  static const String additionalUserInfoApiPath =
      '/api-gw/zjBaseServer/minetab/user/vipInfo/query';

  static const String prizePath = '/vipCode/orderEva/getMyPrizeCount';

  /// 红点信息整合(设备升级、用户中心消息、工单、问题反馈
  static const String badgesApiPath =
      '/api-gw/zjBaseServer/minetab/redpoint/info';

  /// 五星好评提交接口
  static const String fiveStarPraiseApiPath =
      '/api-gw/zjBaseServer/fiveStarPraise/save';

  /// 小优中心生日相关
  static const String xiaoUBirthdayList =
      '/api-gw/zjBaseServer/aiAssistant/virtual/listUser';
  static const String xiaoUBirthdayAdd =
      '/api-gw/zjBaseServer/aiAssistant/virtual/addUser';
  static const String xiaoUBirthdayEdit =
      '/api-gw/zjBaseServer/aiAssistant/virtual/updateUser';
  static const String xiaoUBirthdayDel =
      '/api-gw/zjBaseServer/aiAssistant/virtual/batchDeleteUser';

  static const String switchOperate = '/api-gw/zjBaseServer/switch/operate';
  static const String switchQuery = '/api-gw/zjBaseServer/switch/query';

  /// 获取用户生日列表
  @POST(xiaoUBirthdayList)
  Future<BirthdayResponseModel> getBirthdayListDataApi(
    @Body() Map<String, dynamic> requestMap,
  );

  /// 删除生日
  @POST(xiaoUBirthdayDel)
  Future<UhomeResponseModel> delFamilyBirthday(
    @Body() Map<String, dynamic> requestMap,
  );

  //添加家人生日
  @POST(xiaoUBirthdayAdd)
  Future<UhomeResponseModel> addFamilyBirthday(
    @Body() Map<String, dynamic> requestMap,
  );

  //编辑家人生日
  @POST(xiaoUBirthdayEdit)
  Future<UhomeResponseModel> editFamilyBirthday(
    @Body() Map<String, dynamic> requestMap,
  );

  /// 获取用户附加信息整合(会员等级)
  @POST(additionalUserInfoApiPath)
  Future<AdditionalUserInfoResponseModel> getUserAdditionalDataApi(
    @Body() Map<String, dynamic> requestMap,
  );

  /// 查询小红点
  @POST(badgesApiPath)
  Future<BadgesResponseModel> getBadgesApi();

  /// 五星随手评拒绝和关闭时评价
  @POST(fiveStarPraiseApiPath)
  Future<UhomeResponseModel> fiveStarPraise(
    @Body() Map<String, dynamic> requestMap,
  );

  /// 打开/关闭开关
  @POST(switchOperate)
  Future<UhomeResponseModel> familyCardUpgradeSwitchOperate(
      @Body() Map<String, dynamic> requestMap,
      @Header('appVersion') String appVersion);

  /// 查询开关状态
  @POST(switchQuery)
  Future<SwitchQueryResponseModel> familyCardUpgradeSwitchQuery(
      @Body() Map<String, dynamic> requestMap,
      @Header('appVersion') String appVersion);
}
