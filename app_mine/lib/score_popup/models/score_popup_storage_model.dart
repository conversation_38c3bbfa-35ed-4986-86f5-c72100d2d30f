import 'package:json_annotation/json_annotation.dart';

part 'score_popup_storage_model.g.dart';

/// 五星弹窗存入缓存Storage的数据model
@JsonSerializable(explicitToJson: true, anyMap: true)
class ScorePopupStorageModel {
  ScorePopupStorageModel({
    required this.popNum,
    required this.isNeedPopedUp,
  });

  /// [popNum] 弹出次数
  @JsonKey(defaultValue: 0)
  int popNum;

  /// [isNeedPopedUp] 是否可以弹窗
  @JsonKey(defaultValue: false)
  bool isNeedPopedUp;

    factory ScorePopupStorageModel.fromJson(Map<dynamic, dynamic> json) =>
      _$ScorePopupStorageModelFromJson(json);

  Map<String, dynamic> toJson() => _$ScorePopupStorageModelToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScorePopupStorageModel &&
          runtimeType == other.runtimeType &&
          popNum == other.popNum &&
          isNeedPopedUp == other.isNeedPopedUp;

  @override
  int get hashCode => popNum.hashCode ^ isNeedPopedUp.hashCode;

  @override
  String toString() {
    return 'ScorePopupStorageModel{popNum: $popNum, isNeedPopedUp: $isNeedPopedUp}';
  }
}
