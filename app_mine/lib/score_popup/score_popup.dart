import 'dart:convert';

import 'package:app_mine/score_popup/models/score_popup_storage_model.dart';
import 'package:app_mine/score_popup/store/score_pop_up_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:storage/storage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

/// 五星好评弹窗的功能文件
class ScorePopup {
  static final ScorePopup _instance = ScorePopup._internal();

  ScorePopup._internal();

  factory ScorePopup() => _instance;


  /// 默认的弹窗Storage存储的信息数据结构
  static final Map<String, dynamic> defaultStorePopupInfo = <String, dynamic>{
    /// 弹窗次数
    'popNum': 0,

    /// 能否弹窗
    'isNeedPopedUp': true
  };

  /// 显示五星评价弹窗
  void showScorePopup(BuildContext context) {
    canShowPopup().then((bool canShow) {
      if (canShow) {
        appMineStore.dispatch(UpdateScorePopupShowAction(true));
      }
    });
  }

  /// 判断能否显示弹窗，包含弹窗的计算逻辑
  Future<bool> canShowPopup() async {
    try {
      DevLogger.info(tag: Constant.packageName, msg: 'showScorePopup');
      final LoginStatus? loginStatus = User.getLoginStatusSync();

      if (loginStatus?.isLogin ?? false) {
        /// 查询存储的弹窗key
        final String popKey = await getPopupStorageKey();
        /// 查询缓存中的弹窗信息
        final ScorePopupStorageModel popInfoModel =
            await getPopupStorageValueByKey(popKey);

        if (popInfoModel.popNum < 2) {
          popInfoModel.popNum += 1;
        }
        await Storage.putStringValue(popKey, jsonEncode(popInfoModel.toJson()));
        return popInfoModel.popNum == 2 && popInfoModel.isNeedPopedUp;
      } else {
        return false;
      }
    } catch (e) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'canShowPopup error, err: $e');
      return false;
    }
  }

  /// 查询弹窗缓存
  Future<ScorePopupStorageModel> getPopupStorageValueByKey(
      String popKey) async {
    try {
      if (popKey.isEmpty) {
        return ScorePopupStorageModel(popNum: 0, isNeedPopedUp: true);
      }

      /// 查询缓存中的弹窗信息
      final String popInfoString = await Storage.getStringValue(popKey,
          defaultValue: jsonEncode(defaultStorePopupInfo));
      DevLogger.info(
          tag: Constant.packageName,
          msg: 'getPopupStorageValue popInfoString: $popInfoString');

      /// 解析缓存中的弹窗信息
      final ScorePopupStorageModel popInfoModel =
          ScorePopupStorageModel.fromJson(
              jsonDecode(popInfoString) as Map<String, dynamic>);
      return popInfoModel;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getPopupStorageValue err: $err');
      return ScorePopupStorageModel(popNum: 0, isNeedPopedUp: false);
    }
  }

  /// 查询五星弹窗缓存的key
  Future<String> getPopupStorageKey() async {
    try {
      final UserInfo userInfo = await User.getUserInfo();

      /// 当前年份
      final int currentYear = DateTime.now().year;

      /// 当前月份
      final int currentMonth = DateTime.now().month;

      /// 存储的key
      final String popKey = '$currentYear-$currentMonth-${userInfo.userId}';
      DevLogger.info(
          tag: Constant.packageName,
          msg: '_getPopupStorageKey popKey: $popKey');
      return popKey;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: '_getPopupStorageKey err: $err');
      return '';
    }
  }
}
