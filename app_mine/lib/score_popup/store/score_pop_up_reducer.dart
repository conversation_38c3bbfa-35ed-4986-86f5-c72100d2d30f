import 'package:app_mine/score_popup/store/score_pop_up_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> scorePopupReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateScorePopupShowAction>(_updateScorePopupShowReducer).call,
];

/// 更新五星随手评show的reducer
AppMineState _updateScorePopupShowReducer(
    AppMineState state, UpdateScorePopupShowAction action) {    
    state.scorePopupState.show = action.show;
    return state;
}
