class ScorePopupState {

  /// [show] 是否显示
  bool show = false;

  void clear() {
    show = false;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScorePopupState &&
          runtimeType == other.runtimeType &&
          show == other.show;

  @override
  int get hashCode => show.hashCode;

  @override
  String toString() {
    return 'ScorePopupState{show: $show}';
  }
}
