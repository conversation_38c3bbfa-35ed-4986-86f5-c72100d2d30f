import 'dart:convert';

import 'package:app_mine/score_popup/models/score_popup_storage_model.dart';
import 'package:app_mine/score_popup/score_popup.dart';
import 'package:app_mine/score_popup/store/score_pop_up_action.dart';
import 'package:app_mine/service/service.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';
import 'package:upservice/model/uhome_response_model.dart';

final List<Middleware<AppMineState>> scorePopupMiddlewares =
    <Middleware<AppMineState>>[
  /// 立即评价
  TypedMiddleware<AppMineState, ScorePopupEvaluateAction>(evaluateMiddleware)
      .call,

  /// 拒绝
  TypedMiddleware<AppMineState, ScorePopupRefuseAction>(refuseMiddleware).call
];

/// 五星评价弹窗点击立即评价
Future<void> evaluateMiddleware(
  Store<AppMineState> store,
  ScorePopupEvaluateAction action,
  NextDispatcher next,
) async {
  /// 关闭弹窗
  store.dispatch(UpdateScorePopupShowAction(false));

  /// 跳转
  if (action.jumpUrl != null && action.jumpUrl!.isNotEmpty) {
    goToPage(action.jumpUrl);
  }
}

/// 五星评价弹窗点击拒绝
Future<void> refuseMiddleware(
  Store<AppMineState> store,
  ScorePopupRefuseAction action,
  NextDispatcher next,
) async {
  try {
    /// 关闭弹窗
    store.dispatch(UpdateScorePopupShowAction(false));
    /// 是否主动提交（0.否；1.是，主动提交后会触发发送海贝）
    final UhomeResponseModel? res =
        await Service().fiveStarPraise(<String, int>{
      'activeSubmit': 0,
    });
    DevLogger.info(
        tag: Constant.packageName, msg: '_refuse fiveStarPraise res:$res');

    /// 更新缓存中的弹窗信息，下次不弹
    await _updatePopupStorage();
  } catch (err) {
    DevLogger.error(tag: Constant.packageName, msg: '_refuse err: $err');
  }
}

/// 更新缓存中的弹窗信息
Future<void> _updatePopupStorage() async {
  try {
    /// 存储的key
    final String popKey = await ScorePopup().getPopupStorageKey();

    /// 查询缓存中的弹窗信息
    final ScorePopupStorageModel popInfoModel =
        await ScorePopup().getPopupStorageValueByKey(popKey);

    if (popInfoModel.popNum == 2) {
      popInfoModel.isNeedPopedUp = false;
    }
    await Storage.putStringValue(popKey, jsonEncode(popInfoModel.toJson()));
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName, msg: '_updatePopupStorage err: $err');
  }
}
