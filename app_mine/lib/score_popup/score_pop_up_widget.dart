import 'package:app_mine/score_popup/score_popup_view_model.dart';
import 'package:app_mine/score_popup/store/score_pop_up_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';

class ScorePopupWidget extends StatelessWidget {
  const ScorePopupWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, ScorePopupViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            ScorePopupViewModel.fromStore(store),
        builder: (BuildContext context, ScorePopupViewModel vm) {
          if (vm.isLogin && vm.show) {
            return const ScorePopupStateWidget();
          }
          return const SizedBox.shrink();
        });
  }
}

class ScorePopupStateWidget extends StatefulWidget {
  const ScorePopupStateWidget({super.key});

  @override
  State<ScorePopupStateWidget> createState() => _ScorePopupStateWidgetState();
}

class _ScorePopupStateWidgetState extends State<ScorePopupStateWidget> {
  static OverlayEntry? entry;

  static OverlayState? overlay;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      showPopup();
    });
  }

  @override
  void dispose() {
    entry?.remove();
    entry = null;
    super.dispose();
  }

  /// 显示五星评价弹窗
  void showPopup() {
    gioTrack(
        Constant.scorePopupGio, <String, String>{'content_title': '评价领海贝'});
    overlay = Overlay.of(context);
    entry =
        OverlayEntry(builder: (BuildContext context) => const ScorePopupBody());
    if (entry != null) {
      overlay?.insert(entry!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}

class ScorePopupBody extends StatelessWidget {
  const ScorePopupBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: Alignment.center, children: <Widget>[
      /// 背景
      Container(color: Colors.black.withOpacity(0.5)),

      /// 主弹窗
      const _Popup()
    ]);
  }
}

/// 主弹窗
class _Popup extends StatelessWidget {
  const _Popup({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle.merge(
      style: TextStyle(
        fontFamily: Constant.fontFamilyPingFangSCRegular,
        fontWeight: FontWeight.w400,
        decoration: TextDecoration.none,
        color: AppSemanticColors.item.primary,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            child: Container(
              width: 270.w,
              height: 317.w,
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: const _PopupContent(),
            ),
          ),
          const _CloseBtn()
        ],
      ),
    );
  }
}

/// 弹窗主内容
class _PopupContent extends StatelessWidget {
  const _PopupContent({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        const _Header(),
        const _Content(),
        Divider(height: 1.w, color: const Color.fromRGBO(0, 0, 0, 0.1)),
        const _ChoiceItem(
            title: '立即评价', active: true, jumpUrl: Constant.rateStarUrl),
        Divider(height: 1.w, color: const Color.fromRGBO(0, 0, 0, 0.1)),
        const _ChoiceItem(title: '稍后提醒'),
        Divider(height: 1.w, color: const Color.fromRGBO(0, 0, 0, 0.1)),
        const _ChoiceItem(
          title: Constant.refuseTile,
        ),
      ],
    );
  }
}

/// 头部图片
class _Header extends StatelessWidget {
  const _Header({super.key});

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/images/score_pop.png',
      package: Constant.packageName,
      fit: BoxFit.fill,
      height: 110.w,
      width: 270.w,
    );
  }
}

/// 文字内容区
class _Content extends StatelessWidget {
  const _Content({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 66.w,
      padding: EdgeInsets.only(right: 16.w, left: 16.w, top: 24.w),
      child: Text(
        '您对智家APP的使用满意吗？',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 17.sp,
          color: const Color.fromRGBO(0, 0, 0, 0.8),
        ),
      ),
    );
  }
}

/// 选择项
class _ChoiceItem extends StatelessWidget {
  const _ChoiceItem(
      {super.key, required this.title, this.active = false, this.jumpUrl});

  /// [title] 显示的标题
  final String title;

  /// [active] 是否是激活状态，控制文字颜色，true 激活 文字为蓝色，false 未激活 文字为黑色
  final bool active;

  final String? jumpUrl;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 46.w,
      width: 270.w,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () async {
          /// 网络不稳定的情况下点击【立即评价】toast提示“网络不可用”；下次切换到我的tab上继续展示弹窗；
          /// 点击【稍后提醒】不提示直接关闭当前弹窗，下次切换到我的tab上继续展示弹窗；
          /// 点击【残忍拒绝】及【X】不提示直接关闭当前弹窗，下次切换到我的tab不再展示弹窗。
          /// 埋点
          gioTrack(Constant.rateStarClickGio, <String, String?>{
            'button_name': title,
          });

          /// 立即评级
          if (title == Constant.evaluateTitle) {
            appMineStore.dispatch(ScorePopupEvaluateAction(jumpUrl));
            return;
          }

          /// 残忍拒绝
          if (title == Constant.refuseTile) {
            appMineStore.dispatch(ScorePopupRefuseAction());
            return;
          }

          /// 关闭弹窗
          appMineStore.dispatch(UpdateScorePopupShowAction(false));
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.w),
          width: 135.w,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 17.sp,
              color: active
                  ? const Color.fromRGBO(34, 131, 226, 1)
                  : const Color.fromRGBO(0, 0, 0, 0.6),
            ),
          ),
        ),
      ),
    );
  }
}

/// 关闭按钮
class _CloseBtn extends StatelessWidget {
  const _CloseBtn({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 16.w),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          /// 拒绝
          appMineStore.dispatch(ScorePopupRefuseAction());
        },
        child: Image.asset(
          'assets/images/close_score.png',
          width: 48.w,
          height: 48.w,
          fit: BoxFit.contain,
          package: Constant.packageName,
        ),
      ),
    );
  }
}
