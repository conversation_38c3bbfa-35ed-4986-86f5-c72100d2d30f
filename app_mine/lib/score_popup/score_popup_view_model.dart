import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

class ScorePopupViewModel {
  ScorePopupViewModel({required this.isLogin, required this.show});

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [show] 是否显示五星弹窗
  bool show = false;

  /// 组装viewModel的数据
  static ScorePopupViewModel fromStore(Store<AppMineState> store) {

    return ScorePopupViewModel(
      isLogin: store.state.isLogin,
      show: store.state.scorePopupState.show,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScorePopupViewModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          show == other.show;

  @override
  int get hashCode => isLogin.hashCode ^ show.hashCode;

  @override
  String toString() {
    return 'ScorePopupViewModel{isLogin: $isLogin, show: $show}';
  }
}
