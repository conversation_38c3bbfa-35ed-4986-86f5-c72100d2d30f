class Constant {
  static const String packageName = 'app_mine';

  /// [baseUrl] 智家请求接口域名
  static const String baseUrl = 'https://zj.haier.net';

  /// [baseYsUrl] 智家验收请求接口域名
  static const String baseYsUrl = 'https://zj-yanshou.haier.net';

  /// [hyzHost] 会员请求域名
  static const String hyzYsHost = 'https://hzytest.haier.com';

  static const String hyzHost = 'https://hzy.haier.com';

  static const String fontFamilyPingFangSCRegular = 'PingFangSC-Regular';

  /// 会员等级跳转地址
  static const String vipLevelYsUrl =
      'https://hzytest.haier.com/cn/vipcode/myVipApp?needAuthLogin=1&needLogin=1';
  static const String vipLevelUrl =
      'https://hzy.haier.com/cn/vipcode/myVipApp?needAuthLogin=1&needLogin=1';
  static const String vipLevelClickGio = 'MB10238';

  /// 会员俱乐部跳转地址
  static const String vipPubYsUrl =
      'https://hzytest.haier.com/cn/vipcode/community?needAuthLogin=1';
  static const String vipPubUrl =
      'https://hzy.haier.com/cn/vipcode/community?needAuthLogin=1';
  static const String vipPubClickGio = 'MB40000';

  //// 连字符的占位符，会员部分使用
  static const String doubleHyphenPlaceholder = '--';

  /// 页面滚动的最大高度，用于吸顶效果
  static const double maxOffset = 44.0;

  /// 上滑变化文字颜色的距离，包含昵称，设置、消息图标
  static const int changeTextColorOffset = 22;

  /// 上滑变化透明度的距离，包含LV1，成长值, 皇冠
  static const int changeOpacityOffset = 10;

  /// 导航栏部分上滑的最大距离
  static const double maxScrollOffset = 76.0;
  
  /// 导航栏部分上滑的最小距离，开始变化透明度
  static const double minScrollOffset = 20.0;

  static const double scrollOffsetDelta = 56.0;

  /// 进入页面埋点
  static const String pageInGio = 'MB36954';

  /// 离开页面埋点
  static const String pageLeaveGio = 'MB36955';

  /// 下拉刷新埋点
  static const String easyRefreshGio = 'MB36946';

  /// 菜单列表
  /// 活动中心
  static const String activityCenterYsUrl = 'https://ys-zjrs.haier.net/scs/newActivity/index.html#/';
  static const String activityCenterUrl = 'https://zjrs.haier.net/scs/newActivity/index.html';
  static const String activityCenterGioKey = 'MB34393';

  /// 我的权益
  static const String myRightsUrl = 'mpaas://myRights?needAuthLogin=1';
  static const String myRightsGioKey = 'MB40001';

  //我的交易
  static const String tradeGio = 'MB36773';
  static const String tradeYsUrl =
      'https://m-test.ehaier.com/sgmobile/myTransaction?needAuthLogin=1';
  static const String tradeUrl =
      'https://m.ehaier.com/sgmobile/myTransaction?needAuthLogin=1';

  //我的服务
  static const String serverGio = 'MB31890';
  static const String serverYsUrl =
      'https://service-test.haiersmarthomes.com/app/index.html?type=T00&code=303&push=outSide&orderType=0&needAuthLogin=1#/fuwudan';
  static const String serverUrl =
      'https://service.haiersmarthomes.com/app/iindex.html?tvpe=T00&code=303&push=outSide&orderType=0&needAuthLogin=1#/fuwudan';

  // 我的场景
  static const String sceneGio = 'MB38314';

  // 设备耗材
  static const String consumablesGio = 'MB38315';
  static const String consumablesUrl =
      'mpaas://wholeHouse?needAuthLogin=1';

  // 设备节能
  static const String energySavingGio = 'MB38316';
  static const String energySavingUrl =
      'mpaas://smart_control_manager?needAuthLogin=1#/energy-conservation';

  //设备共享
  static const String shareGio = 'MBA38256';
  static const String shareUrl = 'mpaas://deviceSharing?needAuthLogin=1#/management';

  //我的钱包
  static const String pauseGio = 'MB31891';
  static const String pauseUrl = 'flutter://myPause.html';
  static const String purseTitle = '我的钱包';

  /// 我的钱包资源id
  static const int purseResourceId = 325;

  /// 我的卡包资源id
  static const int pocketResourceId = 326;

  /// 我的钱包配置文件名称
  static const String purseConfigName = 'appmine-configAPP';
  static const String zhiJiaPurse = '智家钱包';

  /// 智家钱包验收跳转地址
  static const String zhiJiaPurseYsUrl =
      'https://standardpay-p2.haiercash.com/home?processId=P16286590146637347&channelNo=N7&firstSpell=LHDL&container_type=3';

  /// 我的卡包
  static const String pocketUrl = 'flutter://myCard.html';
  static const String pocketTitle = '我的卡包';

  /// 食联生态卡
  static const String foodEcologicalCodeUrl =
      'https://foodecological.alphesh.com';

  /// 我的资产
  static const String myAssetsUrl =
      'https://m.ehaier.com/sgmobile/wallet?uChannelCode=appmine-319&needAuthLogin=1&container_type=3';

  //在线客服
  static const String onlineServerGio = 'MB18355';
  static const String onlineServerUrl =
      'mpaas://CustomerService/?fromPageTitle=我的-在线客服&pId=zjapp&sceneId=cd0147&entranceId=jt&needAuthLogin=1#/zhijiaHome';

  //第三方设备
  static const String otherDeviceGio = 'MB36772';
  static const String otherDeviceUrl =
      'https://www.haigeek.com/mobile-page/thirdParty/thirdIndex.html?container_type=3&hidesBottomBarWhenPushed=1&needAuthLogin=1';

  //实验室
  static const String gptCenterGio = 'MB37014';
  static const String gptCenterUrl =
      'flutter://appmine/gptCenter?needAuthLogin=1';

  //我的建议
  static const String suggestGio = 'MB34805';
  static const String suggestUrl =
      'mpaas://mySuggestionsNew?needAuthLogin=1&entrance=我的';

  //关于我们
  static const String aboutUsTitle = '关于海尔智家';
  static const String aboutUsGio = 'MB10263';
  static const String aboutUsExposureGio = 'MB31040';
  static const String aboutUsUrl =
      'flutter://appmine/aboutUs?uChannelCode=appmine-320&hidesBottomBarWhenPushed=1&needAuthLogin=0';

  /// app启动的时候会将要更新的版本号存到Storage中，key如下
  static const String appNewVersionStorageKey = 'appVersion_myTab_newVersion';

  /// 我页缓存的曝光过的版本号(曝光指跳转了二级页面，关于我们)
  static const String appVersionExposureKey = 'appVersion/myTab/exposure';

  /// 设置页面
  static const String settingsUrl =
      'flutter://setting.html?hidesBottomBarWhenPushed=1&needAuthLogin=1';

  /// 设置页面点击gio埋点
  static const String settingsClickGio = 'MB14466';

  //消息中心
  static const String messageCenterUrl =
      'mpaas://messageCenter?needAuthLogin=1';

  /// 消息中心点击gio埋点
  static const String messageCenterClickGio = 'MB36774';

  static const String laboratoryTitle = '语音设置';

  /// 家庭详情页
  static const String familyManageUrl = 'mpaas://familymanage?needAuthLogin=1#/familyInfo';
  static const String familyClickGio = 'MB4000';

  /// 邀请家人
  static const String familyInviteClickGio = 'MB4001';

  /// 网器用户弹窗
  /// 网器用户弹窗展示过的缓存key
  static const String deviceUserPopupShowedStorageKey =
      'my_tab_device_user_shown';
  static const String deviceUserPopupInitGio = 'MB36120';

  //小优技能中心
  static const String skillCenterGio = 'MB18675';
  static const String skillCenterUrl =
      'mpaas://voiceCenter/index.html?needAuthLogin=1';

  //小优家人生日
  static const String birthdayUrl = 'flutter://xiaou/familyBirthdayInfo';

  //智能客服
  static const String smartCustomerTitle = '智能客服';
  static const String smartCustomerGio = 'MB19234';

  //弹窗通知
  static const String dialogMessageTitle = '弹窗通知';
  static const String dialogMessageGio = 'MB35777';

  //冒泡通知
  static const String bubblingMessageTitle = '冒泡通知';
  static const String bubblingMessageGio = 'MB35778';

  //未处理消息通知
  static const String unProcessedMessageTitle = '未处理消息通知';
  static const String unProcessedMessageGio = 'MB35779';

  /// 登录页面
  static const String loginUrl = 'mpaas://usercenter';

  /// 个人资料
  static const String personalInfoUrl = 'flutter://personalInfo.html';

  /// 个人资料页面点击gio埋点
  static const String personalInfoClickGio = 'MB10264';

  /// 网络异常提示语
  static const String netWorkErrorMsg = '网络不可用';

  //海尔百问入口
  static const String voiceMessageTitle = '小优入口';
  static const String voiceMessageGio = 'MB36878';

  //// 五星随手评
  static const String rateStarUrl = 'mpaas://rateStarScore?needAuthLogin=1';
  static const String evaluateTitle = '立即评价';
  static const String refuseTile = '残忍拒绝';

  /// 点击立即评价、稍后提醒、残忍拒绝的埋点key
  static const String rateStarClickGio = 'MB19673';

  /// 五星弹窗弹出的埋点
  static const String scorePopupGio = 'MB19672';

  static const String httpRetCodeSuccess = '00000';

  static const String mineRedPointSp = 'app_mine_red_point_status';

  // 聚合卡片id的前缀
  static const String aggregationIdPrefix = 'AggId';

  static const String noPermission = '暂无操作权限';
  static const String adminCountLimit = '管理员数量已达上限';
  static const String memberCountLimit = '成员数量已达上限';

  static const int maxMemberCount = 50;
  static const int maxAdminCount = 10;

  static const int upgradeBadgeSwitchType = 14;
  static const String statusClose = '1';
  static const String statusOpen = '0';
  static const String sourceName = '我的tab';
}
