import 'package:app_mine/utils/app_info.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show ToastHelper, goToPage;

/// 查询网络状态
Future<bool?> getNetworkState() async {
  try {
    final IsOnline isOnline =
        await Network.isOnline();
    return (isOnline.isOnline);
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName, msg: 'getNetworkStatus err:$err');
    return null;
  }
}

/// vdn根据验收、生产的env跳转
Future<void> goToPageByEnv(String ysUrl, String url,
    {Map<String, dynamic>? params}) async {
  try {
    final String _url = await getUrlOrResourceBitByEnv(ysStr: ysUrl, str: url);
    goToPage(_url, params: params);
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName, msg: 'goToPage Debounce error:$err');
  }
}

/// 根据app环境返回跳转的url或者资源位, 区分验收、生产
Future<String> getUrlOrResourceBitByEnv(
    {required String ysStr, required String str}) async {
  await AppInfo.getAppInfo();
  return AppInfo.env == '验收' ? ysStr : str;
}

/// 1 < 10000 原样展示；
/// 2 >= 10000，保留2位小数, x.xx万
String formatNumber(String input) {
  try {
    /// 将输入字串转换为数字
    final double? number = double.tryParse(input);

    /// 检查转换是否成功，失败则返回输入字串
    if (number == null) {
      return input;
    }

    /// 判断输入数字的大小并进行相应的格式化
    if (number < 10000) {
      /// 原样展示
      return input;
    } else {
      /// 计算截断后的值
      final double truncatedNumber = number / 10000;
      String truncatedString = truncatedNumber.toString();

      /// 查找小数点的位置
      final int dotIndex = truncatedString.indexOf('.');

      /// 截断到小数点后两位
      if (dotIndex != -1 && dotIndex + 3 < truncatedString.length) {
        truncatedString = truncatedString.substring(0, dotIndex + 3);
      }

      return '$truncatedString万';
    }
  } catch (err) {
    /// 异常返回输入字串
    return input;
  }
}

/// 初始化toast
void initToast(BuildContext context) {
  /// 初始化ToastHelper, 设置context
  ToastHelper.init(context);

  /// 设置可以弹窗
  ToastHelper.updateCanShow(true);
}
