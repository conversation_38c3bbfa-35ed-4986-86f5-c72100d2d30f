import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';

class AppInfo {
  /// [_env] app环境
  static String _env = '';

  /// [_grayMode] 是否为灰度，false为生产环境
  static bool _grayMode = false;

  /// [_appVersion] app版本号，如8.0.0
  static String _appVersion = '';

  static String get env => _env;
  static bool get grayMode => _grayMode;
  static String get appVersion => _appVersion;

  static Future<void> getAppInfo() async {
    try {
      if (_appVersion.isNotEmpty) {
        return;
      }
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _env = appInfo.env;
      _grayMode = appInfo.grayMode;
      _appVersion = appInfo.appVersion;
    } catch (err) {
      DevLogger.error(tag: Constant.packageName, msg: 'getAppInfo error:$err');
    }
  }
}
