class GptCenterState {
  bool smartCustomer = false;
  bool dialogMessage = false;
  bool bubblingMessage = false;
  bool unProcessedMessage = false;
  bool voiceGlobalSwitch = false;
  bool voiceUserSwitch = true;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GptCenterState &&
          runtimeType == other.runtimeType &&
          smartCustomer == other.smartCustomer &&
          dialogMessage == other.dialogMessage &&
          bubblingMessage == other.bubblingMessage &&
          unProcessedMessage == other.unProcessedMessage &&
          voiceGlobalSwitch == other.voiceGlobalSwitch &&
          voiceUserSwitch == other.voiceUserSwitch;

  @override
  int get hashCode =>
      smartCustomer.hashCode ^
      dialogMessage.hashCode ^
      bubblingMessage.hashCode ^
      unProcessedMessage.hashCode ^
      voiceGlobalSwitch.hashCode ^
      voiceUserSwitch.hashCode;

  @override
  String toString() {
    return 'GptCenterState{smartCustomer: $smartCustomer, dialogMessage: $dialogMessage, bubblingMessage: $bubblingMessage, unProcessedMessage: $unProcessedMessage, voiceGlobalSwitch: $voiceGlobalSwitch, voiceUserSwitch: $voiceUserSwitch}';
  }
}
