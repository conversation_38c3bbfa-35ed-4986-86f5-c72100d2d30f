import 'package:app_mine/gpt_center/model/switch_type_model.dart';
import 'package:app_mine/gpt_center/store/gpt_center_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:redux/redux.dart';

class GptCenterMiddleware implements MiddlewareClass<AppMineState> {
  @override
  dynamic call(Store<AppMineState> store, dynamic action, NextDispatcher next) {
    if (action is UpdateMessageStateAction) {
      updateMessageState(action);
    } else if (action is UpdateVoiceUserStateAction) {
      setSwitchFormStorage(VOICE_USER_SWITCH_KEY, action.switchState);
    }
    next(action);
  }

  Future<void> updateMessageState(UpdateMessageStateAction action) async {
    if (action.title == Constant.smartCustomerTitle) {
      setSmartCustomerSwitchState(action.switchState);
    } else {
      setSwitchState(action.title, action.switchState);
    }
  }
}
