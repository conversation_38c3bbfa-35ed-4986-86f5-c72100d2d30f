class GptCenterBaseAction {}

class UpdateMessageStateAction extends GptCenterBaseAction {
  UpdateMessageStateAction(this.title, this.switchState);

  String title;
  bool switchState;
}

class UpdateAllMessageStateAction extends GptCenterBaseAction {
  UpdateAllMessageStateAction(this.smartCustomer, this.dialogMessage,
      this.bubblingMessage, this.unProcessedMessage);

  bool smartCustomer;
  bool dialogMessage;
  bool bubblingMessage;
  bool unProcessedMessage;
}

class UpdateVoiceStateAction extends GptCenterBaseAction {
  bool voiceGlobal;
  bool voiceUser;

  UpdateVoiceStateAction(this.voiceGlobal, this.voiceUser);
}

class UpdateVoiceUserStateAction extends GptCenterBaseAction {
  UpdateVoiceUserStateAction(this.switchState);

  bool switchState;
}
