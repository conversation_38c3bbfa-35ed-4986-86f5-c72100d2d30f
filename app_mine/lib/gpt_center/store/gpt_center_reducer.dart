import 'package:app_mine/gpt_center/store/gpt_center_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> gptCenterStateReducer =
    <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateMessageStateAction>(
          _updateMessageStateReducer)
      .call,
  TypedReducer<AppMineState, UpdateAllMessageStateAction>(
          _updateAllMessageStateReducer)
      .call,
  TypedReducer<AppMineState, UpdateVoiceStateAction>(_updateVoiceStateReducer)
      .call,
  TypedReducer<AppMineState, UpdateVoiceUserStateAction>(
          _updateVoiceUserStateReducer)
      .call,
];

AppMineState _updateMessageStateReducer(
    AppMineState state, UpdateMessageStateAction action) {
  switch (action.title) {
    case Constant.dialogMessageTitle:
      state.gptCenterState.dialogMessage = action.switchState;
    case Constant.bubblingMessageTitle:
      state.gptCenterState.bubblingMessage = action.switchState;
    case Constant.unProcessedMessageTitle:
      state.gptCenterState.unProcessedMessage = action.switchState;
    case Constant.smartCustomerTitle:
      state.gptCenterState.smartCustomer = action.switchState;
  }
  return state;
}

AppMineState _updateAllMessageStateReducer(
    AppMineState state, UpdateAllMessageStateAction action) {
  state.gptCenterState.smartCustomer = action.smartCustomer;
  state.gptCenterState.dialogMessage = action.dialogMessage;
  state.gptCenterState.bubblingMessage = action.bubblingMessage;
  state.gptCenterState.unProcessedMessage = action.unProcessedMessage;
  return state;
}

AppMineState _updateVoiceStateReducer(
    AppMineState state, UpdateVoiceStateAction action) {
  state.gptCenterState.voiceGlobalSwitch = action.voiceGlobal;
  state.gptCenterState.voiceUserSwitch = action.voiceUser;
  return state;
}

AppMineState _updateVoiceUserStateReducer(
    AppMineState state, UpdateVoiceUserStateAction action) {
  state.gptCenterState.voiceUserSwitch = action.switchState;
  return state;
}
