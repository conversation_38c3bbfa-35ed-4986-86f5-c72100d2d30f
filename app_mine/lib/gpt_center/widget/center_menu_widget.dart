import 'package:app_mine/gpt_center/store/gpt_center_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show AppSemanticColors, CustomSwitch, PressableOverlayWithTapWidget, gioTrack, goToPage;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/common/alert_common_dialog.dart';

import '../center_view_model.dart';

// 菜单项带点击效果
class CenterMenuWithPressEffect extends StatelessWidget {
  const CenterMenuWithPressEffect(this.centerViewModel, {super.key});

  final  GptCenterViewModel centerViewModel;

  @override
  Widget build(BuildContext context) {
    if (centerViewModel.itemType == CenterItemType.ItemNormalType) {
      return PressableOverlayWithTapWidget(
        overlayClick: () {
          gioTrack(centerViewModel.gioClick);
          goToPage(centerViewModel.jumpUrl);
        },
        child: CenterMenuWidget(centerViewModel));
    }
    return CenterMenuWidget(centerViewModel);
  }
}

class CenterMenuWidget extends StatelessWidget {
  const CenterMenuWidget(this.centerViewModel, {super.key});

  final GptCenterViewModel centerViewModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: <Widget>[
          Image.asset(
            centerViewModel.icon,
            width: 24,
            height: 24,
            package: Constant.packageName,
            fit: BoxFit.cover,
          ),
          const SizedBox(width: 12),
          Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    centerViewModel.title,
                    style: TextStyle(
                        fontSize: 16,
                        color: AppSemanticColors.item.primary,
                        fontWeight: FontWeight.w500),
                  ),
                  Visibility(
                      visible:
                          centerViewModel.title == Constant.voiceMessageTitle,
                      child: Container(
                        margin: EdgeInsets.only(top: 4.w),
                        child: Text('点击可展示/隐藏小优入口，重启APP后生效',
                            style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color.fromRGBO(0, 0, 0, 0.39),
                                fontWeight: FontWeight.w400)),
                      ))
                ],
              )),
          Visibility(
            visible:
                centerViewModel.itemType == CenterItemType.ItemSwitchType,
            child: CustomSwitch(
                value: centerViewModel.switchValue,
                activeColor: AppSemanticColors.item.information.primary,
                inactiveColor: Colors.grey.shade300,
                onChanged: (bool value) {
                  if (centerViewModel.title == Constant.voiceMessageTitle) {
                    AlertCommonDialog.showConfirmAlertDialog(context,
                        title: '温馨提示',
                        info: '确认${value ? '显示' : '隐藏'}小优入口，重启APP后生效',
                        cancelTitle: '取消',
                        confirmTitle: '确定', confirmCallback: () {
                      traceEvent(centerViewModel.gioClick, value);
                      appMineStore
                          .dispatch(UpdateVoiceUserStateAction(value));
                    }, cancelCallback: () {});
                  } else {
                    traceEvent(centerViewModel.gioClick, value);
                    appMineStore.dispatch(UpdateMessageStateAction(
                        centerViewModel.title, value));
                  }
                }),
          ),
          Visibility(
            visible:
                centerViewModel.itemType == CenterItemType.ItemNormalType &&
                    centerViewModel.jumpUrl.isNotEmpty,
            child: Image.asset(
              'assets/images/arrow_right.webp',
              width: 16,
              height: 16,
              package: Constant.packageName,
            ),
          ),
        ],
      ),
    );
  }

  void traceEvent(String gioClick, bool value) {
    String valueStr = 'status=${value ? 'on' : 'off'}';
    if (gioClick == Constant.voiceMessageGio) {
      valueStr = 'value=${value ? 'on' : 'off'}';
    }
    String keyStr = 'value';
    if (gioClick == Constant.dialogMessageGio ||
        gioClick == Constant.bubblingMessageGio ||
        gioClick == Constant.unProcessedMessageGio) {
      keyStr = 'status';
    }

    gioTrack(centerViewModel.gioClick, <String, String?>{
      keyStr: valueStr,
    });
  }
}
