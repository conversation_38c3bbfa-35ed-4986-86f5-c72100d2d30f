import 'package:app_mine/gpt_center/gpt_center_presenter.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:vdn/vdn.dart';

import '../utils/constant.dart';
import '../widget_common/bold_text_style.dart';
import 'center_view_model.dart';
import 'store/gpt_center_state.dart';
import 'widget/center_menu_widget.dart';

class GptCenterWidget extends StatelessWidget {
  const GptCenterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => StoreProvider<AppMineState>(
              store: appMineStore,
              child: Scaffold(
                  appBar: AppBar(
                    elevation: 0,
                    titleSpacing: 0,
                    toolbarHeight: 44,
                    backgroundColor: AppSemanticColors.background.secondary,
                    systemOverlayStyle: const SystemUiOverlayStyle(
                      systemNavigationBarColor: Colors.white,
                      systemNavigationBarIconBrightness: Brightness.dark,
                      statusBarColor: Colors.transparent,
                      statusBarBrightness: Brightness.light,
                      statusBarIconBrightness: Brightness.dark),
                    centerTitle: true,
                    title: Text(
                      Constant.laboratoryTitle,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w500,
                          color: AppSemanticColors.item.primary,
                          fontFamilyFallback: fontFamilyFallback()),
                    ),
                    leading: IconButton(
                        icon: Image.asset(
                          'assets/images/back.webp',
                          width: 24,
                          height: 24,
                          package: Constant.packageName,
                        ),
                        onPressed: () {
                          Vdn.close();
                        }),
                  ),
                  body: Container(
                    padding: const EdgeInsets.only(top: 12),
                    color: AppSemanticColors.background.secondary,
                    child: const CenterMenuListWidget(),
                  )),
            ));
  }
}

class CenterMenuListWidget extends StatefulWidget {
  const CenterMenuListWidget({super.key});

  @override
  State<StatefulWidget> createState() {
    return CenterMenuListState();
  }
}

class CenterMenuListState extends State<CenterMenuListWidget>
     {
  @override
  void initState() {
    super.initState();

    final GptCenterPresenter _presenter = GptCenterPresenter();
    _presenter.getSwitchesState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.resumed:
        ToastHelper.init(context);
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
    }
  }

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, GptCenterListViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) {
          final GptCenterState centerState = store.state.gptCenterState;
          final List<GptCenterViewModel> menuList = <GptCenterViewModel>[];
          menuList.add(GptCenterViewModel(
              '语音技能中心',
              'assets/images/ai_techcenter.webp',
              CenterItemType.ItemNormalType,
              Constant.skillCenterGio,
              Constant.skillCenterUrl,
              false));
          menuList.add(GptCenterViewModel(
              Constant.dialogMessageTitle,
              'assets/images/ai_dialog_message.webp',
              CenterItemType.ItemSwitchType,
              Constant.dialogMessageGio,
              '',
              centerState.dialogMessage));
          menuList.add(GptCenterViewModel(
              Constant.bubblingMessageTitle,
              'assets/images/ai_bubbling_message.webp',
              CenterItemType.ItemSwitchType,
              Constant.bubblingMessageGio,
              '',
              centerState.bubblingMessage));
          menuList.add(GptCenterViewModel(
              Constant.unProcessedMessageTitle,
              'assets/images/ai_un_message.webp',
              CenterItemType.ItemSwitchType,
              Constant.unProcessedMessageGio,
              '',
              centerState.unProcessedMessage));
          return GptCenterListViewModel(menuList);
        },
        builder: (BuildContext context, GptCenterListViewModel viewModel) {
          return DefaultTextStyle(
              style: TextStyle(
                color: AppSemanticColors.item.primary,
                fontFamilyFallback: fontFamilyFallback(),
              ),
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding: const EdgeInsets.symmetric(horizontal: 16),
                children: <Widget>[
                  // 语音技能中心
                  Padding(
                    padding: const EdgeInsets.only(bottom: 18.0),
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child:
                            CenterMenuWithPressEffect(viewModel.centerList[0])),
                  ),
                  // 其他菜单项
                  ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Column(
                      children: viewModel.centerList
                          .sublist(1)
                          .map((GptCenterViewModel centerViewModel) =>
                              CenterMenuWithPressEffect(centerViewModel))
                          .toList(),
                    ),
                  ),
                ],
              ));
        });
  }
}
