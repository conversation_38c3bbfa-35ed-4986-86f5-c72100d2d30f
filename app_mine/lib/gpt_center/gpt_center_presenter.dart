import 'package:app_mine/gpt_center/model/switch_type_model.dart';
import 'package:app_mine/gpt_center/store/gpt_center_action.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';

class GptCenterPresenter {
  GptCenterPresenter();

  Future<void> getSwitchesState() async {
    final bool dialogState = await getSwitchState(Constant.dialogMessageTitle);
    final bool bubblingMessageState =
        await getSwitchState(Constant.bubblingMessageTitle);
    final bool unProcessedMessageState =
        await getSwitchState(Constant.unProcessedMessageTitle);

    final bool smartState = await getSmartCustomerSwitchState();
    appMineStore.dispatch(UpdateAllMessageStateAction(smartState, dialogState,
        bubblingMessageState, unProcessedMessageState));

    final bool voiceUserState =
        await getSwitchFormStorage(VOICE_USER_SWITCH_KEY, true);
    appMineStore.dispatch(UpdateVoiceStateAction(false, voiceUserState));
  }
}
