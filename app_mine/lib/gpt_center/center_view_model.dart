import 'package:device_utils/compare/compare.dart';

enum CenterItemType {
  ItemNormalType,
  ItemSwitchType,
}

class GptCenterListViewModel {
  List<GptCenterViewModel> centerList = <GptCenterViewModel>[];

  GptCenterListViewModel(this.centerList);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GptCenterListViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(centerList, other.centerList);

  @override
  int get hashCode => listHashCode(centerList);
}

class GptCenterViewModel {
  GptCenterViewModel(
      this.title, this.icon, this.itemType, this.gioClick, this.jumpUrl, this.switchValue);

  String title = '';
  String icon = '';
  CenterItemType itemType = CenterItemType.ItemNormalType;
  String gioClick = '';
  String jumpUrl = '';
  bool switchValue = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GptCenterViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          icon == other.icon &&
          itemType == other.itemType &&
          gioClick == other.gioClick &&
          jumpUrl == other.jumpUrl &&
          switchValue == other.switchValue;

  @override
  int get hashCode =>
      title.hashCode ^
      icon.hashCode ^
      itemType.hashCode ^
      gioClick.hashCode ^
      jumpUrl.hashCode ^
      switchValue.hashCode;

  @override
  String toString() {
    return 'GptCenterViewModel{title: $title, icon: $icon, itemType: $itemType, gioClick: $gioClick, jumpUrl: $jumpUrl, switchValue: $switchValue}';
  }
}
