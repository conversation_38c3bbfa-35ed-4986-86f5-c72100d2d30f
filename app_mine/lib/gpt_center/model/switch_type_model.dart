import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/api/api.dart';
import 'package:device_utils/log/log.dart';
import 'package:storage/storage.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

//智能客服
const String SMART_CUSTOMER_KEY = 'key_uplus_xiaou_smart_helper_open';

//弹窗通知,故障
const String DIALOG_SWITCH_STORAGE_KEY = 'voice_switch_warn_key_';
//冒泡通知,耗材
const String BUBBLING_SWITCH_STORAGE_KEY = 'voice_switch_consumable_key_';
//未处理消息
const String UN_PROCESSED_SWITCH_STORAGE_KEY = 'voice_switch_handle_key_';

//海尔百问 远端控制
const String VOICE_GLOBAL_SWITCH_KEY = 'voice_assistant_global_switch';
//海尔百问 用户控制
const String VOICE_USER_SWITCH_KEY = 'voice_assistant_user_switch';

Future<bool> getSwitchFormStorage(String key, bool defaultValue) async {
  bool switchState = true;
  try {
    switchState =
        await Storage.getBooleanValue(key, defaultValue: defaultValue);
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'getSwitchFormStorage key:$key exception: $err',
    );
  }
  return switchState;
}

Future<void> setSwitchFormStorage(String key, bool value) async {
  try {
    await Storage.putBooleanValue(key, value);
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'setSwitchFormStorage key:$key value:$value exception: $err',
    );
  }
}

Future<bool> getSwitchState(String title) async {
  final String key = await _getVoiceSwitchStorageKey(title);
  bool switchState = true;
  try {
    switchState = await Storage.getBooleanValue(key, defaultValue: true);
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'getSwitchStateFromStorage exception: $err',
    );
  }
  return switchState;
}

Future<void> setSwitchState(String title, bool state) async {
  final String key = await _getVoiceSwitchStorageKey(title);
  try {
    await Storage.putBooleanValue(key, state);
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'setSwitchFromStorage exception: $err',
    );
  }
}

Future<bool> getSmartCustomerSwitchState() async {
  bool switchState = true;
  try {
    final String state =
        await Storage.getStringValue(SMART_CUSTOMER_KEY, defaultValue: 'true');
    switchState = (state == 'true');
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'getSmartCustomerSwitchState exception: $err',
    );
  }

  return switchState;
}

Future<void> setSmartCustomerSwitchState(bool state) async {
  try {
    await Storage.putStringValue(SMART_CUSTOMER_KEY, state ? 'true' : 'false');
  } catch (err) {
    DevLogger.error(
      tag: Constant.packageName,
      msg: 'setSmartCustomerSwitchState exception: $err',
    );
  }
}

Future<String> _getVoiceSwitchStorageKey(String title) async {
  await Api.init();
  String userId = '';
  if (Api.login) {
    final UserInfo userInfo = await User.getUserInfo();
    userId = userInfo.userId;
  }

  String storageKey = '';
  if (title == Constant.dialogMessageTitle) {
    storageKey = DIALOG_SWITCH_STORAGE_KEY + userId;
  } else if (title == Constant.bubblingMessageTitle) {
    storageKey = BUBBLING_SWITCH_STORAGE_KEY + userId;
  } else if (title == Constant.unProcessedMessageTitle) {
    storageKey = UN_PROCESSED_SWITCH_STORAGE_KEY + userId;
  }

  return storageKey;
}
