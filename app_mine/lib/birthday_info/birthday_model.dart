import 'package:device_utils/typeId_parse/template_map.dart';

class BirthdayResponseModel {
  BirthdayResponseModel.fromJson(dynamic data) {
    if (data != null && data is Map) {
      retCode = data.stringValueForKey('retCode', '');
      retInfo = data.stringValueForKey('retInfo', '');
      data.listValueForKey('data', <dynamic>[]).forEach((dynamic element) {
        if (element is Map) {
          final BirthdayModel model = BirthdayModel.fromJson(element);
          birthdayList.add(model);
        }
      });
    }
  }

  String retCode = '';
  String retInfo = '';
  List<BirthdayModel> birthdayList = <BirthdayModel>[];

  @override
  String toString() {
    return 'BirthdayResponseModel{birthdayList: $birthdayList}';
  }
}

class BirthdayModel {
  int? id;
  String? nickName = '';
  String? birthday = '';
  int? iotUserId;

  BirthdayModel.fromJson(Map<dynamic, dynamic> json) {
    id = json.nullableIntValueForKey('id');
    nickName = json.nullableStringValueForKey('nickName');
    birthday = json.nullableStringValueForKey('birthday');
    iotUserId = json.nullableIntValueForKey('iotUserId');
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BirthdayModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          nickName == other.nickName &&
          birthday == other.birthday &&
          iotUserId == other.iotUserId;

  @override
  int get hashCode =>
      id.hashCode ^ nickName.hashCode ^ birthday.hashCode ^ iotUserId.hashCode;

  @override
  String toString() {
    return 'BirthdayModel{id: $id, nickName: $nickName, birthday: $birthday, iotUserId: $iotUserId}';
  }
}
