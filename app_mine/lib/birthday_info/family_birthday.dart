import 'package:app_mine/service/service.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:library_widgets/common/alert_common_dialog.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';
import 'package:vdn/vdn.dart';

import 'birthday_create_edit.dart';
import 'birthday_model.dart';
import 'common_util.dart';
import 'family_birthday_item.dart';

List<int?> selectedItemList = <int?>[];
List<Map<String, dynamic>> selectedParamList = <Map<String, dynamic>>[];

// 小优中心 家人生日信息
class FamilyBirthday extends StatefulWidget {
  const FamilyBirthday({super.key});

  @override
  _FamilyBirthdayState createState() => _FamilyBirthdayState();
}

class _FamilyBirthdayState extends State<FamilyBirthday> {
  List<BirthdayModel> birthdayDataList = <BirthdayModel>[];
  bool isEditStatus = false;
  bool isAllSelected = false;
  bool _isDelRequesting = false;

  @override
  void initState() {
    super.initState();

    _requestBirthdayData();
    _isDelRequesting = false;
  }

  Future<void> _requestBirthdayData() async {
    String accountTokenStr = '';
    try {
      final OauthData oauthData = await User.getOauthData();
      accountTokenStr = oauthData.user_center_access_token;
    } catch (e) {
      DevLogger.error(tag: 'FamilyBirthday', msg: 'getOauthData $e');
    }

    final Map<String, Object> headerMap = <String, Object>{};
    headerMap['accountToken'] = accountTokenStr;

    String familyId;
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();
      familyId = familyModel.familyId;
    } catch (e) {
      DevLogger.error(tag: 'FamilyBirthday', msg: 'getCurrentFamily $e');
      familyId = '';
    }

    final Map<String, dynamic> param = <String, dynamic>{
      'familyId': familyId,
    };

    final BirthdayResponseModel? responseModel =
        await Service().getBirthdayList(param);
    DevLogger.info(
        tag: 'FamilyBirthday',
        msg: 'xiaou_birth_requestBirthdayData $responseModel');
    if (responseModel == null) {
      ToastHelper.showToast('查询失败，请稍后再试');
      return;
    }
    if (responseModel.retCode != '00000') {
      ToastHelper.showToast('查询失败，请稍后再试');
      return;
    }

    // 接口返回正常，先清除之前存储
    birthdayDataList.clear();

    if (responseModel.birthdayList.isEmpty) {
      birthdayDataList.clear();
      setState(() {});
      return;
    }
    birthdayDataList = responseModel.birthdayList;

    selectedItemList.clear();
    selectedParamList.clear();
    setState(() {});
  }

  Future<void> _delRequest() async {
    String accountTokenStr = '';
    try {
      final OauthData oauthData = await User.getOauthData();
      accountTokenStr = oauthData.user_center_access_token;
    } catch (e) {
      DevLogger.error(tag: 'FamilyBirthday', msg: 'getOauthData $e');
    }

    final Map<String, Object> headerMap = <String, Object>{};
    headerMap['accountToken'] = accountTokenStr;

    String familyId;
    try {
      final FamilyModel familyModel = await Family.getCurrentFamily();
      familyId = familyModel.familyId;
    } catch (e) {
      DevLogger.error(tag: 'FamilyBirthday', msg: 'getCurrentFamily $e');
      familyId = '';
    }

    final Map<String, dynamic> param = <String, dynamic>{
      'familyId': familyId,
      'ids': selectedParamList
    };

    final UhomeResponseModel? responseModel =
        await Service().delFamilyBirthday(param);
    DevLogger.info(
        tag: 'BirthdayEdit', msg: 'xiaou_birth_delBirthdayData $responseModel');

    _isDelRequesting = false;
    if (responseModel == null) {
      ToastHelper.showToast('删除失败，请稍后再试');
      return;
    }
    if (responseModel.retCode == '09007') {
      ToastHelper.showToast('仅支持编辑本人创建的信息');

      // 此错误码时，为了保证数据显示的正确性，需要重新刷新列表数据
      isEditStatus = false;
      isAllSelected = false;
      _requestBirthdayData();
      return;
    }
    if (responseModel.retCode != '00000') {
      ToastHelper.showToast('删除失败，请稍后再试');
      return;
    } else {
      // 删除成功，退出编辑状态
      isEditStatus = false;
      isAllSelected = false;
      _requestBirthdayData();
    }
  }

  Widget _noDataWidget() {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          SizedBox(height: 208.w),
          Image.asset(
            'assets/images/xiaou_birthday_nobody.png',
            width: 64.w,
            height: 64.w,
            package: Constant.packageName,
            fit: BoxFit.cover,
          ),
          SizedBox(height: 6.w),
          Text(
            '暂无家人生日信息哦',
            style: TextStyle(
              fontSize: 17.sp,
              color: const Color.fromARGB(255, 0x99, 0x99, 0x99),
            ),
          ),
          SizedBox(height: 8.w),
          Container(
            width: 279.w,
            height: 36.w,
            alignment: Alignment.center,
            child: Text(
              '添加生日信息后，小优会及时提醒您家人的生日，为您定制生日场景哦',
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color.fromARGB(255, 0x00, 0x00, 0x00)
                    .withOpacity(0.39),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 12.w),
          ClipRRect(
            borderRadius: BorderRadius.circular(15.0),
            child: GestureDetector(
              child: Container(
                width: 133.w,
                height: 30.w,
                color: const Color.fromARGB(255, 0x22, 0x83, 0xE2),
                alignment: Alignment.center,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Image.asset(
                      'assets/images/xiaou_birthday_add.png',
                      width: 13.w,
                      height: 13.w,
                      package: Constant.packageName,
                      fit: BoxFit.cover,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      '添加家人生日',
                      style: TextStyle(fontSize: 11.sp, color: Colors.white),
                    ),
                  ],
                ),
              ),
              onTap: throttle(() async {
                _openEditWidget();
              }) as void Function()?,
            ),
          )
        ],
      ),
    );
  }

  Widget _birthdayList() {
    return Container(
      padding: EdgeInsets.only(top: 8.w),
      color: const Color.fromRGBO(242, 242, 242, 1),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: ListView.separated(
                itemBuilder: (BuildContext context, int index) {
                  return FamilyBirthdayItem(
                    nickName: birthdayDataList[index].nickName,
                    birthday: birthdayDataList[index].birthday,
                    id: birthdayDataList[index].id,
                    iotUserId: birthdayDataList[index].iotUserId,
                    isEditStatus: isEditStatus,
                    editClick: () {
                      _openEditWidget(
                        id: birthdayDataList[index].id,
                        nickName: birthdayDataList[index].nickName,
                        birthday: birthdayDataList[index].birthday,
                      );
                    },
                    refreshSelectStatus: () {
                      // item选取/取消时，刷新页面
                      // 手动勾选全部item，显示全不选；手动去掉所有选中，显示全选
                      if (selectedItemList.length == birthdayDataList.length) {
                        isAllSelected = true; // 已全选，按钮显示全不选
                      } else {
                        isAllSelected = false; // 未全选，按钮显示全选
                      }
                      setState(() {});
                    },
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return Container(
                    color: Colors.white,
                    height: 0.5.w,
                    child: Container(
                      margin: EdgeInsets.only(left: 16.w),
                      height: 0.5.w,
                      color: const Color.fromRGBO(242, 242, 242, 1),
                    ),
                  );
                },
                itemCount: birthdayDataList.length),
          ),
          if (isEditStatus)
            Padding(
              padding: EdgeInsets.only(top: 11.w, bottom: 34.w),
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: GestureDetector(
                  onTap: () {
                    // 删除
                    if (selectedItemList.isNotEmpty) {
                      AlertCommonDialog.showConfirmAlertDialog(context,
                          title: '温馨提示',
                          info: '确认删除该条生日记录?',
                          cancelTitle: '取消',
                          confirmTitle: '确定', confirmCallback: () {
                        Network
                            .checkNetwork
                            .then((IsOnline onValue) {
                          if (result == NetworkStatusfalse) {
                            ToastHelper.showToast(Constant.netWorkErrorMsg);
                          } else {
                            if (!_isDelRequesting) {
                              _isDelRequesting = true;
                              _delRequest();
                            } else {
                              DevLogger.info(
                                  tag: 'FamilyBirthday',
                                  msg: 'last request is not done');
                            }
                          }
                        });
                      }, cancelCallback: () {});
                    } else {
                      ToastHelper.showToast('请选择要删除的信息');
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(left: 16.w, right: 16.w),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: const Color.fromRGBO(242, 242, 242, 1),
                        borderRadius: BorderRadius.circular(23.0),
                        border: Border.all(
                          color: const Color.fromARGB(255, 0xED, 0x28, 0x56),
                          width: 0.5,
                        ),
                      ),
                      child: Container(
                        height: 46.w,
                        alignment: Alignment.center,
                        child: Text(
                          '删除',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: const Color.fromARGB(255, 0xED, 0x28, 0x56),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            )
          else
            Padding(
              padding: EdgeInsets.only(top: 11.w, bottom: 34.w),
              child: Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(23.0),
                  child: GestureDetector(
                    onTap: throttle(() async {
                      _openEditWidget();
                    }) as void Function()?,
                    child: Container(
                      height: 46.w,
                      color: const Color.fromARGB(255, 0x22, 0x83, 0xE2),
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Image.asset(
                            'assets/images/xiaou_birthday_add.png',
                            width: 20.w,
                            height: 20.w,
                            package: Constant.packageName,
                            fit: BoxFit.cover,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '添加家人生日',
                            style:
                                TextStyle(fontSize: 17.sp, color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ToastHelper.init(context);
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => Scaffold(
            appBar: AppBar(
              elevation: 0,
              titleSpacing: 0,
              backgroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                systemNavigationBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
              ),
              centerTitle: true,
              title: Text(
                '家人生日',
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 18.0.sp,
                    fontWeight: FontWeight.w500,
                    height: 1.4),
              ),
              leading: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.black,
                  ),
                  onPressed: () {
                    if (isEditStatus) {
                      // 如果当前是编辑模式，则退出编辑模式
                      setState(() {
                        isEditStatus = false;
                        isAllSelected = false;
                        selectedItemList.clear();
                        selectedParamList.clear();
                      });
                    } else {
                      Vdn.close();
                    }
                  }),
              actions: <Widget>[
                if (birthdayDataList.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (isEditStatus) {
                          if (!isAllSelected) {
                            // 全选
                            selectedItemList.clear();
                            selectedParamList.clear();
                            for (final BirthdayModel model
                                in birthdayDataList) {
                              selectedItemList.add(model.id);
                              selectedParamList.add(<String, int?>{
                                'id': model.id,
                                'iotUserId': model.iotUserId
                              });
                            }
                            isAllSelected = true;
                          } else {
                            // 全不选
                            selectedItemList.clear();
                            selectedParamList.clear();
                            isAllSelected = false;
                          }
                        } else {
                          //切换为可删除模式
                          isEditStatus = true;
                        }
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(right: 16.w),
                      child: isEditStatus
                          ? Text(
                              isAllSelected ? '全不选' : '全选',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14.w,
                                color: const Color(0xFF2283E2),
                              ),
                            )
                          : Text(
                              '管理',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14.w,
                                color: const Color(0xFF2283E2),
                              ),
                            ),
                    ),
                  )
                else
                  Container()
              ],
            ),
            body:
                birthdayDataList.isEmpty ? _noDataWidget() : _birthdayList()));
  }

  // 打开新增/编辑页面
  void _openEditWidget({int? id, String? nickName, String? birthday}) {
    Navigator.push(
      context,
      MaterialPageRoute<dynamic>(
        builder: (BuildContext context) {
          return BirthdayEdit(
            id: id,
            nickname: nickName,
            birthday: birthday,
            refreshDataCallback: () {
              // 需要刷新列表数据
              _requestBirthdayData();
            },
          );
        },
      ),
    );
  }
}
