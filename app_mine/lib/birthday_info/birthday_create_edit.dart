import 'package:app_mine/service/service.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_picker/Picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart';

import 'common_util.dart';

// ignore: must_be_immutable
class BirthdayEdit extends StatefulWidget {
  int? id;

  String? nickname;

  String? birthday;

  int currentType = 0; // 0代表新增，1代表编辑

  String? currentMonth; //用于临时存储服务器生日数据转成的月，或 生日弹窗选择的月份

  String? currentDay; // 同上

  VoidCallback? refreshDataCallback;

  BirthdayEdit(
      {super.key,
      this.id,
      this.nickname,
      this.birthday,
      this.refreshDataCallback});

  @override
  State<StatefulWidget> createState() {
    return _BirthdayEditState();
  }
}

class _BirthdayEditState extends State<BirthdayEdit> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  TextEditingController nickNameEditController = TextEditingController();

  List<int> dateType = <int>[1, 2];

  bool _isRequesting = false; // 防止接口返回之前，重复请求

  @override
  void initState() {
    super.initState();
    _fillData();
    _isRequesting = false;
  }

  @override
  void dispose() {
    nickNameEditController.dispose();
    super.dispose();
  }

  void _fillData() {
    if (widget.nickname != null) {
      widget.currentType = 1;
      nickNameEditController.text = widget.nickname!;
    }
    if (widget.birthday != null) {
      widget.currentType = 1;
      List<String> birthList = widget.birthday!.split('-');
      widget.currentMonth = birthList[1];
      widget.currentDay = birthList[2];
    }
  }

  Future<void> _submitRequest() async {
    String accountTokenStr = '';
    try {
      final OauthData oauthData = await User.getOauthData();
      accountTokenStr = oauthData.user_center_access_token;
    } catch (e) {
      DevLogger.error(tag: 'BirthdayEdit', msg: 'getOauthData err: $e');
    }

    if (widget.currentType == 1) {
      // 编辑
      final Map<String, Object> headerMap = <String, Object>{};
      headerMap['accountToken'] = accountTokenStr;

      String familyId;
      try {
        final FamilyModel familyModel = await Family.getCurrentFamily();
        familyId = familyModel.familyId;
      } catch (e) {
        DevLogger.error(tag: 'BirthdayEdit', msg: 'getCurrentFamily err: $e');
        familyId = '';
      }

      final Map<String, dynamic> param = <String, dynamic>{
        'id': widget.id,
        'nickName': widget.nickname,
        'birthday': widget.birthday,
        'familyId': familyId,
      };

      final UhomeResponseModel? responseModel =
          await Service().editFamilyBirthday(param);

      DevLogger.info(
          tag: 'BirthdayEdit',
          msg: 'xiaou_birth_editBirthdayData $responseModel');
      _isRequesting = false;
      if (responseModel == null) {
        ToastHelper.showToast('保存失败，请稍后再试');
        return;
      }

      if (responseModel.retCode == '09006') {
        ToastHelper.showToast('当前昵称无法使用，请重新修改');
        return;
      }
      if (responseModel.retCode == '09007') {
        ToastHelper.showToast('仅支持编辑本人创建的信息');
        return;
      }
      if (responseModel.retCode != '00000') {
        ToastHelper.showToast('保存失败，请稍后再试');
        return;
      } else {
        if (widget.refreshDataCallback != null) {
          widget.refreshDataCallback!();
        }
        if (mounted) {
          Navigator.pop(context);
        }
      }
    } else {
      // 新增
      final Map<String, Object> headerMap = <String, Object>{};
      headerMap['accountToken'] = accountTokenStr;

      String familyId;
      try {
        final FamilyModel familyModel = await Family.getCurrentFamily();
        familyId = familyModel.familyId;
      } catch (e) {
        DevLogger.error(tag: 'BirthdayEdit', msg: 'getCurrentFamily err: $e');

        familyId = '';
      }

      final Map<String, dynamic> param = <String, dynamic>{
        'nickName': widget.nickname,
        'birthday': widget.birthday,
        'familyId': familyId,
      };

      final UhomeResponseModel? responseModel =
          await Service().addFamilyBirthday(param);
      DevLogger.info(
          tag: 'BirthdayEdit',
          msg: 'xiaou_birth_addBirthdayData $responseModel');
      _isRequesting = false;
      if (responseModel == null) {
        ToastHelper.showToast('保存失败，请稍后再试');
        return;
      }
      if (responseModel.retCode == '09005') {
        ToastHelper.showToast('最多支持添加50位家人生日信息');
        return;
      }
      if (responseModel.retCode == '09006') {
        ToastHelper.showToast('当前昵称无法使用，请重新修改');
        return;
      }
      if (responseModel.retCode != '00000') {
        ToastHelper.showToast('保存失败，请稍后再试');
        return;
      } else {
        if (widget.refreshDataCallback != null) {
          widget.refreshDataCallback!();
        }
        if (mounted) {
          Navigator.pop(context);
        }
      }
    }
  }

  bool _specialStrCheck() {
    final String nickname = widget.nickname!;
    for (int i = 0; i < nickname.length; i++) {
      final String charText = nickname.substring(i, i + 1);
      if (!RegExp('[\u4e00-\u9fa5]|[0-9]').hasMatch(charText)) {
        // 存在非汉字/数字
        return true;
      }
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    ToastHelper.init(context);
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => Scaffold(
              key: _scaffoldKey,
              appBar: AppBar(
                elevation: 0,
                titleSpacing: 0,
                backgroundColor: Colors.white,
                systemOverlayStyle: const SystemUiOverlayStyle(
                  systemNavigationBarColor: Colors.white,
                  systemNavigationBarIconBrightness: Brightness.dark,
                  statusBarColor: Colors.transparent,
                  statusBarBrightness: Brightness.light,
                  statusBarIconBrightness: Brightness.dark),
                centerTitle: true,
                title: Text(
                  '添加家人',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 18.0.sp,
                      fontWeight: FontWeight.w500,
                      height: 1.4),
                ),
                leading: IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.black,
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    }),
                actions: <Widget>[
                  GestureDetector(
                    onTap: throttle(() async {
                      widget.nickname = nickNameEditController.text.trim();
                      ToastHelper.init(context);
                      if (widget.nickname == null || widget.nickname!.isEmpty) {
                        ToastHelper.showToast('请填写昵称');
                      } else if (_specialStrCheck()) {
                        ToastHelper.showToast('昵称仅支持中文和数字');
                      } else if (widget.birthday == null ||
                          widget.birthday!.isEmpty) {
                        ToastHelper.showToast('请填写生日');
                      } else {
                        // 请求接口
                        Network
                            .checkNetwork
                            .then((IsOnline onValue) {
                          if (result == NetworkStatusfalse) {
                            ToastHelper.showToast(Constant.netWorkErrorMsg);
                          } else {
                            if (!_isRequesting) {
                              _isRequesting = true;
                              _submitRequest();
                            } else {
                              DevLogger.info(
                                  tag: 'BirthdayEdit',
                                  msg: 'last request is not done');
                            }
                          }
                        });
                      }
                    }) as void Function()?,
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.only(right: 16.w),
                      child: Text(
                        '完成',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14.w,
                          color: const Color(0xFF2283E2),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              body: Container(
                padding: EdgeInsets.only(top: 8.w),
                color: const Color.fromRGBO(242, 242, 242, 1),
                child: Column(
                  children: <Widget>[
                    Container(
                      height: 44.w,
                      color: Colors.white,
                      padding: EdgeInsets.fromLTRB(16.w, 0, 0, 0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            '昵称',
                            style: TextStyle(
                                fontSize: 15.sp,
                                color: const Color(0x000000).withOpacity(0.8)),
                          ),
                          SizedBox(width: 17.w),
                          Expanded(
                            flex: 1,
                            child: TextField(
                              controller: nickNameEditController,
                              keyboardType: TextInputType.text,
                              textAlign: TextAlign.left,
                              decoration: InputDecoration(
                                hintText: '请输入昵称',
                                hintStyle: TextStyle(
                                  color:
                                      const Color(0x000000).withOpacity(0.26),
                                  fontSize: 15.sp,
                                ),
                                enabledBorder: const UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Colors.transparent),
                                ),
                                focusedBorder: const UnderlineInputBorder(
                                  borderSide:
                                      BorderSide(color: Colors.transparent),
                                ),
                                contentPadding: EdgeInsets.only(
                                    bottom: 2.5.h), // 临时处理输入框文本不能完全垂直居中问题
                              ),
                              inputFormatters: <LengthLimitingTextInputFormatter>[
                                // WhitelistingTextInputFormatter(
                                //     RegExp('[\u4e00-\u9fa5]|[0-9]')), //只能输入汉字或数字（ios上会导致ZHIJIAAPP-37616）
                                LengthLimitingTextInputFormatter(8),
                                //最大长度
                              ],
                              style: TextStyle(
                                color: const Color(0x000000).withOpacity(0.8),
                                fontSize: 15.sp,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    Container(
                      color: Colors.white,
                      height: 0.5.w,
                      child: Container(
                        margin: EdgeInsets.only(left: 16.w),
                        height: 0.5.w,
                        color: const Color.fromRGBO(242, 242, 242, 1),
                      ),
                    ),
                    Container(
                      height: 44.w,
                      color: Colors.white,
                      padding: EdgeInsets.fromLTRB(16.w, 0, 0, 0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            '生日',
                            style: TextStyle(
                                fontSize: 15.sp,
                                color: const Color(0x000000).withOpacity(0.8)),
                          ),
                          SizedBox(width: 17.w),
                          Expanded(
                            flex: 1,
                            child: GestureDetector(
                              onTap: () {
                                // 打开生日弹窗
                                FocusNode blankNode = FocusNode();
                                FocusScope.of(context)
                                    .requestFocus(blankNode); // 收起键盘
                                showPickerDateTime(context);
                              },
                              child: widget.birthday == null
                                  ? Text(
                                      '请输入生日',
                                      style: TextStyle(
                                        fontSize: 15.sp,
                                        color: const Color(0x000000)
                                            .withOpacity(0.26),
                                      ),
                                    )
                                  : Text(
                                      '${widget.currentMonth}月${widget.currentDay}日',
                                      style: TextStyle(
                                        fontSize: 15.sp,
                                        color: const Color(0x000000)
                                            .withOpacity(0.8),
                                      ),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  void showPickerDateTime(BuildContext context) {
    Picker(
        adapter: DateTimePickerAdapter(
            customColumnType: dateType,
            isNumberMonth: true,
            monthSuffix: '月',
            daySuffix: '日',
            value: DateTime(
                2004,
                widget.currentMonth == null
                    ? 1
                    : int.parse(widget.currentMonth!),
                widget.currentDay == null ? 1 : int.parse(widget.currentDay!))),
        // 2004是闰年，2月可选29日
        title: Text(
          '生日',
          style: TextStyle(
            fontSize: 17.sp,
            color: const Color.fromARGB(255, 0x33, 0x33, 0x33),
          ),
        ),
        textAlign: TextAlign.right,
        selectedTextStyle: const TextStyle(
          color: Color.fromARGB(255, 0x33, 0x33, 0x33),
        ),
        cancelText: '取消',
        confirmText: '确定',
        cancelTextStyle: TextStyle(
          fontSize: 17.sp,
          color: const Color.fromARGB(255, 0x00, 0x00, 0x00).withOpacity(0.39),
        ),
        // color: Color.fromARGB(255, 0x22, 0x83, 0xE2)),
        confirmTextStyle: TextStyle(
          fontSize: 17.sp,
          color: const Color.fromARGB(255, 0x22, 0x83, 0xE2),
        ),
        height: 260.w,
        itemExtent: 44.w,
        // headercolor: Colors.white,
        backgroundColor: Colors.white,
        // containerColor: Colors.white,
        headerDecoration: BoxDecoration(
          border: Border.all(color: Colors.white, width: 1.5.w),
          color: Colors.white,
          borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(15.0), topRight: Radius.circular(15.0)),
        ),
        selectionOverlay: _SelectItemBackgroundWidget(),
        squeeze: 1,
        onConfirm: (Picker picker, List<int>? value) {
          DevLogger.info(
              tag: 'BirthdayEdit',
              msg: 'xiaou_birth_picker confirm item: ${picker.adapter.text}');
          final String formatStr =
              picker.adapter.text.substring(0, 10); // 2022-2-14
          final List<String> ymdStrList = formatStr.split('-');
          if (ymdStrList.length != 3) {
            return;
          }
          setState(() {
            widget.currentMonth = ymdStrList[1];
            widget.currentDay = ymdStrList[2];
            widget.birthday =
                '1990-${widget.currentMonth}-${widget.currentDay}';
          });
        },
        onSelect: (Picker picker, int index, List<int>? selecteds) {
          setState(() {
            // stateText = picker.adapter.toString();
          });
        }).showModal<dynamic>(context, backgroundColor: Colors.transparent);
  }
}

class _SelectItemBackgroundWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Positioned(
          top: 0,
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: 1.w,
            color: const Color.fromRGBO(242, 242, 242, 1),
          ),
        ),
        // 1.此处使用Expanded会有异常，导致选中项灰屏；2.选中项在picker源码中会设置高度，此处设置无效
        Container(height: 42.w),
        Positioned(
          bottom: 0,
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: 1.w,
            color: const Color.fromRGBO(242, 242, 242, 1),
          ),
        ),
      ],
    );
  }
}
