import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'family_birthday.dart';

typedef EditClick = dynamic Function();
typedef SelectItem = dynamic Function(String id);
typedef UnselectItem = dynamic Function(String id);
typedef RefreshSelectStatus = dynamic Function();

// ignore: must_be_immutable
class FamilyBirthdayItem extends StatelessWidget {
  int? iotUserId;
  int? id;
  String? nickName;
  String? birthday;
  EditClick? editClick;
  bool? isEditStatus;
  bool? isSelected;
  String? currentMonth;
  String? currentDay;
  RefreshSelectStatus? refreshSelectStatus;

  FamilyBirthdayItem(
      {super.key,
      this.iotUserId,
      this.id,
      this.nickName,
      this.birthday,
      this.editClick,
      this.isEditStatus,
      this.refreshSelectStatus});

  bool _isSelectedItem() {
    if (selectedItemList.contains(id)) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    if (birthday != null) {
      final List<String> birthList = birthday!.split('-');
      if (birthList.length == 3) {
        currentMonth = birthList[1];
        currentDay = birthList[2];
      }
    }
    return Container(
      height: 44.w,
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(16.w, 0, 16.w, 0),
      child: Row(
        children: <Widget>[
          Text(
            nickName!,
            style: TextStyle(fontSize: 15.sp),
          ),
          const Expanded(
            flex: 1,
            child: SizedBox(),
          ),
          Text(
            '$currentMonth月$currentDay日',
            style: TextStyle(fontSize: 15.sp),
          ),
          Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: isEditStatus!
                ? GestureDetector(
                    onTap: () {
                      //选择/取消
                      if (_isSelectedItem()) {
                        selectedItemList.remove(id);
                        selectedParamList.removeWhere(
                            (Map<dynamic, dynamic> element) =>
                                element['id'] == id);
                      } else {
                        selectedItemList.add(id);
                        selectedParamList.add(
                            <String, int?>{'id': id, 'iotUserId': iotUserId});
                      }
                      refreshSelectStatus!();
                    },
                    child: _isSelectedItem()
                        ? Image.asset(
                            'assets/images/xiaou_birthday_selected.png',
                            width: 24.w,
                            height: 24.w,
                            package: Constant.packageName,
                            fit: BoxFit.cover,
                          )
                        : Image.asset(
                            'assets/images/xiaou_birthday_unselect.png',
                            width: 24.w,
                            height: 24.w,
                            package: Constant.packageName,
                            fit: BoxFit.cover,
                          ),
                  )
                : GestureDetector(
                    onTap: () {
                      editClick!();
                    },
                    child: Image.asset(
                      'assets/images/xiaou_birthday_edit.png',
                      width: 20.w,
                      height: 20.w,
                      package: Constant.packageName,
                      fit: BoxFit.cover,
                    ),
                  ),
          )
        ],
      ),
    );
  }
}
