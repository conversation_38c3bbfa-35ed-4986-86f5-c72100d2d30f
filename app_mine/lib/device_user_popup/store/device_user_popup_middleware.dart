import 'package:app_mine/device_user_popup/store/device_user_popup_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';

final List<Middleware<AppMineState>> deviceUserPopupMiddlewares =
    <Middleware<AppMineState>>[
  TypedMiddleware<AppMineState, WriteDeviceUserPopupShowedStorageAction>(
          writeDeviceUserPopupStorageMiddleware)
      .call
];

/// 写入网器用户弹窗已经弹过缓存middleware
void writeDeviceUserPopupStorageMiddleware(
  Store<AppMineState> store,
  WriteDeviceUserPopupShowedStorageAction action,
  NextDispatcher next,
) {
  _writeDeviceUserPopupStorage();
  next(action);
}

/// 写入网器用户弹窗已经弹过缓存
Future<void> _writeDeviceUserPopupStorage() async {
  try {
    await Storage.putBooleanValue(Constant.deviceUserPopupShowedStorageKey, true);
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName,
        msg: '_writeDeviceUserPopupStorage err: $err');
  }
}
