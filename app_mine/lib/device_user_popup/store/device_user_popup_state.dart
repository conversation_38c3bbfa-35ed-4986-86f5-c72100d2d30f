

/// 网器用户弹窗状态
class DeviceUserState {

  /// 是否显示过弹窗
  bool showed = true;

  /// 清空小红点状态
  void clear() {
    showed = true;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceUserState &&
          runtimeType == other.runtimeType &&
          showed == other.showed;

  @override
  int get hashCode => showed.hashCode;

  @override
  String toString() {
    return 'DeviceUserState{showed: $showed}';
  }
}
