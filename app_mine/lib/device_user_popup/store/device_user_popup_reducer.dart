import 'package:app_mine/device_user_popup/store/device_user_popup_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

final List<Reducer<AppMineState>> deviceUserPopupReducer =
    <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateDeviceUserPopupShowedAction>(
          _updateDeviceUserPopupShowedReducer)
      .call,
  TypedReducer<AppMineState, WriteDeviceUserPopupShowedStorageAction>(
          _updateDevicePopupShowedReducer)
      .call,
];

/// 更新网器用户弹窗是否显示过reducer
AppMineState _updateDeviceUserPopupShowedReducer(
    AppMineState state, UpdateDeviceUserPopupShowedAction action) {
  state.deviceUserState.showed = action.showed;
  return state;
}

AppMineState _updateDevicePopupShowedReducer(
    AppMineState state, WriteDeviceUserPopupShowedStorageAction action) {
  state.deviceUserState.showed = true;
  return state;
}
