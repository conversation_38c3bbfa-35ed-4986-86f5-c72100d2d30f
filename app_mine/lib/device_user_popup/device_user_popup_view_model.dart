import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

/// 网器用户 viewModel
class DeviceUserViewModel {
  DeviceUserViewModel(
      {required this.show});

  /// [show] 是否显示
  final bool show;

  /// 组装viewModel的数据
  static DeviceUserViewModel fromStore(Store<AppMineState> store) {
    return DeviceUserViewModel(
      /// 没有显示过弹窗 并且 是网器用户
      show: !store.state.deviceUserState.showed && store.state.currentFamilyState.deviceUser
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceUserViewModel &&
          runtimeType == other.runtimeType &&
          show == other.show;

  @override
  int get hashCode => show.hashCode;

  @override
  String toString() {
    return 'DeviceUserViewModel{show: $show}';
  }
}
