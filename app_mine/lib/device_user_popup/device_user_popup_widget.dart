import 'dart:async';

import 'package:app_mine/device_user_popup/device_user_popup_view_model.dart';
import 'package:app_mine/device_user_popup/store/device_user_popup_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:redux/redux.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';

/// 网器用户弹框组件
class DeviceUserPopupWidget extends StatelessWidget {
  const DeviceUserPopupWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, DeviceUserViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            DeviceUserViewModel.fromStore(store),
        builder: (BuildContext context, DeviceUserViewModel vm) {
          return Visibility(
              visible: vm.show,

              /// 包裹一层，可以只在弹窗出现的时候添加监听，组件消失的时候取消监听
              child: const _DeviceUserPopup());
        });
  }
}

/// 网器用户弹框StatefulWidget
class _DeviceUserPopup extends StatefulWidget {
  const _DeviceUserPopup({super.key});

  @override
  State<_DeviceUserPopup> createState() => _DeviceUserPopupState();
}

class _DeviceUserPopupState extends State<_DeviceUserPopup> {
  StreamSubscription<HomeTabTapEvent>? homeTabTapSub;
  @override
  void initState() {
    gioTrack(Constant.deviceUserPopupInitGio);

    /// 添加tab点击的监听
    homeTabTapSub =
        UIMessage.sub<HomeTabTapEvent>((HomeTabTapEvent event) async {
      final int tabTapped = event.tabTapIndex ?? -1;

      /// 如果点击其他tab(非我的tab)，则隐藏弹窗
      if (tabTapped != 3) {
        appMineStore.dispatch(WriteDeviceUserPopupShowedStorageAction());
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    homeTabTapSub?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        /// 隐藏弹框
        appMineStore.dispatch(WriteDeviceUserPopupShowedStorageAction());
      },
      child: Scaffold(
          backgroundColor: const Color(0xFF000000).withOpacity(0.6),
          body: Center(
            child: SizedBox(
              width: 375.w,
              height: 375.w,
              child: Image.asset(
                'assets/images/device_user_popup.png',
                package: Constant.packageName,
                fit: BoxFit.fill,
              ),
            ),
          )),
    );
  }
}
