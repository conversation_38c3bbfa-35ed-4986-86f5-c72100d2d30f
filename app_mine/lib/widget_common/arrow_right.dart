import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';

/// 右箭头
class ArrowRight extends StatelessWidget {
  const ArrowRight(
      {super.key, this.width, this.height, this.imagePath, this.color});

  /// [width] 宽度
  final double? width;

  /// [height] 高度
  final double? height;

  /// [imagePath] 本地图片的路径
  final String? imagePath;

  /// [color] 颜色
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath ?? 'assets/images/arrow_right.webp',
      color: color,
      width: width ?? 16,
      height: height ?? 16,
      package: Constant.packageName,
      fit: BoxFit.cover,
    );
  }
}
