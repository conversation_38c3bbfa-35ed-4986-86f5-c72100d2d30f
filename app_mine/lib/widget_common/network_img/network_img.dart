import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/widget_common/network_img/network_img_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class NetworkImg extends StatelessWidget {
  const NetworkImg({
    super.key,
    required this.url,
    this.width,
    this.height,
    this.fit,
    this.placeHolder,
    this.errorWidget,
  });

  final String url;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeHolder;
  final Widget? errorWidget;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, NetworkImgViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            NetworkImgViewModel.fromStore(store),
        builder: (BuildContext context, NetworkImgViewModel vm) {
          return CommonNetWorkImage(
            url: url,
            width: width,
            height: height,
            fit: fit,
            placeHolder: placeHolder,
            errorWidget: errorWidget,
            needReload: vm.refreshCount,
          );
        });
  }
}
