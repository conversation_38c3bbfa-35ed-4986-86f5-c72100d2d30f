import 'package:app_mine/store/mine_state.dart';
import 'package:redux/redux.dart';

class NetworkImgViewModel {
  NetworkImgViewModel({required this.refreshCount});

  final int refreshCount;

  /// 组装viewModel的数据
  static NetworkImgViewModel fromStore(Store<AppMineState> store) {
    return NetworkImgViewModel(
      refreshCount: store.state.refreshCount,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NetworkImgViewModel &&
          runtimeType == other.runtimeType &&
          refreshCount == other.refreshCount;

  @override
  int get hashCode => refreshCount.hashCode;

  @override
  String toString() {
    return 'NetworkImgViewModel{refreshCount: $refreshCount}';
  }
}
