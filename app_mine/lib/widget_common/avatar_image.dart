import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/utils/ui_const.dart';
import 'package:app_mine/widget_common/network_img/network_img.dart';
import 'package:flutter/material.dart';

/// 头像主图
class AvatarImage extends StatelessWidget {
  const AvatarImage({
    super.key,
    required this.avatarUrl,
    this.width,
    this.border,
    this.hasBoxShadow = false,
    this.defaultImagePath,
  });

  final String avatarUrl;

  final double? width;

  final BoxBorder? border;

  final bool hasBoxShadow;

  final String? defaultImagePath;

  static const BoxShadow _avatarBoxShadow = BoxShadow(
    color: Color.fromRGBO(0, 0, 0, .2),
    blurRadius: 3,
  );

  @override
  Widget build(BuildContext context) {
    // 头像宽
    return Container(
      width: width ?? UIConst.avatarSize,
      height: width ?? UIConst.avatarSize,
      decoration: BoxDecoration(
          border: border,
          boxShadow: hasBoxShadow ? const <BoxShadow>[_avatarBoxShadow] : null,
          borderRadius: BorderRadius.circular(32)),
      child: ClipOval(
          child: avatarUrl != ''
              ? NetworkImg(
                  url: avatarUrl,
                  width: width ?? UIConst.avatarSize,
                  height: width ?? UIConst.avatarSize,
                  fit: BoxFit.fill,
                  placeHolder: DefaultAvatar(imagePath: defaultImagePath),
                  errorWidget: DefaultAvatar(imagePath: defaultImagePath))
              : DefaultAvatar(imagePath: defaultImagePath)),
    );
  }
}

/// 默认头像
class DefaultAvatar extends StatelessWidget {
  const DefaultAvatar({
    super.key,
    this.imagePath,
  });

 final String? imagePath;
  @override
  Widget build(BuildContext context) {
    return Image.asset(
      imagePath ?? 'assets/images/avatar.webp',
      package: Constant.packageName,
      fit: BoxFit.cover,
    );
  }
}
