import 'package:badges/badges.dart' as Badges;
import 'package:flutter/material.dart';

class MyBadge extends StatelessWidget {
  const MyBadge({super.key, this.padding, this.child});

  final EdgeInsetsGeometry? padding;

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Badges.Badge(
                      toAnimate: false,
                      padding: padding ?? const EdgeInsets.all(4),
                      badgeColor: const Color(0xFFED2856),
                      position: Badges.BadgePosition.topEnd(top: 0, end: 0),
                      elevation: 0,
                      child: child);
  }
}