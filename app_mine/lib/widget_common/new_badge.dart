import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

/// NEW 标签
class NewBadge extends StatelessWidget {
  const NewBadge({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: 39,
      height: 14,
      decoration: BoxDecoration(
        color: AppSemanticColors.item.success.primary,
        borderRadius: const BorderRadius.all(Radius.circular(7.0)),
      ),
      child: const Center(
        child: Text(
          'NEW',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            height: 1,
          ),
        ),
      ),
    );
  }
}
