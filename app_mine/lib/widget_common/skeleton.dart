import 'package:flutter/material.dart';

class Skeleton extends StatelessWidget {
  const Skeleton(
      {super.key,
      required this.width,
      required this.height,
      this.borderRadius});

  final double width;
  final double height;
  final BorderRadiusGeometry? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            borderRadius: borderRadius,
            color: const Color.fromRGBO(0, 0, 0, .05)),
        width: width,
        height: height);
  }
}
