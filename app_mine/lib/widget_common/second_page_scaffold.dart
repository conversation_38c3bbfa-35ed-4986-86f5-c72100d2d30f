import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:vdn/vdn.dart';

/// 二级页面Scaffold
class SecondPageScaffold extends StatelessWidget {
  const SecondPageScaffold(
      {super.key, required this.title, required this.child});

  final String title;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => Scaffold(
            appBar: AppBar(
              elevation: 0,
              titleSpacing: 0,
              backgroundColor: Colors.white,
              systemOverlayStyle: const SystemUiOverlayStyle(
                systemNavigationBarColor: Colors.white,
                systemNavigationBarIconBrightness: Brightness.dark,
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark),
              centerTitle: true,
              title: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18.w,
                  color: const Color(0xFF111111),
                  height: 1.33,
                ),
              ),
              leading: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.black,
                  ),
                  onPressed: () {
                    Vdn.close();
                  }),
            ),
            body: Container(
                padding: EdgeInsets.only(top: 8.w),
                color: const Color.fromRGBO(242, 242, 242, 1),
                child: child)));
  }
}
