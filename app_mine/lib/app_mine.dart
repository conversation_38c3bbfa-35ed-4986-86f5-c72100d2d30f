import 'package:app_mine/app_mine_presenter.dart';
import 'package:app_mine/current_family/family_card.dart';
import 'package:app_mine/device_user_popup/device_user_popup_widget.dart';
import 'package:app_mine/list_menu/list_menu.dart';
import 'package:app_mine/navbar/navbar_widget.dart';
import 'package:app_mine/navbar/store/navbar_action.dart';
import 'package:app_mine/score_popup/score_pop_up_widget.dart';
import 'package:app_mine/score_popup/score_popup.dart';
import 'package:app_mine/store/mine_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/user_info/user_info_widget.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/utils/util.dart';
import 'package:app_mine/widget_common/network_unavailable.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:eshop_widgets/eshop_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'store/mine_store.dart';
import 'widget_common/bold_text_style.dart';

class AppMine extends StatefulWidget {
  const AppMine({super.key});

  @override
  State<AppMine> createState() => _AppMineState();
}

class _AppMineState extends State<AppMine> with AutomaticKeepAliveClientMixin {
  final AppMinePresenter _presenter = AppMinePresenter();

  final EasyRefreshController _refreshController = EasyRefreshController(
    controlFinishRefresh: true,
  );

  final ValueKey<String> _customScrollViewKey =
      const ValueKey<String>('app_mine_custom_scroll_view');

  /// [_shouldCallDidAppear] 是否应该执行didAppear里的回调
  bool _shouldCallDidAppear = true;

  void didAppear([Map<dynamic, dynamic>? params]) {
    DevLogger.info(
        tag: Constant.packageName,
        msg: 'didAppear _shouldCallDidAppear:$_shouldCallDidAppear');

    /// 设置状态栏
    setStatusBarStyle();

    /// 点击我的tab判断是否需要显示五星弹窗
    if (params?['fromTabClick'] == true) {
      ScorePopup().showScorePopup(context);
    }

    /// 进入页面埋点
    pageIn();

    if (_shouldCallDidAppear) {
      _presenter.initAppData();
      _presenter.addPluginListeners();

      /// 查询网络状态
      _checkNetwork();

      /// 初始化Toast的context并设置可以toast
      initToast(context);
    } else {
      /// 为下一次 didAppear 执行准备
      _shouldCallDidAppear = true;
    }
  }

  void didDisappear() {
    /// 离开页面埋点
    pageLeave(title: '我的');

    _presenter.removePluginListeners();

    DevLogger.info(tag: Constant.packageName, msg: 'didDisappear');
  }

  @override
  void initState() {
    super.initState();
    _presenter.initAppData(initState: true);

    /// 添加持久监听
    _presenter.addPersistentPluginListener();

    /// 添加didAppear时生效，disAppear时取消的监听
    _presenter.addPluginListeners();

    /// 初始化Toast的context并设置可以toast
    initToast(context);

    /// 查询网络状态
    _checkNetwork();

    /// 避免didAppear执行回调
    _shouldCallDidAppear = false;
    pageIn();
  }

  /// 设置状态栏样式
  void setStatusBarStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  /// 查询网络状态
  void _checkNetwork() {
    Network.isOnline().then((IsOnline onValue) {
      DevLogger.info(
          tag: Constant.packageName, msg: 'checkNetwork result:$result');
      appMineStore
          .dispatch(UpdateNetworkAction(isOnline.isOnline));
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ScreenUtilInit(
        designSize: const Size(375, 812),
        builder: () => StoreProvider<AppMineState>(
            store: appMineStore,
            child: Stack(
              children: <Widget>[
                const TabBgWidget(),
                Scaffold(
                  appBar: PreferredSize(
                      child: const NavBarWidget(),
                      preferredSize: Size(MediaQuery.of(context).size.width,
                          44 + MediaQuery.of(context).padding.top)),
                  backgroundColor: Colors.transparent,
                  body: EasyRefresh.builder(
                      controller: _refreshController,

                      /// 修改阻尼系数
                      spring: SpringDescription.withDampingRatio(
                          ratio: 0.8, mass: 1.0, stiffness: 500),
                      clipBehavior: Clipfalse,
                      fit: StackFit.expand,
                      header: LottieHeader(
                          triggerOffset: 56,
                          safeArea: false,
                          clamping: true,
                          position: IndicatorPosition.locator,
                          textColor: const Color(0xFF111111)),
                      onRefresh: () async {
                        final IsOnline isOnline =
                            await Network.isOnline();

                        /// 更新网络状态
                        appMineStore.dispatch(UpdateNetworkAction(
                            isOnline.isOnline));
                        if (!isOnline.isOnline) {
                          ToastHelper.showToast(Constant.netWorkErrorMsg);
                          _refreshController.finishRefresh();
                          return;
                        }
                        gioTrack(Constant.easyRefreshGio,
                            <String, String>{'value': '我的'});
                        _presenter.initAppData(refresh: true);
                        appMineStore.dispatch(UpdateRefreshAction());
                        _refreshController.finishRefresh();
                      },
                      childBuilder:
                          (BuildContext context, ScrollPhysics physics) {
                        return Padding(
                          /// 底部安全区包括BottomNavigationBar的高
                          padding: EdgeInsets.only(
                              bottom: MediaQuery.of(context).padding.bottom),
                          child: DefaultTextStyle(
                            style: TextStyle(
                              color: AppSemanticColors.item.primary,
                              fontFamilyFallback: fontFamilyFallback(),
                            ),
                            child: NotificationListener<ScrollNotification>(
                              onNotification:
                                  (ScrollNotification notification) {
                                appMineStore.dispatch(UpdateScrollOffsetAction(
                                    notification.metrics.pixels));
                                return false;
                              },
                              child: CustomScrollView(
                                key: _customScrollViewKey,
                                physics: const AlwaysScrollableScrollPhysics()
                                    .applyTo(physics),
                                slivers: const <Widget>[
                                  HeaderLocator.sliver(clearExtent: false),

                                  SliverToBoxAdapter(
                                      child: NetUnavailableWidget()),

                                  /// 用户
                                  SliverToBoxAdapter(child: UserInfoWidget()),

                                  SliverToBoxAdapter(
                                      child: Column(children: <Widget>[
                                        /// 家庭
                                        FamilyCardWidget(),
                                      
                                        /// 功能列表区
                                        ListMenuWidget()
                                      ]))
                                ],
                              ),
                            ),
                          ),
                        );
                      }),
                ),

                /// 网器用户弹窗
                const DeviceUserPopupWidget(),

                /// 五星评价弹窗
                const ScorePopupWidget(),
              ],
            )));
  }

  @override
  void dispose() {
    _presenter.removePersistentPluginListeners();
    _presenter.removePluginListeners();
    _refreshController.dispose();

    DevLogger.info(tag: Constant.packageName, msg: '----AppMine dispose-----');
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
