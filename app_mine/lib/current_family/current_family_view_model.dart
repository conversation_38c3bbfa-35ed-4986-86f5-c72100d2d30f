import 'package:app_mine/store/mine_state.dart';
import 'package:device_utils/compare/compare.dart';
import 'package:redux/redux.dart';

/// 导航栏 viewModel
class CurrentFamilyViewModel {
  CurrentFamilyViewModel({
    required this.isLogin,
    required this.familyName,
    required this.memberCount,
    required this.deviceCount,
    required this.avatarList,
    required this.familyId,
  });

  /// [isLogin] 是否登录
  final bool isLogin;

  /// [familyName] 家庭名称
  String familyName;

  /// [memberCount] 家庭成员数量
  int memberCount;

  /// [deviceCount] 设备数量
  int deviceCount;

  /// [avatarList] 头像列表
  List<String> avatarList;

  String familyId;

  /// 组装viewModel的数据
  static CurrentFamilyViewModel fromStore(Store<AppMineState> store) {
    return CurrentFamilyViewModel(
      isLogin: store.state.isLogin,
      familyName: store.state.currentFamilyState.familyName,
      memberCount: store.state.currentFamilyState.memberCount,
      deviceCount: store.state.currentFamilyState.deviceCount,
      avatarList: store.state.currentFamilyState.avatarList,
      familyId: store.state.currentFamilyState.familyId,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CurrentFamilyViewModel &&
          runtimeType == other.runtimeType &&
          isLogin == other.isLogin &&
          familyName == other.familyName &&
          memberCount == other.memberCount &&
          deviceCount == other.deviceCount &&
          isListEqual(avatarList, other.avatarList)&&
          familyId == other.familyId;

  @override
  int get hashCode =>
      isLogin.hashCode ^
      familyName.hashCode ^
      memberCount.hashCode ^
      deviceCount.hashCode ^
      listHashCode(avatarList) ^
      familyId.hashCode;

  @override
  String toString() {
    return 'CurrentFamilyViewModel{isLogin: $isLogin, familyName: $familyName, memberCount: $memberCount, deviceCount: $deviceCount, avatarList: $avatarList, familyId: $familyId}';
  }
}
