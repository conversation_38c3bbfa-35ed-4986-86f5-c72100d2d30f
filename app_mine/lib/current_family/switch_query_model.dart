import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class SwitchQueryResponseModel extends UhomeResponseModel {
  SwitchQueryResponseModel.fromJson(super.json) : super.fromJson() {
    statusModel = StatusModel.fromJson(super.retData) as StatusModel?;
  }

  StatusModel? statusModel;
}

class StatusModel {
  String? status = '';

  StatusModel.fromJson(Map<dynamic, dynamic> json) {
    status = json.nullableStringValueForKey('status');
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StatusModel &&
          runtimeType == other.runtimeType &&
          status == other.status;

  @override
  int get hashCode => status.hashCode;

  @override
  String toString() {
    return 'StatusModel{status: $status}';
  }
}
