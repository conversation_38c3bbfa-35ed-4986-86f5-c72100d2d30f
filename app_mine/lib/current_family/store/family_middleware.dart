import 'package:app_mine/current_family/store/family_action.dart';
import 'package:app_mine/service/service.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:redux/redux.dart';
import 'package:upservice/model/uhome_response_model.dart';
import 'package:user/user.dart';

class FamilyMiddleware implements MiddlewareClass<AppMineState> {
  @override
  dynamic call(
      Store<AppMineState> store, dynamic action, NextDispatcher next) async {
    if (action is UpdateUpgradeBadgeOperateAction) {
      final UhomeResponseModel? responseModel =
          await Service().familyCardUpgradeSwitchOperate(<String, dynamic>{
        'switchType': Constant.upgradeBadgeSwitchType,
        'switchKey': User.getOauthDataSync()?.uc_user_id,
        'status': Constant.statusClose,
      });
      if (responseModel?.retCode == '00000') {
        store.dispatch(UpdateUpgradeBadgeVisibleAction(false));
      }
    }
    next(action);
  }
}
