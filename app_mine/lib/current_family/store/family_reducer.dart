import 'package:app_mine/current_family/store/family_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:family/family_model.dart';
import 'package:family/member_model.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:redux/redux.dart';


final List<Reducer<AppMineState>> currentFamilyReducer = <Reducer<AppMineState>>[
  TypedReducer<AppMineState, UpdateCurrentFamilyInfoAction>(_updateCurrentFamilyInfoReducer).call,
  TypedReducer<AppMineState, UpdateDeviceAction>(_updateDeviceReducer).call,
  TypedReducer<AppMineState, UpdateUpgradeBadgeVisibleAction>(
          _updateUpgradeBadgeVisible)
      .call,
];


/// 更新当前家庭reducer
AppMineState _updateCurrentFamilyInfoReducer(
    AppMineState state, UpdateCurrentFamilyInfoAction action) {
  final FamilyModel familyModel = action.familyModel;
  state.currentFamilyState.familyName = familyModel.info.familyName;
  state.currentFamilyState.familyId = familyModel.familyId;
  final List<MemberModel> memberList = familyModel.members;
  memberList
      .removeWhere((MemberModel element) => element.memberInfo.virtualUserFlag);
  state.currentFamilyState.memberCount = memberList.length;
  final List<String> avatarList = <String>[];
  final int len = memberList.length > 4 ? 4 : memberList.length;
  for (int i = 0; i < len; i++) {
    avatarList.add(memberList[i].memberInfo.avatarUrl);
  }
  state.currentFamilyState.avatarList = avatarList;
  state.currentFamilyState.isShowPlus =
      familyModel.memberType != FamilyRole.member.value;

  return state;
}

/// 更新网器用户和设备数量reducer
AppMineState _updateDeviceReducer(
    AppMineState state, UpdateDeviceAction action) {
   final int deviceCount = action.deviceCount;
    state.currentFamilyState.deviceCount = deviceCount;
    state.currentFamilyState.deviceUser = action.deviceUser;
  return state;
}

AppMineState _updateUpgradeBadgeVisible(
    AppMineState state, UpdateUpgradeBadgeVisibleAction action) {
  state.currentFamilyState.isShowUpgradeBadge = action.isShowUpgradeBadge;
  return state;
}
