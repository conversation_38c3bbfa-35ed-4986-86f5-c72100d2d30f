import 'package:device_utils/compare/compare.dart';

class CurrentFamilyState {
  String familyName = '';
  String familyId = '';
  int memberCount = 0;
  int deviceCount = 0;
  List<String> avatarList = <String>[];
  bool deviceUser = false;
  bool isShowPlus = false;
  bool isShowUpgradeBadge = false;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CurrentFamilyState &&
          runtimeType == other.runtimeType &&
          familyName == other.familyName &&
          familyId == other.familyId &&
          memberCount == other.memberCount &&
          deviceCount == other.deviceCount &&
          isListEqual(avatarList, other.avatarList) &&
          deviceUser == other.deviceUser &&
          isShowPlus == other.isShowPlus &&
          isShowUpgradeBadge == other.isShowUpgradeBadge;

  @override
  int get hashCode =>
      familyName.hashCode ^
      familyId.hashCode ^
      memberCount.hashCode ^
      deviceCount.hashCode ^
      listHashCode(avatarList) ^
      deviceUser.hashCode ^
      isShowPlus.hashCode ^
      isShowUpgradeBadge.hashCode;

  @override
  String toString() {
    return 'CurrentFamilyState{familyName: $familyName, familyId: $familyId, memberCount: $memberCount,'
        ' deviceCount: $deviceCount, avatarList: $avatarList, deviceUser: $deviceUser, isShowPlus: $isShowPlus, isShowUpgradeBadge: $isShowUpgradeBadge}';
  }

  void clear() {
    familyName = '';
    familyId = '';
    deviceCount = 0;
    memberCount = 0;
    avatarList.clear();
    deviceUser = false;
    isShowPlus = false;
    isShowUpgradeBadge = false;
  }
}
