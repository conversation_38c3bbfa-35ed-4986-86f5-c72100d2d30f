import 'package:family/family_model.dart';

class CurrentFamilyBaseAction {}

class UpdateCurrentFamilyInfoAction extends CurrentFamilyBaseAction {
  UpdateCurrentFamilyInfoAction(this.familyModel);

  FamilyModel familyModel;
}

class UpdateDeviceAction extends CurrentFamilyBaseAction {
  UpdateDeviceAction(this.deviceCount, this.deviceUser);

  int deviceCount = 0;
  bool deviceUser = false;
}

class UpdateUpgradeBadgeVisibleAction extends CurrentFamilyBaseAction {
  UpdateUpgradeBadgeVisibleAction(this.isShowUpgradeBadge);

  bool isShowUpgradeBadge = false;
}

class UpdateUpgradeBadgeOperateAction extends CurrentFamilyBaseAction {
  UpdateUpgradeBadgeOperateAction();
}
