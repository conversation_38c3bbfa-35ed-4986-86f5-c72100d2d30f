import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

/// 家庭成员和设备个数
class MemberAndDeviceCount extends StatelessWidget {
  const MemberAndDeviceCount(
      {super.key, required this.memberCount, required this.deviceCount});

  /// [memberCount] 家庭成员数量
  final int memberCount;

  /// [deviceCount] 设备数量
  final int deviceCount;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text.rich(TextSpan(
          style:
              TextStyle(fontSize: 12, color: AppSemanticColors.item.secWeaken),
          children: <InlineSpan>[
            TextSpan(text: '$memberCount个成员'),

            /// 空2个字符
            const TextSpan(text: '  '),

            TextSpan(text: '$deviceCount个设备')
          ])),
    );
  }
}
