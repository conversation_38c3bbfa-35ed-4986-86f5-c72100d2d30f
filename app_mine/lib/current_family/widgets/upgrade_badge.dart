import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/widget_common/bold_text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

const double badgeWidth = 62.0;
const double badgeHeight = 20.0;
const double badgeTop = 0.0;
const double badgeRight = 16.0;

class UpgradeBadgeWidget extends StatelessWidget {
  const UpgradeBadgeWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, bool>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            store.state.currentFamilyState.isShowUpgradeBadge,
        builder: (BuildContext context, bool visible) {
          return Visibility(visible: visible, child: _buildUpgradeBadge());
        });
  }

  Widget _buildUpgradeBadge() {
    return Positioned(
        top: -8,
        right: badgeRight,
        child: SizedBox(
          width: badgeWidth,
          height: badgeHeight,
          child: Stack(
            children: <Widget>[
              Image.asset(
                'assets/images/upgrade_badge_bg.webp',
                width: badgeWidth,
                height: badgeHeight,
                package: Constant.packageName,
              ),
              Positioned(
                left: 4,
                top: 4,
                child: Image.asset(
                  'assets/images/upgrade_badge.webp',
                  width: 12,
                  height: 12,
                  package: Constant.packageName,
                ),
              ),
              Positioned(
                left: badgeRight,
                top: 3,
                child: Text(
                  '全新升级',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontFamilyFallback: fontFamilyFallback(),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
