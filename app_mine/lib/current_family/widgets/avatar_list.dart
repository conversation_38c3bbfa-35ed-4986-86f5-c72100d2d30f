import 'package:app_mine/current_family/store/family_action.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:app_mine/utils/ui_const.dart';
import 'package:app_mine/widget_common/avatar_image.dart';
import 'package:device_utils/log/log.dart';
import 'package:family/family.dart';
import 'package:family/family_model.dart';
import 'package:family/member_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

/// 家庭成员头像
class AvatarList extends StatelessWidget {
  const AvatarList({super.key, required this.avatarList});

  /// [avatarList] 家庭成员头像url列表
  final List<String> avatarList;

  static const double _avatarSpacing = 24.0;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, bool>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            store.state.currentFamilyState.isShowPlus,
        builder: (BuildContext context, bool isShowPlus) {
          return Visibility(
              visible: avatarList.isNotEmpty,
              child: _buildAvatarListStack(isShowPlus, context));
        });
  }

  Widget _buildAvatarListStack(bool isShowPlus, BuildContext context) {
    final int avatarListLength =
        isShowPlus ? avatarList.length : avatarList.length - 1;
    return Stack(
      children: <Widget>[
        _buildAvatarContainer(avatarListLength),
        ..._buildAvatarImages(avatarListLength),
        _buildMaskWidget(isShowPlus),
        _buildPlusWidget(isShowPlus, context)
      ],
    );
  }

  Widget _buildAvatarContainer(int avatarListLength) {
    return SizedBox(
      width: UIConst.avatarSize + avatarListLength * _avatarSpacing,
      height: UIConst.avatarSize,
    );
  }

  List<Widget> _buildAvatarImages(int avatarListLength) {
    final List<Widget> list = <Widget>[];
    for (int i = 0; i < avatarList.length; i++) {
      list.add(Positioned(
        top: 0,
        right: (avatarListLength - i) * _avatarSpacing,
        child: AvatarImage(
          avatarUrl: avatarList[i],
          border: Border.all(color: Colors.white),
          defaultImagePath: 'assets/images/avatar_blue.png',
          hasBoxShadow: true,
        ),
      ));
    }
    return list;
  }

  Widget _buildMaskWidget(bool isShowPlus) {
    return Positioned(
      top: 0,
      right: UIConst.avatarSize - (isShowPlus ? 0 : UIConst.avatarSize),
      width: avatarList.length * _avatarSpacing + (isShowPlus ? 0 : 12),
      height: UIConst.avatarSize,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {},
        child: const SizedBox.expand(),
      ),
    );
  }

  Widget _buildPlusWidget(bool visible, BuildContext context) {
    return Visibility(
      visible: visible,
      child: Positioned(
        top: 0,
        right: 0,
        child: GestureDetector(
          onTap: () {
            _onPlusTap(context);
          },
          child: Image.asset(
            'assets/images/plus.webp',
            width: UIConst.avatarSize,
            height: UIConst.avatarSize,
            package: Constant.packageName,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  void _onPlusTap(BuildContext context) {
    appMineStore.dispatch(UpdateUpgradeBadgeOperateAction());
    Family.getCurrentFamily().then((FamilyModel familyModel) async {
      gioTrack(Constant.familyInviteClickGio, <String, String?>{
        'familyId': familyModel.familyId,
      });
      final List<MemberModel> memberList = _getMemberList(familyModel);

      if (!_isShowInviteFamilyBottomSheet(familyModel, memberList)) {
        return;
      }
      _showInviteFamilyBottomSheet(familyModel, memberList, context);
    }).catchError((dynamic error) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '_onPlusTap _getCurrentFamily error:$error');
    });
  }

  void _showInviteFamilyBottomSheet(FamilyModel? familyModel,
      List<MemberModel> memberList, BuildContext context) {
    showInviteFamilyBottomSheetWidget(
      package: Constant.packageName,
      context,
      sourceName: Constant.sourceName,
      onNextTap: (BuildContext _context, FamilyRole familyRole) {
        if (_isRoleLimitExceeded(familyRole, memberList)) {
          return;
        }
        goToPage(
            'mpaas://familymanage?familyID=${familyModel?.familyId}&memberRole=${familyRole.value}#/invitationPage');
        Navigator.of(_context).pop();
      },
    );
  }

  List<MemberModel> _getMemberList(FamilyModel? familyModel) {
    final List<MemberModel> memberList = <MemberModel>[];
    memberList.addAll(familyModel?.members ?? <MemberModel>[]);

    memberList.removeWhere((MemberModel element) =>
        element.memberInfo.virtualUserFlag ||
        element.memberType == FamilyRole.creator.value);

    return memberList;
  }

  bool _isShowInviteFamilyBottomSheet(
      FamilyModel? familyModel, List<MemberModel> memberList) {
    final int memberType = familyModel?.memberType ?? 0;
    if (memberType == FamilyRole.member.value) {
      ToastHelper.showToast(Constant.noPermission);
      return false;
    }

    if (memberList.length >= Constant.maxMemberCount) {
      ToastHelper.showToast(Constant.memberCountLimit);
      return false;
    }

    return true;
  }

  bool _isRoleLimitExceeded(
      FamilyRole familyRole, List<MemberModel> memberList) {
    int count = 0;
    for (final MemberModel memberModel in memberList) {
      if (memberModel.memberType == familyRole.value) {
        count++;
      }
    }
    if (familyRole == FamilyRole.admin && count >= Constant.maxAdminCount) {
      ToastHelper.showToast(Constant.adminCountLimit);
      return true;
    }
    return false;
  }
}
