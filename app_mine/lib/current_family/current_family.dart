import 'package:app_mine/current_family/current_family_view_model.dart';
import 'package:app_mine/current_family/store/family_action.dart';
import 'package:app_mine/current_family/widgets/avatar_list.dart';
import 'package:app_mine/current_family/widgets/family_name.dart';
import 'package:app_mine/current_family/widgets/member_and_device_count.dart';
import 'package:app_mine/store/mine_state.dart';
import 'package:app_mine/store/mine_store.dart';
import 'package:app_mine/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart'
    show PressableOverlayWidget, gioTrack, goToPage;
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class CurrentFamilyWidget extends StatelessWidget {
  const CurrentFamilyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppMineState, CurrentFamilyViewModel>(
        distinct: true,
        converter: (Store<AppMineState> store) =>
            CurrentFamilyViewModel.fromStore(store),
        builder: (BuildContext context, CurrentFamilyViewModel vm) {
          return Visibility(
            visible: vm.isLogin,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                appMineStore.dispatch(UpdateUpgradeBadgeOperateAction());
                gioTrack(Constant.familyClickGio, <String, String?>{
                  'familyId': vm.familyId,
                });
                goToPage(Constant.familyManageUrl);
              },
              child: Container(
                  decoration: BoxDecoration(
                      gradient: const LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: <Color>[Color(0xFFFFFFFF), Color(0x80FFFFFF)],
                          stops: <double>[0.4, 1]),
                      border: Border.all(
                        color: const Color(0xFFFFFFFF),
                        width: .8,
                      ),
                      borderRadius: BorderRadius.circular(22)),
                  margin:
                      const EdgeInsets.only(left: 15, right: 15, bottom: 12),
                  height: 75,
                  child: ColoredBox(
                    color: Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            /// 左边：家庭名称+家庭成员和设备个数
                            Expanded(
                              child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                  children: <Widget>[
                                    FamilyName(
                                      familyName: vm.familyName,
                                    ),
                                    Expanded(
                                      child: MemberAndDeviceCount(
                                        deviceCount: vm.deviceCount,
                                        memberCount: vm.memberCount,
                                      ),
                                    )
                                  ]),
                            ),
                  
                            const SizedBox(width: 16),
                  
                            /// 右边 家庭成员头像
                            AvatarList(avatarList: vm.avatarList),
                          ]),
                    ),
                  )),
            ),
          );
        });
  }
}
