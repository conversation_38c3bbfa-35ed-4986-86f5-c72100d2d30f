name: app_mine
description: "我的TAB project."
author: din<PERSON><PERSON><PERSON><PERSON>@haier.com
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3
version: 1.0.0+1

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter

  redux: 5.0.0
  flutter_redux: 0.8.2
  dio: 5.3.2
  badges: 2.0.1
  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"
  flutter_screenutil: 5.0.0+2
  card_swiper: 2.0.4
  retrofit: 4.1.0
  extended_image: 8.2.0
  json_annotation: 4.8.1
  visibility_detector: 0.4.0+2
  easy_refresh: 3.4.0
  flutter_picker_plus: 1.0.0
  gradient_borders: 1.0.1

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.2"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.1"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=0.0.12"

  uimessage:
    hosted:
      name: uimessage
      url: http://**************:8083
    version: ">=0.0.1"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.2"

  device_utils:
    hosted:
      name: device_utils
      url: http://**************:8083
    version: ">=2.0.0"

  family:
    hosted:
      name: family
      url: http://**************:8083
    version: ">=0.0.9+2"

  plugin_device:
    hosted:
      name: plugin_device
      url: http://**************:8083
    version: ">=0.0.1"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=0.2.0"

  personal_information:
    hosted:
      name: personal_information
      url: http://**************:8083
    version: ">=0.0.1"

  setting:
    hosted:
      name: setting
      url: http://**************:8083
    version: ">=0.0.1"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.2.0"

  upservice:
    hosted:
      name: upservice
      url: http://**************:8083
    version: ">=0.0.1"

  about_us:
    hosted:
      name: about_us
      url: http://**************:8083
    version: ">=0.0.1"

  plugin_upgrade:
    hosted:
      name: plugin_upgrade
      url: http://**************:8083
    version: ">=0.2.0"

  library_widgets:
    hosted:
      name: library_widgets
      url: http://**************:8083
    version: ">=2.3.0+2023080401"

  eshop_widgets:
    hosted:
      name: eshop_widgets
      url: http://**************:8083
    version: ">=0.3.0+2024082101"

  flutter_common_ui:
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
    version: ">=9.2.1"

  resource:
    version: ">=0.2.0"
    hosted:
      name: resource
      url: http://**************:8083

dev_dependencies:
  build_runner: 2.4.6
  retrofit_generator: 8.1.0
  flutter_test:
    sdk: flutter
  json_serializable: ">=4.0.0"
flutter:
  assets:
    - assets/images/
