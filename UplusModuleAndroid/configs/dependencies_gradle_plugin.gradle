dependencies {
    println"embed start =============="
    def flutterProjectRoot = rootProject.projectDir.parentFile.toPath()
    def plugins = new Properties()
    def pluginsFile = new File(flutterProjectRoot.toFile(), '.flutter-plugins')
    if (pluginsFile.exists()) {
        pluginsFile.withReader('UTF-8') { reader -> plugins.load(reader) }
    }
    plugins.each { name, path ->
        println "start deal plugin name is $name path is $path"
        def pluginPath = flutterProjectRoot.resolve(path)
        def androidDir = new File(pluginPath.toFile(), "android")
        if(androidDir.exists()) {
            embed project(path: ":$name", configuration: 'default')
            println "embed Embedding $name"
        } else {
            println "embed Skipping $name because it does not contain an 'android' directory"
        }
    }
    println"embed end ==============="
}