// Generated file. Do not edit.
apply plugin: 'com.alipay.apollo.optimize'
apply plugin: 'com.android.library'

buildscript {
    ext {
        mpaas_artifact = "mpaas-baseline"
        mpaas_baseline = "10.2.3-54"
        mpaas_version = "*********"
        kotlin_version = "1.5.20"
    }
    repositories {
        maven {
            url 'https://mdpm.haier.net/nexus/repository/public'
        }
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.0.4'
        classpath 'com.growingio.android:vds-gradle-plugin:autotrack-2.9.12'
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
        classpath 'com.android.boost.easyconfig:easyconfig:2.7.4'
        classpath 'com.bytedance.tools.lancet:lancet-plugin-asm6:1.0.2'
        classpath "com.haier.uhome.uplus:initPlugin:*******.2023020901"
    }
}

allprojects {
    repositories {
        maven {
            url 'https://mdpm.haier.net/nexus/repository/public'
        }
        google()
        mavenCentral()
    }
}

android {
    // Conditional for compatibility with AGP <4.2.
    if (project.android.hasProperty("namespace")) {
        namespace 'com.haier.uhome.uplus'
    }

    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 22
    }
}

allprojects {
    configurations {
        all {
            resolutionStrategy {
                force 'com.alipay.android.phone.wallet:nebulaucsdk-build:999.*********.220929181439'
            }
            exclude group: 'com.aliyun.ams', module: 'alicloud-android-utdid'
            exclude group: 'com.alipay.android.phone.thirdparty', module: 'xiaomipush-build'
            //排除flutter aar引用
            exclude group: 'com.haier.uhome.uplus.flutterapp', module: 'flutter'
            //排除flutter module aar引用
            exclude group: 'com.haier.uhome.uplusmodule', module: 'flutter_release'
        }
    }
}