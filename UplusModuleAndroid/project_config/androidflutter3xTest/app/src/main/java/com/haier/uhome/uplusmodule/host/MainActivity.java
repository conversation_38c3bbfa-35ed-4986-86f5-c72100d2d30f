package com.haier.uhome.uplusmodule.host;

import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;


import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;

public class MainActivity extends FlutterActivity {

    private static final String CHANNEL_NAME = "UplusModuleTestChannel";
    private static final String METHOD_NAME = "startAndroidTest";

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        new MethodChannel(flutterEngine.getDartExecutor(), CHANNEL_NAME).setMethodCallHandler((call, result) -> {
            if (TextUtils.equals(call.method, METHOD_NAME)) {
                jumpToHomePage();
                result.success(null);
            } else {
                result.notImplemented();
            }
        });
    }

    public void jumpToHomePage() {
        Intent intent = new Intent(this, com.haier.uhome.uplus.main.MainActivity.class);
        this.startActivity(intent);
        this.overridePendingTransition(0, 0);
        finish();
    }
}
