#import "AppDelegate.h"
#import "FlutterPluginRegistrant/GeneratedPluginRegistrant.h"
#import <UplusSpecial/InitBaseKit.h>
#import <upuserdomain/UpUserDomainHolder.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    InitBaseKit *initBaseKit = [InitBaseKit initUPInitBaseKit:UplusKitEnvironmentProd appId:@"MB-UZHSH-0001" appKey:@"5dfca8714eb26e3a776e58a8273c8752" appVersion:@"6.22.0" isTestMode:NO];
    [[UpUserDomainHolder instance].userDomain addObserver:(id<UpUserDomainObserver>)initBaseKit];
    [[UpUserDomainHolder instance].userDomain autoRefreshToken];
    [GeneratedPluginRegistrant registerWithRegistry:self];
    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

@end

