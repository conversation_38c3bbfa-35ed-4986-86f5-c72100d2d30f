source 'https://git.haier.net/uplus/shell/cocoapods/Specs.git'
source "https://code.aliyun.com/mpaas-public/podspecs.git"
source 'https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git'

mPaaS_baseline '10.1.68'  # 请将 x.x.x 替换成真实基线版本
mPaaS_version_code 41

platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure \"flutter pub get\" is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  # use_frameworks!
  use_modular_headers!
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  pod 'FlutterPluginRegistrant', :path => File.join('Flutter', 'FlutterPluginRegistrant'), :inhibit_warnings => true
  pod 'UpPlugins/CallBack', '2.13.1.2024010501'
  pod 'upuserdomain', '3.25.0.2025011501'
  pod 'UPCore','3.6.0.2024121001'
  pod 'UPResource', '2.26.0.2024090301'
  pod 'uplog', '1.7.4.2024010901'
  pod 'LogicEngine', '3.6.0.2024032901'
  pod 'UplusKit', '1.6.3.2024110501'
  pod 'UPDevice', '7.22.0.2024110101'
  pod 'UPDeviceInitKit','1.13.0.2024072401'
  pod 'uSDK','10.3.0'
  pod 'UPFlutterBasePluginAPI', '0.0.5'
  pod 'UPPluginBaseAPI', '0.1.3'
  pod 'UpPluginFoundation', '0.1.16.1.2023020301'
  pod 'UPTools/Others', '0.2.4.2023112701'
  pod 'UPTools/UPScan', '0.2.4.2023112701'
  pod 'UPDevicePlugin','1.21.0.2024122701'
  pod 'GrowingCoreKit','2.9.13'
  pod 'UPPush', '1.5.6.2024010501'
  pod 'uSDKCommon','1.6.0'
  pod 'uSDKVideo','2.3.3'
  pod 'UpPermissionManager','0.1.15.2024011001'
  pod 'UpPermissionPlugin','0.1.7.2023102201'
  pod 'UPShortCut','0.2.6.2023071801'
  #pod 'UplusSpecial','1.1.0.2024110502'
  pod 'UplusSpecial','999.999.999.2025060601'
  pod 'UpTrace','1.3.5.2025032901'
  pod 'JPush','4.8.1'
  pod 'JCore','3.2.9'
  pod 'UpCrash','1.4.1.2023092601'
  pod 'UpCrashLogPlugin','1.0.0.1.2023013001'
  pod 'UpResourcePlugin','0.3.0.1.2023032401'
  pod 'UpMpaaSPlugin','0.1.1.1.2023020101'
  pod 'UpSystemPlugin','1.1.5.2023101201'
  pod 'UPStorage','1.6.0.2023120101'
  pod 'Hainer','1.16.1.2024110501'
  pod 'lottie-ios', '4.4.0'
  pod 'UplusBase','2.5.4.2024022301'
  pod 'UpNebula/Core', '4.13.0.2024010701'
  pod 'UPCrashDefend','0.1.0.1.2023020901'
  pod 'UplusConfig','0.4.7.2024110501'
  pod 'UpStoragePlugin','0.1.9'
  pod 'UPDeviceWidgetExtensionEngine', '1.4.0.2023092801'
  pod 'UPDeviceGroupData', '0.1.0.2023092801'
  #pod 'UpJVerification','0.2.2.2024091001'
  pod 'uaiSDK','1.2.2'
  pod 'UpQQMusic','0.2.0.2024110501'
  pod 'UpUMShare','1.5.0.2024110502'
  pod 'upsocial','1.3.6.1.2023032201'
  pod 'UPPageTrace','3.1.1.2023102101'
  pod 'upnetwork','4.0.8.2025062301'
  pod 'CocoaLumberjack','3.8.5'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    if target.name == 'permission_handler'
      target.build_configurations.each do |config|
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
          '$(inherited)',
          'COCOAPODS=1',
          'PERMISSION_NOTIFICATIONS=0',
          'PERMISSION_LOCATION=0',
          'PERMISSION_PHOTOS=1',
          'PERMISSION_CAMERA=1',
          'PERMISSION_CONTACTS=0',
          'PERMISSION_SENSORS=0',
          'PERMISSION_EVENTS=0',
          'PERMISSION_REMINDERS=0',
          'PERMISSION_MICROPHONE=1',
          'PERMISSION_SPEECH_RECOGNIZER=0',
          'PERMISSION_MEDIA_LIBRARY=0',
        ]
       end
    end
  end
end
