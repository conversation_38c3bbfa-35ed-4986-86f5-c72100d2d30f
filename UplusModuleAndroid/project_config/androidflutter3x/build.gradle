// Generated file. Do not edit.

buildscript {
    ext.mpaas_artifact = "mpaas-baseline"
    ext.mpaas_baseline = "10.2.3-54"
    ext.mpaas_version = "*********"
    ext.kotlin_version = "1.5.20"
    repositories {
        maven {
            url "https://mdpm.haier.net/nexus/repository/public"
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/groups/public/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/google'
            allowInsecureProtocol true
        }
        maven {
            credentials {
                username "mvn_read_ws"
                password "mrk8929"
            }
            url "http://mvn.cloud.alipay.com/nexus/content/repositories/releases/"
            allowInsecureProtocol true
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/google/'
            name 'aliyun-google'
            allowInsecureProtocol true
        }
        maven {
            url 'http://download.flutter.io'
            allowInsecureProtocol true
        }
        google()
        jcenter()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath 'com.growingio.android:vds-gradle-plugin:autotrack-2.9.6'
        classpath 'com.github.kezong:fat-aar:1.3.8'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        maven {
            url "https://mdpm.haier.net/nexus/repository/public"
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/groups/public/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/google'
            allowInsecureProtocol true
        }
        maven {
            credentials {
                username "mvn_read_ws"
                password "mrk8929"
            }
            url "http://mvn.cloud.alipay.com/nexus/content/repositories/releases/"
            allowInsecureProtocol true
        }
        maven {
            url 'http://download.flutter.io'
            allowInsecureProtocol true
        }
        google()
        jcenter()
    }
}

subprojects {
    repositories {
        maven {
            url 'https://maven.aliyun.com/repository/google'
        }
        maven {
            url 'https://maven.aliyun.com/repository/jcenter'
        }
        maven {
            url "https://mdpm.haier.net/nexus/repository/public"
        }
        maven {
            url 'https://maven.aliyun.com/nexus/content/groups/public/'
        }
        maven {
            url 'http://download.flutter.io'
            allowInsecureProtocol true
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/repositories/releases/'
            allowInsecureProtocol true
        }
        google()
        mavenCentral()
    }

    buildscript {
        repositories {
            maven {
                url 'https://maven.aliyun.com/repository/google'
            }
            maven {
                url 'https://maven.aliyun.com/repository/jcenter'
            }
            maven {
                url "https://mdpm.haier.net/nexus/repository/public"
            }
            maven {
                url 'https://maven.aliyun.com/nexus/content/groups/public/'
            }
            maven {
                url 'http://download.flutter.io'
                allowInsecureProtocol true
            }
            google()
            mavenCentral()
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
