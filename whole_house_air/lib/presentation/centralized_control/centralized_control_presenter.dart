import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:library_widgets/common/log.dart';
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/toast_helper.dart';
import 'package:whole_house_air/common/server/api.dart';
import 'package:whole_house_air/datasource/device/device_protocol/model/wh_device_model.dart';
import 'package:whole_house_air/models/centralized_model.dart';
import 'package:whole_house_air/models/space_control_v2_model.dart';

class CentralizedControlPresenter {
  static const _typeKey = 'type';
  static const _attributesKey = 'attributes';
  static const _valueKey = 'value';
  static const _variantsKey = 'variants';
  static const _valueNameKey = 'valueName';
  static const _labelKey = 'label';
  static const _unitKey = 'unit';
  static const _disableKey = 'disable';
  static const _fanModeValue = '6';
  static const _propertiesKey = 'properties';
  static const _operationModeKey = "operationMode";
  static const _windSpeedKey = "windSpeed";
  static const _targetTemperatureKey = "targetTemperature";
  static const _targetHumidityKey = "targetHumidity";

  // 挂式空调（大类02）
  // 立式空调（大类03）
  // 中央空调（大中类0D002，0D012，0D021，0D081）
  static const List<String> airDeviceTypeList = [
    '2',
    '3',
    'd#2',
    'd#12',
    'd#21',
    'd#81'
  ];

  // 新风 新风机（大类24）
  static const List<String> freshDeviceTypeList = ['24'];

  // 调湿 新风机(有内除湿、新风除湿、加湿功能)
  static const List<String> humidityDeviceProdNoList = [
    'AQ005BM0N',
    'AQ0053X0N'
  ];

  // 初始化页面卡片参数
  Map<String, dynamic> assembleCardArgument(Map<String, dynamic> cardArguments,
      CentralizedStatusModel? centralizedStatus) {
    Map<String, dynamic> _cardArguments = cardArguments;
    if (centralizedStatus != null) {
      // 全屋调温
      if (_cardArguments[_typeKey] == CONSTANT.centralizedControlTypeAir) {
        AttrProperties? airAttrProperties =
            centralizedStatus.airConditionerControlService?.attrProperties;
        if (airAttrProperties?.targetTemperature != null &&
            airAttrProperties?.targetTemperature != '') {
          _cardArguments[_attributesKey][0][_valueKey] =
              airAttrProperties?.targetTemperature;
          _cardArguments[_attributesKey][0][_valueNameKey] =
              _cardArguments[_attributesKey][0][_valueKey] +
                  _cardArguments[_attributesKey][0][_unitKey];
        }
        if (airAttrProperties?.operationMode != null &&
            airAttrProperties?.operationMode != '') {
          _cardArguments[_attributesKey][1][_valueKey] =
              airAttrProperties?.operationMode;

          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][1][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][1][_variantsKey][i][_valueKey] ==
                airAttrProperties?.operationMode) {
              _cardArguments[_attributesKey][1][_valueNameKey] =
                  _cardArguments[_attributesKey][1][_variantsKey][i][_labelKey];
            }
          }
          if (airAttrProperties?.operationMode == _fanModeValue) {
            _cardArguments[_attributesKey][0][_disableKey] = true;
          } else {
            _cardArguments[_attributesKey][0][_disableKey] = false;
          }
        }
        if (airAttrProperties?.windSpeed != null &&
            airAttrProperties?.windSpeed != '') {
          _cardArguments[_attributesKey][2][_valueKey] =
              airAttrProperties?.windSpeed;
          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][2][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][2][_variantsKey][i][_valueKey] ==
                airAttrProperties?.windSpeed) {
              _cardArguments[_attributesKey][2][_valueNameKey] =
                  _cardArguments[_attributesKey][2][_variantsKey][i][_labelKey];
            }
          }
        }
      }
      // 全屋调湿
      if (_cardArguments[_typeKey] == CONSTANT.centralizedControlTypeHumidity) {
        AttrProperties? airAttrProperties =
            centralizedStatus.humidityControlService?.attrProperties;
        if (airAttrProperties?.targetHumidity != null &&
            airAttrProperties?.targetHumidity != '') {
          _cardArguments[_attributesKey][0][_valueKey] =
              airAttrProperties?.targetHumidity;
          _cardArguments[_attributesKey][0][_valueNameKey] =
              _cardArguments[_attributesKey][0][_valueKey] +
                  _cardArguments[_attributesKey][0][_unitKey];
        }
        if (airAttrProperties?.operationMode != null &&
            airAttrProperties?.operationMode != '') {
          _cardArguments[_attributesKey][1][_valueKey] =
              airAttrProperties?.operationMode;
          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][1][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][1][_variantsKey][i][_valueKey] ==
                airAttrProperties?.operationMode) {
              _cardArguments[_attributesKey][1][_valueNameKey] =
                  _cardArguments[_attributesKey][1][_variantsKey][i][_labelKey];
            }
          }
        }
        if (airAttrProperties?.windSpeed != null &&
            airAttrProperties?.windSpeed != '') {
          _cardArguments[_attributesKey][2][_valueKey] =
              airAttrProperties?.windSpeed;
          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][2][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][2][_variantsKey][i][_valueKey] ==
                airAttrProperties?.windSpeed) {
              _cardArguments[_attributesKey][2][_valueNameKey] =
                  _cardArguments[_attributesKey][2][_variantsKey][i][_labelKey];
            }
          }
        }
      }
      // 全屋新风
      if (_cardArguments[_typeKey] == CONSTANT.centralizedControlTypeFresh) {
        AttrProperties? airAttrProperties =
            centralizedStatus.freshAirControlService?.attrProperties;
        if (airAttrProperties?.operationMode != null &&
            airAttrProperties?.operationMode != '') {
          _cardArguments[_attributesKey][0][_valueKey] =
              airAttrProperties?.operationMode;
          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][0][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][0][_variantsKey][i][_valueKey] ==
                airAttrProperties?.operationMode) {
              _cardArguments[_attributesKey][0][_valueNameKey] =
                  _cardArguments[_attributesKey][0][_variantsKey][i][_labelKey];
            }
          }
        }
        if (airAttrProperties?.windSpeed != null &&
            airAttrProperties?.windSpeed != '') {
          _cardArguments[_attributesKey][1][_valueKey] =
              airAttrProperties?.windSpeed;
          for (int i = 0;
              i <
                  (_cardArguments[_attributesKey][1][_variantsKey]
                          as List<dynamic>)
                      .length;
              i++) {
            if (_cardArguments[_attributesKey][1][_variantsKey][i][_valueKey] ==
                airAttrProperties?.windSpeed) {
              _cardArguments[_attributesKey][1][_valueNameKey] =
                  _cardArguments[_attributesKey][1][_variantsKey][i][_labelKey];
            }
          }
        }
      }
    }
    return _cardArguments;
  }

  // 组织命令
  List<dynamic> organizationCommand(
      String familyId, Map<String, dynamic> cardArguments) {
    final List<dynamic> commands = <dynamic>[];
    try {
      if (cardArguments[_typeKey] == CONSTANT.centralizedControlTypeAir) {
        Map<String, dynamic> command = {
          "spaceId": familyId,
          "service": "airConditionerControlService",
          "properties": {
            "onOffStatus": "true",
          },
        };
        if (cardArguments[_attributesKey][0][_valueKey] != '') {
          command[_propertiesKey][_targetTemperatureKey] =
              cardArguments[_attributesKey][0][_valueKey];
        }
        if (cardArguments[_attributesKey][1][_valueKey] != '') {
          command[_propertiesKey][_operationModeKey] =
              cardArguments[_attributesKey][1][_valueKey];
        }
        if (cardArguments[_attributesKey][2][_valueKey] != '') {
          command[_propertiesKey][_windSpeedKey] =
              cardArguments[_attributesKey][2][_valueKey];
        }
        commands.add(command);
      }
      if (cardArguments[_typeKey] == CONSTANT.centralizedControlTypeHumidity) {
        Map<String, dynamic> command = {
          "spaceId": familyId,
          "service": "humidityControlService",
          "properties": {
            "onOffStatus": "true",
          },
        };
        if (cardArguments[_attributesKey][0][_valueKey] != '') {
          command[_propertiesKey][_targetHumidityKey] =
              cardArguments[_attributesKey][0][_valueKey];
        }
        if (cardArguments[_attributesKey][1][_valueKey] != '') {
          command[_propertiesKey][_operationModeKey] =
              cardArguments[_attributesKey][1][_valueKey];
        }
        if (cardArguments[_attributesKey][2][_valueKey] != '') {
          command[_propertiesKey][_windSpeedKey] =
              cardArguments[_attributesKey][2][_valueKey];
        }
        commands.add(command);
      }
      if (cardArguments[_typeKey] == CONSTANT.centralizedControlTypeFresh) {
        Map<String, dynamic> command = {
          "spaceId": familyId,
          "service": "freshAirControlService",
          "properties": {
            "onOffStatus": "true",
          },
        };
        if (cardArguments[_attributesKey][0][_valueKey] != '') {
          command[_propertiesKey][_operationModeKey] =
              cardArguments[_attributesKey][0][_valueKey];
        }
        if (cardArguments[_attributesKey][1][_valueKey] != '') {
          command[_propertiesKey][_windSpeedKey] =
              cardArguments[_attributesKey][1][_valueKey];
        }
        commands.add(command);
      }
    } catch (err) {
      DevLogger.error(
          msg: {'fn': '_organizationCommand', 'err': err},
          tag: CONSTANT.PACKAGE_NAME);
    }
    return commands;
  }

  // 执行
  void execute(String familyId, Map<String, dynamic> cardArguments) async {
    try {
      List<dynamic> commands =
          this.organizationCommand(familyId, cardArguments);
      Map<String, dynamic> params = {
        'familyId': familyId,
        'spaceIds': commands,
      };
      final IsOnline isOnline = await Network.isOnline();
      if (isOnline.isOnline) {
        final SpaceControlV2ServerModel? serverModel =
            await API.spaceCentralizedControl(params);
        DevLogger.info(
            tag: CONSTANT.LOG_TAG, msg: 'spaceCentralizedControl $serverModel');
        if (serverModel != null &&
            serverModel.retCode == CONSTANT.REQUEST_SUCCESS_CODE &&
            serverModel.spaceControlV2Model != null &&
            serverModel.spaceControlV2Model!.controlResults.isNotEmpty) {
          final ControlResultV2Model controlResultV2Model =
              serverModel.spaceControlV2Model!.controlResults[0];

          // 全部成功：即Service全部受理成功。
          if (controlResultV2Model.acceptCode ==
              CONSTANT.REQUEST_SUCCESS_CODE) {
            if (commands[0]['properties'].length == 1) {
              ToastHelper.showToast('已开启所有设备');
            } else {
              ToastHelper.showToast('${cardArguments['title']}执行成功');
            }
          }
          // 部分成功：即部分Service受理成功，部分受理失败
          if (controlResultV2Model.acceptCode ==
              CONSTANT.REQUEST_PART_SUCCESS_CODE) {
            ToastHelper.showToast('${cardArguments['title']}部分执行成功');
          }
          // 全部失败：即所有Service均受理失败
          if (controlResultV2Model.acceptCode == CONSTANT.REQUEST_FAIL_CODE ||
              controlResultV2Model.acceptCode ==
                  CONSTANT.REQUEST_FAIL_CODE_LACK_CONTROL_EQUIPMENT) {
            ToastHelper.showToast('${cardArguments['title']}执行失败');
          }
        } else {
          ToastHelper.showToast(CONSTANT.REQUEST_ERROR);
        }
      } else {
        ToastHelper.showToast(CONSTANT.NET_WORK_ERROR);
      }
    } catch (err) {
      ToastHelper.showToast(CONSTANT.REQUEST_ERROR);
      DevLogger.error(
          msg: {'fn': 'spaceCentralizedControl', 'err': err},
          tag: CONSTANT.LOG_TAG);
    }
  }

  Map<String, dynamic> getOffCommand(String familyId, int type) {
    Map<int, String> typeToService = {
      CONSTANT.centralizedControlTypeAir: 'airConditionerControlService',
      CONSTANT.centralizedControlTypeHumidity: 'humidityControlService',
      CONSTANT.centralizedControlTypeFresh: 'freshAirControlService'
    };
    return {
      "spaceId": familyId,
      "service": typeToService[type],
      "properties": {
        "onOffStatus": "false",
      },
    };
  }

  // 组织关机命令
  organizationOffCommand(String familyId, Map<String, dynamic> cardArguments) {
    List<dynamic> commands = [];

    try {
      commands.add(getOffCommand(familyId, cardArguments[_typeKey] as int));
    } catch (err) {
      DevLogger.error(
          msg: {'fn': '_organizationOffCommand', 'err': err},
          tag: CONSTANT.PACKAGE_NAME);
    }
    return commands;
  }

  // 关机
  void onOff(String familyId, Map<String, dynamic> cardArguments) async {
    try {
      Map<String, dynamic> params = {
        'familyId': familyId,
        'spaceIds': this.organizationOffCommand(familyId, cardArguments),
      };
      final IsOnline isOnline = await Network.isOnline();
      if (isOnline.isOnline) {
        final SpaceControlV2ServerModel? serverModel =
            await API.spaceCentralizedControl(params);
        DevLogger.info(
            tag: CONSTANT.LOG_TAG, msg: 'spaceCentralizedControl $serverModel');

        if (serverModel != null &&
            serverModel.retCode == CONSTANT.REQUEST_SUCCESS_CODE &&
            serverModel.spaceControlV2Model != null &&
            serverModel.spaceControlV2Model!.controlResults.isNotEmpty) {
          final ControlResultV2Model controlResultV2Model =
              serverModel.spaceControlV2Model!.controlResults[0];
          // 全部成功：即Service全部受理成功。
          if (controlResultV2Model.acceptCode ==
              CONSTANT.REQUEST_SUCCESS_CODE) {
            ToastHelper.showToast('已成功关闭所有设备');
          }
          // 部分成功：即部分Service受理成功，部分受理失败
          if (controlResultV2Model.acceptCode ==
              CONSTANT.REQUEST_PART_SUCCESS_CODE) {
            ToastHelper.showToast('部分设备关闭失败,请检查设备状态');
          }
          // 全部失败：即所有Service均受理失败
          if (controlResultV2Model.acceptCode == CONSTANT.REQUEST_FAIL_CODE ||
              controlResultV2Model.acceptCode ==
                  CONSTANT.REQUEST_FAIL_CODE_LACK_CONTROL_EQUIPMENT) {
            ToastHelper.showToast('关闭失败,请检查设备状态');
          }
        } else {
          ToastHelper.showToast(CONSTANT.REQUEST_ERROR);
        }
      } else {
        ToastHelper.showToast(CONSTANT.NET_WORK_ERROR);
      }
    } catch (err) {
      ToastHelper.showToast(CONSTANT.REQUEST_ERROR);
      DevLogger.error(
          msg: {'fn': 'spaceCentralizedControl', 'err': err},
          tag: CONSTANT.LOG_TAG);
    }
  }

  // 获取 全屋空间状态(集控)
  Future<void> getSpaceCentralizedStatus(
      String familyId, Function callback, buildCardWidgetList) async {
    try {
      final CentralizedStatusServerModel? centralizedStatusServerModel =
          await API.spaceCentralizedStatus({
        "familyId": familyId,
        "spaceId": familyId,
        "spaceType": 3,
        "services": [
          "airConditionerControlService",
          "humidityControlService",
          "freshAirControlService"
        ]
      });
      DevLogger.info(
          tag: CONSTANT.PACKAGE_NAME,
          msg: 'getSpaceCentralizedStatus $centralizedStatusServerModel');
      if (centralizedStatusServerModel != null &&
          centralizedStatusServerModel.retCode ==
              CONSTANT.REQUEST_SUCCESS_CODE &&
          centralizedStatusServerModel.centralizedStatusModel != null) {
        // 全屋空气状态

        callback(centralizedStatusServerModel.centralizedStatusModel);
        buildCardWidgetList();
      } else {
        buildCardWidgetList();
      }
    } catch (err) {
      buildCardWidgetList();
      DevLogger.error(
          msg: {'fn': '_getSpaceCentralizedStatus', 'err': err},
          tag: CONSTANT.PACKAGE_NAME);
    }
  }

  // 判断是否支持
  List<String> estimateIsSupport(List<WHDevInfoModel>? deviceList) {
    // 调温
    bool isSupportAir = false;
    // 调湿
    bool isSupportHumidity = false;
    // 新风
    bool isSupportFresh = false;

    List<String> _supportList = [];
    // 过滤出需要展示的设备
    if (deviceList != null && deviceList.length > 0) {
      deviceList.forEach((WHDevInfoModel device) {
        String bigClass = device.basicInfo.bigClass;
        String middleClass = device.basicInfo.middleClass;
        String ruleBigClassKey = bigClass;
        String ruleBigMiddleClassKey = '$bigClass#$middleClass';
        // 调温
        if (airDeviceTypeList.indexOf(ruleBigClassKey) != -1) {
          isSupportAir = true;
        } else if (airDeviceTypeList.indexOf(ruleBigMiddleClassKey) != -1) {
          isSupportAir = true;
        }
        // 新风
        if (freshDeviceTypeList.indexOf(ruleBigClassKey) != -1) {
          isSupportFresh = true;
        }
        // 调湿
        String prodNo = device.basicInfo.prodNo;
        if (humidityDeviceProdNoList.indexOf(prodNo) != -1) {
          isSupportHumidity = true;
        }
      });
    }
    if (isSupportAir) {
      _supportList.add(CONSTANT.centralizedControlKindAir);
    }
    if (isSupportHumidity) {
      _supportList.add(CONSTANT.centralizedControlKindHumidity);
    }
    if (isSupportFresh) {
      _supportList.add(CONSTANT.centralizedControlKindFresh);
    }
    return _supportList;
  }
}
