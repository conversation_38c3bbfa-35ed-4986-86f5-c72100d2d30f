/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> longzong<PERSON><EMAIL>
 * @Date: 2022-05-28 15:20:07
 * @Description: 快捷操控
 */
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/server/api.dart';
import 'package:whole_house_air/common/toast_helper.dart';
import 'package:whole_house_air/common/utils/util_log.dart';
import 'package:whole_house_air/common/utils/utils.dart';
import 'package:whole_house_air/datasource/device/device_protocol/model/wh_device_model.dart';
import 'package:whole_house_air/models/api_response_base_model.dart';
import 'package:whole_house_air/models/family_model.dart';
import 'package:whole_house_air/models/login_status_model.dart';
import 'package:whole_house_air/models/room_model.dart';
import 'package:whole_house_air/models/space_status_model.dart';
import 'package:whole_house_air/presentation/animation/quick_control_animation.dart';


class QuickManipulateScreen extends StatefulWidget {
  const QuickManipulateScreen(
      {Key? key,
      this.networkStatus,
      this.loginStatus,
      this.family,
      this.curRoom,
      required this.deviceList,
      required this.spaceType,
      required this.isFloorShow,
      this.houseSpaceData,
      this.selectRoom})
      : super(key: key);

  final bool? networkStatus;
  final LoginStatus? loginStatus;
  final FamilyModel? family;
  final RoomModel? curRoom;
  final List<WHDevInfoModel> deviceList;

  // 1：room（房间）  3：home（全屋）
  final String spaceType;
  final bool isFloorShow;
  final SpaceStatusModel? houseSpaceData;
  final SelectRoomModal? selectRoom;

  @override
  State<QuickManipulateScreen> createState() => _QuickManipulateScreenState();
}

class _QuickManipulateScreenState extends State<QuickManipulateScreen>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool isDisplayBtn = false;

  // 一键开启和关闭的样式 属性
  Map attrValue = {
    'width': 154.w,
    'height': 56.w,
    'deColor': Colors.white,
    'radius': 10.w,
    'textColor': Color(0xff000000).withOpacity(0.8),
    'transitionColor': Color(0xFFEAEAEA)
  };

  // 完成
  // 控制
  _control(bool onOffStatus) async {
    // if (widget.networkStatus !.isOnline) {
    //   ToastHelper.showToast(CONSTANT.NET_WORK_ERROR, context);
    //   return;
    // }
    // 此处使用store中的networkStatus,会出现bug
    // x_haieruplus-4477(OPPO手机断网后网络状态偶现不更新),所以在这里重新请求网络状态
    final IsOnline isOnline = await Network.isOnline();
    if (isOnline.isOnline) {
      gioTrack(widget.spaceType == '3' ? 'MB30317' : 'MB30328',
          {'status': onOffStatus.toString()});
      _quickControl(onOffStatus);
    } else {
      ToastHelper.showToast(CONSTANT.NET_WORK_ERROR, context);
    }
  }

  // 一键开启/一键关闭操作
  Future<void> _quickControl(bool onOffStatus) async {
    try {
      // 获取roomIds
      List<String> roomIds = [];
      // 全屋时，获取所有房间
      if (widget.spaceType == '3') {
        if (widget.selectRoom != null &&
            widget.selectRoom?.roomName != '全部房间') {
          roomIds.add(widget.selectRoom?.roomId ?? '');
        } else {
          if (widget.houseSpaceData?.subSpaces != null) {
            List<SubSpaces> floorSpaces =
                widget.houseSpaceData?.subSpaces ?? [];
            if (floorSpaces.length > 0) {
              floorSpaces.forEach((element) {
                if (element.subSpaces != null &&
                    element.subSpaces!.length > 0) {
                  element.subSpaces!.forEach((cell) {
                    roomIds.add(cell.spaceId ?? '');
                  });
                }
              });
            }
          }
        }
      } else {
        roomIds.add(widget.curRoom?.roomId ?? '');
      }

      final ApiResponseBaseModel? apiResponseBaseModel =
          await API.quickControl({
        'familyId': widget.family?.familyId ?? '',
        'roomIds': roomIds,
        'spaceType': widget.spaceType,
        'onOffStatus': onOffStatus,
      });

      // print('一键开启/一键关闭操作：$res');
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: '_quickControl $apiResponseBaseModel');
      if (apiResponseBaseModel != null &&
          apiResponseBaseModel.retCode == CONSTANT.REQUEST_SUCCESS_CODE) {
        // 开、关命令下发完成后，过3.5秒遍历设备列表做提示
        Future.delayed(const Duration(milliseconds: 3500), () {
          List operationFailureDeviceList = []; // 操作失败的设备列表
          for (int i = 0; i < widget.deviceList.length; i++) {
            final device = widget.deviceList[i];
            if (onOffStatus &&
                (device.onlineStateV2 != 'ONLINE_READY' ||
                    device.attrs['onOffStatus']?.value != 'true')) {
              // 找出 非在线就绪 未开机成功的设备
              if (widget.selectRoom != null &&
                  widget.selectRoom?.roomName != '全部房间') {
                if (device.basicInfo.roomInfo.roomId ==
                    widget.selectRoom?.roomId) {
                  operationFailureDeviceList.add(device);
                }
              } else {
                operationFailureDeviceList.add(device);
              }
            }
            if (!onOffStatus &&
                (device.onlineStateV2 != 'ONLINE_READY' ||
                    device.attrs['onOffStatus']?.value != 'false')) {
              // 找出  非在线就绪 未关机成功的设备
              if (widget.selectRoom != null &&
                  widget.selectRoom?.roomName != '全部房间') {
                if (device.basicInfo.roomInfo.roomId ==
                    widget.selectRoom?.roomId) {
                  operationFailureDeviceList.add(device);
                }
              } else {
                operationFailureDeviceList.add(device);
              }
            }
          }
          if (operationFailureDeviceList.length == 1) {
            // 如果只有一个设备操作失败，则提示用户
            // 楼层
            String floorName = widget.isFloorShow && widget.spaceType == '3'
                ? (operationFailureDeviceList[0].basicInfo.floorInfo.floorName
                        is String
                    ? operationFailureDeviceList[0]
                        .basicInfo
                        .floorInfo
                        .floorName as String
                    : '')
                : '';
            // 房间
            String roomName = operationFailureDeviceList[0]
                    .basicInfo
                    .roomInfo
                    .roomName is String
                ? operationFailureDeviceList[0].basicInfo.roomInfo.roomName
                    as String
                : '';
            // 设备名称
            final String deviceName = operationFailureDeviceList[0]
                    .basicInfo
                    .deviceName is String
                ? operationFailureDeviceList[0].basicInfo.deviceName as String
                : '';
            ToastHelper.showToast(
                '$floorName$roomName$deviceName未能${onOffStatus ? '开启' : '关闭'}，请检查设备状态',
                context);
          } else if (operationFailureDeviceList.length > 1) {
            ToastHelper.showToast(
                '${operationFailureDeviceList.length}个设备执行异常，请检查相关设备状态',
                context);
          } else if (operationFailureDeviceList.length == 0) {
            ToastHelper.showToast('${onOffStatus ? '开启' : '关闭'}成功', context);
          }
        });
      } else {
        ToastHelper.showToast(CONSTANT.REQUEST_ERROR, context);
      }
    } catch (err) {
      ToastHelper.showToast(CONSTANT.REQUEST_ERROR, context);
      DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_quickControl err:$err');
    }
  }

  // 判断是否显示一键开启/一键关闭
  void _determineWhetherToDisplay() {
    setState(() {
      isDisplayBtn = widget.deviceList.length > 1 &&
          !(widget.deviceList
              .every((element) => element.basicInfo.bigClass == '29'));
    });
  }

  @override
  void initState() {
    _determineWhetherToDisplay();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void didUpdateWidget(QuickManipulateScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 设备列表是否变化
    if (isListEqual(oldWidget.deviceList, widget.deviceList) !.isOnline) {
      _determineWhetherToDisplay();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return isDisplayBtn
        ? Container(
            alignment: Alignment.center,
            height: 80.w,
            padding: EdgeInsets.fromLTRB(12.w, 16.w, 12.w, 8.w),
            child: Row(
              children: [
                Flexible(
                    child: quickControlAnimation(
                        quickControl: _control,
                        isOpen: true,
                        text: '一键开启',
                        attr: attrValue,
                        isColorAnimation: true)),
                Container(width: 11.w, height: 56.w),
                Flexible(
                    child: quickControlAnimation(
                        quickControl: _control,
                        isOpen: false,
                        text: '一键关闭',
                        attr: attrValue,
                        isColorAnimation: true))
              ],
            ))
        : Container();
  }
}
