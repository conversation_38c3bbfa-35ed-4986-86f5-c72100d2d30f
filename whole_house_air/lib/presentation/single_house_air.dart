/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> longzong<PERSON><EMAIL>
 * @Date: 2022-05-26 19:00:55
 * @description: 单空间空气
 */

import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:log/log.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:redux/redux.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart' as userPlugin;
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/server/api.dart';
import 'package:whole_house_air/common/toast_helper.dart';
import 'package:whole_house_air/common/utils/util_log.dart';
import 'package:whole_house_air/common/utils/util_log_tag.dart';
import 'package:whole_house_air/containers/page_container.dart';
import 'package:whole_house_air/datasource/air/air_impl/air_datasource.dart';
import 'package:whole_house_air/datasource/air/air_protocol/air_api.dart';
import 'package:whole_house_air/datasource/device/device_protocol/model/wh_device_model.dart';
import 'package:whole_house_air/models/family_model.dart';
import 'package:whole_house_air/models/login_status_model.dart';
import 'package:whole_house_air/models/room_model.dart';
import 'package:whole_house_air/store/cur_room/cur_room_action.dart';
import 'package:whole_house_air/store/device_list/device_list_action.dart';
import 'package:whole_house_air/store/family/family_action.dart';
import 'package:whole_house_air/store/is_device_list_change/is_device_list_change_action.dart';
import 'package:whole_house_air/store/login_status/login_status_action.dart';
import 'package:whole_house_air/store/network_status/network_status_action.dart';
import 'package:whole_house_air/store/oauth_data/oauth_data_action.dart';
import 'package:whole_house_air/store/reducer.dart';

// 该组件的key, 给父调用该组件的方法使用
GlobalKey singleScreenStateKey = GlobalKey();

class SingleHouseAir extends StatefulWidget {
  SingleHouseAir({Key? key, this.params}) : super(key: key);

  final Map<String, dynamic>? params;

  @override
  State<StatefulWidget> createState() => _SingleHouseAirState();
}

class _SingleHouseAirState extends State<SingleHouseAir>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin
    implements AirSystemProtocol {
  @override
  bool get wantKeepAlive => true;

  Store<WholeHouseAirState> _store = Store<WholeHouseAirState>(
      wholeHouseAirReducer,
      initialState: WholeHouseAirState.initial());
  // 初始化dataSource
  AirDataSource dataSource = AirDataSource();
  // 登出监听
  StreamSubscription<dynamic>? _userLogoutListener;
  // 用户刷新
  StreamSubscription<dynamic>? _userRefreshListener;
  // 当前家庭监听
  StreamSubscription<dynamic>? _curFamilyChangedListener;
  // 家庭列表监听
  StreamSubscription<dynamic>? _familyListChangedListener;
  // 用户信息刷新完成监听
  StreamSubscription<dynamic>? _userRefreshCompleteListener;
  // 网络监听回调
  late void Function(NetworkStatus) _networkStatusChangeCallback;
  // App回到前台监听
  StreamSubscription<AppResumeMessage>? _resumeMessage;
  // App进入后台监听
  StreamSubscription<AppPauseMessage>? _pauseMessage;

  String? roomId; // 房间ID
  String? roomName; // 房间名称
  String? floor; // 楼层名称
  String? floorId; // 楼层ID
  bool? isFloorShow; // 是否显示楼层 true 显示 false 不显示
  List<dynamic>? pageList; // 房间列表数据 包括房间名称 楼层名称
  String? familyId; // 家庭id

  @override
  void initState() {
    try {
      super.initState();
      // 处理跳转携带的参数
      setState(() {
        //TODO  widget.params != null的这种通用判断后续是否可以前置，减少判断逻辑，提升代码可读性。示： if (widget.params == null) { 变量恢复默认值 return;}

        roomId = widget.params != null && widget.params!['roomId'] != null
            ? widget.params!.stringValueForKey('roomId', '')
            : '';
        roomName = widget.params != null && widget.params!['roomName'] != null
            ? widget.params!.stringValueForKey('roomName', '')
            : '';
        floor = widget.params != null && widget.params!['floor'] != null
            ? widget.params!.nullableStringValueForKey('floor')
            : null;
        floorId = widget.params != null && widget.params!['floorId'] != null
            ? widget.params!.nullableStringValueForKey('floorId')
            : null;
        isFloorShow =
            widget.params != null && widget.params!['isFloorShow'] != null
                ? widget.params!['isFloorShow'] == 'true'
                    ? true
                    : false
                : null;
        pageList = widget.params != null && widget.params!['pageList'] != null
            ? jsonDecode(widget.params!.stringValueForKey('pageList', ''))
                    is List<dynamic>
                ? jsonDecode(widget.params!.stringValueForKey('pageList', ''))
                    as List<dynamic>
                : null
            : null;
        familyId = widget.params != null && widget.params!['familyId'] != null
            ? widget.params!.stringValueForKey('familyId', '')
            : '';
      });

      // 打开日志开关
      Log.enableConsoleLog(true);
      // 初始化
      _initData();
      // 防止一级页didDisappear dataSource.removeListener()导致二级页监听失效
      Future.delayed(Duration(milliseconds: 1000), () {
        // 注册dataSource监听
        dataSource.addListener(this);
        // 查询设备列表
        _getDeviceList(roomId ?? '');
        // 能弹toast
        ToastHelper.updateCanShow(true);
      });
      // 获取网络
      updateNetworkStatus(type: 'init');

      // 将当前房间更新到store
      final RoomModel curRoom = RoomModel.fromJson(
          {'roomId': roomId, 'roomName': roomName, 'floor': floor});
      _store.dispatch(CurRoomAction(
          CurRoomActionType.CUR_ROOM_UPDATE, CurRoomPayload(curRoom)));
      /******************************************** 监听初始化 开始 ****************************************/

      // 网络状态监听
      // 注意，android启动app会触发，但iOS不会，所以init时需要主动get一次
      _networkStatusChangeCallback = (NetworkStatus status) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_networkListener:${status.isOnline}');
        _store.dispatch(NetworkStatusAction(
            NetworkStatusActionType.NETWORK_STATUS_UPDATE,
            NetworkStatusPayload(status.isOnline)));
      };
      Network.addNetStateEventListener(_networkStatusChangeCallback);

      // 登出监听
      _userLogoutListener = Message.listen<UserLogoutMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_userLogoutListener:$event');
        // 登出后重置消息，如果退出登录前是我的tab，则回到推荐tab
        _resetData();
      });

      // 当前家庭变化监听
      _curFamilyChangedListener =
          Message.listen<UserCurrentFamilyChangeMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_curFamilyChangedListener:$event');
        _getCurFamily();
      });

      // 家庭列表变化监听
      _familyListChangedListener =
          Message.listen<UserFamilyListChangeMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_familyListChangedListener:$event');
        // 家庭侧边栏点击家庭管理，删除家庭，返回服务页
        // 家庭列表监听触发晚于返回服务页面触发的获取的当前家庭
        // 因此会出现返回到服务页，请求当前家庭，后家庭列表发生变化，当前家庭发生变化，
        // 之前添加判断，没有重新去哪家庭
        _getCurFamily();
      });

      // 用户信息刷新成功监听
      _userRefreshCompleteListener =
          Message.listen<UserRefreshCompletedMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: 'UserRefreshCompletedMessage:$event');
        _getCurFamily();
      });

      // iOS将app放置后台后打开时，不触发didAppear和disappear，所以添加以下两个监听
      /// 回到前台监听
      _resumeMessage = Message.listen<AppResumeMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG,
            msg: {'fn': '_resumeMessage', 'data': event});
        // 页面每次回到前台重新获取一下基础信息(登录状态，当前家庭，用户信息等)
        _initData();
        // 获取网络
        updateNetworkStatus(type: 'init');
      });

      /// 进入后台监听
      _pauseMessage = Message.listen<AppPauseMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: {'fn': '_pauseMessage', 'data': event});
      });
      /******************************************** 监听初始化 结束 ****************************************/
      /***************************************************************************************************/

      DevLogger.debug(tag: CONSTANT.LOG_TAG, msg: 'initState');
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': 'initState', 'err': err});
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  String get pageName => 'SingleHouseAir';

  void onPageShow() {
    // 更新推荐场景 从h5页面返回的时候更新
    _getSceneData();
  }

  void onPageHide() {
    // 页面隐藏时的逻辑
  }

  void onPageDestroy() {
    // 页面销毁时的逻辑
  }

  /// 实现此方法，在tab切换或左右滑动切换时，将被回调
  void didDisappear() {
    // 关闭toast
    ToastHelper.closeToast();
    // 不能弹toast
    ToastHelper.updateCanShow(false);
    // 移除设备列表变化监听
    dataSource.removeListener();
    DevLogger.info(tag: TAG_DEVICES, msg: 'WholeHouse didDisappear');
  }

  @override
  void dispose() {
    super.dispose();
    // 移除设备列表变化监听
    dataSource.removeListener();

    // 移除监听
    Network.removeNetStateEventListener(_networkStatusChangeCallback);
    _userLogoutListener?.cancel();
    _userRefreshListener?.cancel();
    _curFamilyChangedListener?.cancel();
    _familyListChangedListener?.cancel();
    _userRefreshCompleteListener?.cancel();
    _resumeMessage?.cancel();
    _pauseMessage?.cancel();

    // 关闭toast
    ToastHelper.closeToast();
    // 不能弹toast
    ToastHelper.updateCanShow(false);

    ///移除监听器
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: 'dispose');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ScreenUtilInit(
        designSize: Size(375, 667),
        builder: () => StoreProvider<WholeHouseAirState>(
              store: _store,
              child: Container(
                  child: PageContainer(
                      getDeviceList: _getDeviceList,
                      roomId: roomId ?? '',
                      pageList: pageList ?? [],
                      isFloorShow: isFloorShow,
                      familyId: familyId)),
            ));
  }

  // 更新网络状态
  // init时 获取网络状态，用来判断是否是断网第一次打开场景tab，是否需要展示断网页面
  Future<void> updateNetworkStatus({String type = 'update'}) async {
    try {
      final IsOnline isOnline = await Network.isOnline();
      _store.dispatch(NetworkStatusAction(
          NetworkStatusActionType.NETWORK_STATUS_UPDATE,
          NetworkStatusPayload(isOnline.isOnline)));
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {
        'fn': 'updateNetworkStatus',
        'err': {err, type}
      });
    }
  }

  // 初始化登录 -> 鉴权 和 用户手机号 -> 当前家庭
  Future<void> _initData({String type = 'update'}) async {
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_initData begin type:$type');
    try {
      await _getLoginStatus();
      // 1 已登录，拿用户及家庭信息
      // 2 未登录，清空家庭信息
      try {
        if (_store.state.loginStatusState.loginStatus == LoginStatus.login) {
          await _getOauthData();
          await _getCurFamily();
        } else {
          _resetData();
        }
      } catch (err) {
        DevLogger.error(tag: CONSTANT.LOG_TAG, msg: '_initData error $err');
      }

      DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_initData end type:$type');
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: '_initData err: $err');
    }
  }

  // 拿登录信息
  Future<void> _getLoginStatus() async {
    try {
      final res = await userPlugin.User.getLoginStatus();
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getLoginStatus', 'data': res});
      LoginStatus loginStatus = res.isLogining
          ? LoginStatus.loginIng
          : (res.isLogin ? LoginStatus.login : LoginStatus.logout);
      _store.dispatch(LoginStatusAction(
          LoginStatusActionType.LOGIN_STATUS_UPDATE,
          LoginStatusPayload(loginStatus)));
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {'_getLoginStatus err': err});
    }
  }

  // 获取鉴权信息
  Future<void> _getOauthData() async {
    try {
      final res = await userPlugin.User.getOauthData();
      DevLogger.info(tag: CONSTANT.LOG_TAG, msg: {'_getOauthData': res});
      // 更新鉴权
      _store.dispatch(OauthDataAction(
          OauthDataActionType.OAUTH_DATA_UPDATE, OauthDataPayload(res)));
      // 更新api的鉴权信息
      API.updateApiParams(oauthData: res);
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {'_getOauthData err': err});
    }
  }

  // 拿家庭信息
  Future<void> _getCurFamily() async {
    try {
      var data = await Family.getCurrentFamily();
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getCurFamily', 'data': data});
      if (data.familyId == '') {
        final FamilyModel family = FamilyModel.fromJson(
            {'familyId': '', 'familyName': '', 'floorInfos': <dynamic>[]});
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      } else {
        final FamilyModel family = FamilyModel.fromJson({
          'familyId': data.familyId,
          'familyName': data.info.familyName,
          'floorInfos': data.floorInfos
        });
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getCurFamily', 'error': err});
      // 正在刷新数据时，不处理；其他的错误，表示家庭获取失败了，显示‘点击刷新信息’
      if (!(err is PlatformException &&
          err.code == CONSTANT.REFRESHING_USER_DATA_CODE)) {
        final FamilyModel family = FamilyModel.fromJson(
            {'familyId': '', 'familyName': '', 'floorInfos': <dynamic>[]});
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      }
    }
  }

  // 登出后重置数据 未登录、鉴权为null, 当前家庭为null
  Future<void> _resetData() async {
    try {
      // 更新api里面的oauthData
      API.updateApiParams(
          oauthData: OauthData.fromMap({
        'retData': {
          'access_token': '',
          'refresh_token': '',
          'uhome_access_token': '',
          'uhome_user_id': ''
        }
      }));
      _store.dispatch(LoginStatusAction(
          LoginStatusActionType.LOGIN_STATUS_UPDATE,
          LoginStatusPayload(LoginStatus.logout)));
      _store.dispatch(OauthDataAction(
          OauthDataActionType.OAUTH_DATA_UPDATE, OauthDataPayload(null)));
      _store.dispatch(
          FamilyAction(FamilyActionType.FAMILY_UPDATE, FamilyPayload(null)));
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: '_resetData err: $err');
    }
  }

  // 获取设备列表
  void _getDeviceList(String roomId) {
    try {
      dataSource.queryDeviceData(
          roomId,
          (List<WHDevInfoModel> deviceList) => {
                _store.dispatch(DeviceListAction(
                    DeviceListActionType.DEVICE_LIST_UPDATE,
                    DeviceListPayload(deviceList))),
              });
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: 'queryDeviceData err: $err');
    }
  }

  @override
  void airDeviceListChanged(List<WHDevInfoModel> deviceList) {
    try {
      _store.dispatch(DeviceListAction(DeviceListActionType.DEVICE_LIST_UPDATE,
          DeviceListPayload(deviceList)));
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: 'airDeviceListChanged err: $err');
    }
  }

  @override
  void airDeviceListChangedNotify() {
    try {
      int count = new DateTime.now().millisecondsSinceEpoch;
      _store.dispatch(IsDeviceListChangeAction(
          IsDeviceListChangeActionType.IS_DEVICE_LIST_CHANGE,
          IsDeviceListChangePayload(count)));
      // 更新推荐场景
      _getSceneData();
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: 'airDeviceListChangedNotify err: $err');
    }
  }

  // 更新推荐场景
  void _getSceneData() {
    try {
      // 更新推荐场景
      dynamic wholeHouseScenePage = singleScreenStateKey.currentState;
      if (wholeHouseScenePage != null) {
        DevLogger.info(
            tag: CONSTANT.LOG_TAG,
            msg: {'fn': 'sing_house_devices_list_change_scene'});
        wholeHouseScenePage.doRefreshData();
      }
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: 'getSceneData err: $err');
    }
  }
}
