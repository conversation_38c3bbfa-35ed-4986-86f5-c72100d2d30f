/*
 * @Author: long<PERSON><PERSON><PERSON> longzong<PERSON><EMAIL>
 * @Date: 2022-05-26 19:00:55
 */
library wholehouseair;

import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:log/log.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:redux/redux.dart';
import 'package:storage/storage.dart';
import 'package:uplustrace/uplustrace.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/user.dart' as userPlugin;
import 'package:vdn/vdn.dart';
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/server/api.dart';
import 'package:whole_house_air/common/toast_helper.dart';
import 'package:whole_house_air/common/utils/util_log_tag.dart';
import 'package:whole_house_air/common/utils/utils.dart';
import 'package:whole_house_air/containers/home_container.dart';
import 'package:whole_house_air/datasource/air/air_impl/air_datasource.dart';
import 'package:whole_house_air/datasource/air/air_protocol/air_api.dart';
import 'package:whole_house_air/datasource/device/device_protocol/model/wh_device_model.dart';
import 'package:whole_house_air/datasource/device/util/device_datasource_model_builder.dart';
import 'package:whole_house_air/models/family_model.dart';
import 'package:whole_house_air/models/login_status_model.dart';
import 'package:whole_house_air/store/animation_repeat/animation_repeat_action.dart';
import 'package:whole_house_air/store/device_list/device_list_action.dart';
import 'package:whole_house_air/store/family/family_action.dart';
import 'package:whole_house_air/store/is_appear/is_appear_action.dart';
import 'package:whole_house_air/store/is_device_list_change/is_device_list_change_action.dart';
import 'package:whole_house_air/store/is_tourist_mode/is_tourist_mode_action.dart';
import 'package:whole_house_air/store/login_status/login_status_action.dart';
import 'package:whole_house_air/store/network_status/network_status_action.dart';
import 'package:whole_house_air/store/oauth_data/oauth_data_action.dart';
import 'package:whole_house_air/store/reducer.dart';
import 'common/utils/util_log.dart';

GlobalKey appKey = GlobalKey<State>(debugLabel: CONSTANT.PACKAGE_NAME);
// 空气一级页 推荐场景使用key
GlobalKey wholeHouseAirSceneKey = GlobalKey();

class WholeHouseAir extends StatefulWidget {
  WholeHouseAir(
      {Key? key, required this.appbarHeight, required this.appbarChangeHeight})
      : super(key: key);

  final double appbarHeight;
  final double appbarChangeHeight;

  @override
  StatefulElement createElement() {
    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      UplusTrace.startTrack(CONSTANT.TRACE_SCENE_AIR);
    });
    return super.createElement();
  }

  @override
  State<StatefulWidget> createState() => _WholeHouseAirState();
}

class _WholeHouseAirState extends State<WholeHouseAir>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin
    implements AirSystemProtocol {
  @override
  bool get wantKeepAlive => true;

  Store<WholeHouseAirState> _store = Store<WholeHouseAirState>(
      wholeHouseAirReducer,
      initialState: WholeHouseAirState.initial());

  // 初始化dataSource
  AirDataSource dataSource = AirDataSource();

  // 登出监听
  StreamSubscription<dynamic>? _userLogoutListener;

  // 当前家庭监听
  StreamSubscription<dynamic>? _curFamilyChangedListener;

  // 家庭列表监听
  StreamSubscription<dynamic>? _familyListChangedListener;

  // 用户刷新
  StreamSubscription<dynamic>? _userRefreshListener;

  // 用户信息刷新完成监听
  StreamSubscription<dynamic>? _userRefreshCompleteListener;

  // 网络监听回调
  late void Function(NetworkStatus) _networkStatusChangeCallback;

  // App回到前台监听
  StreamSubscription<AppResumeMessage>? _resumeMessage;

  // App进入后台监听
  StreamSubscription<AppPauseMessage>? _pauseMessage;

  @override
  void initState() {
    try {
      super.initState();
      gioTrack('MB30267');
      // 打开日志开关
      Log.enableConsoleLog(true);
      // 添加storage监听游客模式
      Storage.addNodeListner(CONSTANT.touristModeKey, storageChangeCallback);
      // 初始化
      _initData();
      // 注册dataSource监听
      dataSource.addListener(this);
      // 查询设备列表
      _getDeviceList();
      // 获取网络
      updateNetworkStatus(type: 'init');
      // 能弹toast
      ToastHelper.updateCanShow(true);
      // 获取动画缓存
      getAnimationStatus();

      /******************************************** 监听初始化 开始 ****************************************/

      // 动画repeat缓存的监听
      Storage.addDataListener(CONSTANT.WHOLE_HOUSE_ANIMATION_REPEAT_KEY,
          (_, __) {
        getAnimationStatus();
      });
      // 网络状态监听
      // 注意，android启动app会触发，但iOS不会，所以init时需要主动get一次
      _networkStatusChangeCallback = (NetworkStatus status) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_networkListener:${status.isOnline}');
        _store.dispatch(NetworkStatusAction(
            NetworkStatusActionType.NETWORK_STATUS_UPDATE,
            NetworkStatusPayload(status.isOnline)));
      };
      Network.addNetStateEventListener(_networkStatusChangeCallback);

      // 用户数据刷新监听
      _userRefreshListener = Message.listen<UserInfoRefreshedMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_userRefreshListener:$event');
        _initData();
      });

      // 登出监听
      _userLogoutListener = Message.listen<UserLogoutMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_userLogoutListener:$event');
        // 登出后重置消息，如果退出登录前是我的tab，则回到推荐tab
        _resetData();
      });

      // 当前家庭变化监听
      _curFamilyChangedListener =
          Message.listen<UserCurrentFamilyChangeMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_curFamilyChangedListener:$event');
        _getCurFamily();
      });

      // 家庭列表变化监听
      _familyListChangedListener =
          Message.listen<UserFamilyListChangeMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: '_familyListChangedListener:$event');
        // 家庭侧边栏点击家庭管理，删除家庭，返回服务页
        // 家庭列表监听触发晚于返回服务页面触发的获取的当前家庭
        // 因此会出现返回到服务页，请求当前家庭，后家庭列表发生变化，当前家庭发生变化，
        // 之前添加判断，没有重新去哪家庭
        _getCurFamily();
      });

      // 用户信息刷新成功监听
      _userRefreshCompleteListener =
          Message.listen<UserRefreshCompletedMessage>((event) {
        DevLogger.debug(
            tag: CONSTANT.LOG_TAG, msg: 'UserRefreshCompletedMessage:$event');
        _getCurFamily();
      });
      /******************************************** 监听初始化 结束 ****************************************/
      /***************************************************************************************************/

      DevLogger.debug(tag: CONSTANT.LOG_TAG, msg: 'initState');
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': 'initState', 'err': err});
    }
  }

  // 游客模式获取storage
  Future<void> getTouristModeData() async {
    try {
      var res = await Storage.getBooleanValue(CONSTANT.touristModeKey);
      _store.dispatch(IsTouristModeAction(
          IsTouristModeActionType.IS_TOURIST_MODE_UPDATE,
          IsTouristModePayload(res || true)));
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': 'getTouristModeData', 'err': err});
    }
  }

  // 游客模式storage监听回调
  void storageChangeCallback(String name, String action) {
    getTouristModeData();
  }

  /// 实现此方法，在tab切换或左右滑动切换时，将被回调
  void didAppear([Map<dynamic, dynamic>? args]) {
    gioTrack('MB30267');
    // print('页面看见了');
    // 网络更新，如果store中networkStatus=false, 则主动查询网络，处理android过夜网咯监听返回none
    if (_store.state.networkStatusState.networkStatus!.isOnline) {
      Timer(const Duration(milliseconds: 500), () {
        DevLogger.debug(tag: CONSTANT.LOG_TAG, msg: 'delay 500ms get network');
        updateNetworkStatus();
      });
    }
    // 重新获取一下基础信息(登录状态，当前家庭，用户信息等)
    _initData();

    // 能弹toast
    ToastHelper.updateCanShow(true);

    _store.dispatch(IsAppearAction(
        IsAppearActionType.IS_APPEAR_UPDATE, IsAppearPayload(true)));

    // 注册dataSource监听
    dataSource.addListener(this);
    // 查询设备列表
    _getDeviceList();
    DevLogger.info(tag: TAG_DEVICES, msg: 'WholeHouse didAppear');
  }

  /// 实现此方法，在tab切换或左右滑动切换时，将被回调
  void didDisappear() {
    _store.dispatch(IsAppearAction(
        IsAppearActionType.IS_APPEAR_UPDATE, IsAppearPayload(false)));
    // 关闭toast
    ToastHelper.closeToast();
    // 不能弹toast
    ToastHelper.updateCanShow(false);
    // 移除设备列表变化监听
    dataSource.removeListener();
    DevLogger.info(tag: TAG_DEVICES, msg: 'WholeHouse didDisappear');
  }

  String get pageName => 'WholeHouseAir';

  void onPageShow() {
    // 页面显示时的逻辑
    didAppear();
  }

  void onPageHide() {
    // 页面隐藏时的逻辑
    didDisappear();
  }

  void onPageDestroy() {
    // 页面销毁时的逻辑
  }

  @override
  void dispose() {
    super.dispose();
    // 移除游客模式监听
    Storage.removeNodeListener(CONSTANT.touristModeKey, storageChangeCallback);
    // 移除设备列表变化监听
    Storage.removeDataListener(
        CONSTANT.WHOLE_HOUSE_ANIMATION_REPEAT_KEY, (_, __) {});
    dataSource.removeListener();
    // 移除监听
    Network.removeNetStateEventListener(_networkStatusChangeCallback);
    _userLogoutListener?.cancel();
    _curFamilyChangedListener?.cancel();
    _familyListChangedListener?.cancel();
    _userRefreshListener?.cancel();
    _userRefreshCompleteListener?.cancel();
    _resumeMessage?.cancel();
    _pauseMessage?.cancel();
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: 'dispose');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ScreenUtilInit(
      designSize: Size(375, 667),
      builder: () => StoreProvider<WholeHouseAirState>(
          store: _store,
          child: IndexedStack(
            index: 0,
            children: [
              Container(
                  key: appKey,
                  child: Scaffold(
                    backgroundColor: Colors.transparent,
                    extendBodyBehindAppBar: true,
                    appBar: PreferredSize(
                      preferredSize: Size.fromHeight(44.w),
                      child: AppBar(
                        automaticallyImplyLeading: false,
                        shadowColor: Colors.transparent,
                        centerTitle: true,
                        leading: GestureDetector(
                          onTap: () => Vdn.close(),
                          child: Container(
                            height: double.infinity,
                            child: Center(
                              child: Padding(
                                padding: EdgeInsets.only(left: 16.w),
                                child: Image.asset(
                                  'assets/images/back.png',
                                  package: CONSTANT.PACKAGE_NAME,
                                  width: 24.w,
                                  height: 24.w,
                                ),
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          '全屋空气',
                          style: TextStyle(
                            fontSize: 17.sp,
                            fontWeight: FontWeight.w500,
                            color: Color.fromRGBO(0, 0, 0, 0.93),
                          ),
                        ),
                      ),
                    ),
                    body: HomeContainer(
                        appbarHeight: widget.appbarHeight,
                        appbarChangeHeight: widget.appbarChangeHeight,
                        getDeviceList: _getDeviceList,
                        refresInitData: _refresInitData,
                        houseKey: wholeHouseAirSceneKey),
                  )),
            ],
          )),
    );
  }

  // 获取动画是否repeat 缓存没值或者为true时，循环展示，否则只执行一次
  getAnimationStatus() async {
    try {
      final res = await Storage.getTemporaryStorage(
          CONSTANT.WHOLE_HOUSE_ANIMATION_REPEAT_KEY);
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: {'fn': 'getAnimationStatus', 'res': res});
      if (res != '') {
        final value = jsonDecode(res);
        if (value['value'] != null && value['value'] == 'false') {
          _store.dispatch(AnimationRepeatAction(
              AnimationRepeatActionType.ANIMATION_REPEAT_UPDATE,
              AnimationRepeatPayload(false)));
        }
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': 'getAnimationStatus', 'err': err});
    }
  }

  // 更新网络状态
  // init时 获取网络状态，用来判断是否是断网第一次打开场景tab，是否需要展示断网页面

  Future<void> updateNetworkStatus({String type = 'update'}) async {
    try {
      final IsOnline isOnline = await Network.isOnline();
      _store.dispatch(NetworkStatusAction(
          NetworkStatusActionType.NETWORK_STATUS_UPDATE,
          NetworkStatusPayload(isOnline.isOnline)));
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {
        'fn': 'updateNetworkStatus',
        'err': {err, type}
      });
    }
  }

  // 初始化登录 -> 鉴权 和 用户手机号 -> 当前家庭
  Future<void> _initData({String type = 'update'}) async {
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_initData begin type:$type');
    try {
      await _getLoginStatus();
      // 1 已登录，拿用户及家庭信息
      // 2 未登录，清空家庭信息
      try {
        if (_store.state.loginStatusState.loginStatus == LoginStatus.login) {
          await _getOauthData();
          await _getCurFamily();
        } else {
          _resetData();
        }
      } catch (err) {
        DevLogger.error(tag: CONSTANT.LOG_TAG, msg: '_initData error $err');
      }

      DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_initData end type:$type');
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: '_initData err: $err');
    }
  }

  // 拿登录信息
  Future<void> _getLoginStatus() async {
    try {
      final res = await userPlugin.User.getLoginStatus();
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getLoginStatus', 'data': res});
      LoginStatus loginStatus = res.isLogining
          ? LoginStatus.loginIng
          : (res.isLogin ? LoginStatus.login : LoginStatus.logout);
      _store.dispatch(LoginStatusAction(
          LoginStatusActionType.LOGIN_STATUS_UPDATE,
          LoginStatusPayload(loginStatus)));
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {'_getLoginStatus err': err});
    }
  }

  // 获取鉴权信息
  Future<void> _getOauthData() async {
    try {
      final res = await userPlugin.User.getOauthData();
      DevLogger.info(tag: CONSTANT.LOG_TAG, msg: {'_getOauthData': res});
      // 更新鉴权
      _store.dispatch(OauthDataAction(
          OauthDataActionType.OAUTH_DATA_UPDATE, OauthDataPayload(res)));
      // 更新api的鉴权信息
      API.updateApiParams(oauthData: res);
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: {'_getOauthData err': err});
    }
  }

  // 拿家庭信息
  Future<void> _getCurFamily() async {
    try {
      var data = await Family.getCurrentFamily();
      DevLogger.info(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getCurFamily', 'data': data});
      if (data.familyId == '') {
        final FamilyModel family = FamilyModel.fromJson(
            {'familyId': '', 'familyName': '', "floorInfos": <dynamic>[]});
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      } else {
        final FamilyModel family = FamilyModel.fromJson({
          'familyId': data.familyId,
          'familyName': data.info.familyName,
          'floorInfos': data.floorInfos
        });
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getCurFamily', 'error': err});
      // 正在刷新数据时，不处理；其他的错误，表示家庭获取失败了，显示‘点击刷新信息’
      if (!(err is PlatformException &&
          err.code == CONSTANT.REFRESHING_USER_DATA_CODE)) {
        final FamilyModel family = FamilyModel.fromJson(
            {'familyId': '', 'familyName': '', "floorInfos": <dynamic>[]});
        _store.dispatch(FamilyAction(
            FamilyActionType.FAMILY_UPDATE, FamilyPayload(family)));
      }
    }
  }

  // 登出后重置数据 未登录、鉴权为null, 当前家庭为null
  Future<void> _resetData() async {
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: '_resetData');
    // 更新api里面的oauthData
    API.updateApiParams(
        oauthData: OauthData.fromMap({
      'retData': {
        'access_token': '',
        'refresh_token': '',
        'uhome_access_token': '',
        'uhome_user_id': ''
      }
    }));
    _store.dispatch(LoginStatusAction(LoginStatusActionType.LOGIN_STATUS_UPDATE,
        LoginStatusPayload(LoginStatus.logout)));
    _store.dispatch(OauthDataAction(
        OauthDataActionType.OAUTH_DATA_UPDATE, OauthDataPayload(null)));
    _store.dispatch(
        FamilyAction(FamilyActionType.FAMILY_UPDATE, FamilyPayload(null)));
  }

  // 获取设备列表
  void _getDeviceList() {
    try {
      // 如果未登录 不获取设备列表
      if (_store.state.loginStatusState.loginStatus != LoginStatus.login) {
        return;
      }
      dataSource.queryDeviceData(
          ALLROOMID,
          (List<WHDevInfoModel> deviceList) => {
                _store.dispatch(DeviceListAction(
                    DeviceListActionType.DEVICE_LIST_UPDATE,
                    DeviceListPayload(deviceList))),
              });
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_getDeviceList', 'error': err});
    }
  }

  @override
  void airDeviceListChanged(List<WHDevInfoModel> deviceList) {
    try {
      _store.dispatch(DeviceListAction(DeviceListActionType.DEVICE_LIST_UPDATE,
          DeviceListPayload(deviceList)));
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG,
          msg: {'fn': 'airDeviceListChanged', 'error': err});
    }
  }

  @override
  void airDeviceListChangedNotify() {
    int count = new DateTime.now().millisecondsSinceEpoch;
    _store.dispatch(IsDeviceListChangeAction(
        IsDeviceListChangeActionType.IS_DEVICE_LIST_CHANGE,
        IsDeviceListChangePayload(count)));
    // 更新推荐场景
    _getSceneData();
  }

  // 更新推荐场景
  void _getSceneData() {
    try {
      // 更新推荐场景
      dynamic wholeHouseScenePage = wholeHouseAirSceneKey.currentState;
      if (wholeHouseScenePage != null) {
        DevLogger.info(
            tag: CONSTANT.LOG_TAG,
            msg: {'fn': 'house_devices_list_change_scene'});
        wholeHouseScenePage.doRefreshData();
      }
    } catch (err) {
      DevLogger.error(tag: CONSTANT.LOG_TAG, msg: 'getSceneData err: $err');
    }
  }

  // 下拉刷新、获取基础信息 网络、登陆状态、鉴权信息、家庭信息
  Future<void> _refresInitData() async {
    try {
      // 获取网络状态
      final IsOnline isOnline = await Network.isOnline();
      _store.dispatch(NetworkStatusAction(
          NetworkStatusActionType.NETWORK_STATUS_UPDATE,
          NetworkStatusPayload(isOnline.isOnline)));
      // 有网 获取_initData
      if (isOnline.isOnline) {
        _initData();
      }
    } catch (err) {
      DevLogger.error(
          tag: CONSTANT.LOG_TAG, msg: {'fn': '_refresInitData', 'error': err});
    }
  }
}
