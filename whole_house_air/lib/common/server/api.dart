/// 接口请求方法
import 'dart:convert';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:dio/dio.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/server/server.dart';
import 'package:whole_house_air/common/utils/util_log.dart';
import 'package:whole_house_air/common/utils/util_log_tag.dart';
import 'package:whole_house_air/common/utils/utils.dart';
import 'package:whole_house_air/models/api_response_base_model.dart';
import 'package:whole_house_air/models/centralized_model.dart';
import 'package:whole_house_air/models/space_control_v2_model.dart';
import 'package:whole_house_air/models/space_status_model.dart';
import 'package:whole_house_air/models/tips_model.dart';
import 'package:whole_house_air/models/weather_model.dart';

import '../../models/space_control_v1_model.dart';


class API {
  /// 获取一些本地存储的请求信息
  static String _appId = '';
  static String _appKey = '';
  static String _clientId = '';
  static String _appVersion = '';
  static String _env = '';
  static String _accessToken = '';
  static String _userCenterAccessToken = '';
  static String _userId = '';

  // 初始AppInfo，有的时候不更新，防止每次都去取
  static Future<void> initApiParams() async {
    try {
      if (_appId == '') {
        final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
        updateApiParams(appInfo: appInfo);
      }
    } catch (err) {
      DevLogger.error(
          tag: TAG_WHOLEHOUSE, msg: <String, dynamic>{'fn': 'initApiParams', 'error': err});
    }
  }

  // 更新参数
  static void updateApiParams({AppInfoModel? appInfo, OauthData? oauthData}) {
    // app信息
    if (appInfo != null) {
      _appId = appInfo.appId;
      _appKey = appInfo.appKey;
      _appVersion = appInfo.appVersion;
      _clientId = appInfo.clientId;
      _env = appInfo.env;
    }
    // 鉴权信息
    if (oauthData != null) {
      _accessToken = oauthData.uhome_access_token;
      _userCenterAccessToken = oauthData.user_center_access_token;
      _userId = oauthData.uhome_user_id;
    }
  }

  Future<void> getAppInfo() async {
    try {
      final AppInfoModel _appinfo = await AppInfoPlugin.getAppInfo();
      _appId = _appinfo.appId;
      _appKey = _appinfo.appKey;
      _clientId = _appinfo.clientId;
      _appVersion = _appinfo.appVersion;
      if (_appVersion.isEmpty) {
        _appVersion = '7.0.0';
      }
    } catch (e) {
      DevLogger.debug(
          tag: TAG_WHOLEHOUSE, msg: 'getAppInfo exception: $e');
    }
  }

  // 获取天气
  static Future<WeatherServerModel?> getWeather(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.WEATHER_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      CONSTANT.IOT_URL + CONSTANT.WEATHER_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v4',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    WeatherServerModel? weatherServerModel;
    if (res is Map) {
      weatherServerModel = WeatherServerModel.fromJson(res);
    }
    return weatherServerModel;
  }

  // 查询全屋所有空间状态
  static Future<SpaceStatusServerModel?> getHouseSpace(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();
    final Map<String, dynamic> requestMap = params;
    final String bodyJson = json.encode(requestMap);
    final String sign = HTTP.getSign(
        CONSTANT.HOUSE_SPACE_URL, bodyJson, _appId, _appKey, timestamp);

    final dynamic res = await httpManager.postData(
        (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
            CONSTANT.HOUSE_SPACE_URL,
        params: requestMap,
        options: Options(headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        }));
    DevLogger.info(tag: CONSTANT.PACKAGE_NAME, msg: '_getNewHouseSpace $res');
    SpaceStatusServerModel? spaceStatusServerModel;
    if (res is Map) {
      spaceStatusServerModel = SpaceStatusServerModel.fromJson(res);
    }
    return spaceStatusServerModel;
  }

  // 查询单空间空气状态
  static Future<SpaceStatusServerModel?> getSingleHouseSpace(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
        CONSTANT.SINGLE_HOUSE_SPACE_URL, bodyJson, _appId, _appKey, timestamp);

    final dynamic res = await httpManager.postData(
        (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
            CONSTANT.SINGLE_HOUSE_SPACE_URL,
        params: requestMap,
        options: Options(headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'version': 0.6,
          'sign': sign,
        }));
    DevLogger.info(tag: CONSTANT.PACKAGE_NAME, msg: 'getHouseRoomSpace $res');
    SpaceStatusServerModel? spaceStatusServerModel;
    if (res is Map) {
      spaceStatusServerModel = SpaceStatusServerModel.fromJson(res);
    }
    return spaceStatusServerModel;
  }

  // 一键开启、一键关闭
  static Future<ApiResponseBaseModel?> quickControl(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.QUICK_CONTROL_URL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.QUICK_CONTROL_URL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v1',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    ApiResponseBaseModel? apiResponseBaseModel;
    if (res is Map) {
      apiResponseBaseModel = ApiResponseBaseModel.fromJson(res);
    }
    return apiResponseBaseModel;
  }

  static Future<TipsServerModel?> getTipsV4(Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.TIPS_URL_V4,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.TIPS_URL_V4,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v4',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    TipsServerModel? tipsServerModel;
    if (res is Map) {
      tipsServerModel = TipsServerModel.fromJson(res);
    }
    return tipsServerModel;
  }

  // 单房间小贴士V2
  static Future<TipsServerModel?> getSpaceTipsV2(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_TIPS_URL_V2,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );
    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_TIPS_URL_V2,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v2',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    TipsServerModel? tipsServerModel;
    if (res is Map) {
      tipsServerModel = TipsServerModel.fromJson(res);
    }
    return tipsServerModel;
  }

  // 全屋空间集控命令下发
  static Future<SpaceControlV2ServerModel?> spaceCentralizedControl(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_CENTRALIZED_CONTROL,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_CENTRALIZED_CONTROL,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v2',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: 'spaceCentralizedControl $res');

    SpaceControlV2ServerModel? serverModel;
    if (res is Map) {
      serverModel = SpaceControlV2ServerModel.fromJson(res);
    }
    return serverModel;
  }

  // 全屋空气智能优化开启关闭
  static Future<SpaceControlV1ServerModel?> spaceControlBatch(
      String? familyId, bool airServiceQuickStart) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = <String, dynamic>{
      'airServiceQuickStart': airServiceQuickStart,
      'familyId': familyId ?? ''
    };

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_CONTROL_BATCH,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_CONTROL_BATCH,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v2',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    DevLogger.info(tag: CONSTANT.LOG_TAG, msg: 'spaceControl $res');
    SpaceControlV1ServerModel? serverModel;
    if (res is Map) {
      serverModel = SpaceControlV1ServerModel.fromJson(res);
    }
    return serverModel;
  }

  // 查询全屋某个空间状态(集控)
  static Future<CentralizedStatusServerModel?> spaceCentralizedStatus(
      Map<String, dynamic> params) async {
    await initApiParams();
    final int timestamp = HTTP.getTimestamp();

    final Map<String, dynamic> requestMap = params;

    final String bodyJson = json.encode(requestMap);

    final String sign = HTTP.getSign(
      CONSTANT.SPACE_CENTRALIZED_STATUS,
      bodyJson,
      _appId,
      _appKey,
      timestamp,
    );

    final dynamic res = await httpManager.postData(
      (_env == '验收' ? CONSTANT.BASE_URL_YS : CONSTANT.BASE_URL) +
          CONSTANT.SPACE_CENTRALIZED_STATUS,
      params: requestMap,
      options: Options(
        headers: <String, dynamic>{
          'timestamp': timestamp.toString(),
          'sign': sign,
          'appId': _appId,
          'clientId': _clientId,
          'userId': _userId,
          'accessToken': _accessToken,
          'accountToken': _userCenterAccessToken,
          'appVersion': _appVersion,
          'apiVersion': 'v2',
          'sequenceId': DateTime.now().millisecondsSinceEpoch,
          'language': 'zh-cn'
        },
      ),
    );
    DevLogger.info(
        tag: CONSTANT.PACKAGE_NAME, msg: 'getSpaceCentralizedStatus $res');
    CentralizedStatusServerModel? centralizedStatusServerModel;
    if (res is Map) {
      centralizedStatusServerModel = CentralizedStatusServerModel.fromJson(res);
    }

    return centralizedStatusServerModel;
  }
}
