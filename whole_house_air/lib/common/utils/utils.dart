import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:trace/trace.dart';
import 'package:vdn/vdn.dart';
import 'package:whole_house_air/common/constant.dart';
import 'package:whole_house_air/common/server/api.dart';
import 'package:whole_house_air/common/toast_helper.dart';
import 'package:whole_house_air/common/utils/util_log.dart';
import 'package:whole_house_air/models/room_space_model.dart';
import 'package:whole_house_air/models/single_property_model.dart';
import 'package:whole_house_air/models/space_status_model.dart';



typedef VoidCallback = void Function();

/// vdn跳转防抖
class Debounce {
  //函数防抖初始时间, 点击间隔超过1s, 才再次执行
  static int lastTime = 0;

  static void run(VoidCallback action) {
    final int currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - lastTime > 1000) {
      action();
    }
    lastTime = currentTime;
  }
}

// gio打点, 参数可选variable
Future<void> gioTrack(String eventId, [Map<String, dynamic>? variable]) async {
  try {
    if (variable != null) {
      // 循环判断每一项是否为null或者空字串，是的话则修改value为'null'
      if (variable.isNotEmpty) {
        variable.forEach((String key, dynamic value) {
          if (value == null || value == '') {
            variable[key] = 'null';
          }
        });
      }
      DevLogger.debug(tag: CONSTANT.PACKAGE_NAME, msg: <String, dynamic>{
        'fn': 'gioTrack',
        'data': <String, dynamic>{'eventId': eventId, 'variable': variable}
      });
      await Trace.traceEventWithVariable(eventId: eventId, variable: variable);
    } else {
      DevLogger.debug(tag: CONSTANT.PACKAGE_NAME, msg: <String, dynamic>{
        'fn': 'gioTrack',
        'data': <String, String>{'eventId': eventId}
      });
      await Trace.traceEvent(eventId: eventId);
    }
  } catch (err) {
    DevLogger.error(
        tag: CONSTANT.PACKAGE_NAME, msg: <String, dynamic>{'fn': 'gioTrack', 'err': err});
  }
}

// vdn跳转
Future<void> goToPage(String url, {Map<String, dynamic>? params}) async {
  Debounce.run(() async {
    try {
      final IsOnline isOnline = await Network.isOnline();
      if (!isOnline.isOnline) {
        ToastHelper.showToast(CONSTANT.NET_WORK_ERROR);
      } else {
        Vdn.goToPage(url, params: params);
      }
    } catch (err) {
      DevLogger.error(tag: CONSTANT.PACKAGE_NAME, msg: <String, dynamic>{'goToPage': err});
    }
  });
}

bool isListEqual(List<dynamic> list1, List<dynamic> list2) {
  if (identical(list1, list2)) {
    return true;
  }
  int length1 = list1.length;
  int length2 = list2.length;
  if (length1 != length2) {
    return false;
  }
  for (int i = 0; i < length1; i++) {
    if (list1[i] != list2[i]) {
      return false;
    }
  }
  return true;
}

int listHashCode(List<dynamic> list) {
  int hashCode = 0;
  for (final dynamic element in list) {
    hashCode = hashCode ^ element.hashCode;
  }
  return hashCode;
}

// Http请求类
class HTTP {
  // hash string
  static const String HEX_STRING = '0123456789abcdef';

  // 获取时间戳
  static int getTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  // 设置请求头
  static Map<String, dynamic> commonHeaders() {
    return <String, dynamic>{
      'Content-Type': 'application/json;charset=UTF-8',
      'timestamp': getTimestamp().toString(),
      'sequenceId': getTimestamp().toString(),
    };
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getSign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    final Uint8List bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    final List<int> list = sha256.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }
    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  /// 获取请求签名
  /// [urlPath] 请求url路径
  /// [paramsJson] 请求参数Json字符串(请求body)
  /// [appId] appId
  /// [appKey] appKey
  /// [timestamp] 时间戳（毫秒）
  static String getMD5Sign(
    String urlPath,
    String paramsJson,
    String appId,
    String appKey,
    int timestamp,
  ) {
    final Uint8List bytes = utf8
        .encode(urlPath + paramsJson + appId + appKey + timestamp.toString());
    final List<int> list = md5.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }
    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }
}

/// 函数防抖
///
/// [func]: 要执行的方法
/// [delay]: 要迟延的时长
/// [immediately]: 是否立即执行
Function debounce(Function? func,
    [Duration delay = const Duration(milliseconds: 2000),
    bool immediately = true]) {
  Timer? timer;
  void target() {
    if (timer?.isActive ?? false) {
      timer?.cancel();
    }
    // 立即执行
    if (immediately) {
      // 没有定时器，立即执行
      final bool callNow = timer == null;
      // 给定时器赋值
      timer = Timer(delay, () {
        timer!.cancel();
        timer = null;
      });
      if (callNow) {
        func?.call();
      }
    } else {
      timer = Timer(delay, () {
        func?.call();
      });
    }
  }
  return target;
}

// 温度角标
Map<dynamic, dynamic> setTempSign(String? weatherTemp) {
  final Map<dynamic, dynamic>  tempMap = <dynamic, dynamic>{'text': '', 'color': 0};
  if (weatherTemp != '' && weatherTemp != null) {
    double tempCount = double.parse(weatherTemp);
    if (tempCount < 20) {
      tempMap['text'] = '冷';
      tempMap['color'] = 0xff32beff;
    } else if (tempCount >= 20 && tempCount <= 27) {
      tempMap['text'] = '适宜';
      tempMap['color'] = 0xff44be3b;
    } else {
      tempMap['text'] = '热';
      tempMap['color'] = 0xffffb740;
    }
  }
  return tempMap;
}

// 湿度角标
Map<dynamic, dynamic>  setHumiditySign(String? weatherHumidity) {
  final Map<dynamic, dynamic>  humidityMap = <dynamic,dynamic>{'text': '', 'color': 0};
  if (weatherHumidity != '' && weatherHumidity != null) {
    double count = double.parse(weatherHumidity);
    if (count < 30) {
      humidityMap['text'] = '干燥';
      humidityMap['color'] = 0xffffb740;
    } else if (count >= 30 && count <= 70) {
      humidityMap['text'] = '适宜';
      humidityMap['color'] = 0xff44be3b;
    } else {
      humidityMap['text'] = '潮湿';
      humidityMap['color'] = 0xff32beff;
    }
  }
  return humidityMap;
}

// 温度角标使用,Serve数据
Map<dynamic, dynamic> setTempLevelSign(String? weatherTemp) {
  final Map<dynamic, dynamic> tempMap = <dynamic, dynamic>{'text': '', 'color': 0};
  if (weatherTemp != '' && weatherTemp != null) {
    final int tempCount = int.tryParse(weatherTemp) ?? 0;
    if (tempCount == 1) {
      tempMap['text'] = '冷';
      tempMap['color'] = 0xff32beff;
    } else if (tempCount == 0) {
      tempMap['text'] = '适宜';
      tempMap['color'] = 0xff44be3b;
    } else {
      tempMap['text'] = '热';
      tempMap['color'] = 0xffffb740;
    }
  }
  return tempMap;
}

Map<dynamic, dynamic> setHumidityLevelSign(String? weatherHumidity) {
  final Map<dynamic, dynamic> humidityMap = <dynamic, dynamic>{'text': '', 'color': 0};
  if (weatherHumidity != '' && weatherHumidity != null) {
    final int count = int.tryParse(weatherHumidity) ?? 0;
    if (count == 1) {
      humidityMap['text'] = '干燥';
      humidityMap['color'] = 0xffffb740;
    } else if (count == 0) {
      humidityMap['text'] = '适宜';
      humidityMap['color'] = 0xff44be3b;
    } else {
      humidityMap['text'] = '潮湿';
      humidityMap['color'] = 0xff32beff;
    }
  }
  return humidityMap;
}

// pm2.5角标
Map<dynamic, dynamic> setAirPmSign(String? pm25) {
  final Map<dynamic, dynamic> pm25Map = <dynamic, dynamic>{'text': '', 'color': 0};
  if (pm25 != '' && pm25 != null) {
    int count = int.parse(pm25);
    if (count < 35) {
      pm25Map['text'] = '优';
      pm25Map['color'] = 0xff44be3b;
    } else if (count >= 35 && count <= 50) {
      pm25Map['text'] = '良';
      pm25Map['color'] = 0xffffb740;
    } else {
      pm25Map['text'] = '差';
      pm25Map['color'] = 0xffed2856;
    }
  }
  return pm25Map;
}

//计算舒适度
String calculateComfortLevel(String level) {
  // 0：舒适 91：偏冷 92：偏热 19：干燥 29：潮湿 11：干冷 21：湿冷 12：燥热 22：湿热
  final Map<dynamic, dynamic> instance = <dynamic, dynamic>{
    '0': '舒适',
    '91': '冷',
    '92': '热',
    '19': '干燥',
    '29': '潮湿',
    '11': '干冷',
    '21': '湿冷',
    '12': '燥热',
    '22': '湿热',
  };
  final String tempValue =
      instance[level] is String ? instance[level] as String : '--';
  return tempValue;
}

//计算空气质量
String calculateAirLevel(String level) {
  final Map<dynamic, dynamic> instance = <dynamic, dynamic>{
    '0': '优',
    '1': '良',
    '3': '差',
  };
  final String tempValue =
      instance[level] is String ? instance[level] as String : '--';
  return tempValue;
}

//计算甲醛、PM2.5、TVOC、CO2 角标展示
ShowLabel calculateLevel(String source) {
  final Map<dynamic, dynamic> obj = <dynamic, dynamic>{'text': '', 'color': 0};
  if (source != '') {
    int levelValue = int.parse(source);
    if (levelValue == 0) {
      obj['text'] = '优';
      obj['color'] = 0xff44be3b;
    } else if (levelValue == 1) {
      obj['text'] = '良';
      obj['color'] = 0xffffb740;
    } else if (levelValue == 3) {
      obj['text'] = '差';
      obj['color'] = 0xffed2856;
    }
  }
  return ShowLabel(
      text: obj['text'] is String ? obj['text'] as String : '',
      color: obj['color'] is int ? obj['color'] as int : 0);
}

// 获取默认温度 5-10月为制冷温度，默认26°C 11-次年4月为制热温度，默认22°C
String getDefaultTemp() {
  // 获取当前月份
  final int month = DateTime.now().month;
  if (month >= 5 && month <= 10) {
    return '26';
  } else {
    return '22';
  }
}

// 某个房间智能优化状态
RoomSpaceModel roomControlInfo(Properties? properties) {
  // 房间支持的自动调节的属性列表
  final List<PropertiesInfo> propertiesSupportedByTheRoom = <PropertiesInfo>[];
  // 房间支持的属性列表
  final List<dynamic> supportProperties = <dynamic>[];
  // 是否有开启单空间总恒空
  bool isOpenTotal = false;

  if (properties != null) {
    // 是否支持自动调节
    if (properties.temperatureAutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 1,
        'name': '温度',
        'unit': properties.temperatureUnit ?? '℃',
        'status': properties.temperatureAutoStatus,
        'targetTemperature': properties.targetTemperature ?? getDefaultTemp(),
        'currentTemperature': properties.currentTemperature
      }));
    }
    if (properties.humidityAutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 2,
        'name': '湿度',
        'unit': properties.humidityUnit ?? '%',
        'status': properties.humidityAutoStatus,
        'targetHumidity': properties.targetHumidity ?? '50',
        'currentHumidity': properties.currentHumidity
      }));
    }
    if (properties.pm25AutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 3,
        'name': 'PM2.5',
        'unit': properties.pm25Unit ?? 'μg/m³',
        'status': properties.pm25AutoStatus
      }));
    }
    if (properties.co2AutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 4,
        'name': 'CO₂',
        'unit': properties.co2Unit ?? 'PPM',
        'status': properties.co2AutoStatus
      }));
    }
    if (properties.tvocAutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 5,
        'name': 'TVOC',
        'unit': properties.tvocUnit ?? 'mg/m³',
        'status': properties.tvocAutoStatus
      }));
    }
    if (properties.hchoAutoStatus != null) {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 6,
        'name': '甲醛',
        'unit': properties.hchoUnit ?? 'mg/m³',
        'status': properties.hchoAutoStatus
      }));
    }

    // 是否支持该属性
    if (properties.currentTemperature != null) {
      supportProperties
          .add(<String, dynamic>{'currentTemperature': properties.currentTemperature});
    }
    if (properties.currentHumidity != null) {
      supportProperties.add(<String, dynamic>{'currentHumidity': properties.currentHumidity});
    }
    if (properties.pm25Density != null) {
      supportProperties.add(<String, dynamic>{'pm25Density': properties.pm25Density});
    }
    if (properties.co2Density != null) {
      supportProperties.add(<String, dynamic>{'co2Density': properties.co2Density});
    }
    if (properties.tvocDensity != null) {
      supportProperties.add(<String, dynamic>{'tvocDensity': properties.tvocDensity});
    }
    if (properties.hchoDensity != null) {
      supportProperties.add(<String, dynamic>{'hchoDensity': properties.hchoDensity});
    }

    // 是否有开启单空间总恒空
    if (properties.temperatureAutoStatus == 'true' ||
        properties.humidityAutoStatus == 'true' ||
        properties.co2AutoStatus == 'true' ||
        properties.cleanAutoStatus == 'true') {
      isOpenTotal = true;
    }
  }
  // 过滤不支持相关设备的房间
  final RoomSpaceModel roomSpace = RoomSpaceModel(
      totalAutoStatus: isOpenTotal,
      propertiesSupportedByTheRoom: propertiesSupportedByTheRoom,
      supportProperties: supportProperties);
  return roomSpace;
}

//返回当前时间 12:30
String getCurrentTime() {
  final DateTime now = DateTime.now();
  final String hour = now.hour < 10 ? '0${now.hour}' : now.hour.toString();
  String minute =
      now.minute < 10 ? '0${now.minute}' : now.minute.toString();
  return '$hour:$minute';
}

// 请求服务器超时
void showRequestServerTimeout(BuildContext context) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(CONSTANT.TIME_OUT_ERROR, context);
}

//网络不可用
void showNetworkUnavailable(BuildContext context) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(CONSTANT.NET_WORK_ERROR, context);
}

// 请求服务器异常
void showRequestServerException(BuildContext context) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(CONSTANT.REQUEST_ERROR, context);
}

// 保存成功
void showSaveSuccess(BuildContext context) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(CONSTANT.SORTSAVE_SUCCESS, context);
}

// 删除成功
void showDeleteSuccess(BuildContext context) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(CONSTANT.DELETE_SUCCESS, context);
}

// 自由提示
void showFreeToast(BuildContext context, String text) {
  ToastHelper.updateCanShow(true);
  ToastHelper.showToast(text, context);
}

// 获得设备的像素密度
double getDpr() {
  return ScreenUtil().pixelRatio;
}

// 默认值
String returnValue(String? value, String defalueVlaue) {
  if (value == null || value == '') {
    return defalueVlaue;
  } else {
    return value;
  }
}

// 参数名称
String returnParamTitle(String propertiesType) {
  String title = '';
  if (propertiesType == '1') {
    title = '温度调节';
  }
  if (propertiesType == '2') {
    title = '湿度调节';
  }
  if (propertiesType == '3') {
    title = 'CO₂调节';
  }
  if (propertiesType == '4') {
    title = '污染物调节';
  }
  return title;
}

// 参数名称
String returnParamTitle2(String propertiesType) {
  String title = '';
  if (propertiesType == '1') {
    title = '温度调节';
  }
  if (propertiesType == '2') {
    title = '湿度调节';
  }
  if (propertiesType == '5') {
    title = 'CO₂调节';
  }
  if (propertiesType == '3' || propertiesType == '4' || propertiesType == '6') {
    title = '污染物调节';
  }
  return title;
}

// 某个房间智能优化状态
RoomSpaceModel roomControlInfoV2(Properties? properties) {
  // 房间支持的自动调节的属性列表
  final List<PropertiesInfo> propertiesSupportedByTheRoom = <PropertiesInfo>[];

  if (properties != null) {
    // 是否支持自动调节
    if (properties.temperatureAutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 1,
        'name': '温度',
        'unit': properties.temperatureUnit ?? '℃',
        'status': properties.temperatureAutoStatus,
        'targetTemperature': properties.targetTemperature ?? getDefaultTemp(),
        'currentTemperature': properties.currentTemperature
      }));
    }
    if (properties.humidityAutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 2,
        'name': '湿度',
        'unit': properties.humidityUnit ?? '%',
        'status': properties.humidityAutoStatus,
        'targetHumidity': properties.targetHumidity ?? '50',
        'currentHumidity': properties.currentHumidity
      }));
    }
    if (properties.pm25AutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 3,
        'name': 'PM2.5',
        'unit': properties.pm25Unit ?? 'μg/m³',
        'status': properties.pm25AutoStatus
      }));
    }
    if (properties.co2AutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 4,
        'name': 'CO₂',
        'unit': properties.co2Unit ?? 'PPM',
        'status': properties.co2AutoStatus
      }));
    }
    if (properties.tvocAutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 5,
        'name': 'TVOC',
        'unit': properties.tvocUnit ?? 'mg/m³',
        'status': properties.tvocAutoStatus
      }));
    }
    if (properties.hchoAutoStatus != '') {
      propertiesSupportedByTheRoom.add(PropertiesInfo.fromJson(<String, dynamic>{
        'type': 6,
        'name': '甲醛',
        'unit': properties.hchoUnit ?? 'mg/m³',
        'status': properties.hchoAutoStatus
      }));
    }
  }
  // 过滤不支持相关设备的房间
  final RoomSpaceModel roomSpace = RoomSpaceModel(
    propertiesSupportedByTheRoom: propertiesSupportedByTheRoom,
  );
  return roomSpace;
}

String millisecondsToHoursAndMinutes(double milliseconds) {
  // 先将毫秒转换为总分钟数
  final int totalMinutes = (milliseconds / (1000 * 60)).floor();
  // 计算小时数
  final int hours = totalMinutes ~/ 60;
  // 计算剩余分钟数
  final int minutes = totalMinutes % 60;
  // 格式化输出，如果分钟数是个位数，前面补 0
  String timeDesc = '';
  if (hours > 0) {
    timeDesc = '${hours.toString().padLeft(2, '0')}小时';
  }
  if (minutes > 0) {
    timeDesc = '$timeDesc${minutes.toString().padLeft(2, '0')}分钟';
  }
  return timeDesc;
}