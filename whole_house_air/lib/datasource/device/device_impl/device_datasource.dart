// ignore: slash_for_doc_comments
import 'dart:async';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:plugin_device/plugin/updevice_plugin.dart';
import 'package:whole_house_air/common/utils/util_log.dart';
import 'package:whole_house_air/common/utils/util_log_tag.dart';
import 'package:whole_house_air/datasource/device/device_protocol/device_api.dart';
import 'package:whole_house_air/datasource/device/device_protocol/model/wh_device_model.dart';
import 'package:whole_house_air/datasource/device/util/device_datasource_model_builder.dart';
import 'package:whole_house_air/datasource/device/util/device_datasource_util.dart';

class DeviceDataSource extends DeviceDataSourceInterface {
  DeviceDataSourceProtocol? _deviceProtocol;

  StreamSubscription<dynamic>? _networkListener;
  StreamSubscription<dynamic>? _userRefreshCompleteListener;
  StreamSubscription<dynamic>? _userLogoutListener;
  StreamSubscription<dynamic>? _curFamilyChangedListener;
  StreamSubscription<dynamic>? _deviceListChangeListener;

  // list cache
  Map<String, WHDevInfoModel> whDeviceInfoCacheMap =
      Map<String, WHDevInfoModel>();

  List<String> _visibleDeviceIds = [];

  String _curRoomId = "";

  bool _isNeedFilterAccessoryEquipment = true;

  Map<String, List<String>> _ruleMap = Map<String, List<String>>();

  DeviceDataSource(Map<String, List<String>> ruleMap) {
    this._ruleMap = ruleMap;
  }

  @override
  void addListener(DeviceDataSourceProtocol? protocol) {
    _deviceProtocol = protocol;
    _removeDeviceListeners();
    _addDeviceListeners();
  }

  void _addDeviceListeners() {
    // 网络变化监听
    _networkListener = Network
        .onConnectivityChanged
        .listen((IsOnline onValue) async {
      DevLogger.info(tag: TAG_DEVICES, msg: 'networkChanged callback');
      bool isNetAvailable = (onValue.isOnline);
      if (isNetAvailable) {
        _updateDeviceInfoList();
      }
    });
    // 用户数据刷新完成监听
    _userRefreshCompleteListener =
        Message.listen<UserRefreshCompletedMessage>((event) {
      DevLogger.info(
          tag: TAG_DEVICES, msg: ' UserRefreshCompletedMessage callback ');
      _updateDeviceInfoList();
    });
    // 退出登陆监听
    _userLogoutListener = Message.listen<UserLogoutMessage>((event) {
      DevLogger.info(tag: TAG_DEVICES, msg: 'logout callback');
      _deviceProtocol?.deviceListChanged(buildEmptyDeviceModelList());
    });
    // 当前家庭变化监听
    _curFamilyChangedListener =
        Message.listen<UserCurrentFamilyChangeMessage>((event) {
      DevLogger.info(tag: TAG_DEVICES, msg: 'curFamilyChanged callback');
      _updateDeviceInfoList();
    });
    // 设备列表变化监听
    _deviceListChangeListener =
        Message.listen<UserDeviceListChangeMessage>((event) {
      DevLogger.info(tag: TAG_DEVICES, msg: 'userDeviceListChanged callback');
      _updateDeviceInfoList();
    });
  }

  void _updateDeviceInfoList() {
    whDeviceInfoCacheMap.clear();
    buildWhDeviceInfoAttrModelList(_ruleMap, _curRoomId, _isNeedFilterAccessoryEquipment)
        .then((whDeviceInfoModelList) {
      whDeviceInfoModelList.forEach((element) {
        whDeviceInfoCacheMap[element.basicInfo.deviceId] = element;
      }); // protocol callback data
      _deviceProtocol?.deviceListChanged(whDeviceInfoModelList);
    }).catchError((dynamic e) {
      DevLogger.error(
          tag: TAG_DEVICES,
          msg:
              ' queryWaterDeviceInfoList-_buildWhDeviceInfoAttrModelList exception, err:$e');
    });
  }

  @override
  void queryDeviceData(callback, {String roomId = ALLROOMID, bool isNeedFilterAccessoryEquipment = true}) {
    whDeviceInfoCacheMap.clear();
    _curRoomId = roomId;
    _isNeedFilterAccessoryEquipment = isNeedFilterAccessoryEquipment;
    if (_curRoomId.isNotEmpty) {
      buildWhDeviceInfoAttrModelList(_ruleMap, _curRoomId, isNeedFilterAccessoryEquipment)
          .then((whDeviceInfoModelList) {
        // callback data
        callback(whDeviceInfoModelList);
        whDeviceInfoModelList.forEach((element) {
          whDeviceInfoCacheMap[element.basicInfo.deviceId] = element;
        });
      }).catchError((e) {
        DevLogger.error(
            tag: TAG_DEVICES,
            msg:
                ' queryDeviceData-_buildWhDeviceInfoAttrModelList exception, err:$e');
      });
    } else {
      List<WHDevInfoModel> emptyDevInfoModel = [];
      callback(emptyDevInfoModel);
      DevLogger.error(
          tag: TAG_DEVICES, msg: ' queryDeviceData-roomid is empty');
    }
  }

  @override
  void removeListener() {
    _removeDeviceListeners();
  }

  void _removeDeviceListeners() {
    _networkListener?.cancel();
    _userRefreshCompleteListener?.cancel();
    _userLogoutListener?.cancel();
    _curFamilyChangedListener?.cancel();
    _deviceListChangeListener?.cancel();
  }

  void subscribeDeviceAttributeChanged(List<String> deviceIds) {
    try {
      //解除旧的订阅
      if (_visibleDeviceIds.length > 0) {
        unSubscribeDeviceAttributeChanged(_visibleDeviceIds);
        _visibleDeviceIds.clear();
      }

      //订阅新的
      _visibleDeviceIds = deviceIds;
      UpDevicePlugin.subscribeDeviceChangeByDeviceIds(deviceIds,
              (deviceAttributeModelMap) {
            if (whDeviceInfoCacheMap.isEmpty || deviceIds.isEmpty) {
              return;
            }
            Map<String,
                WHDevInfoModel> deviceInfoModelMap = buildDeviceModelMap(
                deviceIds, deviceAttributeModelMap, whDeviceInfoCacheMap,
                _ruleMap);
            DevLogger.info(
                tag: TAG_DEVICES,
                msg:
                ' deviceAttributeChanged deviceInfoModelMap = ' +
                    deviceInfoModelMap.toString());
            _deviceProtocol?.deviceAttributeChanged(deviceInfoModelMap);
          });
    } catch (err) {
      DevLogger.error(
          tag: TAG_DEVICES,
          msg: ' subscribeDeviceChangeByDeviceIds exception $err');
    }
  }

  @override
  void unSubscribeDeviceAttributeChanged(List<String> deviceIds) {
    try {
      UpDevicePlugin.unsubscribeDeviceChangeByDeviceIds(deviceIds);
    } catch (err) {
      DevLogger.error(
          tag: TAG_DEVICES,
          msg: ' unSubscribeDeviceAttributeChanged exception $err');
    }
  }
}
