name: app_service
description: "服务TAB project."
author: lime<PERSON><PERSON><PERSON>@haier.com
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3
version: 1.0.0+1

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter

  redux: 5.0.0
  flutter_redux: 0.8.2
  dio: 5.3.2
  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"
  card_swiper: 2.0.4
  retrofit: 4.1.0
  easy_refresh: 3.4.0

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.1"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=0.0.12"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.2"

  device_utils:
    hosted:
      name: device_utils
      url: http://**************:8083
    version: ">=2.0.0"

  flutter_common_ui:
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
    version: ">=9.2.1"

  upservice:
    hosted:
      name: upservice
      url: http://**************:8083
      version: ">=0.0.1"

  uimessage:
    hosted:
      name: uimessage
      url: http://**************:8083
    version: ">=0.0.1"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=1.0.3"

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.2"

  eshop_widgets:
    hosted:
      name: eshop_widgets
      url: http://**************:8083
    version: ">=0.3.0+2024082101"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.2.0"

  upsystem:
    hosted:
      name: upsystem
      url: http://**************:8083
    version: ">=0.1.1"

dev_dependencies:
  retrofit_generator: 8.1.0
  build_runner: 2.4.6
  flutter_test:
    sdk: flutter
flutter:
  assets:
    - assets/images/
