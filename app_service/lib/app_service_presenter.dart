import 'dart:async';

import 'package:app_service/device_services/store/device_services_action.dart';
import 'package:app_service/my_devices/model/my_devices_model.dart';
import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/self_service_inquiry/store/self_service_inquiry_action.dart';
import 'package:app_service/service/http_service.dart';
import 'package:app_service/service_order/model/service_order_response.dart';
import 'package:app_service/service_order/store/service_order_action.dart';
import 'package:app_service/store/service_action.dart';
import 'package:app_service/store/service_store.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/utils/storage_util.dart';
import 'package:app_service/value_added_services/store/value_added_services_action.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:message/message.dart';
import 'package:message/msgmodel.dart';
import 'package:uimessage/event_definition/common_envent.dart';
import 'package:uimessage/uimessage.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

class AppServicePresenter {
  StreamSubscription<UserLogoutMessage>? _userLogoutListener;
  StreamSubscription<NetworkStatus>? _networkListener;
  StreamSubscription<UserLoginSuccess>? _userLoginSuccessListener;
  StreamSubscription<UserInfoRefreshedMessage>? _userInfoRefreshedListener;
  NetworkStatus currentNetworkType = NetworkStatusfalse;

  void initAppData() {
    requestDataByLoginStatus();
    _getValueAddedServicesList();
    _getDeviceServicesList();
    _getSelfServiceInquiryList();
  }

  Future<void> getServiceCacheData(
      bool isLoginByMain, bool isFirstScreen) async {
    bool? isLogin;
    if (isFirstScreen) {
      isLogin = isLoginByMain;
    } else {
      isLogin = User.getLoginStatusSync()?.isLogin;
      DevLogger.info(
          tag: Constant.packageName,
          msg: '_getMyDevicesCacheData loginStatus:$isLogin');
    }

    if (isLogin ?? false) {
      await _getUserIdAndSendAction();
    }

    serviceStore.dispatch(UpdateAppLoginStatusAction(isLogin));
    _getMyDevicesCacheData(isLogin);
    _getDeviceServicesCacheData();
    _getServiceOrderCacheData(isLogin);
    _getSelfServiceInquiryCacheData();
    _getValueAddedCacheData();

    final IsOnline isOnline =
        await Network.isOnline();
    currentNetworkType = connectivityResult;
    serviceStore.dispatch(
        UpdateNetworkAction(isOnline.isOnline));
    if (isOnline.isOnline) {
      if (isLogin ?? false) {
        _getDeviceList();
        _getServiceOrderList();
      }
      _getDeviceServicesList();
      _getSelfServiceInquiryList();
      _getValueAddedServicesList();
    }
  }

  Future<void> _getMyDevicesCacheData(bool? isLogin) async {
    if (isLogin ?? false) {
      try {
        final UserInfo userInfo = await User.getUserInfo();
        final String storageMyDevicesData = await getStringValueByStorage(
            '${userInfo.userId}_${Constant.storageMyDevicesKey}');
        if (storageMyDevicesData.isNotEmpty) {
          final MyDevicesData? myDevicesData =
              MyDevicesResponseModel.fromJson(storageMyDevicesData)
                  .myDevicesData;
          serviceStore.dispatch(UpdateDeviceListAction(
              myDevicesData?.myDevicesDataModelList ?? <MyDevicesDataModel>[],
              myDevicesData?.title ?? '',
              myDevicesData?.jumpUrl ?? ''));
        }
      } catch (err) {
        DevLogger.error(
            tag: Constant.packageName, msg: '_getMyDevicesCacheData err: $err');
      }
    }
  }

  Future<void> _getValueAddedCacheData() async {
    final String storageValueAddedData =
        await getStringValueByStorage(Constant.storageValueAddedKey);
    if (storageValueAddedData.isNotEmpty) {
      final AdvertisementData? advertisementData =
          ValueAddedServicesResponseModel.fromJson(storageValueAddedData)
              .advertisementData;
      serviceStore.dispatch(UpdateValueAddedServicesListAction(
          advertisementData?.slideList ?? <SlideModel>[],
          advertisementData?.title ?? ''));
    }
  }

  Future<void> _getDeviceServicesCacheData() async {
    final String storageDeviceServicesData =
        await getStringValueByStorage(Constant.storageDeviceServicesKey);
    if (storageDeviceServicesData.isNotEmpty) {
      final AdvertisementData? advertisementData =
          ValueAddedServicesResponseModel.fromJson(storageDeviceServicesData)
              .advertisementData;
      serviceStore.dispatch(UpdateDeviceServicesListAction(
          advertisementData?.slideList ?? <SlideModel>[],
          advertisementData?.title ?? ''));
    }
  }

  Future<void> _getServiceOrderCacheData(bool? isLogin) async {
    if (isLogin ?? false) {
      try {
        final String cache = await getStringValueByStorage(
            StorageKeyUtil.generateKeyJoinUserId(Constant.storageServiceOrderKey));
        if (cache.isNotEmpty) {
          final ServiceOrderModel orderModel =
              ServiceOrderResponseModel.fromJson(cache).orderModel;
          serviceStore.dispatch(UpdateServiceOrderListAction(orderModel));
        }
      } catch (err) {
        DevLogger.error(
            tag: Constant.packageName,
            msg: '_getServiceOrderCacheData err: $err');
      }
    }
  }

  Future<void> _getSelfServiceInquiryCacheData() async {
    final String storageSelfServiceInquiryData =
        await getStringValueByStorage(Constant.storageSelfServiceInquiryKey);
    if (storageSelfServiceInquiryData.isNotEmpty) {
      final AdvertisementData? advertisementData =
          ValueAddedServicesResponseModel.fromJson(
                  storageSelfServiceInquiryData)
              .advertisementData;
      serviceStore.dispatch(UpdateSelfServiceInquiryListAction(
          advertisementData?.slideList ?? <SlideModel>[],
          advertisementData?.title ?? ''));
    }
  }

  Future<void> requestDataByLoginStatus() async {
    final LoginStatus? loginStatus = User.getLoginStatusSync();
    DevLogger.info(
        tag: Constant.packageName,
        msg: 'requestDataByLoginStatus loginStatus:${loginStatus?.isLogin}');
    serviceStore.dispatch(UpdateAppLoginStatusAction(loginStatus?.isLogin));
    if (loginStatus?.isLogin ?? false) {
      _getDeviceList();
      _getServiceOrderList();
    }
  }

  /// 持久监听，伴随页面整个生命周期,包括用户登录、登出监听
  void addPersistentPluginListener() {
    // 退出登陆监听
    _userLogoutListener =
        Message.listen<UserLogoutMessage>((UserLogoutMessage event) {
      DevLogger.info(
          tag: Constant.packageName, msg: 'UserLogoutMessage callback');
      serviceStore.dispatch(UpdateAppLoginStatusAction(false));
    });

    /// 用户登录成功监听，此时OauthData也已经刷新
    _userLoginSuccessListener =
        UIMessage.sub<UserLoginSuccess>((UserLoginSuccess event) {
      DevLogger.info(
          tag: Constant.packageName, msg: '_userLoginSuccessListener callback');

      requestDataByLoginStatus();
    });

    _networkListener = Network
        .onConnectivityChanged
        .listen((IsOnline onValue) async {
      DevLogger.info(
          tag: Constant.packageName,
          msg:
              'onConnectivityChanged callback: $result---currentNetworkType: $currentNetworkType');
      serviceStore
          .dispatch(UpdateNetworkAction(isOnline.isOnline));
      if (isOnline.isOnline && result != currentNetworkType) {
        initAppData();
        serviceStore.dispatch(UpdateImageRefreshCountAction());
      }
      currentNetworkType = result;
    });

    /// 用户信息刷新完成
    _userInfoRefreshedListener = Message.listen<UserInfoRefreshedMessage>(
        (UserInfoRefreshedMessage event) {
      DevLogger.info(
          tag: Constant.packageName, msg: 'UserInfoRefreshedMessage callback');
      _getUserIdAndSendAction();
    });
  }

  /// 移除持久监听，包括用户登录、登出
  void removePersistentPluginListeners() {
    _networkListener?.cancel();
    _userLoginSuccessListener?.cancel();
    _userLogoutListener?.cancel();
    _userInfoRefreshedListener?.cancel();
  }

  Future<void> _getSelfServiceInquiryList() async {
    final ValueAddedServicesResponseModel? selfServiceInquiryResponseModel =
        await Service().getRotationResponseModel(
            Service.adLocationSelfServiceInquiry,
            Service.adLocationSelfServiceInquiryYS,
            Constant.storageSelfServiceInquiryKey);
    if (selfServiceInquiryResponseModel == null) {
      DevLogger.info(
          tag: Constant.packageName,
          msg:
              '--_getSelfServiceInquiryList--selfServiceInquiryResponseModel== null');
      return;
    }
    serviceStore.dispatch(UpdateSelfServiceInquiryListAction(
        selfServiceInquiryResponseModel.advertisementData?.slideList ??
            <SlideModel>[],
        selfServiceInquiryResponseModel.advertisementData?.title ?? ''));
  }

  Future<void> _getDeviceServicesList() async {
    final ValueAddedServicesResponseModel? deviceServicesResponseModel =
        await Service().getRotationResponseModel(
            Service.adLocationDeviceServices,
            Service.adLocationDeviceServicesYS,
            Constant.storageDeviceServicesKey);
    if (deviceServicesResponseModel == null) {
      DevLogger.info(
          tag: Constant.packageName,
          msg: '--_getDeviceServicesList--deviceServicesResponseModel== null');
      return;
    }
    serviceStore.dispatch(UpdateDeviceServicesListAction(
        deviceServicesResponseModel.advertisementData?.slideList ??
            <SlideModel>[],
        deviceServicesResponseModel.advertisementData?.title ?? ''));
  }

  Future<void> _getValueAddedServicesList() async {
    final ValueAddedServicesResponseModel? valueAddedServicesResponseModel =
        await Service().getRotationResponseModel(
            Service.adLocationValueAddedServices,
            Service.adLocationValueAddedServicesYS,
            Constant.storageValueAddedKey);
    if (valueAddedServicesResponseModel == null) {
      DevLogger.info(
          tag: Constant.packageName,
          msg:
              '--_getValueAddedServicesList--valueAddedServicesResponseModel== null');
      return;
    }
    serviceStore.dispatch(UpdateValueAddedServicesListAction(
        valueAddedServicesResponseModel.advertisementData?.slideList ??
            <SlideModel>[],
        valueAddedServicesResponseModel.advertisementData?.title ?? ''));
  }

  Future<void> _getDeviceList() async {
    final MyDevicesResponseModel? myDevicesResponseModel =
        await Service().getMyDevicesModel();
    if (myDevicesResponseModel == null) {
      DevLogger.info(
          tag: Constant.packageName,
          msg: '--_getDeviceList--myDevicesResponseModel== null');
      return;
    }
    serviceStore.dispatch(UpdateDeviceListAction(
        myDevicesResponseModel.myDevicesData?.myDevicesDataModelList ??
            <MyDevicesDataModel>[],
        myDevicesResponseModel.myDevicesData?.title ?? '',
        myDevicesResponseModel.myDevicesData?.jumpUrl ?? ''));
  }

  Future<void> _getServiceOrderList() async {
    final ServiceOrderModel? serviceOrderModel =
        await Service().getServiceOrderList();
    if (serviceOrderModel == null) {
      DevLogger.info(
          tag: Constant.packageName,
          msg: '--_getServiceOrderList--serviceOrderModel== null');
      return;
    }
    serviceStore.dispatch(UpdateServiceOrderListAction(serviceOrderModel));
  }

  Future<void> _getUserIdAndSendAction() async {
    try {
      final UserInfo userInfo = await User.getUserInfo();
      serviceStore.dispatch(UpdateUserIdAction(userInfo.userId));
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: '__getUserIdAndSendAction--err: $err');
    }
  }
}
