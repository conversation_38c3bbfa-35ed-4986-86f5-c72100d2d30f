import 'package:app_service/store/service_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class NetUnavailableWidget extends StatelessWidget {
  const NetUnavailableWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, bool>(
        distinct: true,
        converter: (Store<AppServiceState> store) => store.state.netAvailable,
        builder: (BuildContext context, bool netAvailable) {
          return Padding(
            padding: EdgeInsets.symmetric(
                horizontal: 16, vertical: netAvailable ? 0 : 12),
            child: NetworkUnavailable(netAvailable: netAvailable),
          );
        });
  }
}
