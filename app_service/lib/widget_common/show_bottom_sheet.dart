import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

void showMyDeviceBottomSheet(BuildContext context) {
  final Dialogs dialogs = Dialogs();
  dialogs.showSingleBtnModal<dynamic>(
    context: context,
    enableDrag: false,
    callback: () {
      dialogs.closeSmartHomeModalBottomSheet();
    },
    child: (BuildContext context) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(
            height: 28,
          ),
          Text.rich(
            TextSpan(
              children: <InlineSpan>[
                buildBoldText('亲爱的用户，您好!\n\n'),
                buildText('以下是您个人账号下，通过'),
                buildBoldText('服务记录、智能家电绑定、手动添加或在会员中心认证家电'),
                buildText(
                    '等方式，获取到的家电保修卡。\n\n海尔智家将尽力保障您的账号所匹配家电的一致性，但在购买人与收货人手机号不一致的情况下，您的家电可能会有误差:'),
              ],
            ),
          ),
          buildBulletList(),
          Text.rich(
            TextSpan(
              children: <InlineSpan>[
                buildText('\n另外，您也可以联系'),
                buildBoldText('在线客服'),
                buildText('协助进行操作，感谢您的使用~'),
              ],
            ),
          ),
          const SizedBox(
            height: 12,
          ),
        ],
      );
    },
  );
}

Widget buildBulletList() {
  return Container(
    alignment: Alignment.centerLeft,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Baseline(
              baseline: 14,
              baselineType: TextBaseline.alphabetic,
              child: Text(
                '\u2022',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.priWeaken,
                ),
              ),
            ),
            const SizedBox(
              width: 6,
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: <InlineSpan>[
                    buildText('如果缺失家电保修卡，您可以通过'),
                    buildBoldText('「去添加」'),
                    buildText('按钮进行添加；'),
                  ],
                ),
              ),
            ),
          ],
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Baseline(
              baseline: 14,
              baselineType: TextBaseline.alphabetic,
              child: Text(
                '\u2022',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.priWeaken,
                ),
              ),
            ),
            const SizedBox(
              width: 6,
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  children: <InlineSpan>[
                    buildText('如果有不是您本人的家电保修卡，您可以在保修卡页面进行解绑，解绑后仍可以扫码重新添加。'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

TextSpan buildText(String text) {
  return TextSpan(
    text: text,
    style: TextStyle(
      color: AppSemanticColors.item.priWeaken,
      fontSize: 14,
    ),
  );
}

TextSpan buildBoldText(String text) {
  return TextSpan(
    text: text,
    style: TextStyle(
      fontSize: 14,
      color: AppSemanticColors.item.priWeaken,
      fontWeight: FontWeight.w500,
      fontFamilyFallback: fontFamilyFallback(),
    ),
  );
}
