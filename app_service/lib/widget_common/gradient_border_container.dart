import 'package:flutter/widgets.dart';

class GradientBoxBorder extends BoxBorder {
  const GradientBoxBorder({
    required this.gradient,
    this.width = 1.0,
    this.isThreeSides = false,
  })  : assert(width >= 0.0),
        assert(gradient != null);

  final Gradient gradient;

  final double width;

  final bool isThreeSides;

  @override
  BorderSide get bottom => BorderSide.none;

  @override
  BorderSide get top => BorderSide.none;

  @override
  EdgeInsetsGeometry get dimensions => EdgeInsets.all(width);

  @override
  bool get isUniform => true;

  @override
  void paint(
    Canvas canvas,
    Rect rect, {
    TextDirection? textDirection,
    BoxShape shape = BoxShape.rectangle,
    BorderRadius? borderRadius,
  }) {
    switch (shape) {
      case BoxShape.circle:
        assert(
          borderRadius == null,
          'A borderRadius can only be given for rectangular boxes.',
        );
        _paintCircle(canvas, rect);
        break;
      case BoxShape.rectangle:
        if (isThreeSides) {
          if (borderRadius != null) {
            _paintRRectThreeSides(canvas, rect, borderRadius);
          } else {
            _paintRectThreeSides(canvas, rect);
          }
          return;
        }
        if (borderRadius != null) {
          _paintRRect(canvas, rect, borderRadius);
          return;
        }
        _paintRect(canvas, rect);
        break;
    }
  }

  void _paintRect(Canvas canvas, Rect rect) {
    canvas.drawRect(rect.deflate(width / 2), _getPaint(rect));
  }

  void _paintRRect(Canvas canvas, Rect rect, BorderRadius borderRadius) {
    final RRect rrect = borderRadius.toRRect(rect).deflate(width / 2);
    canvas.drawRRect(rrect, _getPaint(rect));
  }

  void _paintCircle(Canvas canvas, Rect rect) {
    final Paint paint = _getPaint(rect);
    final double radius = (rect.shortestSide - width) / 2.0;
    canvas.drawCircle(rect.center, radius, paint);
  }

  void _paintRectThreeSides(Canvas canvas, Rect rect) {
    final Paint paint = _getPaint(rect);
    _paintThreeSides(canvas, rect.deflate(width / 2), paint);
  }

  void _paintRRectThreeSides(
      Canvas canvas, Rect rect, BorderRadius borderRadius) {
    final Paint paint = _getPaint(rect);
    final RRect rrect = borderRadius.toRRect(rect).deflate(width / 2);

    // 使用 Path 绘制左、下、右三条边
    final Path path = Path()
      // 从左上角开始
      ..moveTo(rrect.left, rrect.top + rrect.tlRadiusY)
      // 左边
      ..lineTo(rrect.left, rrect.bottom - rrect.blRadiusY)
      ..arcToPoint(
        Offset(rrect.left + rrect.blRadiusX, rrect.bottom),
        radius: Radius.circular(rrect.blRadiusX),
        clockwise: false,
      )
      // 下边
      ..lineTo(rrect.right - rrect.brRadiusX, rrect.bottom)
      ..arcToPoint(
        Offset(rrect.right, rrect.bottom - rrect.brRadiusY),
        radius: Radius.circular(rrect.brRadiusX),
        clockwise: false,
      )
      // 右边
      ..lineTo(rrect.right, rrect.top + rrect.trRadiusY);

    canvas.drawPath(path, paint);
  }

  void _paintThreeSides(Canvas canvas, Rect rect, Paint paint) {
    // 上边
    canvas.drawLine(rect.topLeft, rect.topRight, paint);
    // 左边
    canvas.drawLine(rect.topLeft, rect.bottomLeft, paint);
    // 右边
    canvas.drawLine(rect.topRight, rect.bottomRight, paint);
  }

  @override
  ShapeBorder scale(double t) {
    return this;
  }

  Paint _getPaint(Rect rect) {
    return Paint()
      ..strokeWidth = width
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke;
  }
}
