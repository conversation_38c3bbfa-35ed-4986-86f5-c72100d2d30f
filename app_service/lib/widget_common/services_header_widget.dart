import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class ServicesHeaderWidget extends StatelessWidget {
  ServicesHeaderWidget(
      {super.key, required this.title, required this.netAvailable});

  String title = '';
  bool netAvailable = true;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedOpacity(
            opacity: title.isEmpty && !netAvailable ? 1 : 0,
            duration: const Duration(milliseconds: 600),
            child: Container(
                height: 18,
                width: 72,
                margin: const EdgeInsets.only(left: 18, bottom: 16),
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(22)),
                  color: Colors.white,
                ))),
        AnimatedOpacity(
          opacity: title.isNotEmpty ? 1 : 0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.only(left: 18, bottom: 12),
            child: Text(
              title,
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
                fontSize: 18,
                color: AppSemanticColors.item.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
