import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:app_service/widget_common/common_network_image_widget.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class GridItemWidget extends StatelessWidget {
  GridItemWidget(
      {super.key,
      required this.itemViewModel,
      required this.itemClickGio,
      required this.paddingTop});

  ValueAddedServicesItemViewModel itemViewModel;
  String itemClickGio;
  double paddingTop;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (itemViewModel.title.isEmpty) {
          DevLogger.error(
              tag: Constant.packageName,
              msg:
                  'GridItemWidget Empty onTap:--jumpUrl= ${itemViewModel.jumpUrl}');
        } else {
          gioTrack(itemClickGio,
              <String, Object>{'content_title': itemViewModel.title});
          goToPage(itemViewModel.jumpUrl);
        }
      },
      child: Container(
        padding: EdgeInsets.only(top: paddingTop),
        child: Column(
          children: <Widget>[
            CommonNetworkImg(
              width: 28,
              height: 28,
              imageUrl: itemViewModel.iconUrl,
              errorWidget: _buildDefaultCircularWidget(),
              placeHolder: _buildDefaultCircularWidget(),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 4, right: 4),
              child: Text(
                textHeightBehavior: const TextHeightBehavior(
                    leadingDistribution: TextLeadingDistribution.even),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                itemViewModel.title,
                style: TextStyle(
                  fontSize: 12,
                  color: AppSemanticColors.item.primary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCircularWidget() {
    return Container(
        width: 28,
        height: 28,
        decoration: const BoxDecoration(
          color: Color(0xFFEAEAEA),
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        child: Center(
          child: Image.asset(
            'assets/images/haier_logo.webp',
            width: 16,
            height: 7,
            package: Constant.packageName,
          ),
        ));
  }
}
