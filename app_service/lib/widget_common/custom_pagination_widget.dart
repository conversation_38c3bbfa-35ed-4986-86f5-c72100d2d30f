import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:app_service/widget_common/common_network_image_widget.dart';
import 'package:app_service/widget_common/custom_pagination_indicator.dart';
import 'package:app_service/widget_common/gradient_border_container.dart';
import 'package:app_service/widget_common/grid_item_widget.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class CustomPaginatedWidget extends StatefulWidget {
  CustomPaginatedWidget({
    super.key,
    this.dataList,
    required this.totalPages,
    required this.height,
    required this.mainAxisExtent,
    this.itemPaddingTop = 16,
    this.isDeviceServices = false,
    this.otherList,
    this.firstLineList,
    this.secondLineList,
    required this.itemClickGio,
    this.isValueAddedServices = false,
  });

  final List<ValueAddedServicesListViewModel>? dataList;
  final int totalPages;
  final double height;
  final double mainAxisExtent;
  final double itemPaddingTop;
  bool isDeviceServices;
  List<ValueAddedServicesListViewModel>? otherList;
  List<ValueAddedServicesItemViewModel>? firstLineList;
  List<ValueAddedServicesItemViewModel>? secondLineList;
  String itemClickGio;
  bool isValueAddedServices;

  @override
  _CustomPaginatedWidgetState createState() => _CustomPaginatedWidgetState();
}

class _CustomPaginatedWidgetState extends State<CustomPaginatedWidget> {
  PageController? _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _currentPage = 0;
  }

  @override
  void dispose() {
    _pageController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: <Widget>[
        if (widget.isValueAddedServices)
          const SizedBox()
        else
          Container(
            height: widget.totalPages <= 1 ? widget.height : widget.height + 3,
            width: MediaQuery.of(context).size.width - 32,
            margin: const EdgeInsets.only(left: 16, right: 16),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(22)),
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/my_device_bg.webp',
                  package: Constant.packageName,
                ),
                fit: BoxFit.contain,
              ),
            ),
          ),
        Container(
          height: widget.totalPages <= 1 ? widget.height : widget.height + 3,
          margin: const EdgeInsets.only(left: 16, right: 16),
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.totalPages,
            itemBuilder: (BuildContext context, int page) {
              return _buildItemContent(page);
            },
            onPageChanged: (int page) {
              if (mounted) {
                setState(() {
                  _currentPage = page;
                });
              }
            },
          ),
        ),
        CustomPaginationIndicator(
          currentPage: _currentPage,
          totalPages: widget.totalPages,
          onPageChanged: (int page) {
            _pageController?.animateToPage(
              page,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          },
        ),
      ],
    );
  }

  Widget _buildItemContent(int page) {
    return Container(
      width: MediaQuery.of(context).size.width - 32,
      height: widget.totalPages <= 1 ? widget.height : widget.height + 3,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(22)),
        color: Color.fromRGBO(255, 255, 255, 0.7),
        border: GradientBoxBorder(
          width: 1,
          gradient: LinearGradient(
            begin: Alignment(-0.1, -0.8),
            end: Alignment(0.1, 0.8),
            colors: <Color>[
              Colors.white,
              Color.fromRGBO(255, 255, 255, 0.01),
              Color.fromRGBO(255, 255, 255, 0.5),
              Colors.white,
            ],
            stops: <double>[0, 0.4, 0.6, 1],
          ),
        ),
      ),
      child: page == 0 && widget.isDeviceServices
          ? _buildFirstPage()
          : _buildPageContentWidget(_getItemList(page)),
    );
  }

  List<ValueAddedServicesItemViewModel> _getItemList(int page) {
    if (widget.isDeviceServices) {
      if (page == 0) {
        return <ValueAddedServicesItemViewModel>[];
      }
      return widget.otherList?[page - 1].valueAddedServicesVMList ??
          <ValueAddedServicesItemViewModel>[];
    } else {
      return widget.dataList?[page].valueAddedServicesVMList ??
          <ValueAddedServicesItemViewModel>[];
    }
  }

  Widget _buildPageContentWidget(
      List<ValueAddedServicesItemViewModel> dataList) {
    return GridView.builder(
      shrinkWrap: true,
      primary: false,
      padding: widget.itemPaddingTop == 10
          ? const EdgeInsets.only(top: 10)
          : EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: Constant.crossAxisCount,
          mainAxisExtent: widget.mainAxisExtent),
      itemCount: dataList.length,
      itemBuilder: (BuildContext context, int index) {
        return GridItemWidget(
          paddingTop: widget.itemPaddingTop,
          itemViewModel: dataList[index],
          itemClickGio: widget.itemClickGio,
        );
      },
    );
  }

  Widget _buildFirstPage() {
    return Column(
      children: <Widget>[
        Container(
          height: 80,
          width: MediaQuery.of(context).size.width - 32,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(22),
              topRight: Radius.circular(22),
            ),
            color: Color.fromRGBO(255, 255, 255, 0.3),
          ),
          child: ListView.builder(
            primary: false,
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.firstLineList?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              return _buildGridViewItemFirstLine(widget.firstLineList?[index]);
            },
          ),
        ),
        Container(
          height: 1,
          width: MediaQuery.of(context).size.width - 32,
          color: const Color.fromRGBO(255, 255, 255, 1),
        ),
        SizedBox(
          height: 90,
          child: GridView.builder(
            primary: false,
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: Constant.crossAxisCount,
            ),
            itemCount: widget.secondLineList?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              if (widget.secondLineList?[index] != null) {
                return GridItemWidget(
                  paddingTop: widget.itemPaddingTop,
                  itemViewModel: widget.secondLineList![index],
                  itemClickGio: widget.itemClickGio,
                );
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGridViewItemFirstLine(
      ValueAddedServicesItemViewModel? deviceServicesItemViewModel) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        goToPage(deviceServicesItemViewModel?.jumpUrl);
        gioTrack(widget.itemClickGio, <String, Object>{
          'content_title': deviceServicesItemViewModel?.title ?? ''
        });
      },
      child: SizedBox(
        width: MediaQuery.of(context).size.width / 2 - 16,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              padding: const EdgeInsets.only(right: 12, top: 20),
              height: 75,
              alignment: Alignment.topCenter,
              child: CommonNetworkImg(
                width: 40,
                height: 40,
                imageUrl: deviceServicesItemViewModel?.iconUrl ?? '',
                errorWidget: _buildDefaultCircularWidget(),
                placeHolder: _buildDefaultCircularWidget(),
              ),
            ),
            SizedBox(
              height: 75,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const SizedBox(height: 21),
                  Text(
                    textHeightBehavior: const TextHeightBehavior(
                        leadingDistribution: TextLeadingDistribution.even),
                    deviceServicesItemViewModel?.title ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontFamilyFallback: fontFamilyFallback(),
                      fontSize: 14,
                      color: AppSemanticColors.item.primary,
                    ),
                  ),
                  Text(
                    textHeightBehavior: const TextHeightBehavior(
                        leadingDistribution: TextLeadingDistribution.even),
                    deviceServicesItemViewModel?.subtitle ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppSemanticColors.item.secondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCircularWidget() {
    return Container(
        width: 40,
        height: 40,
        decoration: const BoxDecoration(
          color: Color(0xFFEAEAEA),
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        child: Center(
          child: Image.asset(
            'assets/images/haier_logo.webp',
            width: 20,
            height: 10,
            package: Constant.packageName,
          ),
        ));
  }
}
