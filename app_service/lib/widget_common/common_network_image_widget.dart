import 'package:app_service/store/service_state.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class CommonNetworkImg extends StatelessWidget {
  const CommonNetworkImg({
    super.key,
    required this.imageUrl,
    required this.errorWidget,
    required this.placeHolder,
    required this.width,
    required this.height,
  });

  final double? width;
  final double? height;
  final String imageUrl;
  final Widget? errorWidget;
  final Widget? placeHolder;

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, CommonNetworkImgViewModel>(
        distinct: true,
        converter: (Store<AppServiceState> store) =>
            CommonNetworkImgViewModel.fromStore(store),
        builder: (BuildContext context, CommonNetworkImgViewModel vm) {
          return CommonNetWorkImage(
            width: width,
            height: height,
            url: imageUrl,
            fit: BoxFit.cover,
            errorWidget: errorWidget,
            needReload: vm.imageRefreshCount,
            placeHolder: placeHolder,
          );
        });
  }
}

class CommonNetworkImgViewModel {
  CommonNetworkImgViewModel({required this.imageRefreshCount});

  final int imageRefreshCount;

  static CommonNetworkImgViewModel fromStore(Store<AppServiceState> store) {
    return CommonNetworkImgViewModel(
      imageRefreshCount: store.state.imageRefreshCount,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommonNetworkImgViewModel &&
          runtimeType == other.runtimeType &&
          imageRefreshCount == other.imageRefreshCount;

  @override
  int get hashCode => imageRefreshCount.hashCode;

  @override
  String toString() {
    return 'CommonNetworkImgViewModel{imageRefreshCount: $imageRefreshCount}';
  }
}
