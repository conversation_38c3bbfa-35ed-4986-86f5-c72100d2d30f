import 'package:flutter/material.dart';

class CustomPaginationIndicator extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final ValueChanged<int> onPageChanged;

  const CustomPaginationIndicator({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: totalPages > 1,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: <Widget>[
          Container(
            margin: const EdgeInsets.only(bottom: 10),
            alignment: Alignment.center,
            width: 12.0 * totalPages,
            height: 3,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
              color: Color(0xffDEECFB),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List<Widget>.generate(totalPages, (int index) {
              return GestureDetector(
                onTap: () => onPageChanged(index),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 10),
                  width: 12,
                  height: 3,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(4)),
                    color: currentPage == index
                        ? const Color(0xFF2283E2)
                        : Colors.transparent,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
