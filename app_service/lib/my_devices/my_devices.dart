import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/my_devices/widget/animated_list_view.dart';
import 'package:app_service/my_devices/widget/my_devices_add.dart';
import 'package:app_service/my_devices/widget/my_devices_item_card.dart';
import 'package:app_service/my_devices/widget/my_devices_show_more.dart';
import 'package:app_service/my_devices/widget/my_devices_title.dart';
import 'package:app_service/my_devices/widget/my_devices_unlogin.dart';
import 'package:app_service/store/service_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class MyDevicesWidget extends StatelessWidget {
  const MyDevicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        const MyDevicesTitleWidget(),
        _buildMyDevicesContentWidget()
      ],
    );
  }

  Widget _buildMyDevicesContentWidget() {
    return StoreConnector<AppServiceState, MyDevicesViewModel>(
      distinct: true,
      builder: (BuildContext context, MyDevicesViewModel myDevicesViewModel) {
        switch (myDevicesViewModel.deviceCardType) {
          case DeviceCardType.unLogin:
            return const MyDevicesUnLoginWidget();
          case DeviceCardType.non:
            return const AddDevicesWidget();
          case DeviceCardType.single:
            return Column(
              children: <Widget>[
                _buildDeviceListWidget(myDevicesViewModel),
                const SizedBox(height: 24)
              ],
            );
          default:
            return Column(
              children: <Widget>[
                _buildDeviceListWidget(myDevicesViewModel),
                MyDevicesShowMoreWidget(
                    expansionStatus: myDevicesViewModel.expansionStatus)
              ],
            );
        }
      },
      converter: (Store<AppServiceState> store) {
        return MyDevicesViewModel(
            store.state.myDevicesState.devicesVMList,
            store.state.myDevicesState.deviceCardType,
            store.state.myDevicesState.expansionStatus,
            store.state.myDevicesState.deviceListJumpUrl);
      },
    );
  }

  Widget _buildDeviceListWidget(MyDevicesViewModel myDevicesViewModel) {
    return AnimatedListView<DeviceItemViewModel>(
      deviceCardType: myDevicesViewModel.deviceCardType,
      list: myDevicesViewModel.devicesVMList,
      expansionStatus: myDevicesViewModel.expansionStatus,
      deviceListJumpUrl: myDevicesViewModel.deviceListJumpUrl,
      itemBuilder: (DeviceItemViewModel item, int index) => MyDevicesItemCard(
        index: index,
        deviceModel: item,
        amount: myDevicesViewModel.devicesVMList.length,
      ),
    );
  }
}
