import 'package:device_utils/typeId_parse/template_map.dart';

class ScanResultModel {
  ScanResultModel.fromJson(dynamic data) {
    Map<dynamic, dynamic> json = <dynamic, dynamic>{};
    if (data != null && data is Map) {
      json = data;
      retCode = json.stringValueForKey('retCode', '');
      retInfo = json.stringValueForKey('retInfo', '');
      dataModel = DataModel.fromJson(
          json.mapValueForKey('retData', <dynamic, dynamic>{}));
    }
  }

  String retCode = '';
  String retInfo = '';
  DataModel? dataModel;
}

class DataModel {
  DataModel.fromJson(Map<dynamic, dynamic> json) {
    resultModel = ResultModel.fromJson(json);
  }

  ResultModel? resultModel;
}

class ResultModel {
  String scanResult = '';

  ResultModel.fromJson(Map<dynamic, dynamic> json) {
    scanResult = json.stringValueFor<PERSON>ey('scanResult', '');
  }
}
