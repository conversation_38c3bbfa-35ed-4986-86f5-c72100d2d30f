import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class MyDevicesResponseModel extends UhomeResponseModel {
  MyDevicesResponseModel.fromJson(super.data) : super.fromJson() {
    myDevicesData = MyDevicesData.fromJson(super.retData);
  }

  MyDevicesData? myDevicesData;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (myDevicesData != null) {
      data['data'] = myDevicesData?.toJson();
    }
    data['retCode'] = retCode;
    data['retInfo'] = retInfo;
    return data;
  }
}

class MyDevicesData {
  List<MyDevicesDataModel> myDevicesDataModelList = <MyDevicesDataModel>[];
  String title = '';
  String jumpUrl = '';

  MyDevicesData.fromJson(Map<dynamic, dynamic> json) {
    title = json.stringValueForKey('title', '');
    jumpUrl = json.stringValueForKey('jumpUrl', '');
    json.listValueForKey('items', <dynamic>[]).forEach((dynamic element) {
      if (element is Map) {
        final MyDevicesDataModel model = MyDevicesDataModel.fromJson(element);
        myDevicesDataModelList.add(model);
      }
    });
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['jumpUrl'] = jumpUrl;
    data['items'] = myDevicesDataModelList
        .map((MyDevicesDataModel model) => model.toJson())
        .toList();

    return data;
  }

  @override
  String toString() {
    return 'MyDevicesData{myDevicesDataModelList: $myDevicesDataModelList, title: $title, jumpUrl: $jumpUrl}';
  }
}

class MyDevicesDataModel {
  String imgUrl = '';
  String mainProdName = '';
  String productModel = '';
  String appClickUrl = '';
  List<QuickEntranceModel> quickEntranceListVM = <QuickEntranceModel>[];

  MyDevicesDataModel.fromJson(Map<dynamic, dynamic> json) {
    imgUrl = json.stringValueForKey('img_url', '');
    mainProdName = json.stringValueForKey('main_prod_name', '');
    productModel = json.stringValueForKey('product_model', '');
    appClickUrl = json.stringValueForKey('appClickUrl', '');

    json.listValueForKey('buttonList', <dynamic>[]).forEach((dynamic element) {
      if (element is Map) {
        final QuickEntranceModel model = QuickEntranceModel.fromJson(element);
        quickEntranceListVM.add(model);
      }
    });
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['img_url'] = imgUrl;
    data['main_prod_name'] = mainProdName;
    data['product_model'] = productModel;
    data['appClickUrl'] = appClickUrl;
    data['buttonList'] = quickEntranceListVM
        .map((QuickEntranceModel model) => model.toJson())
        .toList();
    return data;
  }

  @override
  String toString() {
    return 'ResultModel{imgUrl: $imgUrl, mainProdName: $mainProdName, productModel: $productModel, '
        'quickEntranceListVM: $quickEntranceListVM, appClickUrl: $appClickUrl}';
  }
}

class QuickEntranceModel {
  String buttonName = '';
  String buttonUrl = '';

  QuickEntranceModel.fromJson(Map<dynamic, dynamic> json) {
    buttonName = json.stringValueForKey('buttonName', '');
    buttonUrl = json.stringValueForKey('buttonUrl', '');
  }

  Map<String, String> toJson() {
    final Map<String, String> data = <String, String>{};
    data['buttonName'] = buttonName;
    data['buttonUrl'] = buttonUrl;

    return data;
  }

  @override
  String toString() {
    return 'QuickEntranceModel{buttonName: $buttonName, buttonUrl: $buttonUrl}';
  }
}
