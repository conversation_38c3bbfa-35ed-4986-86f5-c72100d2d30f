import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class DeviceInfoModel extends UhomeResponseModel {
  DeviceInfoModel.fromJson(super.data) : super.fromJson() {
    resultModel = ResultModel.fromJson(super.retData);
  }

  ResultModel? resultModel;
}

class ResultModel {
  String barCode = '';

  ResultModel.fromJson(Map<dynamic, dynamic> json) {
    barCode = json.stringValueForKey('barCode', '');
  }
}
