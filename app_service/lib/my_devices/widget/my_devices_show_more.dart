import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/store/service_store.dart';
import 'package:app_service/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class MyDevicesShowMoreWidget extends StatefulWidget {
  MyDevicesShowMoreWidget({super.key, required this.expansionStatus});

  ExpansionStatus expansionStatus;

  @override
  _RotationAnimationState createState() => _RotationAnimationState();
}

class _RotationAnimationState extends State<MyDevicesShowMoreWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 0.5).animate(_controller)
      ..addListener(() {
        if (mounted) {
          setState(() {});
        }
      });
    if (widget.expansionStatus == ExpansionStatus.expand) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(covariant MyDevicesShowMoreWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.expansionStatus == ExpansionStatus.expand &&
        oldWidget.expansionStatus != ExpansionStatus.expand) {
      _controller.forward();
    } else if (widget.expansionStatus != ExpansionStatus.expand &&
        oldWidget.expansionStatus == ExpansionStatus.expand) {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleRotation() {
    if (widget.expansionStatus != ExpansionStatus.expand) {
      serviceStore
          .dispatch(UpdateExpansionStatusAction(ExpansionStatus.expand));
      gioTrack(Constant.myDevicesShowMore, <String, Object>{
        'result': '展开',
      });
    } else {
      serviceStore
          .dispatch(UpdateExpansionStatusAction(ExpansionStatus.collapse));
      gioTrack(Constant.myDevicesShowMore, <String, Object>{
        'result': '收起',
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: _toggleRotation,
      child: Container(
        padding: const EdgeInsets.only(top: 16, bottom: 4),
        margin: const EdgeInsets.only(bottom: 12),
        height: 32,
        width: double.infinity,
        child: RotationTransition(
          turns: _animation,
          child: const Center(
            child: Image(
              width: 12,
              height: 12,
              fit: BoxFit.contain,
              image: AssetImage('assets/images/device_show_more_icon.webp',
                  package: Constant.packageName),
            ),
          ),
        ),
      ),
    );
  }
}
