import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import '../../store/service_store.dart';

class AddDevicesWidget extends StatelessWidget {
  const AddDevicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        serviceStore.dispatch(AddDeviceAction(false));
      },
      child: Container(
        height: Constant.myDevicesCardHeight,
        width: double.infinity,
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 24),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(22)),
          color: Colors.white,
        ),
        child: Column(
          children: <Widget>[
            const SizedBox(height: 31),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Image(
                  width: 15,
                  height: 15,
                  fit: BoxFit.cover,
                  image: AssetImage('assets/images/device_add_icon.webp',
                      package: Constant.packageName),
                ),
                const SizedBox(width: 2),
                Text(
                  '去添加',
                  textHeightBehavior: const TextHeightBehavior(
                      leadingDistribution: TextLeadingDistribution.even),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppSemanticColors.item.primary,
                    fontWeight: FontWeight.w500,
                    fontFamilyFallback: fontFamilyFallback(),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Text(
                '添加后可查看家电质保信息、使用说明等',
                textHeightBehavior: const TextHeightBehavior(
                    leadingDistribution: TextLeadingDistribution.even),
                style: TextStyle(
                  fontSize: 12,
                  color: AppSemanticColors.item.secWeaken,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
