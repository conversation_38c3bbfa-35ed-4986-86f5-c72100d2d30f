import 'dart:async';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

const int animationDelay = 100;
double runSpacing = 12;
double spacing = 12;

typedef AnimatedListViewItemBuilder<U> = Widget Function(U content, int index);

class AnimatedListView<T> extends StatefulWidget {
  const AnimatedListView({
    super.key,
    required this.deviceCardType,
    required this.list,
    required this.expansionStatus,
    required this.itemBuilder,
    required this.deviceListJumpUrl,
  });

  final DeviceCardType deviceCardType;
  final List<T> list;
  final ExpansionStatus expansionStatus;
  final AnimatedListViewItemBuilder<T> itemBuilder;
  final String deviceListJumpUrl;

  @override
  State<AnimatedListView<T>> createState() => _AnimatedListViewState<T>();
}

class _AnimatedListViewState<T> extends State<AnimatedListView<T>> {
  @override
  Widget build(BuildContext context) {
    final int listLength = widget.list.length;

    final double allDevicesWidgetHeight =
        widget.deviceCardType == DeviceCardType.showMore ? 60 : 0;

    final double widgetHeight = widget.expansionStatus == ExpansionStatus.expand
        ? (listLength / 2).ceil() * Constant.myDevicesCardHeight +
            ((listLength / 2).ceil() - 1) * runSpacing +
            allDevicesWidgetHeight
        : Constant.myDevicesCardHeight;

    final Duration duration = Duration(
        milliseconds: widget.expansionStatus == ExpansionStatus.quicklyCollapse
            ? 200
            : 400);
    return AnimatedContainer(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      curve: Curves.ease,
      color: Colors.transparent,
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      height: widgetHeight,
      duration: duration,
      child: Wrap(
        spacing: spacing,
        runSpacing: runSpacing,
        children: <Widget>[
          for (int i = 0; i < listLength; i++)
            ItemBuilder(
              total: listLength,
              expanded: widget.expansionStatus == ExpansionStatus.expand,
              animation: i >= 2,
              index: i,
              content: widget.itemBuilder(widget.list[i], i),
            ),
          _buildAllDevicesButtonWidget()
        ],
      ),
    );
  }

  Widget _buildAllDevicesButtonWidget() {
    if (widget.deviceCardType != DeviceCardType.showMore) {
      return const SizedBox();
    }
    return AnimatedOpacity(
        opacity: widget.expansionStatus == ExpansionStatus.expand ? 1 : 0,
        duration: const Duration(milliseconds: 400),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            if (widget.deviceListJumpUrl.isEmpty) {
              ToastHelper.showToast('无响应，请稍后再试');
            } else {
              goToPage(widget.deviceListJumpUrl);
            }

            gioTrack(Constant.myDevicesAllGio);
          },
          child: Container(
              alignment: Alignment.center,
              height: 44,
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 4, top: 4),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(16)),
                color: Colors.white,
              ),
              child: Text(
                '查看全部',
                style: TextStyle(
                  color: AppSemanticColors.component.information.on,
                  fontSize: 16,
                  fontFamilyFallback: fontFamilyFallback(),
                  fontWeight: FontWeight.w500,
                ),
              )),
        ));
  }
}

class ItemBuilder extends StatefulWidget {
  const ItemBuilder(
      {super.key,
      required this.index,
      required this.expanded,
      required this.total,
      required this.animation,
      required this.content});

  final int total;
  final int index;
  final bool expanded;
  final bool animation;
  final Widget content;

  @override
  State<ItemBuilder> createState() => _ItemBuilderState();
}

class _ItemBuilderState extends State<ItemBuilder> {
  Timer? timer;

  bool? _expanded;

  @override
  void initState() {
    super.initState();
    _expanded = widget.expanded;
    _startAnimation();
  }

  @override
  void didUpdateWidget(covariant ItemBuilder oldWidget) {
    super.didUpdateWidget(oldWidget);
    _startAnimation();
  }

  void _startAnimation() {
    timer?.cancel();
    // 延迟3秒后触发动画
    if (widget.expanded) {
      timer = Timer(
          Duration(
              milliseconds: ((widget.index / 2).floor() - 1) * animationDelay),
          () {
        if (mounted) {
          setState(() {
            _expanded = widget.expanded;
          });
        }
      });
    } else {
      timer = Timer(
          Duration(
              milliseconds:
                  ((widget.total / 2).ceil() - (widget.index / 2).floor() - 1) *
                      animationDelay), () {
        if (mounted) {
          setState(() {
            _expanded = widget.expanded;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    final double itemWidth = MediaQuery.of(context).size.width / 2 - 22;
    if (!widget.animation) {
      return Container(
        alignment: Alignment.bottomCenter,
        width: itemWidth,
        height: Constant.myDevicesCardHeight,
        child: widget.content,
      );
    }
    return AnimatedOpacity(
      opacity: _expanded! ? 1 : 0,
      duration: const Duration(milliseconds: 400),
      child: AnimatedContainer(
        curve: Curves.easeOutCubic,
        alignment: Alignment.bottomCenter,
        duration: const Duration(milliseconds: 200),
        width: itemWidth,
        height: Constant.myDevicesCardHeight,
        child: widget.content,
      ),
    );
  }
}
