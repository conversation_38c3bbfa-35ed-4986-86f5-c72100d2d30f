import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/gradient_border_container.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class MyDevicesUnLoginWidget extends StatelessWidget {
  const MyDevicesUnLoginWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        goToPage(Constant.loginUrl);
        gioTrack(Constant.myDevicesLoginGio);
      },
      child: Container(
        height: Constant.myDevicesCardHeight,
        width: double.infinity,
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 24, top: 16),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(22)),
          color: Color.fromRGBO(255, 255, 255, 0.7),
          border: GradientBoxBorder(
            width: 1,
            gradient: LinearGradient(
              begin: Alignment(-0.1, -0.8),
              end: Alignment(0.1, 0.8),
              colors: <Color>[
                Colors.white,
                Color.fromRGBO(255, 255, 255, 0.01),
                Color.fromRGBO(255, 255, 255, 0.01),
                Colors.white,
              ],
              stops: <double>[0, 0.4, 0.6, 1],
            ),
          ),
        ),
        child: Column(
          children: <Widget>[
            const SizedBox(height: 31),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  '立即登录',
                  textHeightBehavior: const TextHeightBehavior(
                      leadingDistribution: TextLeadingDistribution.even),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppSemanticColors.item.primary,
                    fontWeight: FontWeight.w500,
                    fontFamilyFallback: fontFamilyFallback(),
                  ),
                ),
                const SizedBox(width: 4),
                const Image(
                  width: 12,
                  height: 12,
                  fit: BoxFit.cover,
                  image: AssetImage('assets/images/device_unlogin_icon.webp',
                      package: Constant.packageName),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '登录后可查看家电质保信息、使用说明等',
                textHeightBehavior: const TextHeightBehavior(
                    leadingDistribution: TextLeadingDistribution.even),
                style: TextStyle(
                  fontSize: 12,
                  color: AppSemanticColors.item.secWeaken,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
