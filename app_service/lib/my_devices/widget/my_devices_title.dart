import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/my_devices/view_model/devices_title_viewmodel.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/show_bottom_sheet.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';
import '../../store/service_store.dart';

class MyDevicesTitleWidget extends StatelessWidget {
  const MyDevicesTitleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        _buildTitleWidget(context),
        _buildAddDeviceWidget(),
      ],
    );
  }

  Widget _buildTitleWidget(BuildContext context) {
    return StoreConnector<AppServiceState, DevicesTitleViewModel>(
      distinct: true,
      builder:
          (BuildContext context, DevicesTitleViewModel devicesTitleViewModel) {
        return Visibility(
          visible: devicesTitleViewModel.isVisible,
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              gioTrack(Constant.myDevicesQuestionClickGio);
              showMyDeviceBottomSheet(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  margin: const EdgeInsets.only(
                      left: 16, top: 12, bottom: 12, right: 6),
                  child: Text(
                    devicesTitleViewModel.deviceListTitle,
                    textHeightBehavior: const TextHeightBehavior(
                        leadingDistribution: TextLeadingDistribution.even),
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontFamilyFallback: fontFamilyFallback(),
                      fontSize: 18,
                      color: AppSemanticColors.item.primary,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 8, bottom: 7),
                  child: Image.asset(
                    'assets/images/device_question.webp',
                    width: 16,
                    height: 16,
                    package: Constant.packageName,
                  ),
                ),
              ],
            ),
          ),
        );
      },
      converter: (Store<AppServiceState> store) {
        return DevicesTitleViewModel(
            store.state.isLogin &&
                store.state.myDevicesState.deviceListTitle.isNotEmpty,
            store.state.myDevicesState.deviceListTitle);
      },
    );
  }

  Widget _buildAddDeviceWidget() {
    return StoreConnector<AppServiceState, DeviceCardType>(
      distinct: true,
      builder: (BuildContext context, DeviceCardType deviceCardType) {
        if (deviceCardType == DeviceCardType.unLogin ||
            deviceCardType == DeviceCardType.non) {
          return const SizedBox();
        }
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            serviceStore.dispatch(AddDeviceAction(true));
          },
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const Image(
                  width: 16,
                  height: 16,
                  fit: BoxFit.cover,
                  image: AssetImage('assets/images/device_add_icon_s.webp',
                      package: Constant.packageName),
                ),
                const SizedBox(width: 2),
                Text(
                  '去添加',
                  textHeightBehavior: const TextHeightBehavior(
                      leadingDistribution: TextLeadingDistribution.even),
                  style: TextStyle(
                    fontSize: 12,
                    color: AppSemanticColors.item.secondary,
                    fontWeight: FontWeight.w500,
                    fontFamilyFallback: fontFamilyFallback(),
                  ),
                ),
                const SizedBox(width: 8)
              ],
            ),
          ),
        );
      },
      converter: (Store<AppServiceState> store) {
        return store.state.myDevicesState.deviceCardType;
      },
    );
  }
}
