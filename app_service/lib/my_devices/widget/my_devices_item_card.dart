import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/common_network_image_widget.dart';
import 'package:app_service/widget_common/gradient_border_container.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class MyDevicesItemCard extends StatelessWidget {
  DeviceItemViewModel deviceModel;
  int index;
  int amount;

  MyDevicesItemCard({
    super.key,
    required this.deviceModel,
    required this.index,
    required this.amount,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        goToPage(deviceModel.itemClickUrl);
        gioTrack(Constant.myDevicesItemClickGio, <String, Object>{
          'main_prod_name': deviceModel.mainProdName,
          'product_model': deviceModel.productModel,
        });
      },
      child: Stack(
        children: <Widget>[
          Container(
            height: Constant.myDevicesCardHeight,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(22),
              image: const DecorationImage(
                image: AssetImage(
                  'assets/images/my_device_bg_small.webp',
                  package: Constant.packageName,
                ),
                fit: BoxFit.contain,
              ),
            ),
          ),
          ExposureDetector(
            key: Key('devices_multiple_key_$index'),
            onExposure: (VisibilityInfo? visibilityInfo) {
              _gioTrackDeviceDetailContent();
            },
            child: Container(
              height: Constant.myDevicesCardHeight,
              width: double.infinity,
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(22)),
                color: Color.fromRGBO(255, 255, 255, 0.6),
              ),
              child: Column(
                children: <Widget>[
                  Container(
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(22),
                        topRight: Radius.circular(22),
                      ),
                      color: Color.fromRGBO(255, 255, 255, 0.8),
                      border: GradientBoxBorder(
                        width: 1,
                        gradient: LinearGradient(
                          begin: Alignment(-0.1, -0.8),
                          end: Alignment(0.1, 0.8),
                          colors: <Color>[
                            Colors.white,
                            Color.fromRGBO(255, 255, 255, 0.01),
                            Color.fromRGBO(255, 255, 255, 0.01),
                            Colors.white,
                          ],
                          stops: <double>[0, 0.4, 0.6, 1],
                        ),
                      ),
                    ),
                    child: Column(
                      children: <Widget>[
                        const SizedBox(height: 16),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            const SizedBox(width: 12),
                            CommonNetworkImg(
                              width: 40,
                              height: 40,
                              imageUrl: deviceModel.deviceImgUrl,
                              errorWidget: _buildDefaultIconWidget(),
                              placeHolder: _buildDefaultIconWidget(),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              height: 40,
                              width: MediaQuery.of(context).size.width / 2 - 98,
                              alignment: Alignment.centerLeft,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    textHeightBehavior:
                                        const TextHeightBehavior(
                                            leadingDistribution:
                                                TextLeadingDistribution.even),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    deviceModel.mainProdName,
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      fontFamilyFallback: fontFamilyFallback(),
                                      color: AppSemanticColors.item.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    maxLines: 1,
                                    textHeightBehavior:
                                        const TextHeightBehavior(
                                            leadingDistribution:
                                                TextLeadingDistribution.even),
                                    overflow: TextOverflow.ellipsis,
                                    deviceModel.productModel,
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: AppSemanticColors.item.secWeaken,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 12),
                          ],
                        ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                  Container(
                    height: 36,
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(22),
                        bottomRight: Radius.circular(22),
                      ),
                      border: GradientBoxBorder(
                        width: 1,
                        isThreeSides: true,
                        gradient: LinearGradient(
                          begin: Alignment(-0.1, -0.8),
                          end: Alignment(0.1, 0.8),
                          colors: <Color>[
                            Colors.white,
                            Color.fromRGBO(255, 255, 255, 0.01),
                            Color.fromRGBO(255, 255, 255, 0.01),
                            Colors.white,
                          ],
                          stops: <double>[0, 0.4, 0.6, 1],
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _buildQuickEntranceListWidgetList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultIconWidget() {
    return SizedBox(
      width: 40,
      height: 40,
      child: Stack(
        children: <Widget>[
          Container(
            width: 40,
            height: 40,
            decoration: const BoxDecoration(
              color: Color(0xFFEAEAEA),
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
          ),
          Center(
            child: Image.asset(
              'assets/images/haier_logo.webp',
              width: 20,
              height: 10,
              package: Constant.packageName,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildQuickEntranceListWidgetList() {
    final List<Widget> item = <Widget>[];
    final List<QuickEntranceViewModel> quickEntranceList =
        deviceModel.quickEntranceListVM;
    for (int index = 0; index < quickEntranceList.length; index++) {
      final QuickEntranceViewModel element = quickEntranceList[index];
      if (index == 0) {
        item.add(_buildQuickEntranceItemWidget(element, 22, 0));
      } else if (index == 1) {
        item.add(
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 1,
            height: 24,
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: <Color>[
                  Color.fromRGBO(255, 255, 255, 0),
                  Color.fromRGBO(255, 255, 255, 1),
                  Color.fromRGBO(255, 255, 255, 0),
                ],
                stops: <double>[0, 0.5, 1],
              ),
            ),
          ),
        );
        item.add(_buildQuickEntranceItemWidget(element, 0, 22));
      }
    }
    return item;
  }

  Widget _buildQuickEntranceItemWidget(QuickEntranceViewModel viewModel,
      double bottomLeftRadius, double bottomRightRadius) {
    return Expanded(
      child: PressableOverlayWithTapWidget(
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(bottomLeftRadius),
            bottomRight: Radius.circular(bottomRightRadius)),
        overlayClick: () {
          _quickEntranceItemClick(viewModel);
        },
        child: Container(
          height: 28,
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: Text(
            viewModel.title,
            textHeightBehavior: const TextHeightBehavior(
                leadingDistribution: TextLeadingDistribution.even),
            style: TextStyle(
              fontSize: 12,
              color: AppSemanticColors.item.secondary,
            ),
          ),
        ),
      ),
    );
  }

  void _quickEntranceItemClick(QuickEntranceViewModel viewModel) {
    goToPage(viewModel.jumpUrl);
    gioTrack(Constant.myDevicesQuickEntranceClickGio, <String, Object>{
      'main_prod_name': deviceModel.mainProdName,
      'product_model': deviceModel.productModel,
      'button_name': viewModel.title,
    });
  }

  void _gioTrackDeviceDetailContent() {
    gioTrack(Constant.myDevicesItemShowGio, <String, Object>{
      'main_prod_name': deviceModel.mainProdName,
      'product_model': deviceModel.productModel,
      'site_number': index.toString(),
      'amount': amount.toString(),
    });
  }
}
