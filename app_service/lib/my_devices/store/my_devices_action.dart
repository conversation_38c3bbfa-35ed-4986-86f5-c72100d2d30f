import 'package:app_service/my_devices/model/my_devices_model.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';

class MyDevicesBaseAction {}

class UpdateDeviceListAction extends MyDevicesBaseAction {
  UpdateDeviceListAction(this.myDevicesDataModelList, this.deviceListTitle,
      this.deviceListJumpUrl);

  List<MyDevicesDataModel> myDevicesDataModelList = <MyDevicesDataModel>[];
  String deviceListTitle = '';
  String deviceListJumpUrl = '';
}

class AddDeviceAction extends MyDevicesBaseAction {
  AddDeviceAction(this.isRight);

  bool isRight;
}

class UpdateExpansionStatusAction extends MyDevicesBaseAction {
  UpdateExpansionStatusAction(this.expansionStatus);

  ExpansionStatus expansionStatus;
}
