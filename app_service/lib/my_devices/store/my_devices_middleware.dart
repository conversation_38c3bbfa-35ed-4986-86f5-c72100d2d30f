import 'package:app_service/my_devices/model/device_info_model.dart';
import 'package:app_service/my_devices/model/scan_result_model.dart';
import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/service/http_service.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_common_ui/src/utils/util.dart';
import 'package:redux/redux.dart';
import 'package:vdn/vdn.dart';

class MyDevicesMiddleware implements MiddlewareClass<AppServiceState> {
  @override
  dynamic call(
      Store<AppServiceState> store, dynamic action, NextDispatcher next) {
    if (action is AddDeviceAction) {
      _addDevice(action.isRight);
      return;
    }
    next(action);
  }

  bool _isAlphaNumeric(String str) {
    const String pattern = r'^[a-zA-Z0-9]+$';
    final RegExp regExp = RegExp(pattern);
    return regExp.hasMatch(str);
  }

  Future<void> _addDevice(bool isRight) async {
    try {
      if (isRight) {
        gioTrack(Constant.myDevicesAddRightClickGio);
      } else {
        gioTrack(Constant.myDevicesAddClickGio);
      }

      final IsOnline isOnline =
          await Network.isOnline();
      if (!isOnline.isOnline) {
        ToastHelper.showToast(Constant.netWorkError);
        return;
      }
      final Map<dynamic, dynamic> scanResult = await Vdn.goToPageForResult(
          'http://uplus.haier.com/uplusapp/scan/commonscanpage.html?needAuthLogin=1&btn1_Link=https%3A%2F%2Fservice.haiersmarthomes.com%2Fapp%2Findex.html%23%2FserviceProduct%3Ffrom%3Djdlb%26type%3DT99%26push%3DoutSide%26fromPage%3Dnull%26userId%3D%26stageId%3D%26taskId%3D&btn1_Title=%E6%89%8B%E5%8A%A8%E6%B7%BB%E5%8A%A0&btn2_Link=https%3A%2F%2Fservice.haiersmarthomes.com%2Fapp%2Findex.html%23%2FmyHomeAppliance%2FscanExample%3Ffrom%3Djdlb%26push%3DoutSide&btn2_Title=%E6%9F%A5%E7%9C%8B%E7%A4%BA%E4%BE%8B&scanTitle=%E6%89%AB%E6%8F%8F%E4%BA%8C%E7%BB%B4%E7%A0%81&showAlbum=1&scanError=%E6%89%AB%E7%A0%81%E5%A4%B1%E8%B4%A5');

      DevLogger.info(
          tag: Constant.packageName,
          msg: '_addDevice scan page result: $scanResult');
      final ScanResultModel scanResultModel =
          ScanResultModel.fromJson(scanResult);
      ToastHelper.updateCanShow(true);
      if (scanResultModel.retCode != '000000') {
        ToastHelper.showToast(Constant.scanError);
        gioTrackAddFail(scanResultModel.retCode, Constant.scanError);
        return;
      }
      final String? oid = scanResultModel.dataModel?.resultModel?.scanResult;
      if (oid == null || oid.isEmpty) {
        // 处理不扫码返回，或者无结果时，不进行toast提示
        return;
      }
      if (oid.contains('oid.haier.com/oid?ewm=D00')) {
        final DeviceInfoModel? res = await Service()
            .getDeviceInfo(requestMap: <String, String>{'oid': oid});
        final String? barCode = res?.resultModel?.barCode;
        if (barCode == null) {
          ToastHelper.showToast(Constant.scanError);
          gioTrackAddFail(scanResultModel.retCode, Constant.scanError);
          return;
        }

        if (barCode.isNotEmpty) {
          _navigateToAddAppliance(barCode);
        } else {
          ToastHelper.showToast(Constant.scanResultEmptyTip);
          gioTrackAddFail(scanResultModel.retCode, Constant.scanResultEmptyTip);
          return;
        }
      } else if ((oid.length == 20 || oid.length == 22) &&
          _isAlphaNumeric(oid)) {
        if (oid.endsWith('00000000')) {
          ToastHelper.showToast(Constant.notSupportAddTip);
          gioTrackAddFail(scanResultModel.retCode, Constant.notSupportAddTip);
          return;
        } else {
          _navigateToAddAppliance(oid);
        }
      } else {
        ToastHelper.showToast(Constant.scanError);
        gioTrackAddFail(scanResultModel.retCode, Constant.scanError);
        return;
      }
    } catch (e) {
      DevLogger.error(tag: Constant.packageName, msg: 'scan page error: $e');
    }
  }

  void _navigateToAddAppliance(String wholeProductCode) {
    goToPage(
        'https://service.haiersmarthomes.com/app/index.html#/addAppliance?form_page=zjwq&whole_product_code=$wholeProductCode&scan=0&push=outSide');
  }

  void gioTrackAddFail(String errCode, String errMsg) {
    gioTrack(Constant.myDevicesAddFailGio,
        <String, Object>{'code': errCode, 'extend_info': errMsg});
  }
}
