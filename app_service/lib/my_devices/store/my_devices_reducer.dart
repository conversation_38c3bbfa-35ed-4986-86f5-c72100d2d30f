import 'dart:math';

import 'package:app_service/my_devices/model/my_devices_model.dart';
import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:redux/redux.dart';

final Reducer<AppServiceState> myDevicesReducer =
    combineReducers(<Reducer<AppServiceState>>[
  TypedReducer<AppServiceState, UpdateDeviceListAction>(
          _updateDeviceListReducer)
      .call,
  TypedReducer<AppServiceState, UpdateExpansionStatusAction>(
          _updateExpansionStatusReducer)
      .call,
]);

AppServiceState _updateDeviceListReducer(
    AppServiceState state, UpdateDeviceListAction action) {
  List<MyDevicesDataModel> devicesVMList = action.myDevicesDataModelList;
  state.myDevicesState.devicesVMList = <DeviceItemViewModel>[];
  final int length = devicesVMList.length;
  DevLogger.info(
      tag: Constant.packageName,
      msg: '_updateDeviceListReducer myDevicesDataModelList.length--$length');

  if (length == 0) {
    state.myDevicesState.deviceCardType = DeviceCardType.non;
  } else if (length > 0 && length <= 2) {
    state.myDevicesState.deviceCardType = DeviceCardType.single;
  } else if (length > 2 && length <= 6) {
    state.myDevicesState.deviceCardType = DeviceCardType.multiple;
  } else if (length > 6) {
    devicesVMList = action.myDevicesDataModelList.sublist(0, 6);
    state.myDevicesState.deviceCardType = DeviceCardType.showMore;
  }

  state.myDevicesState.deviceListTitle = action.deviceListTitle;
  state.myDevicesState.deviceListJumpUrl = action.deviceListJumpUrl;

  devicesVMList.forEach((MyDevicesDataModel element) {
    final List<QuickEntranceViewModel> quickEntranceListVM =
        <QuickEntranceViewModel>[];
    final int quickEntranceListLength =
        min(2, element.quickEntranceListVM.length);
    for (int index = 0; index < quickEntranceListLength; index++) {
      final QuickEntranceModel quickEntranceModelElement =
          element.quickEntranceListVM[index];
      if (quickEntranceModelElement.buttonName.isEmpty ||
          quickEntranceModelElement.buttonUrl.isEmpty) {
        DevLogger.info(
            tag: Constant.packageName,
            msg:
                '_updateDeviceListReducer --buttonName: ${quickEntranceModelElement.buttonName} '
                '-- buttonUrl: ${quickEntranceModelElement.buttonUrl}');
        return;
      }
      quickEntranceListVM.add(QuickEntranceViewModel(
          quickEntranceModelElement.buttonName,
          quickEntranceModelElement.buttonUrl));
    }
    DevLogger.info(
        tag: Constant.packageName,
        msg:
            '_updateDeviceListReducer quickEntranceListVM.length--${quickEntranceListVM.length}');
    state.myDevicesState.devicesVMList.add(DeviceItemViewModel(
        element.imgUrl,
        element.productModel,
        element.mainProdName,
        element.appClickUrl,
        quickEntranceListVM));
  });
  return state;
}

AppServiceState _updateExpansionStatusReducer(
    AppServiceState state, UpdateExpansionStatusAction action) {
  state.myDevicesState.expansionStatus = action.expansionStatus;
  return state;
}
