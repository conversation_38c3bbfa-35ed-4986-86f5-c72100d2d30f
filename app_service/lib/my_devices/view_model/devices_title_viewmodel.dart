class DevicesTitleViewModel {
  bool isVisible = false;
  String deviceListTitle = '';

  DevicesTitleViewModel(this.isVisible, this.deviceListTitle);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DevicesTitleViewModel &&
          runtimeType == other.runtimeType &&
          isVisible == other.isVisible &&
          deviceListTitle == other.deviceListTitle;

  @override
  int get hashCode => isVisible.hashCode ^ deviceListTitle.hashCode;

  @override
  String toString() {
    return 'DevicesTitleViewModel{isVisible: $isVisible, deviceListTitle: $deviceListTitle}';
  }
}
