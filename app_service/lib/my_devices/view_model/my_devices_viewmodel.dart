import 'package:device_utils/compare/compare.dart';

class MyDevicesViewModel {
  List<DeviceItemViewModel> devicesVMList = <DeviceItemViewModel>[];
  DeviceCardType deviceCardType = DeviceCardType.showMore;
  ExpansionStatus expansionStatus = ExpansionStatus.collapse;
  String deviceListJumpUrl = '';

  MyDevicesViewModel(this.devicesVMList, this.deviceCardType,
      this.expansionStatus, this.deviceListJumpUrl);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MyDevicesViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(devicesVMList, other.devicesVMList) &&
          deviceCardType == other.deviceCardType &&
          expansionStatus == other.expansionStatus &&
          deviceListJumpUrl == other.deviceListJumpUrl;

  @override
  int get hashCode =>
      listHashCode(devicesVMList) ^
      deviceCardType.hashCode ^
      expansionStatus.hashCode ^
      deviceListJumpUrl.hashCode;

  @override
  String toString() {
    return 'MyDevicesViewModel{devicesVMList: $devicesVMList, deviceCardType: $deviceCardType, '
        'expansionStatus: $expansionStatus, deviceListJumpUrl: $deviceListJumpUrl}';
  }
}

enum ExpansionStatus { expand, collapse, quicklyCollapse }

enum DeviceCardType { unLogin, non, single, multiple, showMore }

class DeviceItemViewModel {
  String deviceImgUrl = '';
  String productModel = '';
  String mainProdName = '';
  String itemClickUrl = '';

  DeviceItemViewModel(
    this.deviceImgUrl,
    this.productModel,
    this.mainProdName,
    this.itemClickUrl,
    this.quickEntranceListVM,
  );

  List<QuickEntranceViewModel> quickEntranceListVM = <QuickEntranceViewModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceItemViewModel &&
          runtimeType == other.runtimeType &&
          deviceImgUrl == other.deviceImgUrl &&
          productModel == other.productModel &&
          mainProdName == other.mainProdName &&
          itemClickUrl == other.itemClickUrl &&
          isListEqual(quickEntranceListVM, other.quickEntranceListVM);

  @override
  int get hashCode =>
      deviceImgUrl.hashCode ^
      productModel.hashCode ^
      mainProdName.hashCode ^
      itemClickUrl.hashCode ^
      listHashCode(quickEntranceListVM);
}

class QuickEntranceViewModel {
  String title = '';
  String jumpUrl = '';

  QuickEntranceViewModel(this.title, this.jumpUrl);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is QuickEntranceViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          jumpUrl == other.jumpUrl;

  @override
  int get hashCode => title.hashCode ^ jumpUrl.hashCode;
}
