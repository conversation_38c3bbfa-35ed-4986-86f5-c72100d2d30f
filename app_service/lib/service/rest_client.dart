import 'package:app_service/my_devices/model/device_info_model.dart';
import 'package:app_service/my_devices/model/my_devices_model.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../service_order/model/service_order_response.dart';

part 'rest_client.g.dart';

@RestApi()
abstract class AppServiceRestClient {
  factory AppServiceRestClient(Dio dio, {String baseUrl}) =
      _AppServiceRestClient;

  @POST('/api-gw/shpmResource/ad/v1/rotation')
  Future<ValueAddedServicesResponseModel> postRotationData(
      @Body() Map<String, dynamic> params);

  /// 二维码中心接口获取机器编码
  @POST('/api-gw/shpmResource/appResource/checkOidNew')
  Future<DeviceInfoModel> getDeviceInfo(
    @Body() Map<String, dynamic> requestMap,
  );

  @POST('/api-gw/shpmResource/appResource/v4/getDeviceByFamilyId')
  Future<MyDevicesResponseModel> getMyDevices(
      @Body() Map<String, dynamic> requestMap);

  /// 获取我的服务单:https://stp.haier.net/project/191/interface/api/235792
  @POST('/api-gw/shpmResource/appResource/getServiceWorkOrderList')
  Future<ServiceOrderResponseModel> getServiceOrderList(
      @Body() Map<String, dynamic> requestMap);
}
