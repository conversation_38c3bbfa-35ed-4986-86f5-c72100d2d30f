import 'dart:convert';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:app_service/my_devices/model/device_info_model.dart';
import 'package:app_service/my_devices/model/my_devices_model.dart';
import 'package:app_service/service/rest_client.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/utils/storage_util.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:device_utils/log/log.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import '../app_service_presenter.dart';
import '../service_order/model/service_order_response.dart';

class Service {
  static const String adLocationValueAddedServices = 'B0351'; // 增值服务广告位标识
  static const String adLocationValueAddedServicesYS =
      'B0380'; // 增值服务广告位标识(验收环境)
  static const String devicesLength = '7'; // 我的家电数据最多7条

  static const String adLocationDeviceServices = 'B0348'; // 家电服务广告位标识
  static const String adLocationDeviceServicesYS = 'B0407'; // 家电服务广告位标识(验收环境)

  static const String adLocationSelfServiceInquiry = 'B0350'; // 自助查询广告位标识
  static const String adLocationSelfServiceInquiryYS =
      'B0408'; // 自助查询广告位标识(验收环境)

  Future<ValueAddedServicesResponseModel?> getRotationResponseModel(
      String identification, String identificationYS, String storageKey) async {
    String adLocation = identification;
    try {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();

      if (appInfo.env == '验收') {
        adLocation = identificationYS;
      }
      final Map<String, String> messageParams = <String, String>{
        'adLocation': adLocation,
      };
      final ValueAddedServicesResponseModel valueAddedResponseModel =
          await AppServiceRestClient(UhomeDio().dio)
              .postRotationData(messageParams);
      putStringValueToStorage(
          jsonEncode(valueAddedResponseModel.toJson()), storageKey);
      DevLogger.info(
          tag: Constant.packageName,
          msg:
              'getRotationResponseModel : $adLocation--$valueAddedResponseModel');
      return valueAddedResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName,
          msg: 'getRotationResponseModel $adLocation err: $err');
      return null;
    }
  }

  /// 调用二维码中心接口获取机器编码
  Future<DeviceInfoModel?> getDeviceInfo({
    required Map<String, dynamic> requestMap,
  }) async {
    try {
      final DeviceInfoModel deviceInfoModel =
          await AppServiceRestClient(UhomeDio().dio).getDeviceInfo(
        requestMap,
      );
      DevLogger.info(
          tag: Constant.packageName, msg: 'getDeviceInfo : $deviceInfoModel');
      return deviceInfoModel;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getDeviceInfo err: $err');
      return null;
    }
  }

  Future<MyDevicesResponseModel?> getMyDevicesModel() async {
    try {
      final Map<String, String> requestMap = <String, String>{
        'pageSize': devicesLength
      };
      final MyDevicesResponseModel myDevicesResponseModel =
          await AppServiceRestClient(UhomeDio().dio).getMyDevices(requestMap);
      final UserInfo userInfo = await User.getUserInfo();
      putStringValueToStorage(jsonEncode(myDevicesResponseModel.toJson()),
          '${userInfo.userId}_${Constant.storageMyDevicesKey}');
      DevLogger.info(
          tag: Constant.packageName,
          msg: '--getMyDevicesModel-- : $myDevicesResponseModel');
      return myDevicesResponseModel;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getMyDevicesModel err: $err');
      return null;
    }
  }

  /// 进行中工单接口
  Future<ServiceOrderModel?> getServiceOrderList() async {
    try {
      final Map<String, String> requestMap = <String, String>{
        'pageSize': '6',
        'pageNum': '1'
      };
      final ServiceOrderResponseModel responseModel =
          await AppServiceRestClient(UhomeDio().dio)
              .getServiceOrderList(requestMap);
      putStringValueToStorage(jsonEncode(responseModel.toJson()),
          StorageKeyUtil.generateKeyJoinUserId(Constant.storageServiceOrderKey));
      DevLogger.info(
          tag: Constant.packageName,
          msg: '--getServiceOrderList-- : $responseModel');
      return responseModel.orderModel;
    } catch (err) {
      DevLogger.error(
          tag: Constant.packageName, msg: 'getServiceOrderList err: $err');
      return null;
    }
  }
}
