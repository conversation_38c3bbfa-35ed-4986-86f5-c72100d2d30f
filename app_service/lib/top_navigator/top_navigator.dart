import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/text_style.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';

class TopNavigatorWidget extends StatelessWidget {
  const TopNavigatorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.light,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: Container(
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        height: 44 + MediaQuery.of(context).padding.top,
        color: Colors.transparent,
        child: Stack(
          children: <Widget>[
            _buildLoginTitleWidget(),
            _buildCustomerServiceWidget()
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerServiceWidget() {
    return GestureDetector(
      onTap: () {
        goToPage(Constant.customerServiceUrl);
        gioTrack(Constant.customerServiceGio);
      },
      child: Container(
          alignment: Alignment.centerRight,
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: const Image(
            width: 24,
            height: 24,
            fit: BoxFit.cover,
            image: AssetImage('assets/images/customer_service_icon.webp',
                package: Constant.packageName),
          )),
    );
  }

  Widget _buildLoginTitleWidget() {
    return Container(
      height: 44,
      padding: const EdgeInsets.only(left: 18),
      alignment: Alignment.centerLeft,
      child: Text(
        '服务中心',
        style: TextStyle(
          fontWeight: FontWeight.w500,
          fontFamilyFallback: fontFamilyFallback(),
          fontSize: 20,
          color: AppSemanticColors.item.primary,
        ),
      ),
    );
  }
}
