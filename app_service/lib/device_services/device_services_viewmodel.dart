import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:device_utils/compare/compare.dart';

class DeviceServicesViewModel {
  DeviceServicesViewModel(this.deviceServicesListViewModel, this.totalPages,
      this.isVisible, this.title, this.netAvailable);

  DeviceServicesListViewModel deviceServicesListViewModel;
  int totalPages;
  bool isVisible = true;
  String title = '';
  bool netAvailable = true;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceServicesViewModel &&
          runtimeType == other.runtimeType &&
          deviceServicesListViewModel == other.deviceServicesListViewModel &&
          isVisible == other.isVisible &&
          totalPages == other.totalPages &&
          title == other.title &&
          netAvailable == other.netAvailable;

  @override
  int get hashCode =>
      deviceServicesListViewModel.hashCode ^
      isVisible.hashCode ^
      totalPages.hashCode ^
      title.hashCode ^
      netAvailable.hashCode;

  @override
  String toString() {
    return 'DeviceServicesViewModel{deviceServicesListViewModel: $deviceServicesListViewModel '
        ',isVisible: $isVisible ,title: $title ,totalPages: $totalPages ,netAvailable: $netAvailable}';
  }
}

class DeviceServicesListViewModel {
  DeviceServicesListViewModel(
      this.otherList, this.firstLineList, this.secondLineList);

  List<ValueAddedServicesListViewModel> otherList =
      <ValueAddedServicesListViewModel>[];
  List<ValueAddedServicesItemViewModel> firstLineList =
      <ValueAddedServicesItemViewModel>[];
  List<ValueAddedServicesItemViewModel> secondLineList =
      <ValueAddedServicesItemViewModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DeviceServicesListViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(otherList, other.otherList) &&
          isListEqual(firstLineList, other.firstLineList) &&
          isListEqual(secondLineList, other.secondLineList);

  @override
  int get hashCode =>
      listHashCode(otherList) ^
      listHashCode(firstLineList) ^
      listHashCode(secondLineList);

  @override
  String toString() {
    return 'DeviceServicesViewModel{otherList: $otherList firstLineList: $firstLineList,secondLineList: $secondLineList}';
  }
}
