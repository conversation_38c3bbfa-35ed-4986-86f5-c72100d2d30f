import 'dart:math';

import 'package:app_service/device_services/device_services_viewmodel.dart';
import 'package:app_service/device_services/store/device_services_action.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:redux/redux.dart';

final Reducer<AppServiceState> deviceServicesReducer =
    combineReducers(<Reducer<AppServiceState>>[
  TypedReducer<AppServiceState, UpdateDeviceServicesListAction>(
          _updateDeviceServicesListReducer)
      .call,
]);

AppServiceState _updateDeviceServicesListReducer(
    AppServiceState state, UpdateDeviceServicesListAction action) {
  final List<ValueAddedServicesItemViewModel> firstLineList =
      <ValueAddedServicesItemViewModel>[];
  final List<ValueAddedServicesItemViewModel> secondLineList =
      <ValueAddedServicesItemViewModel>[];
  final List<ValueAddedServicesListViewModel> otherList =
      <ValueAddedServicesListViewModel>[];
  final List<ValueAddedServicesItemViewModel> dataList =
      <ValueAddedServicesItemViewModel>[];

  final List<SlideModel> deviceServicesVMList = action.deviceServicesVMList;
  for (int i = 0; i < deviceServicesVMList.length; i++) {
    final SlideModel element = deviceServicesVMList[i];
    if (i < 2) {
      firstLineList.add(ValueAddedServicesItemViewModel(element.title,
          element.detailsUrl, element.pictureUrl, element.subtitle));
    } else if (i < Constant.deviceServicesFirstPageCount) {
      secondLineList.add(ValueAddedServicesItemViewModel(element.title,
          element.detailsUrl, element.pictureUrl, element.subtitle));
    } else {
      dataList.add(ValueAddedServicesItemViewModel(element.title,
          element.detailsUrl, element.pictureUrl, element.subtitle));
    }
  }

  final int totalPages = (deviceServicesVMList.length >
          Constant.deviceServicesFirstPageCount)
      ? ((deviceServicesVMList.length - Constant.deviceServicesFirstPageCount) /
                  Constant.selfServiceInquiryCount)
              .ceil() +
          1
      : 1;
  for (int pageIndex = 1; pageIndex < totalPages; pageIndex++) {
    final int startIndex = (pageIndex - 1) * Constant.selfServiceInquiryCount;
    final int endIndex = min(pageIndex * Constant.selfServiceInquiryCount,
        deviceServicesVMList.length - Constant.deviceServicesFirstPageCount);

    final List<ValueAddedServicesItemViewModel> pageData =
        dataList.sublist(startIndex, endIndex);
    otherList.add(ValueAddedServicesListViewModel(pageData));
  }
  state.deviceServicesState.deviceServicesListViewModel =
      DeviceServicesListViewModel(otherList, firstLineList, secondLineList);
  state.deviceServicesState.totalPages = totalPages;

  if (firstLineList.isNotEmpty) {
    state.deviceServicesState.isVisible = true;
  } else {
    state.deviceServicesState.isVisible = false;
  }
  state.deviceServicesState.title = action.title;
  return state;
}
