import 'package:app_service/device_services/device_services_viewmodel.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/widget_common/custom_pagination_widget.dart';
import 'package:app_service/widget_common/services_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class DeviceServicesWidget extends StatelessWidget {
  const DeviceServicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, DeviceServicesViewModel>(
      distinct: true,
      builder: (BuildContext context,
          DeviceServicesViewModel deviceServicesViewModel) {
        return Visibility(
          visible: deviceServicesViewModel.isVisible,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              ServicesHeaderWidget(
                  title: deviceServicesViewModel.title,
                  netAvailable: deviceServicesViewModel.netAvailable),
              CustomPaginatedWidget(
                otherList: deviceServicesViewModel
                    .deviceServicesListViewModel.otherList,
                firstLineList: deviceServicesViewModel
                    .deviceServicesListViewModel.firstLineList,
                secondLineList: deviceServicesViewModel
                    .deviceServicesListViewModel.secondLineList,
                totalPages: deviceServicesViewModel.totalPages,
                height: 173,
                mainAxisExtent: 88,
                isDeviceServices: true,
                itemClickGio: Constant.deviceServicesItemClickGio,
              ),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
      converter: (Store<AppServiceState> store) {
        return DeviceServicesViewModel(
            store.state.deviceServicesState.deviceServicesListViewModel,
            store.state.deviceServicesState.totalPages,
            store.state.deviceServicesState.isVisible,
            store.state.deviceServicesState.title,
            store.state.netAvailable);
      },
    );
  }
}
