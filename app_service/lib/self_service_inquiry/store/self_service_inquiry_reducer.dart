import 'dart:math';

import 'package:app_service/self_service_inquiry/store/self_service_inquiry_action.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:redux/redux.dart';

final Reducer<AppServiceState> selfServiceInquiryReducer =
    combineReducers(<Reducer<AppServiceState>>[
  TypedReducer<AppServiceState, UpdateSelfServiceInquiryListAction>(
          _updateSelfServiceInquiryListReducer)
      .call,
]);

AppServiceState _updateSelfServiceInquiryListReducer(
    AppServiceState state, UpdateSelfServiceInquiryListAction action) {
  state.selfServiceInquiryState.dataList = <ValueAddedServicesListViewModel>[];
  final List<ValueAddedServicesItemViewModel> dataList =
      <ValueAddedServicesItemViewModel>[];
  action.selfServiceInquiryVMList.forEach((SlideModel element) {
    dataList.add(ValueAddedServicesItemViewModel(element.title,
        element.detailsUrl, element.pictureUrl, element.subtitle));
  });

  final int totalPages =
      (dataList.length / Constant.selfServiceInquiryCount).ceil();
  for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
    final int startIndex = pageIndex * Constant.selfServiceInquiryCount;
    final int endIndex = min(
        (pageIndex + 1) * Constant.selfServiceInquiryCount, dataList.length);
    final List<ValueAddedServicesItemViewModel> pageData =
        dataList.sublist(startIndex, endIndex);
    state.selfServiceInquiryState.dataList
        .add(ValueAddedServicesListViewModel(pageData));
  }
  state.selfServiceInquiryState.totalPages = totalPages;

  if (action.selfServiceInquiryVMList.isNotEmpty) {
    state.selfServiceInquiryState.isVisible = true;
  } else {
    state.selfServiceInquiryState.isVisible = false;
  }
  state.selfServiceInquiryState.title = action.title;
  return state;
}
