import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:app_service/widget_common/custom_pagination_widget.dart';
import 'package:app_service/widget_common/services_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class SelfServiceInquiryWidget extends StatelessWidget {
  const SelfServiceInquiryWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, ValueAddedServicesViewModel>(
      distinct: true,
      builder: (BuildContext context,
          ValueAddedServicesViewModel selfServiceInquiryVM) {
        return Visibility(
          visible: selfServiceInquiryVM.isVisible,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              ServicesHeaderWidget(
                  title: selfServiceInquiryVM.title,
                  netAvailable: selfServiceInquiryVM.netAvailable),
              CustomPaginatedWidget(
                  dataList: selfServiceInquiryVM.dataList,
                  totalPages: selfServiceInquiryVM.totalPages,
                  height: Constant.selfServiceInquiryHeight,
                  mainAxisExtent: 72,
                  itemPaddingTop: 10,
                  itemClickGio: Constant.selfServiceInquiryItemClickGio),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
      converter: (Store<AppServiceState> store) {
        return ValueAddedServicesViewModel(
            store.state.selfServiceInquiryState.dataList,
            store.state.selfServiceInquiryState.isVisible,
            store.state.selfServiceInquiryState.title,
            store.state.selfServiceInquiryState.totalPages,
            store.state.netAvailable);
      },
    );
  }
}
