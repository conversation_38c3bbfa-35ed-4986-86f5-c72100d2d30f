import 'package:app_service/app_service_presenter.dart';
import 'package:app_service/device_services/device_services.dart';
import 'package:app_service/my_devices/my_devices.dart';
import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/self_service_inquiry/self_service_inquiry.dart';
import 'package:app_service/service_order/service_order_widget.dart';
import 'package:app_service/store/service_action.dart';
import 'package:app_service/store/service_state.dart';
import 'package:app_service/store/service_store.dart';
import 'package:app_service/top_navigator/top_navigator.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services.dart';
import 'package:app_service/widget_common/network_unavailable.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:device_utils/log/log.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:eshop_widgets/eshop_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class AppService extends StatefulWidget {
  bool isLogin = false;
  bool isFirstScreen = false;

  AppService({super.key, required this.isLogin, required this.isFirstScreen});

  @override
  State<AppService> createState() => _AppServiceState();
}

class _AppServiceState extends State<AppService>
    with AutomaticKeepAliveClientMixin {
  final AppServicePresenter _presenter = AppServicePresenter();

  /// [_shouldCallDidAppear] 是否应该执行didAppear里的回调
  bool _shouldCallDidAppear = true;
  String brand = SmartHomeBrand.haier;

  final ValueKey<String> _customScrollViewKey =
      const ValueKey<String>('app_service_custom_scroll_view');

  @override
  void initState() {
    super.initState();
    DevLogger.info(
        tag: Constant.packageName, msg: '----AppService initState-----');
    _setStatusBarStyle();
    pageIn();
    _initToastHelper();

    /// 添加持久监听
    _presenter.addPersistentPluginListener();
    _presenter.getServiceCacheData(widget.isLogin, widget.isFirstScreen);

    /// 避免didAppear执行回调
    _shouldCallDidAppear = false;
  }

  void didAppear([Map<dynamic, dynamic>? args]) {
    DevLogger.info(
        tag: Constant.packageName,
        msg: '----AppService didAppear----'
            ' shouldCallDidAppear: $_shouldCallDidAppear--'
            ' fromTabClick: ${args?['fromTabClick']}--'
            ' expansionStatus: ${serviceStore.state.myDevicesState.expansionStatus}');
    if (args?['fromTabClick'] == true &&
        serviceStore.state.myDevicesState.expansionStatus ==
            ExpansionStatus.expand) {
      serviceStore.dispatch(
          UpdateExpansionStatusAction(ExpansionStatus.quicklyCollapse));
    }

    gioTrack(Constant.enterServiceGio);
    if (_shouldCallDidAppear) {
      _setStatusBarStyle();
      pageIn();
      _initToastHelper();
      Network
          .checkNetwork
          .then((IsOnline isOnline) {
        serviceStore.dispatch(
            UpdateNetworkAction(isOnline.isOnline));
        if (isOnline.isOnline) {
          _presenter.requestDataByLoginStatus();
        }
      });
    } else {
      /// 为下一次 didAppear 执行准备
      _shouldCallDidAppear = true;
    }
  }

  void didDisappear() {
    DevLogger.info(
        tag: Constant.packageName, msg: '----AppService didDisappear-----');
    pageLeave(title: '服务');
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return StoreProvider<AppServiceState>(
        store: serviceStore,
        child: Stack(
          children: <Widget>[
            const TabBgWidget(),
            Scaffold(
              backgroundColor: Colors.transparent,
              appBar: PreferredSize(
                  child: const TopNavigatorWidget(),
                  preferredSize: Size(MediaQuery.of(context).size.width,
                      44 + MediaQuery.of(context).padding.top)),
              body: Stack(children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).padding.bottom),
                  child: EasyRefresh.builder(
                    spring: SpringDescription.withDampingRatio(
                        ratio: 0.8, mass: 1.0, stiffness: 500),
                    clipBehavior: Clipfalse,
                    fit: StackFit.expand,
                    header: LottieHeader(
                      triggerOffset: 56,
                      safeArea: false,
                      clamping: true,
                      position: IndicatorPosition.locator,
                      textColor: AppSemanticColors.item.primary,
                    ),
                    onRefresh: () async {
                      final IsOnline isOnline =
                          await Network.isOnline();
                      serviceStore.dispatch(UpdateNetworkAction(
                          isOnline.isOnline));
                      if (!isOnline.isOnline) {
                        ToastHelper.showToast(Constant.netWorkError);
                        DevLogger.info(
                            tag: Constant.packageName,
                            msg: 'onRefresh NetworkStatus==none');
                        return;
                      }
                      gioTrack(Constant.pullToRefreshGio, <String, String>{
                        'value': '服务',
                      });
                      serviceStore.dispatch(UpdateImageRefreshCountAction());
                      _presenter.initAppData();
                    },
                    childBuilder:
                        (BuildContext context, ScrollPhysics physics) {
                      return CustomScrollView(
                        key: _customScrollViewKey,
                        physics: const AlwaysScrollableScrollPhysics()
                            .applyTo(physics),
                        slivers: <Widget>[
                          const HeaderLocator.sliver(clearExtent: false),
                          SliverToBoxAdapter(child: _buildContent())
                        ],
                      );
                    },
                  ),
                ),
              ]),
            ),
          ],
        ));
  }

  Widget _buildContent() {
    return const Column(
      children: <Widget>[
        NetUnavailableWidget(),
        MyDevicesWidget(),
        ServiceOrderWidget(),
        DeviceServicesWidget(),
        SelfServiceInquiryWidget(),
        ValueAddedServicesWidget(),
        SizedBox(height: 20),
      ],
    );
  }

  /// 设置状态栏样式
  void _setStatusBarStyle() {
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
      statusBarColor: Colors.transparent,
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));
  }

  @override
  void dispose() {
    _presenter.removePersistentPluginListeners();
    DevLogger.info(
        tag: Constant.packageName, msg: '----AppService dispose-----');
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void _initToastHelper() {
    ToastHelper.init(context);
    ToastHelper.updateCanShow(true);
  }
}
