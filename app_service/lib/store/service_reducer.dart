import 'package:app_service/device_services/store/device_services_action.dart';
import 'package:app_service/device_services/store/device_services_reducer.dart';
import 'package:app_service/my_devices/store/my_devices_action.dart';
import 'package:app_service/my_devices/store/my_devices_reducer.dart';
import 'package:app_service/my_devices/view_model/my_devices_viewmodel.dart';
import 'package:app_service/self_service_inquiry/store/self_service_inquiry_action.dart';
import 'package:app_service/self_service_inquiry/store/self_service_inquiry_reducer.dart';
import 'package:app_service/store/service_action.dart';
import 'package:app_service/value_added_services/store/value_added_services_action.dart';
import 'package:redux/redux.dart';
import '../service_order/model/service_order_viewmodel.dart';
import '../service_order/store/service_order_reducer.dart';
import '../value_added_services/store/value_added_services_reducer.dart';
import 'service_state.dart';

final Reducer<AppServiceState> serviceReducer = combineReducers(<AppServiceState
    Function(AppServiceState state, dynamic action)>[
  TypedReducer<AppServiceState, UpdateValueAddedServicesListAction>(
          valueAddedServicesReducer)
      .call,
  TypedReducer<AppServiceState, MyDevicesBaseAction>(myDevicesReducer).call,
  TypedReducer<AppServiceState, UpdateAppLoginStatusAction>(_loginReducer).call,
  TypedReducer<AppServiceState, UpdateImageRefreshCountAction>(
          _imageRefreshCountReducer)
      .call,
  TypedReducer<AppServiceState, UpdateDeviceServicesListAction>(
          deviceServicesReducer)
      .call,
  TypedReducer<AppServiceState, UpdateSelfServiceInquiryListAction>(
          selfServiceInquiryReducer)
      .call,
  TypedReducer<AppServiceState, UpdateNetworkAction>(_networkReducer).call,
  TypedReducer<AppServiceState, UpdateUserIdAction>(_updateUserIdReducer).call,
  serviceOrderReducer,
]);

AppServiceState _loginReducer(
    AppServiceState state, UpdateAppLoginStatusAction action) {
  if (action.isLogin == null || action.isLogin == false) {
    state.isLogin = false;
  } else {
    state.isLogin = true;
  }

  if (action.isLogin == false) {
    state.myDevicesState.clear();
    state.serviceOrderState.orderCardType = OrderCardType.unLogin;
    state.userId = '';
  } else {
    if (state.myDevicesState.devicesVMList.isEmpty) {
      state.myDevicesState.deviceCardType = DeviceCardType.non;
    }
  }
  return state;
}

AppServiceState _imageRefreshCountReducer(
    AppServiceState state, UpdateImageRefreshCountAction action) {
  state.imageRefreshCount += 1;
  return state;
}

AppServiceState _networkReducer(
    AppServiceState state, UpdateNetworkAction action) {
  state.netAvailable = action.netAvailable;
  return state;
}

AppServiceState _updateUserIdReducer(
    AppServiceState state, UpdateUserIdAction action) {
  state.userId = action.userId;
  return state;
}