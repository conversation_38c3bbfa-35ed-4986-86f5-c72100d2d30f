import 'package:app_service/device_services/store/device_services_state.dart';
import 'package:app_service/my_devices/store/my_devices_state.dart';
import 'package:app_service/self_service_inquiry/store/self_service_inquiry_state.dart';
import 'package:app_service/service_order/store/service_order_state.dart';
import 'package:app_service/value_added_services/store/value_added_services_state.dart';

class AppServiceState {
  SelfServiceInquiryState selfServiceInquiryState = SelfServiceInquiryState();
  DeviceServicesState deviceServicesState = DeviceServicesState();
  ValueAddedServicesState valueAddedServicesState = ValueAddedServicesState();
  MyDevicesState myDevicesState = MyDevicesState();
  ServiceOrderState serviceOrderState = ServiceOrderState();
  String userId = '';

  int imageRefreshCount = 0;
  bool isLogin = false;
  bool yearTheme = false;
  bool netAvailable = true;
}
