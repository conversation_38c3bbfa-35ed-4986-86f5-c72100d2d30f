
class Constant {
  static const String packageName = 'app_service';

  /// [baseUrl] 智家请求接口域名
  static const String baseUrl = 'https://zj.haier.net';

  static const int valueAddedServicesCount = 4;
  static const int selfServiceInquiryCount = 8;
  static const int deviceServicesFirstPageCount = 6;
  static const int crossAxisCount = 4;

  static const double valueAddedServicesHeight = 92;
  static const double selfServiceInquiryHeight = 164;

  static const double myDevicesCardHeight = 106;

  static const String netWorkError = '网络不可用';
  static const String scanError = '请扫描正确的二维码';
  static const String scanResultEmptyTip = '二维码还在收录中，请手动添加';
  static const String notSupportAddTip = '请扫描SIN条形码或手动输入';

  static const String storageValueAddedKey = 'storage_service_value_added_key';
  static const String storageMyDevicesKey = 'storage_service_my_devices_key';
  static const String storageServiceOrderKey = 'storage_service_order_key';
  static const String storageDeviceServicesKey =
      'storage_service_device_services_key';
  static const String storageSelfServiceInquiryKey =
      'storage_service_self_service_inquiry_key';

  static const String haveWarrantyCard = '1'; //1代表有家电保修卡

  static const String customerServiceUrl =
      'mpaas://CustomerService/?accountId=zhijiaservice&fromPageTitle=服务首页&pId=zjapp&sceneId=cd0146&entranceId=jt&needAuthLogin=1#/zhijia';

  static const String loginUrl = 'mpaas://usercenter';

  ///GIO点位
  static const String myDevicesAllGio = 'MB34390'; //我的家电-点击全部家电
  static const String valueAddedServicesItemClickGio =
      'MB36782'; //服务tab-增值服务-点击功能入口
  static const String deviceServicesItemClickGio =
      'MB39271'; //服务tab-家电服务-点击功能入口
  static const String selfServiceInquiryItemClickGio =
      'MB39272'; //服务tab-自助查询-点击功能入口
  static const String customerServiceGio = 'MB34589'; //服务-点击客服（7.24.0）
  static const String enterServiceGio = 'MB36953'; //服务-进入
  static const String myDevicesItemClickGio = 'MB34489'; //我的家电-点击家电卡片
  static const String myDevicesQuickEntranceClickGio = 'MB34487'; //我的家电-点击快捷入口
  static const String myDevicesAddClickGio = 'MB36796'; //我的家电-家电卡片-点击添加家电
  static const String myDevicesAddRightClickGio = 'MB38388'; //我的家电-点击右侧添加家电
  static const String myDevicesAddFailGio = 'MB36305'; //我的家电-添加家电失败
  static const String myDevicesLoginGio = 'MB37063'; //家电卡片-点击登录
  static const String pullToRefreshGio =
      'MB36946'; // MB36946 智家APP830-框架-下拉刷新  value: {智家、服务、商城、我的}
  static const String myDevicesItemShowGio = 'MB37206'; //服务tab-我的家电卡片曝光
  static const String myDevicesQuestionClickGio = 'MB37339'; //服务tab-我的家电-点击问号
  static const String myDevicesShowMore = 'MB38617'; //服务tab-我的家电-点击箭头

  static const String serviceOrderClickGio = 'MB38311'; //点击服务工单卡片
  static const String serviceOrderShowGio = 'MB38312'; //服务工单曝光
  static const String serviceOrderTitleClickGio = 'MB38310'; //点击服务工单顶栏
}
