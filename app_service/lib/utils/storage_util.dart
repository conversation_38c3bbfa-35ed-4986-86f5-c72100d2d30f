import 'package:app_service/utils/constant.dart';
import 'package:device_utils/log/log.dart';
import 'package:storage/storage.dart';

import '../store/service_store.dart';

Future<String> getStringValueByStorage(String key) async {
  String value = '';
  try {
    value = await Storage.getStringValue(key);
    DevLogger.info(
        tag: Constant.packageName,
        msg: 'getStringValueByStorage key $key ----json: $value');
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName,
        msg: 'getStringValueByStorage exception: $err');
  }
  return value;
}

Future<void> putStringValueToStorage(String value, String key) async {
  DevLogger.info(
      tag: Constant.packageName,
      msg: 'putStringValueToStorage key $key ----value: $value');
  try {
    await Storage.putStringValue(key, value);
  } catch (err) {
    DevLogger.error(
        tag: Constant.packageName,
        msg: 'putStringValueToStorage exception: $err');
  }
}


class StorageKeyUtil {
  static String generateKeyJoinUserId(String keyName) {
    return '${serviceStore.state.userId}_$keyName';
  }
}