import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class ValueAddedServicesResponseModel extends UhomeResponseModel {
  ValueAddedServicesResponseModel.fromJson(super.data) : super.fromJson() {
    advertisementData = AdvertisementData.fromJson(super.retData);
  }

  AdvertisementData? advertisementData;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (advertisementData != null) {
      data['data'] = advertisementData?.toJson();
    }
    data['retCode'] = retCode;
    data['retInfo'] = retInfo;
    return data;
  }
}

class AdvertisementData {
  List<SlideModel> slideList = <SlideModel>[];
  String title = '';

  AdvertisementData.fromJson(Map<dynamic, dynamic> json) {
    title = json.stringValueForKey('title', '');
    json.listValueFor<PERSON>ey('slideList', <dynamic>[]).forEach((dynamic element) {
      if (element is Map) {
        final SlideModel model = SlideModel.fromJson(element);
        slideList.add(model);
      }
    });
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['slideList'] =
        slideList.map((SlideModel model) => model.toJson()).toList();

    return data;
  }
}

class SlideModel {
  String title = '';
  String pictureUrl = '';
  String detailsUrl = '';
  String subtitle = '';

  SlideModel(this.title, this.pictureUrl, this.detailsUrl);

  SlideModel.fromJson(Map<dynamic, dynamic> json) {
    title = json.stringValueForKey('title', '');
    pictureUrl = json.stringValueForKey('pictureUrl', '');
    detailsUrl = json.stringValueForKey('detailsUrl', '');
    subtitle = json.stringValueForKey('subtitle', '');
  }

  Map<String, String> toJson() {
    final Map<String, String> data = <String, String>{};
    data['title'] = title;
    data['pictureUrl'] = pictureUrl;
    data['detailsUrl'] = detailsUrl;
    data['subtitle'] = subtitle;

    return data;
  }
}
