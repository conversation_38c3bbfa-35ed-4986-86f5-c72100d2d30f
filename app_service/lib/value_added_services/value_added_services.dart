import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:app_service/widget_common/custom_pagination_widget.dart';
import 'package:app_service/widget_common/services_header_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

class ValueAddedServicesWidget extends StatelessWidget {
  const ValueAddedServicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, ValueAddedServicesViewModel>(
      distinct: true,
      builder: (BuildContext context,
          ValueAddedServicesViewModel valueAddedServicesViewModel) {
        return Visibility(
          visible: valueAddedServicesViewModel.isVisible,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              ServicesHeaderWidget(
                  title: valueAddedServicesViewModel.title,
                  netAvailable: valueAddedServicesViewModel.netAvailable),
              CustomPaginatedWidget(
                dataList: valueAddedServicesViewModel.dataList,
                totalPages: valueAddedServicesViewModel.totalPages,
                height: Constant.valueAddedServicesHeight,
                itemPaddingTop: 20,
                mainAxisExtent: Constant.valueAddedServicesHeight,
                itemClickGio: Constant.valueAddedServicesItemClickGio,
                isValueAddedServices: true,
              ),
            ],
          ),
        );
      },
      converter: (Store<AppServiceState> store) {
        return ValueAddedServicesViewModel(
            store.state.valueAddedServicesState.dataList,
            store.state.valueAddedServicesState.isVisible,
            store.state.valueAddedServicesState.title,
            store.state.valueAddedServicesState.totalPages,
            store.state.netAvailable);
      },
    );
  }
}
