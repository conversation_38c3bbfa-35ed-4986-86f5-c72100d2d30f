import 'package:device_utils/compare/compare.dart';

class ValueAddedServicesViewModel {
  ValueAddedServicesViewModel(this.dataList, this.isVisible, this.title,
      this.totalPages, this.netAvailable);

  List<ValueAddedServicesListViewModel> dataList =
      <ValueAddedServicesListViewModel>[];
  bool isVisible = false;
  String title = '';
  int totalPages;
  bool netAvailable = true;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValueAddedServicesViewModel &&
          runtimeType == other.runtimeType &&
          isVisible == other.isVisible &&
          title == other.title &&
          totalPages == other.totalPages &&
          isListEqual(dataList, other.dataList) &&
          netAvailable == other.netAvailable;

  @override
  int get hashCode =>
      listHashCode(dataList) ^
      isVisible.hashCode ^
      title.hashCode ^
      totalPages.hashCode ^
      netAvailable.hashCode;

  @override
  String toString() {
    return 'ValueAddedServicesViewModel{dataList: $dataList ,isVisible: $isVisible '
        ',title: $title ,totalPages: $totalPages ,netAvailable: $netAvailable}';
  }
}

class ValueAddedServicesListViewModel {
  List<ValueAddedServicesItemViewModel> valueAddedServicesVMList =
      <ValueAddedServicesItemViewModel>[];

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValueAddedServicesListViewModel &&
          runtimeType == other.runtimeType &&
          isListEqual(valueAddedServicesVMList, other.valueAddedServicesVMList);

  @override
  int get hashCode => listHashCode(valueAddedServicesVMList);

  @override
  String toString() {
    return 'ValueAddedServicesListViewModel{valueAddedServicesVMList: $valueAddedServicesVMList}';
  }

  ValueAddedServicesListViewModel(this.valueAddedServicesVMList);
}

class ValueAddedServicesItemViewModel {
  String title = '';
  String jumpUrl = '';
  String iconUrl = '';
  String subtitle = '';

  ValueAddedServicesItemViewModel(
      this.title, this.jumpUrl, this.iconUrl, this.subtitle);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ValueAddedServicesItemViewModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          jumpUrl == other.jumpUrl &&
          iconUrl == other.iconUrl &&
          subtitle == other.subtitle;

  @override
  int get hashCode =>
      title.hashCode ^ jumpUrl.hashCode ^ iconUrl.hashCode ^ subtitle.hashCode;

  @override
  String toString() {
    return 'ValueAddedServicesItemViewModel{title: $title, jumpUrl: $jumpUrl, iconUrl: $iconUrl, subtitle: $subtitle}';
  }
}
