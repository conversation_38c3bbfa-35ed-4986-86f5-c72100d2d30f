import 'dart:math';

import 'package:app_service/store/service_state.dart';
import 'package:app_service/utils/constant.dart';
import 'package:app_service/value_added_services/store/value_added_services_action.dart';
import 'package:app_service/value_added_services/value_added_services_model.dart';
import 'package:app_service/value_added_services/value_added_services_viewmodel.dart';
import 'package:redux/redux.dart';

final Reducer<AppServiceState> valueAddedServicesReducer =
    combineReducers(<Reducer<AppServiceState>>[
  TypedReducer<AppServiceState, UpdateValueAddedServicesListAction>(
          _updateValueAddedServicesListReducer)
      .call,
]);

AppServiceState _updateValueAddedServicesListReducer(
    AppServiceState state, UpdateValueAddedServicesListAction action) {
  state.valueAddedServicesState.dataList = <ValueAddedServicesListViewModel>[];
  final List<ValueAddedServicesItemViewModel> dataList =
      <ValueAddedServicesItemViewModel>[];
  action.valueAddedServicesVMList.forEach((SlideModel element) {
    dataList.add(ValueAddedServicesItemViewModel(element.title,
        element.detailsUrl, element.pictureUrl, element.subtitle));
  });

  final int totalPages =
      (dataList.length / Constant.valueAddedServicesCount).ceil();
  for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
    final int startIndex = pageIndex * Constant.valueAddedServicesCount;
    final int endIndex = min(
        (pageIndex + 1) * Constant.valueAddedServicesCount, dataList.length);
    final List<ValueAddedServicesItemViewModel> pageData =
        dataList.sublist(startIndex, endIndex);
    state.valueAddedServicesState.dataList
        .add(ValueAddedServicesListViewModel(pageData));
  }
  state.valueAddedServicesState.totalPages = totalPages;

  if (action.valueAddedServicesVMList.isNotEmpty) {
    state.valueAddedServicesState.isVisible = true;
  } else {
    state.valueAddedServicesState.isVisible = false;
  }
  state.valueAddedServicesState.title = action.title;
  return state;
}
