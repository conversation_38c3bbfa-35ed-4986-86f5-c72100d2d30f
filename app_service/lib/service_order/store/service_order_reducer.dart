import 'dart:math';

import 'package:app_service/service_order/model/service_order_viewmodel.dart';
import 'package:app_service/service_order/store/service_order_action.dart';
import 'package:redux/redux.dart';

import '../../store/service_state.dart';
import '../model/service_order_response.dart';

final Reducer<AppServiceState> serviceOrderReducer =
    combineReducers(<Reducer<AppServiceState>>[
  TypedReducer<AppServiceState, UpdateServiceOrderListAction>(
          _updateServiceOrderListReducer)
      .call,
]);

AppServiceState _updateServiceOrderListReducer(
    AppServiceState state, UpdateServiceOrderListAction action) {
  final ServiceOrderModel orderModel = action.serviceOrderModel;
  final List<ServiceOrderItemModel> orderList =
      _processOrderStatusIndex(orderModel.items, orderModel.statusList);
  state.serviceOrderState.progressListUrl = orderModel.jumpUrl;
  state.serviceOrderState.totalNums = orderModel.totalNums;

  final OrderCardType orderCardType = _determineOrderCardType(orderList);
  state.serviceOrderState.orderCardType = orderCardType;

  final List<ServiceOrderItemModel> finalOrderList =
      orderCardType == OrderCardType.showMore
          ? orderList.sublist(0, min(5, orderList.length))
          : orderList;

  state.serviceOrderState.orderVMList = finalOrderList;
  state.serviceOrderState.statusList = orderModel.statusList;
  return state;
}

List<ServiceOrderItemModel> _processOrderStatusIndex(
    List<ServiceOrderItemModel> orderList,
    List<ServiceOrderStatusModel> statusList) {
  for (final ServiceOrderItemModel item in orderList) {
    final int index = statusList.indexWhere(
        (ServiceOrderStatusModel status) => status.code == item.statusCode);
    if (index != -1) {
      item.statusIndex = index;
    }
  }
  return orderList;
}

const int kMaxMultipleOrders = 5;

OrderCardType _determineOrderCardType(List<ServiceOrderItemModel> list) {
  final int length = list.length;
  if (length == 0) {
    return OrderCardType.non;
  } else if (length == 1) {
    return OrderCardType.single;
  } else if (length <= kMaxMultipleOrders) {
    return OrderCardType.multiple;
  } else {
    return OrderCardType.showMore;
  }
}
