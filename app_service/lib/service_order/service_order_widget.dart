import 'package:app_service/service_order/model/service_order_viewmodel.dart';
import 'package:app_service/service_order/order_page_view_widget.dart';
import 'package:app_service/service_order/store/service_order_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_redux/flutter_redux.dart';
import 'package:redux/redux.dart';

import '../store/service_state.dart';
import '../utils/constant.dart';
import '../widget_common/text_style.dart';

class ServiceOrderWidget extends StatelessWidget {
  const ServiceOrderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return StoreConnector<AppServiceState, ServiceOrderViewModel>(
        distinct: true,
        builder: (BuildContext context, ServiceOrderViewModel model) {
          final OrderCardType orderCardType = model.orderCardType;
          if (orderCardType == OrderCardType.unLogin ||
              orderCardType == OrderCardType.non) {
            return Container();
          }
          return Column(
            children: <Widget>[
              const SizedBox(
                height: 12,
              ),
              _buildOrderWidget(model),
              const SizedBox(
                height: 24,
              ),
            ],
          );
        },
        converter: (Store<AppServiceState> store) {
          final ServiceOrderState state = store.state.serviceOrderState;
          return ServiceOrderViewModel(
            state.orderCardType,
            state.orderVMList,
            state.statusList,
            state.progressListUrl,
            state.totalNums,
          );
        });
  }

  Widget _buildOrderWidget(ServiceOrderViewModel model) {
    return ExposureDetector(
      key: const Key(Constant.storageServiceOrderKey),
      onExposure: (VisibilityInfo? visibilityInfo) {
        gioTrack(Constant.serviceOrderShowGio);
      },
      child: Container(
        height: model.orderVMList.length <= 1 ? 188 : 201,
        margin: const EdgeInsets.only(left: 16, right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(22),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.topRight,
            colors: <Color>[
              Color.fromRGBO(0, 129, 255, 0.8),
              Color.fromRGBO(139, 209, 255, 0.8)
            ],
          ),
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: <Widget>[
            Positioned(
              top: 0,
              left: 16,
              right: 0,
              child: _buildOrderTitleWidget(model),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildOrderPageViewWidget(model),
            ),
            Positioned(
              top: 44,
              left: 0,
              right: 0,
              child: _buildDivideLine(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivideLine() {
    return Container(
      height: 1,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: <Color>[
            Color.fromRGBO(255, 255, 255, 0.1),
            Colors.white,
            Colors.white,
            Colors.white,
            Color.fromRGBO(255, 255, 255, 0.1),
          ],
          stops: <double>[0, 0.2, 0.5, 0.8, 1],
        ),
      ),
    );
  }

  Widget _buildOrderTitleWidget(ServiceOrderViewModel model) {
    return GestureDetector(
      onTap: () {
        goToPage(model.progressListUrl);
        gioTrack(Constant.serviceOrderTitleClickGio);
      },
      child: Container(
        height: 44,
        width: double.infinity,
        color: Colors.transparent,
        child: Row(
          children: <Widget>[
            Text(
              '您有${model.totalNums}个服务订单进行中',
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              textAlign: TextAlign.center,
              style: TextStyle(
                color: AppSemanticColors.background.primary,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                fontFamilyFallback: fontFamilyFallback(),
              ),
            ),
            const Image(
              width: 12,
              height: 12,
              fit: BoxFit.contain,
              image: AssetImage('assets/images/device_show_white_icon.webp',
                  package: Constant.packageName),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildOrderPageViewWidget(ServiceOrderViewModel model) {
    return Container(
      height: model.orderVMList.length <= 1 ? 145 : 157,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(22)),
        color: Color.fromRGBO(255, 255, 255, 0.8),
      ),
      child: OrderPageViewWidget(orderVM: model),
    );
  }
}
