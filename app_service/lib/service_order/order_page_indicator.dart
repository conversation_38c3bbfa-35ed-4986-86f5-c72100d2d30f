import 'package:flutter/material.dart';

// 指示器常量
const double kIndicatorInitialWidth = 4;
const double kIndicatorMaxWidth = 8;
const double kIndicatorInitialRadius = 7;
const Color kIndicatorInactiveColor = Color.fromRGBO(0, 0, 0, 0.1);
const Color kIndicatorActiveColor = Color(0xFF2283E2);
const double kIndicatorHeight = 4;
const double kIndicatorSpacing = 4;

class OrderPageIndicator extends StatelessWidget {
  final int index;
  final double currentPage;

  const OrderPageIndicator({
    super.key,
    required this.index,
    required this.currentPage,
  });

  @override
  Widget build(BuildContext context) {
    // 判断当前指示器是否激活
    final bool isActive = index == currentPage.round();
    // 计算目标宽度
    final double targetWidth =
        isActive ? kIndicatorMaxWidth : kIndicatorInitialWidth;
    // 计算目标颜色
    final Color targetColor =
        isActive ? kIndicatorActiveColor : kIndicatorInactiveColor;

    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: kIndicatorMaxWidth, end: targetWidth),
      duration: const Duration(milliseconds: 100),
      curve: Curves.easeInOutBack,
      builder: (BuildContext context, double width, Widget? child) {
        // 计算当前颜色
        final Color color = Color.lerp(
                kIndicatorInactiveColor,
                targetColor,
                (width - kIndicatorInitialWidth) /
                    (kIndicatorMaxWidth - kIndicatorInitialWidth)) ??
            kIndicatorInactiveColor;
        return Container(
          width: width,
          height: kIndicatorHeight,
          margin: const EdgeInsets.symmetric(horizontal: kIndicatorSpacing),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(kIndicatorInitialRadius),
          ),
        );
      },
    );
  }
}
