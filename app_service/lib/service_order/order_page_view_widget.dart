import 'dart:math';

import 'package:app_service/service_order/order_page_indicator.dart';
import 'package:app_service/service_order/store/service_order_reducer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:upsystem/upsystem.dart';
import '../utils/constant.dart';
import '../widget_common/common_network_image_widget.dart';
import '../widget_common/text_style.dart';
import 'model/service_order_response.dart';
import 'model/service_order_viewmodel.dart';

class OrderPageViewWidget extends StatefulWidget {
  final ServiceOrderViewModel orderVM;

  const OrderPageViewWidget({super.key, required this.orderVM});

  @override
  State<StatefulWidget> createState() => _OrderPageViewWidgetState();
}

class _OrderPageViewWidgetState extends State<OrderPageViewWidget> {
  final PageController _pageController = PageController();
  final ValueNotifier<int> _currentPageNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> isRotateNotifier = ValueNotifier<bool>(false);

  Color activeColor = AppSemanticColors.item.information.primary;
  Color inactiveColor = AppSemanticColors.item.terWeaken;

  static const int maxMultipleOrderIndex = kMaxMultipleOrders - 1;
  static const double swipeLeftChange = maxMultipleOrderIndex + 0.15;
  static const double swipeLeftJump = maxMultipleOrderIndex + 0.25;

  @override
  void initState() {
    super.initState();
    int _lastReportedPage = 0;
    _pageController.addListener(() {
      final int currentPage = _pageController.page?.round() ?? 0;
      if (_lastReportedPage != currentPage) {
        _lastReportedPage = currentPage;
        _currentPageNotifier.value = currentPage;
      }

      final double pageDouble = _pageController.page ?? 0;
      if (pageDouble < swipeLeftChange) {
        isRotateNotifier.value = false;
      } else if (pageDouble < swipeLeftJump) {
        isRotateNotifier.value = true;
      } else if (pageDouble > swipeLeftJump) {
        UpSystem.impactFeedBack();
        goToPage(widget.orderVM.progressListUrl);
        _pageController.jumpToPage(maxMultipleOrderIndex);
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentPageNotifier.dispose();
    isRotateNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ServiceOrderViewModel orderVM = widget.orderVM;
    final int pageCount = orderVM.orderCardType == OrderCardType.showMore
        ? orderVM.orderVMList.length + 1
        : orderVM.orderVMList.length;
    return Stack(
      children: <Widget>[
        PageView.builder(
          controller: _pageController,
          itemCount: pageCount,
          itemBuilder: (BuildContext context, int index) {
            return Padding(
              padding: const EdgeInsets.only(top: 16),
              child: index == orderVM.orderVMList.length &&
                      orderVM.orderCardType == OrderCardType.showMore
                  ? _buildLoadingMore()
                  : _buildPageItemWidget(
                      index, orderVM.statusList, orderVM.orderVMList.length),
            );
          },
        ),
        _buildPageIndicators(orderVM.orderVMList.length),
      ],
    );
  }

  Widget _buildLoadingMore() {
    return ValueListenableBuilder<bool>(
      valueListenable: isRotateNotifier,
      builder: (BuildContext context, bool value, Widget? child) {
        return Container(
          margin: const EdgeInsets.only(left: 8, top: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Transform.rotate(
                angle: isRotateNotifier.value ? 0 : pi,
                child: Image.asset(
                  'assets/images/swipe_left_icon.webp',
                  width: 10,
                  height: 10,
                  package: Constant.packageName,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '滑\n动\n查\n看\n全\n部',
                style: TextStyle(
                  fontSize: 10,
                  color: AppSemanticColors.item.primary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPageIndicators(int pageCount) {
    if (pageCount <= 1) {
      return const SizedBox.shrink();
    }
    return ValueListenableBuilder<int>(
      valueListenable: _currentPageNotifier,
      builder: (BuildContext context, int currentPage, _) {
        return Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List<Widget>.generate(
              pageCount,
              (int index) => OrderPageIndicator(
                index: index,
                currentPage: _pageController.page ?? currentPage.toDouble(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageItemWidget(
      int index, List<ServiceOrderStatusModel> statusList, int pageCount) {
    final ServiceOrderItemModel model = widget.orderVM.orderVMList[index];
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        gioTrack(Constant.serviceOrderClickGio);
        goToPage(model.detailUrl);
      },
      child: Column(
        children: <Widget>[
          _buildHeaderSection(model),
          const SizedBox(height: 16),
          _buildProgressSection(statusList, model.statusIndex, pageCount),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(ServiceOrderItemModel model) {
    return Row(children: <Widget>[
      const SizedBox(width: 12),
      CommonNetworkImg(
        width: 40,
        height: 40,
        imageUrl: model.imgUrl,
        errorWidget: _buildDefaultImageWidget(),
        placeHolder: _buildDefaultImageWidget(),
      ),
      const SizedBox(width: 8),
      _buildOrderTitleText(model),
    ]);
  }

  Widget _buildOrderTitleText(ServiceOrderItemModel model) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            constraints: const BoxConstraints(maxWidth: 226),
            child: Text(
              model.title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textHeightBehavior: const TextHeightBehavior(
                  leadingDistribution: TextLeadingDistribution.even),
              style: TextStyle(
                fontSize: 14,
                color: AppSemanticColors.item.primary,
                fontFamilyFallback: fontFamilyFallback(),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            model.serviceTime.isEmpty ? '' : '预约时间：${model.serviceTime}',
            textHeightBehavior: const TextHeightBehavior(
                leadingDistribution: TextLeadingDistribution.even),
            style: TextStyle(
              fontSize: 11,
              color: AppSemanticColors.item.secWeaken,
            ),
          ),
        ]);
  }

  Widget _buildProgressSection(List<ServiceOrderStatusModel> statusList,
      int statusIndex, int pageCount) {
    return Container(
      height: pageCount <= 1 ? 69 : 81,
      padding: const EdgeInsets.only(top: 16),
      margin: const EdgeInsets.only(left: 4, right: 4, bottom: 4),
      decoration: BoxDecoration(
        color: const Color.fromRGBO(255, 255, 255, 0.8),
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.white, width: 1),
      ),
      child: Row(
        children: List<Widget>.generate(
          statusList.length,
          (int index) => Expanded(
            child: _buildOrderProgressWidget(
              index,
              statusList[index].name,
              statusList.length,
              statusIndex,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOrderProgressWidget(
      int index, String name, int count, int currentIndex) {
    final Color leftColor = index <= currentIndex ? activeColor : inactiveColor;
    final Color rightColor = index < currentIndex ? activeColor : inactiveColor;
    final Color textColor = index < currentIndex
        ? AppSemanticColors.item.primary
        : (index == currentIndex
            ? activeColor
            : AppSemanticColors.item.secWeaken);

    return Column(
      children: <Widget>[
        _buildProgressLine(index, count, leftColor, rightColor, currentIndex),
        const SizedBox(height: 7),
        _buildProgressText(name, index, currentIndex, textColor),
      ],
    );
  }

  Widget _buildProgressLine(int index, int count, Color leftColor,
      Color rightColor, int currentIndex) {
    return SizedBox(
      height: 14,
      child: Row(
        children: <Widget>[
          _buildLeftLine(index, leftColor),
          if (index != 0) const SizedBox(width: 4),
          _buildProgressIndicator(index, currentIndex),
          if (index != count - 1) const SizedBox(width: 4),
          _buildRightLine(index, count, rightColor),
        ],
      ),
    );
  }

  Widget _buildLeftLine(int index, Color color) {
    return Expanded(
      child: Container(
        height: 1.5,
        decoration: BoxDecoration(
            color: index == 0 ? Colors.transparent : color,
            borderRadius: BorderRadius.circular(1.5)),
      ),
    );
  }

  Widget _buildRightLine(int index, int count, Color color) {
    return Expanded(
      child: Container(
        height: 1.5,
        decoration: BoxDecoration(
            color: index == count - 1 ? Colors.transparent : color,
            borderRadius: BorderRadius.circular(1.5)),
      ),
    );
  }

  static const double _kProgressOuterSize = 14.0;
  static const double _kProgressInnerSize = 6.0;
  static const Color _kProgressActiveBgColor =
      Color.fromRGBO(34, 131, 226, 0.15);
  static const Color _kProgressInactiveColor = Color.fromRGBO(0, 0, 0, 0.15);

  Widget _buildProgressIndicator(int index, int currentIndex) {
    final bool isActive = currentIndex == index;
    final bool isPast = index < currentIndex;

    if (isActive) {
      return Container(
        width: _kProgressOuterSize,
        height: _kProgressOuterSize,
        decoration: BoxDecoration(
            color: _kProgressActiveBgColor,
            borderRadius: BorderRadius.circular(_kProgressOuterSize)),
        child: Center(
          child: Container(
              width: _kProgressInnerSize,
              height: _kProgressInnerSize,
              decoration: BoxDecoration(
                  color: activeColor,
                  borderRadius: BorderRadius.circular(_kProgressInnerSize))),
        ),
      );
    }

    return Container(
      height: _kProgressInnerSize,
      width: _kProgressInnerSize,
      decoration: BoxDecoration(
          color: isPast ? activeColor : _kProgressInactiveColor,
          borderRadius: BorderRadius.circular(_kProgressInnerSize / 2)),
    );
  }

  Widget _buildProgressText(
      String name, int index, int currentIndex, Color textColor) {
    return SizedBox(
      height: 17,
      child: Text(
        name,
        textHeightBehavior: const TextHeightBehavior(
            leadingDistribution: TextLeadingDistribution.even),
        style: TextStyle(
            fontWeight:
                index == currentIndex ? FontWeight.w600 : FontWeight.w400,
            fontFamilyFallback: fontFamilyFallback(),
            fontSize: index == currentIndex ? 12 : 11,
            color: textColor),
      ),
    );
  }

  Widget _buildDefaultImageWidget() {
    return Container(
      width: 48,
      height: 48,
      color: const Color(0xFFEAEAEA),
      child: Center(
        child: Image.asset(
          'assets/images/haier_logo.webp',
          width: 20,
          height: 10,
          package: Constant.packageName,
        ),
      ),
    );
  }
}
