import 'package:device_utils/compare/compare.dart';
import 'package:device_utils/typeId_parse/template_map.dart';
import 'package:upservice/model/uhome_response_model.dart';

class ServiceOrderResponseModel extends UhomeResponseModel {
  ServiceOrderResponseModel.fromJson(super.data) : super.fromJson() {
    orderModel = ServiceOrderModel.fromJson(super.retData);
  }

  ServiceOrderModel orderModel =
      ServiceOrderModel.fromJson(<dynamic, dynamic>{});

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['data'] = orderModel.toJson();
    data['retCode'] = retCode;
    data['retInfo'] = retInfo;
    return data;
  }
}

class ServiceOrderModel {
  int totalNums = 0;
  String jumpUrl = '';
  List<ServiceOrderItemModel> items = <ServiceOrderItemModel>[];
  List<ServiceOrderStatusModel> statusList = <ServiceOrderStatusModel>[];

  ServiceOrderModel.fromJson(Map<dynamic, dynamic> json) {
    totalNums = json.intValueForKey('totalNums', 0);
    jumpUrl = json.stringValueForKey('jumpUrl', '');
    for (final dynamic item in json.listValueForKey('items', <dynamic>[])) {
      if (item is Map<String, dynamic>) {
        items.add(ServiceOrderItemModel.fromJson(item));
      }
    }

    for (final dynamic item
        in json.listValueForKey('statusList', <dynamic>[])) {
      if (item is Map) {
        statusList.add(ServiceOrderStatusModel.fromJson(item));
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalNums'] = totalNums;
    data['jumpUrl'] = jumpUrl;
    data['items'] =
        items.map((ServiceOrderItemModel model) => model.toJson()).toList();
    data['statusList'] = statusList
        .map((ServiceOrderStatusModel model) => model.toJson())
        .toList();
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceOrderModel &&
          runtimeType == other.runtimeType &&
          totalNums == other.totalNums &&
          jumpUrl == other.jumpUrl &&
          isListEqual(items, other.items) &&
          isListEqual(statusList, other.statusList);

  @override
  int get hashCode =>
      totalNums.hashCode ^
      jumpUrl.hashCode ^
      listHashCode(items) ^
      listHashCode(statusList);
}

class ServiceOrderItemModel {
  String title = '';
  String imgUrl = '';
  String serviceTime = '';
  String detailUrl = '';
  String statusCode = '';
  int statusIndex = 0;

  ServiceOrderItemModel.fromJson(Map<String, dynamic> json) {
    title = json.stringValueForKey('title', '');
    imgUrl = json.stringValueForKey('imgUrl', '');
    serviceTime = json.stringValueForKey('serviceTime', '');
    detailUrl = json.stringValueForKey('detailUrl', '');
    statusCode = json.stringValueForKey('statusCode', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['imgUrl'] = imgUrl;
    data['serviceTime'] = serviceTime;
    data['detailUrl'] = detailUrl;
    data['statusCode'] = statusCode;
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceOrderItemModel &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          imgUrl == other.imgUrl &&
          serviceTime == other.serviceTime &&
          detailUrl == other.detailUrl &&
          statusCode == other.statusCode &&
          statusIndex == other.statusIndex;

  @override
  int get hashCode =>
      title.hashCode ^
      imgUrl.hashCode ^
      serviceTime.hashCode ^
      detailUrl.hashCode ^
      statusCode.hashCode ^
      statusIndex.hashCode;
}

/// 订单状态
class ServiceOrderStatusModel {
  String name = '';
  String code = '';

  ServiceOrderStatusModel.fromJson(Map<dynamic, dynamic> json) {
    name = json.stringValueForKey('name', '');
    code = json.stringValueForKey('code', '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['code'] = code;
    return data;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceOrderStatusModel &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          code == other.code;

  @override
  int get hashCode => name.hashCode ^ code.hashCode;
}
