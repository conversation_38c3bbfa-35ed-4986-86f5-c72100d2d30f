import 'package:app_service/service_order/model/service_order_response.dart';
import 'package:device_utils/compare/compare.dart';

enum OrderCardType { unLogin, non, single, multiple, showMore }

class ServiceOrderViewModel {
  OrderCardType orderCardType;
  List<ServiceOrderItemModel> orderVMList;
  List<ServiceOrderStatusModel> statusList;
  String progressListUrl;
  int totalNums;

  ServiceOrderViewModel(this.orderCardType, this.orderVMList, this.statusList,
      this.progressListUrl, this.totalNums);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceOrderViewModel &&
          runtimeType == other.runtimeType &&
          orderCardType == other.orderCardType &&
          progressListUrl == other.progressListUrl &&
          totalNums == other.totalNums &&
          isListEqual(orderVMList, other.orderVMList) &&
          isListEqual(statusList, other.statusList);

  @override
  int get hashCode =>
      progressListUrl.hashCode ^
      orderCardType.hashCode ^
      totalNums.hashCode ^
      listHashCode(orderVMList) ^
      listHashCode(statusList);
}
