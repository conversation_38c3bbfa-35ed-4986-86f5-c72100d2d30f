import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import '../uhome_dio/uhome_request_options.dart';

class IOTRequestOptions extends UhomeRequestOptions {
  static Future<String> baseUrl() async {
    final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();

    if (appInfo.env == '验收') {
      return 'https://ys-uws.haier.net';
    } else {
      return 'https://uws.haier.net';
    }
  }
}
