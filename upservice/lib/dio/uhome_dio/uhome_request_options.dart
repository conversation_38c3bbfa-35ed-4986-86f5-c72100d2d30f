import 'dart:convert';
import 'dart:core';
import 'dart:typed_data';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:crypto/crypto.dart';
import 'package:device_utils/log/log.dart';
import 'package:dio/dio.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

class UhomeRequestOptions {
  static Future<String> baseUrl() async {
    final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
    if (appInfo.env == '验收') {
      return 'https://zj-yanshou.haier.net';
    } else {
      return 'https://zj.haier.net';
    }
  }

  Future<Map<String, dynamic>> header(RequestOptions options,
      {Map<String, dynamic>? extendHeaderInfo}) async {
    try {
      String userId = '';
      String accessToken = '';
      String accountToken = '';

      final LoginStatus? loginStatus = User.getLoginStatusSync();

      if (loginStatus != null && loginStatus.isLogin) {
        final OauthData oauthData =
            User.getOauthDataSync() ?? await User.getOauthData();
        final UserInfo userInfo = await User.getUserInfo();
        userId = userInfo.userId;
        accessToken = oauthData.uhome_access_token ?? '';
        accountToken = oauthData.user_center_access_token ?? '';
      }

      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();

      final int timestamp = DateTime.now().millisecondsSinceEpoch;
      final String method = options.method;
      final String path = options.path;
      final dynamic params =
          method == 'GET' ? options.queryParameters : options.data;
      final String paramsString = _formatPramsString(method, path, params);
      final String signString =
          _sign(path, paramsString, appInfo.appId, appInfo.appKey, timestamp);

      final Map<String, dynamic> headers = <String, dynamic>{
        'timestamp': timestamp,
        'appId': appInfo.appId,
        'clientId': appInfo.clientId,
        'userId': userId,
        'accessToken': accessToken,
        'accountToken': accountToken,
        'sign': signString
      };
      DevLogger.info(tag: 'Service', msg: '--headers--$headers---path--$path');
      if (extendHeaderInfo != null) {
        headers.addAll(extendHeaderInfo);
      }
      return headers;
    } catch (e) {
      DevLogger.error(tag: 'Service', msg: 'error $e');
      return <String, dynamic>{};
    }
  }

  String _sign(
    String path,
    String paramsString,
    String appId,
    String appKey,
    int timestamp,
  ) {
    final List<int> bytes = utf8
        .encode(path + paramsString + appId + appKey + timestamp.toString());
    final List<int> list = sha256.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }

    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    const String HEX_STRING = '0123456789abcdef';
    for (int j = 0, i = 0; j < list.length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = HEX_STRING[index].codeUnitAt(0);
      uList[k] = HEX_STRING[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }

  String _formatPramsString(String method, String path, dynamic params) {
    if (params is! Map) {
      return '';
    }

    if (params.isEmpty) {
      return '';
    }

    if (method == 'POST') {
      return _replaceSpecialChars(jsonEncode(params));
    }

    String paramsString = '';
    if (!path.contains('?')) {
      paramsString = '?';
    }
    final Map<dynamic, dynamic> paramsMap =
        params is Map<dynamic, dynamic> ? params : <dynamic, dynamic>{};

    final List<String> paramPairs = <String>[];
    for (final dynamic key in paramsMap.keys) {
      paramPairs.add('$key=${paramsMap[key]}');
    }

    paramsString = paramsString + paramPairs.join('&');
    return _replaceSpecialChars(paramsString);
  }

  static String _replaceSpecialChars(String str) {
    return str
        .replaceAll(' ', '')
        .replaceAll('\t', '')
        .replaceAll('\r', '')
        .replaceAll('\n', '');
  }
}
