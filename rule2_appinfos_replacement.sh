#!/bin/bash

# Rule 2: Appinfos替换脚本

# 定义要处理的目录（根据修改规则文档中的13个目录）
DIRS=(
    "./smart_home"
    "./app_service" 
    "./app_mine"
    "./library_widgets"
    "./flutter_common_ui"
    "./whole_house_air"
    "./personal_information"
    "./setting"
    "./about_us"
    "./upservice"
    "./wash_device_manager"
    "./whole_house_music"
    "./flutter_main"
)

echo "开始执行Rule 2: Appinfos替换..."

# 第一步：替换pubspec.yaml中的Appinfos依赖
echo "步骤1: 替换pubspec.yaml中的Appinfos依赖"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pubspec_file="$dir/pubspec.yaml"
        if [ -f "$pubspec_file" ]; then
            # 检查是否包含Appinfos
            if grep -q "Appinfos:" "$pubspec_file"; then
                echo "  处理文件: $pubspec_file"
                
                # 使用sed替换Appinfos依赖
                sed -i '' '/Appinfos:/,/version:/ {
                    s/Appinfos:/app_info:/
                    s/name: Appinfos/name: app_info/
                }' "$pubspec_file"
            fi
        fi
    else
        echo "目录不存在: $dir"
    fi
done

echo ""
echo "步骤2: 替换dart文件中的import语句"

# 第二步：替换dart文件中的import语句
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "处理目录: $dir"
        
        # 查找包含Appinfos import的dart文件并替换
        find "$dir" -name "*.dart" -type f -exec grep -l "package:Appinfos/" {} \; | while read file; do
            echo "  替换import文件: $file"
            
            # 替换import语句
            sed -i '' "s|import 'package:Appinfos/|import 'package:app_info/|g" "$file"
        done
    fi
done

echo ""
echo "Rule 2执行完成!"
echo ""
echo "检查替换结果:"

# 检查替换结果
echo "检查pubspec.yaml文件中是否还有Appinfos引用:"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pubspec_file="$dir/pubspec.yaml"
        if [ -f "$pubspec_file" ]; then
            if grep -q "Appinfos" "$pubspec_file"; then
                echo "  警告: $pubspec_file 中仍有Appinfos引用"
            fi
        fi
    fi
done

echo ""
echo "检查dart文件中是否还有package:Appinfos引用:"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        remaining=$(find "$dir" -name "*.dart" -type f -exec grep -l "package:Appinfos/" {} \; 2>/dev/null)
        if [ ! -z "$remaining" ]; then
            echo "目录 $dir 中以下文件仍有package:Appinfos引用:"
            echo "$remaining"
        fi
    fi
done
