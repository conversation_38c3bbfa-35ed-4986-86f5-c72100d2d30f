#!/bin/bash

# Rule 3: flutter_picker替换脚本

# 定义要处理的目录（根据修改规则文档中的13个目录）
DIRS=(
    "./smart_home"
    "./app_service" 
    "./app_mine"
    "./library_widgets"
    "./flutter_common_ui"
    "./whole_house_air"
    "./personal_information"
    "./setting"
    "./about_us"
    "./upservice"
    "./wash_device_manager"
    "./whole_house_music"
    "./flutter_main"
)

echo "开始执行Rule 3: flutter_picker替换..."

# 替换pubspec.yaml中的flutter_picker依赖
echo "步骤1: 替换pubspec.yaml中的flutter_picker依赖"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pubspec_file="$dir/pubspec.yaml"
        if [ -f "$pubspec_file" ]; then
            # 检查是否包含flutter_picker
            if grep -q "flutter_picker:" "$pubspec_file"; then
                echo "  处理文件: $pubspec_file"
                
                # 使用sed替换flutter_picker依赖
                sed -i '' 's/flutter_picker: 2\.0\.3/flutter_picker_plus: 1.0.0/g' "$pubspec_file"
                
                # 也处理可能的其他版本号
                sed -i '' 's/flutter_picker: [0-9]\+\.[0-9]\+\.[0-9]\+/flutter_picker_plus: 1.0.0/g' "$pubspec_file"
            fi
        fi
    else
        echo "目录不存在: $dir"
    fi
done

echo ""
echo "Rule 3执行完成!"
echo ""
echo "检查替换结果:"

# 检查替换结果
echo "检查pubspec.yaml文件中是否还有flutter_picker引用:"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pubspec_file="$dir/pubspec.yaml"
        if [ -f "$pubspec_file" ]; then
            if grep -q "flutter_picker:" "$pubspec_file"; then
                echo "  警告: $pubspec_file 中仍有flutter_picker引用"
                grep "flutter_picker:" "$pubspec_file"
            fi
        fi
    fi
done

echo ""
echo "检查是否成功添加了flutter_picker_plus:"
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        pubspec_file="$dir/pubspec.yaml"
        if [ -f "$pubspec_file" ]; then
            if grep -q "flutter_picker_plus:" "$pubspec_file"; then
                echo "  成功: $pubspec_file 中已添加flutter_picker_plus"
                grep "flutter_picker_plus:" "$pubspec_file"
            fi
        fi
    fi
done
