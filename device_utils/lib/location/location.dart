import 'dart:convert';

import 'package:location/location.dart';
import 'package:location/locationmodel.dart';
import 'package:storage/storage.dart';

import '../device_util_constant.dart';
import '../log/log.dart';
import '../typeId_parse/template_map.dart';

const String locationUserKey = 'uplus_user_loc'; // 用户选择城市
const String locationCurrentKey = 'uplus_current_city';
const String locationEnvTagKey = 'LOCATION_ENV_TAG_KEY';

class CityModel {
  CityModel(this.areaId, this.areaName);

  factory CityModel.fromJson(Map<String, dynamic> json) {
    return CityModel(json.stringValueForKey('areaId', ''),
        json.stringValueForKey('areaName', ''));
  }

  final String areaId;
  final String areaName;

  Map<String, String> toJson() {
    return <String, String>{'areaId': areaId, 'areaName': areaName};
  }

  @override
  String toString() {
    return 'CityModel{areaId: $areaId, areaName: $areaName}';
  }
}

CityModel defaultCity = CityModel('370212', '青岛市');

/// 获取定位 COPY apphome
// 临时缓存 > 高德 > 缓存 > 青岛市
Future<CityModel> getCityModel() async {
  final bool isGuestMode =
      await Storage.getBooleanValue(GuestConst.kCurrentMode);
  if (isGuestMode) {
    // 默认青岛市
    return defaultCity;
  }

  try {
    // 临时缓存
    final CityModel? locationTemp = await _getTempCity();
    if (locationTemp is CityModel) {
      DevLogger.info(tag: locationEnvTagKey, msg: 'location $locationTemp');

      return locationTemp;
    }
    // 高德
    final CityModel? locationGeography = await getGeography();
    if (locationGeography is CityModel) {
      DevLogger.info(tag: locationEnvTagKey, msg: 'location $locationTemp');

      return locationGeography;
    }
    // 缓存
    final CityModel? locationCache = await getCacheCity();
    if (locationCache is CityModel) {
      DevLogger.info(tag: locationEnvTagKey, msg: 'location $locationTemp');

      return locationCache;
    }
  } catch (err) {
    DevLogger.info(tag: locationEnvTagKey, msg: '_getCity exception: $err');
  }

  // 默认青岛市
  final CityModel bjLocation = CityModel.fromJson(
      <String, dynamic>{'areaId': '370212', 'areaName': '青岛市'});
  return bjLocation;
}

// 从缓存里拿地理位置
Future<CityModel?> getCacheCity() async {
  // 缓存
  try {
    final String cacheCity = await Storage.getStringValue(locationUserKey);
    if (cacheCity.isNotEmpty) {
      final dynamic decodeValue = jsonDecode(cacheCity);
      String areaId = '';
      String areaName = '';
      if (decodeValue is Map<dynamic, dynamic>) {
        areaId = decodeValue.stringValueForKey('areaId', '');
        areaName = decodeValue.stringValueForKey('areaName', '');
      }
      return CityModel.fromJson(
          <String, String>{'areaId': areaId, 'areaName': areaName});
    }
  } catch (e) {
    DevLogger.debug(tag: locationEnvTagKey, msg: 'getCacheCity exception: $e');
  }
  return null;
}

// 拿临时缓存的城市
Future<CityModel?> _getTempCity() async {
  try {
    // 临时缓存
    final String tempCity =
        await Storage.getTemporaryStorage(locationCurrentKey);
    dynamic decodeValue = jsonDecode(tempCity);
    String tempCityObj = '';
    if (decodeValue is Map<dynamic, dynamic>) {
      tempCityObj = decodeValue.stringValueForKey('value', '');
    }

    if (tempCityObj.isNotEmpty) {
      decodeValue = jsonDecode(tempCityObj);
      String areaId = '';
      String areaName = '';
      if (decodeValue is Map<dynamic, dynamic>) {
        areaId = decodeValue.stringValueForKey('areaId', '');
        areaName = decodeValue.stringValueForKey('areaName', '');
      }
      return CityModel.fromJson(
          <String, String>{'areaId': areaId, 'areaName': areaName});
    }
  } catch (e) {
    DevLogger.debug(tag: locationEnvTagKey, msg: 'getTempCity exception: $e');
  }
  return null;
}

// 高德地图拿地理位置
Future<CityModel?> getGeography() async {
  try {
    // 高德
    final LocationModel locationCity =
        await Location.getLocation(isNeedRequestPermission: false);
    if (locationCity.adcode != '' &&
        ((locationCity.district.isNotEmpty) ||
            (locationCity.city.isNotEmpty))) {
      return CityModel.fromJson(<String, dynamic>{
        'areaId': locationCity.adcode,
        'areaName': locationCity.district.isNotEmpty
            ? locationCity.district
            : locationCity.city
      });
    }
  } catch (e) {
    DevLogger.debug(tag: locationEnvTagKey, msg: 'getGeography exception: $e');
  }
  return null;
}
