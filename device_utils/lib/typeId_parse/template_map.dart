/*
 * 描述：xxx
 * 作者：tangshaojun
 * 创建时间：2024/1/12
 */

import 'dart:ui';

extension TemplateMap on Map<dynamic, dynamic> {
  String stringValueForKey(dynamic key, String defaultValue) {
    return _valueForKey<String>(key, defaultValue);
  }

  String? nullableStringValueForKey(dynamic key) {
    return _nullableValueForKey<String>(key);
  }

  int intValueForKey(dynamic key, int defaultValue) {
    return _valueForKey<int>(key, defaultValue);
  }

  int? nullableIntValueForKey(dynamic key) {
    return _nullableValueForKey<int>(key);
  }

  double doubleValueForKey(dynamic key, double defaultValue) {
    return _valueForKey<double>(key, defaultValue);
  }

  double? nullableDoubleValueForKey(dynamic key) {
    return _nullableValueForKey<double>(key);
  }

  bool boolValueForKey(dynamic key, bool defaultValue) {
    return _valueForKey<bool>(key, defaultValue);
  }

  bool? nullableBoolValueFor<PERSON>ey(dynamic key) {
    return _nullableValueForKey<bool>(key);
  }

  Map<dynamic, dynamic> mapValueForKey(
      dynamic key, Map<dynamic, dynamic> defaultValue) {
    return _valueForKey<Map<dynamic, dynamic>>(key, defaultValue);
  }

  Map<dynamic, dynamic>? nullableMapValueForKey(dynamic key) {
    return _nullableValueForKey<Map<dynamic, dynamic>>(key);
  }

  List<dynamic> listValueForKey(dynamic key, List<dynamic> defaultValue) {
    return _valueForKey<List<dynamic>>(key, defaultValue);
  }

  List<dynamic>? nullableListValueForKey(dynamic key) {
    return _nullableValueForKey<List<dynamic>>(key);
  }

  Color colorValueForKey(dynamic key, Color defaultValue) {
    return _valueForKey<Color>(key, defaultValue);
  }

  Color? nullableColorValueForKey(dynamic key) {
    return _nullableValueForKey<Color>(key);
  }

  /// 必须指定 T 类型，否则会有奇效
  T _valueForKey<T>(dynamic key, T defaultValue) {
    if (this[key] is T) {
      return this[key] as T;
    }
    return defaultValue;
  }

  T? _nullableValueForKey<T>(dynamic key) {
    if (this[key] is T) {
      return this[key] as T;
    }
    return null;
  }
}
