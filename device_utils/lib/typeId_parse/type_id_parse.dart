/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2023/11/13
 */


class TypeIdParse {
  // 判断是否为16进值正整数
  static bool _isLetterDigit(String typeId) {
    if (typeId == '') {
      return false;
    }
    final RegExp regex = RegExp(r'^[a-f0-9A-F]+$');
    return regex.hasMatch(typeId);
  }

// 16进制radix
  static const int hexRadix = 16;

  static int _parseTypeIdByte(
      String target, int begin, int offset, int left, int right) {
    final int first =
        int.parse(target.substring(begin, begin + offset), radix: hexRadix) <<
            left;
    begin += offset;
    final int second =
        int.parse(target.substring(begin, begin + offset), radix: hexRadix) >>
            right;

    return (first + second) & 0xff;
  }

  /// 获取typeId大类 16进制
  static String firstTypeCode(String typeId) {
    int typeNum = -1;
    if (!_isLetterDigit(typeId)) {
      return typeNum.toString();
    }

    if (typeId.length >= 30) {
      if (typeId.startsWith('0')) {
        typeNum = _parseTypeIdByte(typeId, 14, 2, 2, 6);
      } else {
        typeNum = _parseTypeIdByte(typeId, 16, 1, 4, 0);
      }
    }

    return typeNum.toRadixString(hexRadix);
  }

  /// 获取typeId中类 16进制
  static String secondTypeCode(String typeId) {
    int secondCode = -1;
    if (!_isLetterDigit(typeId)) {
      return secondCode.toString();
    }

    if (typeId.length >= 30) {
      if (typeId.startsWith('0')) {
        secondCode = _parseTypeIdByte(typeId, 16, 2, 2, 6);
      } else {
        secondCode = _parseTypeIdByte(typeId, 18, 1, 4, 0);
      }
    }
    return secondCode.toRadixString(hexRadix);
  }

  bool isEngineDevice(String typeId) {
    final String type = firstTypeCode(typeId);
//洗衣机
    if (type == '5' || type == '4' || type == '31') {
      return false;
    }

    return true;
  }
}

