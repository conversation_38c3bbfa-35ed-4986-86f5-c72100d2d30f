/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2023/11/13
 */

import 'dart:async';

/// 使用示例
/// 初始化
/// final TaskQueueUtil _taskQueue = TaskQueueUtil.get('WashDeviceManagerTask');
///
/// 队列所有任务执行完成回调
/// _taskQueue.allTaskFinishCallback = () {
///       // all task finish
/// };
///
/// 添加任务至任务队列
/// _taskQueue.addTask(() {
///       return _func(param);
/// });
///
class TaskQueueUtil {
  TaskQueueUtil._();

  static final Map<String, TaskQueueUtil> _instance = <String, TaskQueueUtil>{};

  static TaskQueueUtil get(String key) {
    if (_instance[key] is! TaskQueueUtil) {
      _instance[key] = TaskQueueUtil._();
    }
    return _instance[key]!;
  }

  List<TaskInfo> _taskInfoList = <TaskInfo>[];
  bool _isTaskRunning = false;
  int _mId = 0;
  bool _isCancelQueue = false;

  Function? allTaskFinishCallback;

  Future<TaskInfo> addTask(Function doSomething) {
    //
    _isCancelQueue = false;
    _mId++;
    // 创建Future
    final Completer<TaskInfo> taskCompleter = Completer<TaskInfo>();
    // 创建当前任务Stream
    final StreamController<TaskInfo> streamController =
        StreamController<TaskInfo>();

    final TaskInfo taskInfo = TaskInfo(_mId, doSomething, streamController);

    // 添加到任务队列
    _taskInfoList.add(taskInfo);
    // 当前任务Stream添加监听
    streamController.stream.listen((TaskInfo completeTaskInfo) {
      if (completeTaskInfo.id == taskInfo.id) {
        taskCompleter.complete(completeTaskInfo);
        streamController.close();
      }
    });

    // 触发任务
    _doTask();

    return taskCompleter.future;
  }

  Future<void> _doTask() async {
    if (_isTaskRunning) {
      return;
    }
    if (_taskInfoList.isEmpty) {
      if (allTaskFinishCallback is Function) {
        allTaskFinishCallback!();
      }
      return;
    }
    // 取任务
    final TaskInfo taskInfo = _taskInfoList[0];
    _isTaskRunning = true;
    // 执行任务
    await taskInfo.doSomething.call();
    taskInfo.controller.sink.add(taskInfo);
    if (_isCancelQueue) {
      return;
    }
    // 任务完成，移除队列
    _taskInfoList.remove(taskInfo);
    _isTaskRunning = false;
    // 递归执行任务
    _doTask();
  }

  void cancelTask() {
    _taskInfoList = <TaskInfo>[];
    _mId = 0;
    _isTaskRunning = false;
    _isCancelQueue = true;
  }
}

class TaskInfo {
  TaskInfo(this.id, this.doSomething, this.controller);

  int id;
  Function doSomething;
  StreamController<TaskInfo> controller;
}
