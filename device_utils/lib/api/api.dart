/*
 * 描述：api请求
 * 作者：songFJ
 * 创建时间：2023/11/9
 */

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/modle/oauth_data_model.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import '../device_util_constant.dart';
import '../log/log.dart';

class Api {
  static String appId = '';
  static String appKey = '';
  static String clientId = '';
  static String appVersion = '';
  static bool login = false;

  /// 初始化一些基本请求信息
  static Future<void> init() async {
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      login = loginStatus.isLogin;
    } catch (e) {
      login = false;
    }
    if (appId == '') {
      await Api.getAppInfo();
    }
  }

  static Future<Map<String, dynamic>> headers(
      [Map<String, dynamic>? otherHeaderInfo, String? timestamp]) async {
    String accessToken;
    String accountToken;
    String userId;
    try {
      if (login) {
        final OauthData oauthData = await User.getOauthData();
        final UserInfo userInfo = await User.getUserInfo();
        accessToken = oauthData.uhome_access_token;
        accountToken = oauthData.user_center_access_token;
        userId = userInfo.userId;
      } else {
        accessToken = '';
        accountToken = '';
        userId = '';
      }

      final Map<String, dynamic> headers = <String, dynamic>{
        'timestamp': timestamp.toString(),
        'appId': appId,
        'clientId': clientId,
        'userId': userId,
        'accessToken': accessToken,
        'accountToken': accountToken,
        'appVersion': appVersion,
      };

      if (otherHeaderInfo != null) {
        headers.addAll(otherHeaderInfo);
      }
      return headers;
    } catch (e) {
      DevLogger.debug(
          tag: DeviceUtilConstant.packageName,
          msg: 'getUserInfo exception: $e');
      return <String, dynamic>{};
    }
  }

  static Future<void> getAppInfo() async {
    try {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      appId = appInfo.appId;
      appKey = appInfo.appKey;
      clientId = appInfo.clientId;
      appVersion = appInfo.appVersion;
      if (appVersion.isEmpty) {
        appVersion = '7.0.0';
      }
    } catch (e) {
      DevLogger.debug(
          tag: DeviceUtilConstant.packageName, msg: 'getAppInfo exception: $e');
    }
  }
}
