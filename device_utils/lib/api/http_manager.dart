/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2023/11/9
 */

import 'dart:convert';

import 'package:dio/dio.dart';

import '../device_util_constant.dart';
import '../log/log.dart';
// import 'wash_constant.dart';

class HeaderInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.connectTimeout = const Duration(seconds: 30);
    handler.next(options);
  }
}

class ResponseInterceptors extends InterceptorsWrapper {
  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }
}

class LogsInterceptors extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    return handler.next(options);
  }

  @override
  void onResponse(
      Response<dynamic> response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    DevLogger.debug(tag: DeviceUtilConstant.packageName, msg: '请求异常: $err');
    return handler.next(err);
  }
}

class HttpManager {
  HttpManager() {
    _dio.interceptors
      ..add(HeaderInterceptors())
      ..add(LogsInterceptors())
      ..add(ResponseInterceptors());
  }

  final Dio _dio = Dio();

  Future<Map<dynamic, dynamic>> get(
    String url, {
    Map<String, dynamic>? header,
    Map<String, dynamic>? params,
  }) async {
    final Map<String, dynamic> headers = <String, dynamic>{};
    if (header != null) {
      headers.addAll(header);
    }
    final Options options = Options();
    options.headers = headers;

    Response<dynamic> response;
    DevLogger.info(
        tag: DeviceUtilConstant.packageName,
        msg: 'url: $url, headers: $header, params: $params');
    try {
      response = await _dio.get(url, queryParameters: params, options: options);
    } catch (e) {
      DevLogger.debug(tag: DeviceUtilConstant.packageName, msg: '网络请求异常: $e');
      rethrow;
    }
    dynamic data = response.data ?? <String, dynamic>{};
    if (data is String) {
      data = jsonDecode(data);
    }
    DevLogger.debug(
        tag: DeviceUtilConstant.packageName,
        msg: 'http response , url: $url, response: $response, data: $data');
    if (data is! Map<dynamic, dynamic>) {
      return <dynamic, dynamic>{};
    }
    return data;
  }

  Future<Map<dynamic, dynamic>> post(
    String url, {
    required Map<String, dynamic> params,
    Map<String, dynamic>? header,
    bool query = false,
  }) async {
    final Options options = Options();
    options.headers = header;
    Response<dynamic> response;
    DevLogger.info(
        tag: DeviceUtilConstant.packageName,
        msg: 'url: $url, headers: $header, params: $params');
    try {
      response = await _dio.post(url,
          data: params,
          queryParameters: query ? params : null,
          options: options);
    } catch (e) {
      DevLogger.debug(tag: DeviceUtilConstant.packageName, msg: '网络请求异常: $e');
      rethrow;
    }
    DevLogger.info(
        tag: DeviceUtilConstant.packageName,
        msg: 'url: $url, ret: $response');

    dynamic data = response.data ?? <String, dynamic>{};
    if (data is String) {
      data = jsonDecode(data);
    }
    if (data is! Map<dynamic, dynamic>) {
      return <dynamic, dynamic>{};
    }
    return data;
  }
}



final HttpManager httpManager = HttpManager();
