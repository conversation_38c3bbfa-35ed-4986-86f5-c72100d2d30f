/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2023/11/9
 */

// Http请求类
import 'dart:convert';
import 'dart:core';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';

enum Method { get, post }

class HttpTool {
  // hash string
  static const String hexString = '0123456789abcdef';

  // 获取时间戳
  static int getTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  // 设置请求头
  static Map<String, dynamic> commonHeaders() {
    return {
      'Content-Type': 'application/json;charset=UTF-8',
      'timestamp': getTimestamp().toString(),
      'sequenceId': getTimestamp().toString(),
    };
  }

  static String sign(
    Method method,
    HttpSignModel signModel,
  ) {
    String paramString = '';
    if (signModel.paramsMap.isEmpty) {
      return _getSign(signModel);
    }
    if (method == Method.post) {
      paramString = json.encode(signModel.paramsMap);
    } else {
      if (!signModel.urlPath.contains('?')) {
        paramString = '?';
      }
      for (final String key in signModel.paramsMap.keys) {
        paramString = '$paramString${'$key=${signModel.paramsMap[key]}&'}';
      }
      if (paramString.contains('&')) {
          paramString = paramString.substring(0, paramString.length - 1);
        }
    }
    signModel.paramsString = paramString;
    return _getSign(signModel);
  }

  static String _getSign(HttpSignModel signModel) {
    final List<int> bytes = utf8.encode(signModel.urlPath +
        signModel.paramsString +
        signModel.appId +
        signModel.appKey +
        signModel.timestamp.toString());
    final List<int> list = sha256.convert(bytes).bytes;
    if (list.isEmpty) {
      return '';
    }
    final int length = list.length;
    final Uint8List uList = Uint8List(length << 1);
    int i = 0;
    for (int j = 0; j < length; j++) {
      final int k = i + 1;
      final int index = (list[j] >> 4) & 0xF;
      uList[i] = hexString[index].codeUnitAt(0);
      uList[k] = hexString[list[j] & 0xF].codeUnitAt(0);
      i = k + 1;
    }
    return String.fromCharCodes(uList);
  }
}

class HttpSignModel {
  HttpSignModel(
      {this.urlPath = '',
      this.paramsMap = const <String, dynamic>{},
      this.appId = '',
      this.appKey = '',
      this.timestamp = 0});

  String urlPath = '';
  Map<String, dynamic> paramsMap = <String, dynamic>{};
  String paramsString = '';
  String appId = '';
  String appKey = '';
  int timestamp = 0;
}