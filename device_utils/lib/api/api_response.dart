/*
 * 描述：xxx
 * 作者：songFJ
 * 创建时间：2023/11/13
 */
import '../typeId_parse/template_map.dart';

class ApiResponse {
  ApiResponse.fromJson(Map<dynamic, dynamic> json) {
    retCode = json.stringValueForKey('retCode', '');

    retInfo = json.stringValueForKey('retInfo', '');
  }

  String retCode = '';
  String retInfo = '';

  @override
  String toString() {
    return 'ApiResponse{retCode: $retCode, retInfo: $retInfo}';
  }
}
