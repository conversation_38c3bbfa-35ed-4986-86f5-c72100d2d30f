/*
 * 描述：list & map equal判断&hashCode获取
 * 作者：songFJ
 * 创建时间：2024/3/4
 */
import 'package:flutter/foundation.dart';

bool isListEqual(List<dynamic> list1, List<dynamic> list2) {
  return listEquals(list1, list2);
}

int listHashCode(List<dynamic> list) {
  int hashCode = 0;
  for (final dynamic element in list) {
    hashCode = hashCode ^ element.hashCode;
  }
  return hashCode;
}

bool isMapEqual(Map<dynamic, dynamic> map1, Map<dynamic, dynamic> map2) {
  return mapEquals(map1, map2);
}

int mapHashCode(Map<dynamic, dynamic> map) {
  if (map.isEmpty) {
    return 0;
  }
  int hashCode = 0;
  map.forEach((dynamic key, dynamic value) {
    hashCode = hashCode ^ value.hashCode;
  });
  return hashCode;
}

bool mapListEquals(
    Map<String, List<dynamic>>? a, Map<String, List<dynamic>>? b) {
  if (a == null) {
    return b == null;
  }
  if (b == null || a.length != b.length) {
    return false;
  }
  if (identical(a, b)) {
    return true;
  }
  for (final String key in a.keys) {
    if (!b.containsKey(key)) {
      return false;
    } else if (a[key] is List && b[key] is List) {
      return listEquals(a[key], b[key]);
    } else if (b[key] != a[key]) {
      return false;
    }
  }
  return true;
}

int mapListHashCode(Map<String, List<dynamic>>? map) {
  if (map == null || map.isEmpty) {
    return 0;
  }
  int hashCode = 0;
  map.forEach((String key, List<dynamic> value) {
    hashCode = hashCode ^ listHashCode(value);
  });
  return hashCode;
}
