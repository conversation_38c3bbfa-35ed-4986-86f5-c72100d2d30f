/*
 * 描述：日志
 * 作者：songFJ
 * 创建时间：2023/11/9
 */

import 'package:log/log.dart';
import 'package:log/log_modle.dart';

class DevLogger {
  // verbose，debug，info，warning，error
  static const String LOG_V = 'verbose';
  static const String LOG_I = 'info';
  static const String LOG_D = 'debug';
  static const String LOG_W = 'warning';
  static const String LOG_E = 'error';

  static void verbose({required String tag, required Object msg}) {
    _sendToMpaas(LOG_V, tag, msg.toString());
  }

  static void info({required String tag, required Object msg}) {
    _sendToMpaas(LOG_I, tag, msg.toString());
  }

  static void debug({required String tag, required Object msg}) {
    _sendToMpaas(LOG_D, tag, msg.toString());
  }

  static void warning({required String tag, required Object msg}) {
    _sendToMpaas(LOG_W, tag, msg.toString());
  }

  static void error({required String tag, required Object msg}) {
    _sendToMpaas(LOG_E, tag, msg.toString());
  }

  static Future<void> _sendToMpaas(String level, String tag, String msg) async {
    Log.printLog(LogModle(level, tag, msg)).then((Map<dynamic, dynamic> value) {
      // send to mPaas success
    }).catchError((dynamic onError) {});
  }
}
