#!/bin/bash

# 批量替换connectivity import语句的脚本

# 定义要处理的目录
DIRS=(
    "./smart_home"
    "./app_service" 
    "./app_mine"
    "./library_widgets"
    "./flutter_common_ui"
    "./whole_house_air"
    "./personal_information"
    "./setting"
    "./about_us"
)

echo "开始批量替换connectivity import语句..."

# 遍历每个目录
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "处理目录: $dir"
        
        # 查找包含connectivity import的dart文件并替换
        find "$dir" -name "*.dart" -type f -exec grep -l "import 'package:connectivity/connectivity.dart'" {} \; | while read file; do
            echo "  替换文件: $file"
            
            # 使用sed替换import语句
            sed -i '' "s|import 'package:connectivity/connectivity.dart';|import 'package:network/isonline.dart';\nimport 'package:network/network.dart';|g" "$file"
        done
    else
        echo "目录不存在: $dir"
    fi
done

echo "import语句替换完成!"
echo ""
echo "接下来需要替换API调用..."
echo "查找需要替换API调用的文件:"

# 查找包含Connectivity API调用的文件
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        find "$dir" -name "*.dart" -type f -exec grep -l "Connectivity()" {} \; | while read file; do
            echo "  需要替换API: $file"
        done
    fi
done
