#!/bin/bash

# 修复剩余的connectivity引用的脚本

# 定义要处理的目录
DIRS=(
    "./smart_home"
    "./app_service" 
    "./app_mine"
    "./library_widgets"
    "./flutter_common_ui"
    "./whole_house_air"
    "./personal_information"
    "./setting"
    "./about_us"
)

echo "开始修复剩余的connectivity引用..."

# 遍历每个目录
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "处理目录: $dir"
        
        # 查找包含Connectivity或ConnectivityResult的dart文件并替换
        find "$dir" -name "*.dart" -type f -exec grep -l "Connectivity\|ConnectivityResult" {} \; | while read file; do
            echo "  修复文件: $file"
            
            # 替换StreamSubscription<ConnectivityResult>为StreamSubscription<NetworkStatus>
            sed -i '' 's/StreamSubscription<ConnectivityResult>/StreamSubscription<NetworkStatus>/g' "$file"
            
            # 替换ConnectivityResult为NetworkStatus
            sed -i '' 's/ConnectivityResult/NetworkStatus/g' "$file"
            
            # 替换Connectivity().onConnectivityChanged为Network.addNetStateEventListener
            sed -i '' 's/Connectivity().onConnectivityChanged.listen/Network.addNetStateEventListener/g' "$file"
            
            # 替换其他可能的Connectivity调用
            sed -i '' 's/Connectivity()/Network/g' "$file"
            
            # 替换result变量名为onValue或status
            sed -i '' 's/ConnectivityResult result/NetworkStatus onValue/g' "$file"
            sed -i '' 's/ConnectivityResult connectivityResult/NetworkStatus isOnline/g' "$file"
            
            # 替换.none检查
            sed -i '' 's/\.none/false/g' "$file"
            
            # 替换!= ConnectivityResult.none为.isOnline
            sed -i '' 's/!= false/.isOnline/g' "$file"
            
            # 替换== ConnectivityResult.none为!.isOnline
            sed -i '' 's/== false/!.isOnline/g' "$file"
            
        done
    else
        echo "目录不存在: $dir"
    fi
done

echo "修复完成!"
echo ""
echo "检查是否还有遗漏的引用:"

# 检查是否还有遗漏的引用
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        remaining=$(find "$dir" -name "*.dart" -type f -exec grep -l "Connectivity\|ConnectivityResult" {} \; 2>/dev/null)
        if [ ! -z "$remaining" ]; then
            echo "目录 $dir 中以下文件可能还有遗漏的引用:"
            echo "$remaining"
        fi
    fi
done
