#!/bin/bash

# Rule 4: 页面生命周期修改脚本

echo "开始执行Rule 4: 页面生命周期修改..."

# 定义包含didChangeAppLifecycleState的文件列表
FILES=(
    "./flutter_main/lib/pages/home_tab_page_new.dart"
    "./personal_information/lib/pages/home.dart"
    "./personal_information/lib/pages/nickname.dart"
    "./personal_information/lib/pages/address.dart"
    "./personal_information/lib/pages/name.dart"
    "./personal_information/lib/pages/newAddress.dart"
    "./app_mine/lib/gpt_center/gpt_center_widget.dart"
    "./smart_home/lib/device/device_widget/camera_land_play.dart"
    "./smart_home/lib/device/device_widget/camera.dart"
    "./setting/lib/pages/home.dart"
    "./whole_house_air/lib/wholehouseair.dart"
    "./whole_house_air/lib/presentation/single_house_air.dart"
)

echo "找到 ${#FILES[@]} 个包含didChangeAppLifecycleState的文件"

# 第一步：添加import语句
echo ""
echo "步骤1: 添加flutter_common_ui import语句"
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  处理文件: $file"
        
        # 检查是否已经有flutter_common_ui import
        if ! grep -q "import 'package:flutter_common_ui/flutter_common_ui.dart';" "$file"; then
            # 在第一个import语句后添加新的import
            sed -i '' "1,/^import/ {
                /^import.*flutter\/material\.dart/ a\\
import 'package:flutter_common_ui/flutter_common_ui.dart';
            }" "$file"
        fi
    else
        echo "  文件不存在: $file"
    fi
done

echo ""
echo "步骤2: 修改class定义，添加UplusLifecycleMixin"
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  处理文件: $file"
        
        # 查找class定义行并添加UplusLifecycleMixin
        # 这需要根据具体的class名称来处理，比较复杂
        # 先标记需要手动处理的文件
        echo "    需要手动添加UplusLifecycleMixin到class定义"
    fi
done

echo ""
echo "步骤3: 移除WidgetsBindingObserver"
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  处理文件: $file"
        
        # 移除WidgetsBindingObserver
        sed -i '' 's/, WidgetsBindingObserver//g' "$file"
        sed -i '' 's/WidgetsBindingObserver, //g' "$file"
        sed -i '' 's/with WidgetsBindingObserver//g' "$file"
    fi
done

echo ""
echo "步骤4: 移除addObserver和removeObserver调用"
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  处理文件: $file"
        
        # 移除addObserver调用
        sed -i '' '/WidgetsBinding\.instance\.addObserver(this);/d' "$file"
        
        # 移除removeObserver调用
        sed -i '' '/WidgetsBinding\.instance\.removeObserver(this);/d' "$file"
    fi
done

echo ""
echo "Rule 4 基础修改完成!"
echo ""
echo "注意: 以下步骤需要手动完成:"
echo "1. 为每个class添加 'with UplusLifecycleMixin<ClassName>'"
echo "2. 重写onPageShow(), onPageHide(), onPageDestroy()方法"
echo "3. 删除didChangeAppLifecycleState方法"
echo "4. 将原有的生命周期逻辑迁移到新的方法中"

echo ""
echo "需要手动处理的文件列表:"
for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  $file"
    fi
done
