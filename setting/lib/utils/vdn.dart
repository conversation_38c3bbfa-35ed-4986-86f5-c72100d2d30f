import 'dart:async';
import 'package:setting/utils/toast.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:vdn/vdn.dart';
import 'package:trace/trace.dart';

typedef VoidCallback = void Function();

//函数防抖初始时间
int lastTime = 0;

/// 函数防抖
class Debounce {
  static run(VoidCallback action) {
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - lastTime > 1000) {
      action();
    }
    lastTime = currentTime;
  }
}

class VDN {
  /// vdn 跳转页面，不打点
  static Future<void> gotoPage(String url,
      {Map<String, dynamic>? params}) async {
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (isOnline.isOnline) {
      Debounce.run(() {
        Vdn.goToPage(url, params: params);
      });
    } else {
      // CustomToast.showToast('网络不可用');
    }
  }

  /// 关闭当前 flutter 容器
  static void close() {
    Vdn.close();
  }

  /// vdn 不带参数进行跳转页面
  static Future<void> gotoPageWidthTrack(
      {required String url,
      String? eventId,
      Map<String, dynamic>? params}) async {
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (isOnline.isOnline) {
      Debounce.run(() {
        Trace.traceEvent(eventId: eventId ?? '');
        Vdn.goToPage(url, params: params);
      });
    } else {
      // CustomToast.showToast('网络不可用');
    }
    return;
  }

  /// vdn 带参数进行跳转
  static Future<void> gotoPageWithParams(
      {required String url,
      String? eventId,
      Map<String, dynamic>? params}) async {
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (isOnline.isOnline) {
      Debounce.run(() {
        Trace.traceEventWithVariable(eventId: eventId ?? '', variable: params);
        Vdn.goToPage(url);
      });
    } else {
      // CustomToast.showToast('网络不可用');
    }
    return;
  }
}
