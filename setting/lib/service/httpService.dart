import 'dart:collection';
import 'dart:convert';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:dio/dio.dart';

class HeaderInterceptors extends InterceptorsWrapper {
  @override
  onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    /// 超时时间设置
    options.connectTimeout = const Duration(seconds: 30);

    handler.next(options);
  }
}

/// http 请求封装
class HttpManager {
  static Dio _dio = Dio(); // 使用默认配置

  HttpManager() {
    _dio.interceptors.add(new HeaderInterceptors());
  }

  /// 统一封装 get 请求
  ///
  static Future<dynamic> getData(
    String url, {
    Map<String, dynamic>? params,
    required Options options,
    Map<String, dynamic>? header,
  }) async {
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      return null;
    }
    Map<String, dynamic> _headers = new HashMap();
    if (header != null) {
      _headers.addAll(header);
    }

    Options _options = options;
    if (_options.headers != null) {
      _options.headers!.addAll(_headers);
    } else {
      _options.headers = _headers;
    }
    Response? response;
    try {
      response =
          await _dio.get(url, queryParameters: params, options: _options);
    } catch (e) {
      // print('request Error: ' + e.toString());
    }

    var data = response!.data ?? Map();
    if (data is String) {
      data = jsonDecode(data);
    }

    // 兼容不同接口返回数据格式
    if (data['retCode'] == '000000') {
      return data;
    } else {
      return null;
    }
  }

  /// 统一封装 post 请求
  static Future<dynamic> postData(
    String url, {
    dynamic? params,
    required Options options,
    Map<String, dynamic>? header,
    Map<String, dynamic>? queryParameters,
  }) async {
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      return null;
    }
    Map<String, dynamic> _headers = new HashMap();
    if (header != null) {
      _headers.addAll(header);
    }
    Options _options = options;
    if (_options.headers != null) {
      _options.headers!.addAll(_headers);
    } else {
      _options.headers = _headers;
    }

    Response? response;
    try {
      response = await _dio.post(
        url,
        data: params,
        queryParameters: queryParameters,
        options: _options,
      );
    } catch (e) {
      // print('post data error: $e');
    }
    var data = response!.data ?? Map();

    if (data is String) {
      data = jsonDecode(data);
    }

    // 兼容不同接口返回数据格式
    if (data['retCode'] == '00000') {
      return data;
    } else {
      return null;
    }
  }
}

final HttpManager httpManager = new HttpManager();
