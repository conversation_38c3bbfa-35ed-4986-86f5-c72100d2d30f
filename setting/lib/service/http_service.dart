import 'dart:core';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:device_utils/log/log.dart';
import 'package:upservice/dio/uhome_dio/uhome_dio.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../models/switch_status_operate_model.dart';
import '../models/switch_status_query_model.dart';
import '../utils/constant.dart';
import '../utils/http.dart';
import 'rest_client.dart';

class HttpService {
  static String _appVersion = '';
  static const String httpSuccess = '00000';

  static Future<String> getAppVersion() async {
    if (_appVersion.isEmpty) {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _appVersion = appInfo.appVersion;
    }
    return _appVersion;
  }

  // 查询开关状态
  static Future<SwitchStatusResponseModel?> switchQuery(
      SwitchStatusRequestModel reqModel) async {
    DevLogger.debug(tag: CONSTANT.package, msg: 'switchQuery req: $reqModel');
    try {
      final String appVersion = await getAppVersion();
      final SwitchStatusResponseModel response =
          await SmartHomeRestClient(UhomeDio().dio, baseUrl: HttpUtils.BASE_URL)
              .switchQuery(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: CONSTANT.package, msg: 'switchQuery response: $response');
      return response;
    } catch (err) {
      DevLogger.error(tag: CONSTANT.package, msg: 'switchQuery err: $err');
      return null;
    }
  }

  // 打开/关闭开关
  static Future<UhomeResponseModel?> switchOperate(
      SwitchStatusOperateRequestModel reqModel) async {
    DevLogger.debug(tag: CONSTANT.package, msg: 'switchOperate req: $reqModel');
    try {
      final String appVersion = await getAppVersion();
      final UhomeResponseModel response =
          await SmartHomeRestClient(UhomeDio().dio, baseUrl: HttpUtils.BASE_URL)
              .switchOperate(reqModel.toJson(), appVersion);
      DevLogger.debug(
          tag: CONSTANT.package, msg: 'switchOperate response: $response');
      return response;
    } catch (err) {
      DevLogger.error(tag: CONSTANT.package, msg: 'switchOperate err: $err');
      return null;
    }
  }
}
