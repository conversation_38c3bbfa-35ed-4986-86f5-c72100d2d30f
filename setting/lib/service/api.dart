import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:setting/service/httpService.dart';
import 'package:setting/utils/models.dart';
import 'package:setting/utils/util_log.dart';
import 'package:setting/viewmodels/family_view_model.dart';
import 'package:user/user.dart';
import 'package:setting/utils/http.dart';
import 'package:app_info/Appinfos.dart';

class HTTP {
  // 设置开机广告、首页弹窗状态
  static Future setStartPopStatus(String wlType, String type) async {
    final oauthdata = await User.getOauthData();
    final appinfo = await AppInfoPlugin.getAppInfo();
    final _timestamp = HttpUtils.getTimestamp();
    final accountToken = oauthdata.user_center_access_token;
    Map<String, dynamic> requestMap = {'wlType': wlType, 'type': type};
    String bodyJson = json.encode(requestMap);
    String sign = HttpUtils.getSHA256Sign(
      HttpUtils.START_POP_SETTING,
      bodyJson,
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    return await HttpManager.postData(
      (appinfo.env == '生产' ? HttpUtils.BASE_URL : HttpUtils.BASE_URL_YANSHOU) +
          HttpUtils.START_POP_SETTING,
      params: requestMap,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'timestamp': _timestamp,
          'sign': sign,
          'accountToken': accountToken,
          'appId': appinfo.appId,
          'clientId': appinfo.clientId,
          'appVersion': appinfo.appVersion,
          'accessToken': oauthdata.uhome_access_token
        },
      ),
    );
  }

  // 获取开机广告、首页弹窗状态
  static Future<StartPopModel> getStartPopStatus() async {
    final oauthdata = await User.getOauthData();
    final appinfo = await AppInfoPlugin.getAppInfo();
    final _timestamp = HttpUtils.getTimestamp();
    final accountToken = oauthdata.user_center_access_token;
    Map<String, dynamic> requestMap = {
      'wlType': ['startPage', 'popAd']
    };
    String bodyJson = json.encode(requestMap);
    String sign = HttpUtils.getSHA256Sign(
      HttpUtils.START_POP_STATUS,
      bodyJson,
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    final res = await HttpManager.postData(
      (appinfo.env == '生产' ? HttpUtils.BASE_URL : HttpUtils.BASE_URL_YANSHOU) +
          HttpUtils.START_POP_STATUS,
      params: requestMap,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'timestamp': _timestamp,
          'sign': sign,
          'accountToken': accountToken,
          'appId': appinfo.appId,
          'clientId': appinfo.clientId,
          'appVersion': appinfo.appVersion,
          'accessToken': oauthdata.uhome_access_token
        },
      ),
    );
    DevLogger.info(tag: TAG_MODULE, msg: 'setStartPopStatus response: $res');

    if (res != null) {
      final ResponseModel responseModel = ResponseModel.fromJSON(res);
      if (responseModel.data.length == 2) {
        final StartPopModel startPopModel =
            StartPopModel.fromJSON(responseModel.data);
        return startPopModel;
      }
    }
    // 默认值
    return StartPopModel(popAd: true, startPage: true);
  }

  // 获取智慧场景开关和个人推荐状态
  static Future getSmartAndPersonalStatus(
      BuildContext context, int switchType) async {
    final appinfo = await AppInfoPlugin.getAppInfo();
    final userinfo = await User.getUserInfo();
    final oauthdata = await User.getOauthData();
    final _timestamp = HttpUtils.getTimestamp();
    Map<String, dynamic> requestMap = {
      'switchType': switchType,
      'switchKey': switchType == 1
          ? Provider.of<FamilyViewModel>(context, listen: false).familyId
          : userinfo.userId,
    };
    String bodyJson = json.encode(requestMap);
    String sign = HttpUtils.getSHA256Sign(
      HttpUtils.SMART_SCENE_STATUS,
      bodyJson,
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    return await HttpManager.postData(
      (appinfo.env == '生产' ? HttpUtils.BASE_URL : HttpUtils.BASE_URL_YANSHOU) +
          HttpUtils.SMART_SCENE_STATUS,
      params: requestMap,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'timestamp': _timestamp,
          'sign': sign,
          'appId': appinfo.appId,
          'clientId': appinfo.clientId,
          'appVersion': appinfo.appVersion,
          'accountToken': oauthdata.user_center_access_token,
          'accessToken': oauthdata.uhome_access_token
        },
      ),
    );
  }

  // 设置智慧场景开关和个人推荐状态
  static Future setSmartAndPersonalStatus(
      BuildContext context, String value, int switchType) async {
    final appinfo = await AppInfoPlugin.getAppInfo();
    final userinfo = await User.getUserInfo();
    final oauthdata = await User.getOauthData();
    final _timestamp = HttpUtils.getTimestamp();
    Map<String, dynamic> requestMap = {
      'switchType': switchType,
      'switchKey': switchType == 1
          ? Provider.of<FamilyViewModel>(context, listen: false).familyId
          : userinfo.userId,
      'status': value
    };
    String bodyJson = json.encode(requestMap);
    String sign = HttpUtils.getSHA256Sign(
      HttpUtils.SMART_SCENE_SETTING,
      bodyJson,
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    return await HttpManager.postData(
      (appinfo.env == '生产' ? HttpUtils.BASE_URL : HttpUtils.BASE_URL_YANSHOU) +
          HttpUtils.SMART_SCENE_SETTING,
      params: requestMap,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'timestamp': _timestamp,
          'sign': sign,
          'appId': appinfo.appId,
          'clientId': appinfo.clientId,
          'appVersion': appinfo.appVersion,
          'accountToken': oauthdata.user_center_access_token,
          'accessToken': oauthdata.uhome_access_token
        },
      ),
    );
  }

  /// 获取升级优化状态
  static Future<UpgradeModel> getUpgradeAssistant() async {
    final oauthdata = await User.getOauthData();
    final appinfo = await AppInfoPlugin.getAppInfo();
    final _timestamp = HttpUtils.getTimestamp();
    String sign = HttpUtils.getSHA256Sign(
      HttpUtils.UPGRADE_ASSISTANT,
      '',
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );
    final result = await HttpManager.postData(
      HttpUtils.IOT_URL + HttpUtils.UPGRADE_ASSISTANT,
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'accesstoken': oauthdata.user_center_access_token,
          'clientId': appinfo.clientId,
          'appVersion': appinfo.appVersion,
          'appId': appinfo.appId,
          'sign': sign,
          'apiVersion': 'v1',
          'timestamp': _timestamp.toString(),
        },
      ),
    );

    DevLogger.info(tag: TAG_MODULE, msg: 'getUpgradeAssistant result: $result');
    if (result != null && result is Map<String, dynamic>) {
      final UpgradeModel upgradeModel = UpgradeModel.fromJSON(result);
      return upgradeModel;
    }

    return UpgradeModel(payload: false);
  }
}
