import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:family/family.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:storage/storage.dart';
import 'package:trace/trace.dart';
import 'package:upservice/model/uhome_response_model.dart';

import '../models/switch_status_operate_model.dart';
import '../models/switch_status_query_model.dart';
import '../service/http_service.dart';
import '../utils/constant.dart';
import '../utils/toast.dart';
import '../utils/util_log.dart';
import '../widgets/card_text_style.dart';

class LabFunctionPage extends StatefulWidget {
  const LabFunctionPage({super.key});

  @override
  LabFunctionPageState createState() => LabFunctionPageState();
}

class LabFunctionPageState extends State<LabFunctionPage> {
  bool _switchValue = false;

  String _familyId = '';

  @override
  void initState() {
    super.initState();
    _familyId = Family.getCurrentFamilySync()?.familyId ?? '';

    _querySwitchStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        leading: GestureDetector(
          onTap: () => Navigator.pop(context),
          child: _buildGoBackImage(),
        ),
        centerTitle: true,
        title: _buildTitle(),
        backgroundColor: Colors.transparent,
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        toolbarHeight: 44,
        elevation: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 12),
          _buildLabSwitchItem(context),
          const SizedBox(height: 16),
          _buildLabSwitchIntroduction(),
        ],
      ),
    );
  }

  Widget _buildGoBackImage() {
    return Center(
      child: Image.asset(
        'assets/images/go_back.webp',
        width: 24,
        height: 24,
        package: 'setting',
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      CONSTANT.LAB_FUNCTION,
      style: TextStyle(
        fontSize: 17,
        fontFamilyFallback: fontFamilyFallback(),
        fontWeight: FontWeight.w500,
        color: const Color.fromRGBO(38, 38, 38, 1),
      ),
    );
  }

  Widget _buildLabSwitchItem(BuildContext context) {
    return Container(
        height: 54,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Row(
            children: <Widget>[
              Text(
                CONSTANT.switchTitle,
                style: TextStyle(
                    fontSize: 16,
                    color: const Color.fromRGBO(17, 17, 17, 1),
                    fontFamilyFallback: fontFamilyFallback(),
                    fontWeight: FontWeight.w500),
              ),
              Expanded(child: Container()),
              CustomSwitch(
                value: _switchValue,
                activeColor: AppSemanticColors.item.information.primary,
                inactiveColor: Colors.grey.shade300,
                onChanged: (bool value) {
                  Network
                      .checkNetwork
                      .then((IsOnline onValue) {
                    if (result == NetworkStatusfalse) {
                      CustomToast.showToast(context, '网络不可用');
                      return;
                    }
                    Trace.traceEvent(eventId: CONSTANT.gioSwitchClick);
                    if (mounted) {
                      setState(() {
                        _switchValue = value;
                      });
                    }
                    _operateSwitch();
                  });
                },
              ),
            ],
          ),
        ));
  }

  Widget _buildLabSwitchIntroduction() {
    return Padding(
      padding: const EdgeInsets.only(left: 32, right: 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            CONSTANT.introductionTitle,
            style: TextStyle(
              fontSize: 14,
              fontFamilyFallback: fontFamilyFallback(),
              fontWeight: FontWeight.w400,
              height: 20 / 14,
              color: const Color.fromRGBO(17, 17, 17, 1),
            ),
          ),
          const Text(
            CONSTANT.introductionContent,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              height: 17 / 12,
              color: Color.fromRGBO(102, 102, 102, 1),
            ),
          ),
        ],
      ),
    );
  }

  void _querySwitchStatus() {
    final SwitchStatusRequestModel reqModel = SwitchStatusRequestModel(
        switchType: CONSTANT.switchType, switchKey: _familyId);
    HttpService.switchQuery(reqModel)
        .then((SwitchStatusResponseModel? response) {
      if (response?.retCode == HttpService.httpSuccess) {
        final String switchStatus =
            response?.data.status ?? CONSTANT.switchClose;
        if (mounted) {
          setState(() {
            _switchValue = switchStatus == CONSTANT.switchOpen;
          });
        }
        saveSwitchStatusToStorage();
      }
    }).catchError((dynamic err) {
      DevLogger.error(tag: CONSTANT.package, msg: 'switchQuery err: $err');
    });
  }

  void _operateSwitch() {
    final String familyId = Family.getCurrentFamilySync()?.familyId ?? '';
    final SwitchStatusOperateRequestModel reqModel =
        SwitchStatusOperateRequestModel(
            switchType: CONSTANT.switchType,
            switchKey: familyId,
            status: _switchValue ? CONSTANT.switchOpen : CONSTANT.switchClose);
    HttpService.switchOperate(reqModel).then((UhomeResponseModel? response) {
      if (response?.retCode == HttpService.httpSuccess) {
        saveSwitchStatusToStorage();
      } else {
        _revertSwitchStatusWith('switchOperate fail, revert switchStatus');
      }
    }).catchError((dynamic err) {
      _revertSwitchStatusWith(
          'switchOperate err, revert switchStatus, err: $err');
    });
  }

  void _revertSwitchStatusWith(String errorMsg) {
    DevLogger.error(tag: CONSTANT.package, msg: errorMsg);
    if (mounted) {
      setState(() {
        _switchValue = !_switchValue;
      });
    }
  }

  void saveSwitchStatusToStorage() {
    final String switchKey = 'switch_scene_lab_$_familyId';

    Storage.putBooleanValue(switchKey, _switchValue).then((bool response) {
      DevLogger.debug(
          tag: CONSTANT.package,
          msg:
              'saveSwitchStatusToStorage end, key:$switchKey, response: $response');
    }).catchError((dynamic err) {
      DevLogger.error(
          tag: CONSTANT.package,
          msg: 'saveSwitchStatusToStorage err, key:$switchKey, err: $err');
    });
  }
}
