import 'dart:io';

import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:provider/provider.dart';
import 'package:trace/trace.dart';
import 'package:uplustrace/uplustrace.dart';

import '../utils/constant.dart';
import '../utils/toast.dart';
import '../utils/vdn.dart';
import '../viewmodels/ai_assistant_view_model.dart';
import '../viewmodels/app_cache_view_model.dart';
import '../viewmodels/auto_download_view_model.dart';
import '../viewmodels/device_shake_view_model.dart';
import '../viewmodels/evaluate_invite_view_model.dart';
import '../viewmodels/family_view_model.dart';
import '../viewmodels/location_recommend_view_model.dart';
import '../viewmodels/logout_view_model.dart';
import '../viewmodels/personal_recommend_view_model.dart';
import '../viewmodels/switch_click_view_model.dart';
import '../viewmodels/upgrade_assistant_view_model.dart';
import '../widgets/appbar.dart';
import '../widgets/card_text_style.dart';
import '../widgets/my_badge.dart';

GlobalKey settingPageKey = GlobalKey(debugLabel: 'settingPage');

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  _HomePage createState() {
    return _HomePage();
  }

  @override
  StatefulElement createElement() {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      UplusTrace.finishTrack(CONSTANT.TRACE_SETTING);
    });
    return super.createElement();
  }
}

class _HomePage extends State<HomePage>  {
  Dialogs dialogs = Dialogs();
  int _count = 0;
  @override
  void initState() {
    super.initState();
    UplusTrace.startTrack(CONSTANT.TRACE_SETTING);
    CustomToast.init(context);
    _initData();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _count == 0) {
      _initData();
    }
  }

  void _initData() {
    _count++;
    final FamilyViewModel familyViewModel =
        Provider.of<FamilyViewModel>(context, listen: false);
    familyViewModel.getCurrentFamilyId();
    final AiAssistantViewModel aiAssistantViewModel =
        Provider.of<AiAssistantViewModel>(context, listen: false);
    aiAssistantViewModel.getAiAssistantStatus();
    final AppCacheViewModel appCacheViewModel =
        Provider.of<AppCacheViewModel>(context, listen: false);
    appCacheViewModel.getAppCacheSize();
    final PersonalRecommendViewModel personalRecommendViewModel =
        Provider.of<PersonalRecommendViewModel>(context, listen: false);
    personalRecommendViewModel.getPersonalRecommendStatus(context, 2);
    final LocationRecommendViewModel locationRecommendViewModel =
        Provider.of<LocationRecommendViewModel>(context, listen: false);
    locationRecommendViewModel.getLocationRecommendStatus(context, 12);
    final UpgradeAssistantViewModel upgradeAssistantViewModel =
        Provider.of<UpgradeAssistantViewModel>(context, listen: false);
    upgradeAssistantViewModel.getUpgradeAssistant();
    final EvaluateInviteViewModel evaluateInviteViewModel =
        Provider.of<EvaluateInviteViewModel>(context, listen: false);
    evaluateInviteViewModel.getEvaluateInvitedStatus(context, 7);
    final DeviceShakeFeedBackViewModel deviceShakeFeedBackViewModel =
        Provider.of<DeviceShakeFeedBackViewModel>(context, listen: false);
    deviceShakeFeedBackViewModel.getShakeFeedbackStatus();
    if (Platform.isAndroid) {
      final AutoDownlodViewModel autoDownlodViewModel =
          Provider.of<AutoDownlodViewModel>(context, listen: false);
      autoDownlodViewModel.getAutoDownloadStatus();
    }
  }

  @override
  void dispose() {
    super.dispose();
    _count = 0;
  }

  /// 构建模块标题
  Widget _buildModuleTitle({
    required String title,
  }) {
    return Container(
      height: 41,
      padding: const EdgeInsets.symmetric(vertical: 12),
      width: double.infinity,
      child: Text(
        title,
        style: TextStyle(
          color: AppSemanticColors.item.secondary,
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  /// 构建显示文字widget
  Widget _buildProfileTextWidget({
    required String leftText,
    String rightText = '',
    bool isSwitch = false,
    bool showTips = false,
  }) {
    return PressableOverlayWithTapWidget(
        overlayClick: () {
          _count = 0;
          Provider.of<SwitchClickViewModel>(context, listen: false)
              .handleClickEvent(context, isSwitch: isSwitch, text: leftText);
        },
        color: isSwitch ? Colors.transparent : null,
        child: Container(
          height: 54,
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Row(
                children: <Widget>[
                  Text(
                    leftText,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppSemanticColors.item.primary,
                    ),
                  ),
                  if (showTips)
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        Provider.of<SwitchClickViewModel>(context,
                                listen: false)
                            .handleTipsClickEvent(context, text: leftText);
                      },
                      child: Center(
                        child: Image.asset(
                          'assets/images/app-setting-icon-explain.webp',
                          width: 16,
                          height: 16,
                          package: 'setting',
                        ),
                      ),
                    )
                  else
                    Container()
                ],
              ),
              if (isSwitch)
                CustomSwitch(
                  value: Provider.of<SwitchClickViewModel>(context)
                      .getSwitchValue(context, text: leftText),
                  activeColor: AppSemanticColors.item.information.primary,
                  inactiveColor: Colors.grey.shade300,
                  onChanged: (bool value) async {
                    CustomToast.cancelToast();
                    final IsOnline isOnline =
                        await Network.isOnline();
                    if (mounted) {
                      if (!isOnline.isOnline) {
                        CustomToast.showToast(context, '网络不可用');
                      } else {
                        Provider.of<SwitchClickViewModel>(context,
                                listen: false)
                            .handleSwitchLogic(context,
                                text: leftText, value: value);
                      }
                    }
                  },
                )
              else
                Row(
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.only(left: 12),
                      constraints: const BoxConstraints(
                          maxWidth: 230), // 限制显示最大宽度220，超出显示省略号
                      child: Text(
                        rightText,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppSemanticColors.item.secWeaken,
                        ),
                      ),
                    ),
                    Visibility(
                        visible: leftText == CONSTANT.UPGRADE_ASSISTANT &&
                            Provider.of<UpgradeAssistantViewModel>(context)
                                .upgradeAssistant,
                        child: const MyBadge()),
                    const SizedBox(width: 4),
                    // 右箭头
                    Image.asset(
                      'assets/images/right-arrow.webp',
                      width: 16,
                      height: 16,
                      package: 'setting',
                    ),
                  ],
                ),
            ],
          ),
        ));
  }

  /// [退出登录] 按钮
  Widget _buildLogOutButton() {
    return Padding(
        padding: const EdgeInsets.only(top: 16),
        child: PressableOverlayWithTapWidget(
          overlayClick: () {
            Trace.traceEvent(eventId: CONSTANT.LOGIN_OUT_GIO);
            _showLogoutConfirmDialog1();
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Container(
              color: AppSemanticColors.component.secondary.invert,
              width: double.infinity,
              height: 44,
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      '退出登录',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppSemanticColors.component.warn.on,
                        fontWeight: FontWeight.w500,
                        decoration: TextDecorationfalse,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ));
  }

  /// [确认退出登录]对话框背景
  Widget _showLogoutConfirmDialogBg() {
    return Positioned(
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
      ),
    );
  }

  /// [确认退出登录]对话框
  void _showLogoutConfirmDialog1() {
    dialogs.showDoubleBtnDialog(
      context: context,
      title: '温馨提示',
      content: '确定要退出当前账号吗？',
      cancelCallback: () {
        dialogTraceGio(true, '取消');
      },
      confirmCallback: () {
        Provider.of<LogoutViewModel>(context, listen: false).logout(context);
        dialogTraceGio(true, '确定');
      },
    );
  }

  /// [清除缓存]对话框
  void _showClearCacheConfirmDialog() {
    dialogs.showDoubleBtnDialog(
      context: context,
      title: '清除缓存',
      content: '是否确认清除本地缓存？',
      cancelCallback: () {
        dialogTraceGio(false, '取消');
      },
      confirmCallback: () {
        Provider.of<AppCacheViewModel>(context, listen: false)
            .clearAppCache(context);
        dialogTraceGio(false, '确定');
      },
    );
  }

  void dialogTraceGio(bool isLogout, String text) {
    final Map<String, dynamic> map = <String, dynamic>{'result': text};
    Trace.traceEventWithVariable(
        eventId: isLogout
            ? CONSTANT.LOGIN_OUT_DIALOG_GIO
            : CONSTANT.CLEAR_CATCH_DIALOG_GIO,
        variable: map);
  }

  @override
  Widget build(BuildContext context) {
    double paddingBottom = MediaQuery.of(context).padding.bottom;
    paddingBottom = paddingBottom > 0 ? paddingBottom : 12;
    return Stack(
      children: <Widget>[
        Scaffold(
          key: settingPageKey,
          backgroundColor: AppSemanticColors.background.secondary,
          appBar: MyAppBar(
            title: '设置',
            handleBackEvent: () => VDN.close(),
            rightActions: const <Widget>[],
          ),
          body: DefaultTextStyle(
              style: TextStyle(
                color: AppSemanticColors.item.primary,
                fontWeight: FontWeight.w400,
                fontFamilyFallback: fontFamilyFallback(),
              ),
              child: ListView(
                physics: const ClampingScrollPhysics(),
                padding:
                    EdgeInsets.only(left: 16, right: 16, bottom: paddingBottom),
                children: <Widget>[
                  _buildSettingGroup(
                    title: CONSTANT.ACCOUNT,
                    children: <Widget>[
                      _buildProfileTextWidget(
                          leftText: CONSTANT.ACCOUNT_SECURITY),
                      _buildProfileTextWidget(leftText: CONSTANT.PRIVACY_RIGHT),
                      _buildProfileTextWidget(
                        leftText: CONSTANT.ACCOUNT_AUTH_SERVICE,
                      ),
                    ],
                  ),
                  _buildSettingGroup(
                    title: CONSTANT.DEVICE,
                    children: <Widget>[
                      _buildProfileTextWidget(leftText: CONSTANT.FAMILY_MANAGE),
                      _buildProfileTextWidget(
                          leftText: CONSTANT.DISCOVER_DEVICES),
                      _buildProfileTextWidget(leftText: CONSTANT.LAB_FUNCTION),
                      _buildProfileTextWidget(
                        leftText: CONSTANT.EVALUATE_INVITE,
                        isSwitch: true,
                      ),
                      _buildProfileTextWidget(
                        leftText: CONSTANT.DEVICE_SHAKE_FEEDBACK,
                        isSwitch: true,
                      ),
                      if (Platform.isAndroid)
                        _buildProfileTextWidget(
                          leftText: CONSTANT.WIFI_AUTO_DOWNLOAD,
                          isSwitch: true,
                        )
                      else
                        Container(),
                    ],
                  ),
                  _buildSettingGroup(title: '个性化推荐', children: <Widget>[
                    _buildProfileTextWidget(
                      leftText: CONSTANT.PERSONAL_RECOMMEND,
                      isSwitch: true,
                      showTips: true,
                    ),
                    // 个性化开关为开启时，不显示地理位置开关
                    if (Provider.of<PersonalRecommendViewModel>(context)
                        .personalRecommendStatus)
                      Container()
                    else
                      _buildProfileTextWidget(
                        leftText: CONSTANT.LOCATION_RECOMMEND,
                        isSwitch: true,
                        showTips: true,
                      )
                  ]),
                  _buildSettingGroup(title: CONSTANT.OTHERS, children: <Widget>[
                    _buildProfileTextWidget(leftText: CONSTANT.STORE_HELP),
                    _buildProfileTextWidget(leftText: CONSTANT.MESSAGE_SETTING),
                    _buildProfileTextWidget(
                        leftText: CONSTANT.voiceSettingName),
                    _buildProfileTextWidget(leftText: CONSTANT.noPwdPayName),
                    _buildProfileTextWidget(
                        leftText: CONSTANT.UPGRADE_ASSISTANT),
                    Consumer<AppCacheViewModel>(
                      builder: (BuildContext context,
                          AppCacheViewModel provider, Widget? child) {
                        return _buildProfileTextWidget(
                          leftText: CONSTANT.CLEAR_CACHE,
                          rightText: provider.isCleanCacheSuccess ||
                                  provider.appCache.toStringAsFixed(1) == '0.0'
                              ? '暂无缓存'
                              : '${provider.appCache.toStringAsFixed(1)}M',
                        );
                      },
                    ),
                  ]),

                  /// 退出登录按钮
                  _buildLogOutButton()
                ],
              )),
        ),
        Consumer<AppCacheViewModel>(
          builder: (BuildContext context, AppCacheViewModel appCacheVM, Widget? child) {
            if (appCacheVM.isShowDialog) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _showClearCacheConfirmDialog();
                appCacheVM.setShowDialog(false);
              });
            }
            return const SizedBox.shrink();
          },
    ),
      ],
    );
  }

  Widget _buildSettingGroup({
    required String title,
    required List<Widget> children,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: Column(
        children: <Widget>[
          _buildModuleTitle(title: title),
          ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }
}
