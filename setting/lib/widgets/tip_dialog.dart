import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:setting/utils/toast.dart';
import 'package:setting/utils/util_log.dart';

class TipDialog extends Dialog {
  final String? title;
  final String? content;
  final String? entranceText;
  final Function? entranceCallBack;
  final String? confirmText;
  final Function? confirmCallBack;

  final double? titleFontSize;
  final double? contentFontSize;
  final double? btnFontSize;
  final double? dialogWidth;
  final double? dialogHeight;
  bool showHyperLink = true;

  TipDialog({
    this.title,
    this.content,
    this.entranceText,
    this.entranceCallBack,
    this.confirmText,
    this.confirmCallBack,
    this.btnFontSize,
    this.contentFontSize,
    this.titleFontSize,
    this.dialogWidth,
    this.dialogHeight,
    this.showHyperLink = true,
  });

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
        BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width,
            maxHeight: MediaQuery.of(context).size.height),
        designSize: Size(750 / 2, 1334 / 2),
        orientation: Orientation.portrait);

    return Scaffold(
      backgroundColor: Color.fromARGB(127, 0, 0, 0),
      body: Center(
        child: Container(
          width: dialogWidth!.w,
          height: dialogHeight!.w,
          decoration: ShapeDecoration(
            color: Color(0xFFFFFFFF),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(12.w)),
            ),
          ),
          child: Stack(
            children: <Widget>[
              Positioned(
                top: 0.w,
                left: 16.w,
                right: 16.w,
                child: Column(
                  children: <Widget>[
                    //标题
                    Container(
                      padding: EdgeInsets.only(top: 19.w),
                      child: Text(
                        title!,
                        style: TextStyle(fontSize: titleFontSize!.sp),
                      ),
                    ),
                    SizedBox(height: 12.w),
                    //内容
                    Align(
                      alignment: Alignment.center,
                      child: RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: content!,
                              style: TextStyle(
                                fontSize: contentFontSize!.sp,
                                color: Color.fromRGBO(0, 0, 0, 0.6),
                              ),
                            ),
                            // 区分个性化提示与地理位置提示弹窗, 有跳转超链接的为个性化提示
                            if (showHyperLink)
                              TextSpan(
                                text: entranceText!,
                                style: TextStyle(
                                  fontSize: contentFontSize!.sp,
                                  color: const Color(0xFF2283E2),
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    // 点击隐私协议
                                    DevLogger.info(
                                        tag: TAG_MODULE, msg: '点击隐私协议');
                                    Network
                                        .checkNetwork
                                        .then((IsOnline onValue) {
                                      if (NetworkStatusfalse == result) {
                                        CustomToast.cancelToast();
                                        CustomToast.showToast(context, '网络不可用');
                                        return;
                                      } else {
                                        if (entranceCallBack != null) {
                                          entranceCallBack!();
                                        }
                                      }
                                    }).catchError((dynamic err) {
                                      DevLogger.info(
                                          tag: TAG_MODULE,
                                          msg:
                                              'getEvaluateInvitedStatus end $err');
                                    });
                                  },
                              )
                            else
                              const TextSpan()
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 按钮
              Positioned(
                bottom: 0.w,
                left: 0.w,
                right: 0.w,
                child: Container(
                  height: 46.w,
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      Divider(
                        height: 1.w,
                        color: Color(0xFFEEEEEE),
                      ),
                      Expanded(
                          child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        child: Container(
                            child: Center(
                                child: Text(confirmText!,
                                    style: TextStyle(
                                        color: Color(0xFF2282E3),
                                        fontSize: btnFontSize)))),
                        onTap: () {
                          Navigator.of(context).pop();
                          if (confirmCallBack != null) {
                            confirmCallBack!();
                          }
                        },
                      )),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
