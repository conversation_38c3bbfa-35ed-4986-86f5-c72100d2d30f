import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:provider/provider.dart';
import 'package:setting/utils/constant.dart';
import 'package:setting/utils/toast.dart';
import 'package:setting/utils/vdn.dart';
import 'package:setting/viewmodels/app_cache_view_model.dart';
import 'package:setting/viewmodels/auto_download_view_model.dart';
import 'package:setting/viewmodels/evaluate_invite_view_model.dart';
import 'package:setting/viewmodels/personal_recommend_view_model.dart';
import 'package:trace/trace.dart';
import 'package:user/modle/user_info_model.dart';
import 'package:user/user.dart';

import '../pages/lab_function.dart';
import '../utils/util_log.dart';
import 'device_shake_view_model.dart';
import 'location_recommend_view_model.dart';

class SwitchClickViewModel with ChangeNotifier {
  final Dialogs _dialogs = Dialogs();
  Map<String, String> eventClickList = <String, String>{
    CONSTANT.PRIVACY_RIGHT: 'MB13991',
    CONSTANT.ACCOUNT_AUTH_SERVICE: 'MB16005',
    CONSTANT.FAMILY_MANAGE: 'MB34538',
    CONSTANT.STORE_HELP: 'MB14010',
    CONSTANT.DISCOVER_DEVICES: 'MB34304',
    CONSTANT.LAB_FUNCTION: 'MB38888', // 我的tab-设置-点击实验室功能

    /// 点击设备升级的埋点
    CONSTANT.UPGRADE_ASSISTANT: CONSTANT.upgradeAssistantClickGio,
  };
  Map<String, String> eventSwitchList = <String, String>{
    CONSTANT.AI_ASSISTANT: 'MB13993',
    CONSTANT.START_PAGE: 'MB16286',
    CONSTANT.POP_AD: 'MB16287',
    CONSTANT.PERSONAL_RECOMMEND: 'MB18977',
    CONSTANT.LOCATION_RECOMMEND: 'MB36311',
    CONSTANT.WIFI_AUTO_DOWNLOAD: 'MB17042',
    CONSTANT.EVALUATE_INVITE: 'MB34305',
    CONSTANT.DEVICE_SHAKE_FEEDBACK: 'MB35887',
  };

  Future<void> handleTipsClickEvent(BuildContext context,
      {required String text}) async {
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      if (context.mounted) {
        CustomToast.showToast(context, '网络不可用');
      }
      return;
    }
    final Map<String, void Function()> actions = <String, void Function()>{
      CONSTANT.PERSONAL_RECOMMEND: () => personalTipClickEvent(context),
      CONSTANT.LOCATION_RECOMMEND: () => locationTipClickEvent(context),
    };
    if (actions[text] != null) {
      return actions[text]!();
    }
  }

  Widget _buildPersonalTipContent(BuildContext context) {
    return SizedBox(
      height: 40,
      child: Align(
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            children: <InlineSpan>[
              TextSpan(
                text: CONSTANT.PERSONAL_TIP_CONTENT,
                style: TextStyle(
                  fontSize: 14,
                  color: AppSemanticColors.item.secondary,
                ),
              ),
              // 区分个性化提示与地理位置提示弹窗, 有跳转超链接的为个性化提示
              TextSpan(
                text: CONSTANT.PERSONAL_TIP_CONTENT_LINK_ENTRANCE,
                style: TextStyle(
                  fontSize: 14,
                  color: AppSemanticColors.item.information.primary,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    // 点击隐私协议
                    DevLogger.info(tag: TAG_MODULE, msg: '点击隐私协议');
                    Network
                        .checkNetwork
                        .then((IsOnline onValue) {
                      if (NetworkStatusfalse == result) {
                        CustomToast.cancelToast();
                        CustomToast.showToast(context, '网络不可用');
                        return;
                      } else {
                        VDN.gotoPage(CONSTANT.PRIVACY_AGREEMENT_URL);
                      }
                    }).catchError((dynamic err) {
                      DevLogger.info(
                          tag: TAG_MODULE,
                          msg: 'getEvaluateInvitedStatus end $err');
                    });
                  },
              )
            ],
          ),
        ),
      ),
    );
  }

  void personalTipClickEvent(BuildContext context) {
    _dialogs.showSingleBtnDialogWithBuilder(
      context: context,
      title: CONSTANT.PERSONAL_TIP_TITLE,
      builder: (BuildContext context) => _buildPersonalTipContent(context),
      btnText: '知道了',
      callback: () {},
    );
  }

  void locationTipClickEvent(BuildContext context) {
    _dialogs.showSingleBtnDialog(
      context: context,
      title: '推荐使用地理位置说明',
      content: '此处不影响地理位置系统授权，仅针对推荐场景使用，开启后，将基于地理位置提升您的商城体验。',
      btnText: '知道了',
      callback: () {},
    );
  }

  /// 点击进行跳转判断处理
  Future<void> handleClickEvent(BuildContext context,
      {required bool isSwitch, required String text}) async {
    if (isSwitch) {
      return;
    }
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      if (context.mounted) {
        CustomToast.showToast(context, '网络不可用');
      }
      return;
    }
    if (eventClickList[text] != null) {
      Trace.traceEvent(eventId: eventClickList[text] ?? '');
    }
    final AppInfoModel appinfo = await AppInfoPlugin.getAppInfo();
    final Map<String, Future<void>? Function()> actions = <String, Future<void>? Function()>{
      CONSTANT.ACCOUNT_SECURITY: () =>
          VDN.gotoPage(CONSTANT.ACCOUNT_SECURITY_URL),
      CONSTANT.PRIVACY_RIGHT: () => VDN.gotoPage(CONSTANT.PRIVACY_RIGHT_URL),
      CONSTANT.ACCOUNT_AUTH_SERVICE: () =>
          VDN.gotoPage(CONSTANT.ACCOUNT_AUTH_SERVICE_URL),
      CONSTANT.FAMILY_MANAGE: () => VDN.gotoPage(CONSTANT.INVITE_FAMILY_URL),
      CONSTANT.DISCOVER_DEVICES: () async {
        final String discoverSetUrl =
            (appinfo.env == '生产' ? CONSTANT.BASE_URL : CONSTANT.BASE_URL_YS) +
                CONSTANT.DISCOVER_DEVICES_SETTING_URL;
        VDN.gotoPage(discoverSetUrl);
      },
      CONSTANT.LAB_FUNCTION: () {
        Navigator.push(
            context,
            MaterialPageRoute<LabFunctionPage>(
                builder: (BuildContext context) => const LabFunctionPage()));
        return null;
      },
      CONSTANT.STORE_HELP: () => VDN.gotoPage(CONSTANT.STORE_HELP_URL),
      CONSTANT.MESSAGE_SETTING: () =>
          VDN.gotoPage(CONSTANT.MESSAGE_SETTING_URL),
      CONSTANT.voiceSettingName: (){
          Trace.traceEvent(eventId: CONSTANT.voiceSettingGioKey);
          VDN.gotoPage(CONSTANT.voiceSettingUrl);
          return null;
      },
      CONSTANT.noPwdPayName: (){
          Trace.traceEvent(eventId: CONSTANT.noPwdPayGioKey);
          VDN.gotoPage(appinfo.env == '生产' ? CONSTANT.noPwdPayUrl : CONSTANT.noPwdPayYsUrl);
          return null;
      },
      CONSTANT.UPGRADE_ASSISTANT: () =>
          VDN.gotoPage(CONSTANT.UPGRADE_ASSISTANT_URL),
      CONSTANT.CLEAR_CACHE: () {
        Trace.traceEvent(eventId: CONSTANT.CLEAR_CATCH_GIO);
        Provider.of<AppCacheViewModel>(context, listen: false)
            .setShowDialog(true);
        return null;
      }
    };
    if (actions[text] != null) {
      return actions[text]!();
    }
  }

  /// 开关点击点位逻辑处理
  Future<void> handleSwitchTraceLogic(
      {required String text, required bool value}) async {
    // user_id 属性获取
    final UserInfo userinfo = await User.getUserInfo();
    final String userId = userinfo.userId;
    const String key_userId = 'user_id';
    // value 属性组装
    final String valueStr = 'status=${value ? 'on' : 'off'}';
    const String key = 'value';
    // 上报属性整合上报
    final Map<String, Object> variable = <String, Object>{key: valueStr, key_userId: userId};
    Trace.traceEventWithVariable(
        eventId: eventSwitchList[text] ?? '', variable: variable);
  }

  /// 开关逻辑处理
  dynamic handleSwitchLogic(BuildContext context,
      {required String text, required bool value}) {
    if (eventSwitchList[text] != null) {
      handleSwitchTraceLogic(text: text, value: value);
    }
    final Map<String, Function> actions = <String, Function>{
      CONSTANT.PERSONAL_RECOMMEND: () =>
          Provider.of<PersonalRecommendViewModel>(context, listen: false)
              .setPersonalRecommendStatus(context, value, 2),
      CONSTANT.LOCATION_RECOMMEND: () =>
          Provider.of<LocationRecommendViewModel>(context, listen: false)
              .setLocationRecommendStatus(context, value, 12),
      CONSTANT.WIFI_AUTO_DOWNLOAD: () =>
          Provider.of<AutoDownlodViewModel>(context, listen: false)
              .setAutoDownloadStatus(value),
      CONSTANT.EVALUATE_INVITE: () =>
          Provider.of<EvaluateInviteViewModel>(context, listen: false)
              .setEvaluateInvitedStatus(context, value, 7),
      CONSTANT.DEVICE_SHAKE_FEEDBACK: () =>
          Provider.of<DeviceShakeFeedBackViewModel>(context, listen: false)
              .setShakeFeedbackStatus(value),
    };
    if (actions[text] != null) {
      return actions[text]!();
    }
  }

  /// 获取开关按钮值
  bool getSwitchValue(BuildContext context, {required String text}) {
    final Map<String, bool> values = <String, bool>{
      CONSTANT.PERSONAL_RECOMMEND:
          Provider.of<PersonalRecommendViewModel>(context)
              .personalRecommendStatus,
      CONSTANT.LOCATION_RECOMMEND:
          Provider.of<LocationRecommendViewModel>(context)
              .locationRecommendStatus,
      CONSTANT.EVALUATE_INVITE:
          Provider.of<EvaluateInviteViewModel>(context).evaluateInviteStatus,
      CONSTANT.WIFI_AUTO_DOWNLOAD:
          Provider.of<AutoDownlodViewModel>(context).autoDownloadSatus,
      CONSTANT.DEVICE_SHAKE_FEEDBACK:
          Provider.of<DeviceShakeFeedBackViewModel>(context).shakeFeedbackStatus,
    };
    if (values[text] != null) {
      return values[text]!;
    } else {
      return false;
    }
  }
}
