import 'package:app_info/Appinfos.dart';
import 'package:flutter/material.dart';
import 'package:setting/utils/toast.dart';

class AppCacheViewModel with ChangeNotifier {
  // app 缓存大小
  double _appCache = 0;
  // 清除缓存确认弹窗
  bool _isShowDialog = false;
  // 是否清楚缓存成功
  bool _isCleanCacheSuccess = false;

  double get appCache => _appCache;
  bool get isShowDialog => _isShowDialog;
  bool get isCleanCacheSuccess => _isCleanCacheSuccess;

  void setShowDialog(bool value) {
    _isShowDialog = value;
    notifyListeners();
  }

  void setCleanCacheSuccess(bool value) {
    _isCleanCacheSuccess = value;
    notifyListeners();
  }

  void getAppCacheSize() async {
    try {
      int result = await AppInfoPlugin.getApplicationCacheSize();
      _appCache = result / 1000000;
      // print('getAppCacheSize result: $result');
    } catch (err) {
      // print('getAppCacheSize error: $err');
    } finally {
      notifyListeners();
    }
  }

  void clearAppCache(BuildContext context) async {
    try {
      CustomToast.cancelToast();
      await AppInfoPlugin.cleanApplicationCache();
      CustomToast.showToast(context, '缓存清除成功');
      setShowDialog(false);
      setCleanCacheSuccess(true);
    } catch (err) {
      // print('clearAppCache error: $err');
    }
  }
}
