name: personal_information
description: Flutter 个人资料库
author: <EMAIL>
version: 0.0.3+**********
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3

environment:
  sdk: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: 1.0.4
  dio: 5.3.2
  network:
    hosted:
      name: network
      url: http://**************:8083
    version: ">=0.0.1"
  flutter_screenutil: 5.0.0+2
  extended_image: 8.2.0
  path_provider: 2.0.14
  provider: 6.0.5

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=0.0.1"

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: ">=0.1.13"

  vdn:
    hosted:
      name: vdn
      url: http://**************:8083
    version: ">=0.0.24"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: ">=0.0.12"

  storage:
    hosted:
      name: storage
      url: http://**************:8083
    version: ">=0.0.4"

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: ">=0.0.5"

  location:
    hosted:
      name: location
      url: http://**************:8083
    version: ">=1.0.5"

  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: ">=1.0.0"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.1"

  flutter_common_ui:
    hosted:
      name: flutter_common_ui
      url: http://**************:8083
    version: ">=9.2.1 <999.999.999"

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/images/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
