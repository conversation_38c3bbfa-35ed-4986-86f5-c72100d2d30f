import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:personal_information/utils/StringUtils.dart';
import 'package:personal_information/viewmodels/current_location_view_model.dart';
import 'package:personal_information/viewmodels/personal_address_view_model.dart';
import 'package:personal_information/widgets/appbar.dart';
import 'package:provider/provider.dart';
import 'package:user/modle/user_address_model.dart';
import 'package:vdn/vdn.dart';

import '../utils/toast.dart';

GlobalKey addressListKey = GlobalKey(debugLabel: 'addressList');

/// 地址列表页面
class AddressPage extends StatefulWidget {
  const AddressPage({Key? key}) : super(key: key);

  @override
  _AddressPage createState() => _AddressPage();
}

class _AddressPage extends State<AddressPage> {
  @override
  void initState() {
    super.initState();

    ///注册监听器
    _initData();
    CustomToast.init(context);
  }

  void _initData() {
    PersonalAddressViewModel personalAddressViewModel =
        Provider.of<PersonalAddressViewModel>(context, listen: false);
    personalAddressViewModel.getAddressList();
    CurrentLocationViewModel currentLocationViewModel =
        Provider.of<CurrentLocationViewModel>(context, listen: false);
    currentLocationViewModel.getCurrentLocation();
  }

  /// [默认] 显示widget
  Widget _buildDefaultContainer() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.w)),
        border: Border.all(
          color: const Color.fromRGBO(34, 131, 226, 1),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 8.w),
      height: 18.w,
      // width: 36,
      child: Text(
        '默认',
        style: TextStyle(
          color: const Color.fromRGBO(34, 131, 226, 1),
          fontSize: 10.w,
          fontWeight: FontWeight.w400,
          height: 1.6,
        ),
      ),
    );
  }

  /// [姓名] 显示widget
  Widget _buildNameWidget(String name) {
    return Container(
      constraints: BoxConstraints(maxWidth: 140.w),
      child: Text(
        name,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
          fontSize: 17.w,
          fontWeight: Platform.isAndroid ? FontWeight.w500 : FontWeight.bold,
          color: const Color.fromRGBO(0, 0, 0, 0.8),
          height: 1.41,
        ),
      ),
    );
  }

  /// [手机号] 显示widget
  Widget _buildPhoneWidget(String mobile) {
    return Text(
      StringUtils.handleFormatPhoneNumber(mobile),
      style: TextStyle(
        fontSize: 16.w,
        fontWeight: FontWeight.w400,
        color: const Color.fromRGBO(0, 0, 0, 0.39),
        height: 1.375,
      ),
    );
  }

  /// 构造地址卡片
  Widget _buildAddressCard(int index) {
    List<UserAddress> _addressList =
        Provider.of<PersonalAddressViewModel>(context, listen: false)
            .addressList;
    return GestureDetector(
      onTap: () async {
        Provider.of<PersonalAddressViewModel>(context, listen: false)
            .editAddress(context, index);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.w),
        padding: EdgeInsets.all(12.w),
        height: 96.w,
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 255, 255, 0.76),
          borderRadius: BorderRadius.all(Radius.circular(12.w)),
        ),
        child: Column(
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    _buildNameWidget(_addressList[index].receiverName),
                    Padding(padding: EdgeInsets.only(left: 8.w)),
                    _buildPhoneWidget(_addressList[index].receiverMobile),
                    Padding(padding: EdgeInsets.only(left: 8.w)),
                    _addressList[index].isDefault
                        ? _buildDefaultContainer()
                        : Container(),
                  ],
                ),
                Image.asset(
                  'assets/images/edit.png',
                  package: 'personal_information',
                  width: 20.w,
                  height: 20.w,
                ),
              ],
            ),
            Padding(padding: EdgeInsets.only(top: 4.w)),
            SizedBox(
              width: double.infinity,
              child: Text(
                _addressList[index].province +
                    ' ' +
                    _addressList[index].city +
                    ' ' +
                    _addressList[index].district +
                    ' ' +
                    (_addressList[index].line1.isNotEmpty
                        ? _addressList[index].line1
                        : _addressList[index].line2),
                maxLines: 2,
                softWrap: true,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.left,
                style: TextStyle(
                  fontSize: 15.w,
                  color: const Color.fromRGBO(0, 0, 0, 0.8),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// [新建地址] 按钮
  Widget _buildCreateAddressButton() {
    return GestureDetector(
      onTap: () async {
        Provider.of<PersonalAddressViewModel>(context, listen: false)
            .addNewAddress(context);
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color.fromRGBO(34, 131, 226, 1),
          borderRadius: BorderRadius.all(Radius.circular(24.w)),
        ),
        width: 343.w,
        height: 46.w,
        child: Center(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Image.asset(
                'assets/images/createAddress.png',
                package: 'personal_information',
                width: 20.w,
                height: 20.w,
              ),
              Padding(padding: EdgeInsets.only(left: 4.w)),
              Text(
                '新建地址',
                style: TextStyle(
                  fontSize: 17.w,
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                  height: 1.3,
                  decoration: TextDecoration.none,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// [无地址信息] widget
  Widget _buildEmptyAddressTip() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Image.asset(
            'assets/images/emptyAddress.png',
            package: 'personal_information',
            width: 72.w,
            height: 72.w,
          ),
          Padding(padding: EdgeInsets.only(top: 16.w)),
          Text(
            '暂无地址',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 15.w,
              fontWeight: FontWeight.w400,
              color: const Color.fromRGBO(0, 0, 0, 0.39),
            ),
          ),
        ],
      ),
    );
  }

  void _updateAppBarBrightness() {
    Future<void>.delayed(const Duration(milliseconds: 0), () {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
      );
    });
  }

  String get pageName => 'AddressPage';

  void onPageShow() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'address--onPageShow'));
    _updateAppBarBrightness();
  }

  void onPageHide() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'address--onPageHide'));
  }

  void onPageDestroy() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'address--onPageDestroy'));
    CustomToast.cancelToast();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height,
      ),
      designSize: const Size(375, 667),
      orientation: Orientation.portrait,
    );
    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Stack(
          children: <Widget>[
            Scaffold(
              key: addressListKey,
              extendBodyBehindAppBar: true,
              appBar: MyAppBar(
                title: '我的地址',
                handleBackEvent: () =>
                    Vdn.close(result: <String, String>{'res': 'close'}),
              ),
              body: Container(
                margin: EdgeInsets.only(top: 16.w),
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  bottom: 90.w,
                ),
                color: const Color.fromRGBO(245, 245, 245, 1),
                child: Consumer<PersonalAddressViewModel>(
                  builder: (BuildContext context,
                      PersonalAddressViewModel provider, Widget? child) {
                    return provider.addressList.length == 0
                        ? _buildEmptyAddressTip()
                        : ListView.builder(
                            physics: const ClampingScrollPhysics(),
                            itemCount:
                                Provider.of<PersonalAddressViewModel>(context)
                                    .addressList
                                    .length,
                            itemBuilder: (BuildContext context, int index) {
                              return _buildAddressCard(index);
                            },
                          );
                  },
                ),
              ),
            ),
            Positioned(
              bottom: 20.w,
              left: 16.w,
              child: _buildCreateAddressButton(),
            ),
          ],
        ));
  }
}
