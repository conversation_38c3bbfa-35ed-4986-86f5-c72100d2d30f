import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:personal_information/utils/toast.dart';
import 'package:personal_information/viewmodels/user_input_view_model.dart';
import 'package:personal_information/widgets/appbar.dart';
import 'package:personal_information/widgets/textFiled.dart';
import 'package:provider/provider.dart';
import 'package:vdn/vdn.dart';

import '../viewmodels/user_nickname_view_model.dart';

GlobalKey nicknameKey = GlobalKey(debugLabel: 'nickname');

/// 昵称页面
class NickNamePage extends StatefulWidget {
  const NickNamePage({
    Key? key,
    required this.nickname,
    required this.mobile,
  }) : super(key: key);

  /// 传入的昵称
  final String nickname;

  /// 传入的手机号
  final String mobile;

  @override
  _NickNamePage createState() => _NickNamePage();
}

class _NickNamePage extends State<NickNamePage> {
  final Color _textColorDisabled = const Color.fromRGBO(0, 0, 0, 0.39);
  final Color _textColor = AppSemanticColors.item.information.primary;

  @override
  void initState() {
    super.initState();
    final String text = widget.nickname.isEmpty
        ? widget.mobile.replaceFirst(RegExp(r'\d{4}'), '****', 3)
        : widget.nickname;
    _addInputEventListener(text);
    CustomToast.init(context);
  }

  String get pageName => 'NickNamePage';

  void onPageShow() {
    // 页面显示时的逻辑
  }

  void onPageHide() {
    // 页面隐藏时的逻辑
  }

  void onPageDestroy() {
    // 页面销毁时的逻辑
    CustomToast.cancelToast();
  }

  void _addInputEventListener(String text) {
    final UserInputViewModel userInputNicknameViewModel =
        Provider.of<UserInputViewModel>(context, listen: false);
    userInputNicknameViewModel.addInputEventListener(text);
  }

  /// 更新用户昵称
  void _updateUserNickName(String nickname) {
    final UserNicknameViewModel userNicknameViewModel =
        Provider.of<UserNicknameViewModel>(context, listen: false);
    userNicknameViewModel.updateUserNickName(nickname);
  }

  Widget _buildInputWidget(
      BuildContext context, UserInputViewModel userInputViewModel) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: MyTextFiled(
        height: 54,
        text: '昵称',
        placeholder: '请输入昵称',
        maxLines: 1,
        keyboardType: TextInputType.text,
        textController: userInputViewModel.textController,
        focusNode: userInputViewModel.focusNode,
        showBorder: false,
        isShowSuffix: userInputViewModel.textController.text.isNotEmpty &&
            userInputViewModel.focusNode.hasFocus,
        onSubmitted: (String value) {
          userInputViewModel.focusNode.unfocus();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height,
      ),
      designSize: const Size(375, 667),
      orientation: Orientation.portrait,
    );
    final UserInputViewModel userInputViewModel =
        Provider.of<UserInputViewModel>(context);
    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Scaffold(
          key: nicknameKey,
          appBar: MyAppBar(
            title: '编辑昵称',
            handleBackEvent: () =>
                Vdn.close(result: <String, String>{'res': 'close'}),
            leadingText: '取消',
            rightActions: <Widget>[
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  if (!userInputViewModel.checkInputRule(context, '昵称', 16)) {
                    return;
                  }
                  if (!userInputViewModel.composingIsValid) {
                    userInputViewModel.focusNode.unfocus();
                    _updateUserNickName(userInputViewModel.textController.text);
                  }
                },
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '完成',
                      style: TextStyle(
                        fontSize: 17,
                        fontWeight: FontWeight.w400,
                        color: userInputViewModel.composingIsValid
                            ? _textColorDisabled
                            : _textColor,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Stack(
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  userInputViewModel.focusNode.unfocus();
                },
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: AppSemanticColors.background.secondary,
                ),
              ),
              _buildInputWidget(context, userInputViewModel),
            ],
          ),
        ));
  }
}
