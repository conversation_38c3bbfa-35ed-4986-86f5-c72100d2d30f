import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:personal_information/viewmodels/user_input_view_model.dart';
import 'package:personal_information/viewmodels/user_name_view_model.dart';
import 'package:personal_information/widgets/appbar.dart';
import 'package:personal_information/widgets/textFiled.dart';
import 'package:provider/provider.dart';
import 'package:vdn/vdn.dart';

import '../utils/constant.dart';
import '../utils/toast.dart';

GlobalKey nameKey = GlobalKey(debugLabel: 'name');

/// 姓名页面
class NamePage extends StatefulWidget {
  const NamePage({
    Key? key,
    required this.name,
  }) : super(key: key);

  /// 传入的姓名
  final String name;

  @override
  _NamePage createState() => _NamePage();
}

class _NamePage extends State<NamePage> {
  final Color _textColorDisabled = const Color.fromRGBO(0, 0, 0, 0.39);
  final Color _textColor = const Color(0xFF2283E2);

  @override
  void initState() {
    super.initState();
    _addInputEventListener(widget.name);
    CustomToast.init(context);
  }

  String get pageName => 'NamePage';

  void onPageShow() {
    // 页面显示时的逻辑
  }

  void onPageHide() {
    // 页面隐藏时的逻辑
  }

  void onPageDestroy() {
    // 页面销毁时的逻辑
    CustomToast.cancelToast();
  }

  void _addInputEventListener(String text) {
    final UserInputViewModel userInputNicknameViewModel =
        Provider.of<UserInputViewModel>(context, listen: false);
    userInputNicknameViewModel.addInputEventListener(text);
  }

  /// 更新用户昵称
  void _updateUserName(String name) {
    final UserNameViewModel userNameViewModel =
        Provider.of<UserNameViewModel>(context, listen: false);
    userNameViewModel.updateUserName(name);
  }

  Widget _buildInputWidget(
      BuildContext context, UserInputViewModel userInputViewModel) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: MyTextFiled(
        height: 54,
        text: '姓名',
        placeholder: '请输入姓名',
        maxLines: 1,
        keyboardType: TextInputType.text,
        textController: userInputViewModel.textController,
        focusNode: userInputViewModel.focusNode,
        showBorder: false,
        isShowSuffix: userInputViewModel.textController.text.isNotEmpty &&
            userInputViewModel.focusNode.hasFocus,
        onSubmitted: (String value) {
          userInputViewModel.focusNode.unfocus();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height,
      ),
      designSize: const Size(375, 667),
      orientation: Orientation.portrait,
    );
    final UserInputViewModel userInputViewModel =
        Provider.of<UserInputViewModel>(context);
    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Scaffold(
          key: nameKey,
          backgroundColor: AppSemanticColors.background.secondary,
          appBar: MyAppBar(
            title: '姓名',
            leadingText: '取消',
            handleBackEvent: () =>
                Vdn.close(result: <String, String>{'res': ''}),
            rightActions: <Widget>[
              InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                onTap: () {
                  if (!userInputViewModel.checkInputRule(context, '姓名', 32)) {
                    return;
                  }
                  if (!userInputViewModel.composingIsValid) {
                    userInputViewModel.focusNode.unfocus();
                    _updateUserName(userInputViewModel.textController.text);
                  }
                },
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.only(right: 20),
                    child: Text(
                      '完成',
                      style: TextStyle(
                        fontFamily: CONSTANT.fontFamilyPingFangSCRegular,
                        fontSize: 17,
                        fontWeight: FontWeight.w400,
                        color: userInputViewModel.composingIsValid
                            ? _textColorDisabled
                            : _textColor,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Stack(
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  userInputViewModel.focusNode.unfocus();
                },
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: MediaQuery.of(context).size.height,
                  color: Colors.transparent,
                ),
              ),
              _buildInputWidget(context, userInputViewModel),
            ],
          ),
        ));
  }
}
