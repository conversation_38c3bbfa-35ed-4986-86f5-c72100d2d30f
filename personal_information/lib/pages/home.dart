import 'package:extended_image/extended_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:log/log.dart';
import 'package:log/log_modle.dart';
import 'package:provider/provider.dart';
import 'package:trace/trace.dart';
import 'package:uplustrace/uplustrace.dart';
import 'package:vdn/vdn.dart';

import '../models/avatar_picker_model.dart';
import '../utils/StringUtils.dart';
import '../utils/constant.dart';
import '../utils/networkStatus.dart';
import '../utils/toast.dart';
import '../utils/util.dart';
import '../utils/vdn.dart';
import '../viewmodels/pop_ups_view_model.dart';
import '../viewmodels/user_info_view_model.dart';
import '../widgets/appbar.dart';
import '../widgets/bold_text_style.dart';
import '../widgets/date_picker.dart';

const double _avatarSize = 56.0;
GlobalKey homePageKey = GlobalKey(debugLabel: 'homePage');

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  _HomePage createState() => _HomePage();
}

class _HomePage extends State<HomePage> with SingleTickerProviderStateMixin {
  // 动画持续时间
  final int _animationTime = 300;
  // 动画控制器
  AnimationController? _control;
  // 动画移动
  Animation<double>? _trasl;

  final Dialogs dialogs = Dialogs();

  @override
  void initState() {
    super.initState();
    UplusTrace.startTrack(CONSTANT.TRACE_USER_PROFILE);

    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      UplusTrace.finishTrack(CONSTANT.TRACE_USER_PROFILE);
    });
    CustomToast.init(context);

    _control = AnimationController(
      duration: Duration(milliseconds: _animationTime),
      vsync: this,
    )..addStatusListener((AnimationStatus status) {
        if (status == AnimationStatus.completed) {
          // AnimationStatus.completed 表示动画正向开始，在结束时停止的状态
        } else if (status == AnimationStatus.dismissed) {
          // AnimationStatus.dismissed 表示动画反向开始，在结束时停止的状态
        }
      });

    _trasl = Tween<double>(
      begin: -200,
      end: 0,
    ).animate(_control!);
    _initUserInfoData();
  }

  void _initUserInfoData() {
    UserInfoViewModel userInfoViewModel =
        Provider.of<UserInfoViewModel>(context, listen: false);
    userInfoViewModel.fetchUserInfo();
  }

  String get pageName => 'HomePage';

  void onPageShow() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'home--onPageShow'));
    _updateAppBarBrightness();
  }

  void onPageHide() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'home--onPageHide'));
  }

  void onPageDestroy() {
    Log.printLog(
        LogModle('debug', '[personal_information]', 'home--onPageDestroy'));
    CustomToast.cancelToast();
    _control?.dispose();
  }

  /// 构建头像widget
  Widget _buildAvatarWidget() {
    return PressableOverlayWithTapWidget(
        overlayClick: () async {
          // 点击头像打点
          Trace.traceEvent(eventId: 'MB17911');
          NetworkStatus.checkConnectionStatus(homePageKey.currentState!.context)
              .then((bool value) {
            if (!value) {
              return;
            }
            avatarClickEvent(context);
          });
        },
        child: Container(
          height: 88,
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '头像',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppSemanticColors.item.primary,
                ),
              ),
              Row(
                children: <Widget>[
                  // 头像
                  ClipOval(
                    child: Provider.of<UserInfoViewModel>(context)
                            .avatarUrl
                            .isNotEmpty
                        ? ExtendedImage.network(
                            Provider.of<UserInfoViewModel>(context).avatarUrl,
                            fit: BoxFit.fill,
                            width: _avatarSize,
                            height: _avatarSize,
                            loadStateChanged: (ExtendedImageState state) {
                            switch (state.extendedImageLoadState) {
                              case LoadState.loading:
                                return const _DefaultAvatar(); // loading占位图
                              case LoadState.failed: // 加载失败情况
                                return const _DefaultAvatar();
                              case LoadState.completed:
                                return null;
                            }
                          })
                        : const _DefaultAvatar(),
                  ),
                  const SizedBox(width: 4),
                  // 右箭头
                  Image.asset(
                    'assets/images/right-arrow.webp',
                    width: 16,
                    height: 16,
                    package: 'personal_information',
                  ),
                ],
              ),
            ],
          ),
        ));
  }

  /// 构建显示文字widget--（昵称/姓名/地址/性别/生日）
  Widget _buildProfileTextWidget({
    required String leftText,
    required String rightText,
    bool showBottomBorder = true,
  }) {
    return PressableOverlayWithTapWidget(
      overlayClick: () async {
        if (!(await NetworkStatus.checkConnectionStatus(
            homePageKey.currentState!.context))) {
          return;
        }
        if (!mounted) {
          return;
        }
        UserInfoViewModel userInfoViewModel =
            Provider.of<UserInfoViewModel>(context, listen: false);
        switch (leftText) {
          case '昵称':
            if (userInfoViewModel.nickname == '' &&
                userInfoViewModel.mobile == '') {
              return;
            }
            Trace.traceEvent(eventId: 'MB17912');
            Vdn.goToPage(
              'flutter://personal/nickname',
              params: <String, dynamic>{
                'nickname': userInfoViewModel.nickname,
                'mobile': userInfoViewModel.mobile,
              },
            ).then((Map<dynamic, dynamic> value) {
              if (value['res'] == 'close') {
                _initUserInfoData();
              }
            });
            break;
          case '姓名':
            Trace.traceEvent(eventId: 'MB17921');
            Vdn.goToPage(
              'flutter://personal/name',
              params: <String, dynamic>{
                'name': userInfoViewModel.givenName,
              },
            ).then((Map<dynamic, dynamic> value) {
              if (value['res'] == 'close') {
                _initUserInfoData();
              }
            });
            break;
          case '性别':
            Trace.traceEvent(eventId: 'MB17914');
            if (!mounted) {
              return;
            }
            genderClickEvent(context);
            break;
          case '生日':
            Trace.traceEvent(eventId: 'MB17915');
            if (!mounted) {
              return;
            }
            birthdayClickEvent(context);
            break;
          default:
            break;
        }
      },
      child: Container(
        height: 54,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(
              leftText,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            Row(
              children: <Widget>[
                Container(
                  padding: const EdgeInsets.only(left: 16),
                  constraints: const BoxConstraints(
                      maxWidth: 230), // 限制显示最大宽度220，超出显示省略号
                  child: Text(
                    rightText,
                    textAlign: TextAlign.right,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppSemanticColors.item.secWeaken,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                // 右箭头
                Image.asset(
                  'assets/images/right-arrow.webp',
                  width: 16,
                  height: 16,
                  package: 'personal_information',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 按钮公共组件
  Widget _buildButton(
      {required String text,
      bool isRadius = false,
      double? height,
      Color? bgColor}) {
    return Container(
      height: height ?? 44,
      width: double.infinity,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius:
            isRadius ? const BorderRadius.all(Radius.circular(16)) : null,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamilyFallback: fontFamilyFallback(),
          decoration: TextDecoration.none,
        ),
      ),
    );
  }

  /// 选择底部弹窗背景组件
  Widget _buildModalBgWidget() {
    return Positioned(
      child: GestureDetector(
        onTap: () {
          // 动画反向移动
          _control!.reverse();
          if (Provider.of<PopUpsViewModel>(context, listen: false).showGender) {
            Provider.of<UserInfoViewModel>(context, listen: false)
                .resetTemGender();
          }
          Future<void>.delayed(
            Duration(milliseconds: _animationTime),
            () {
              Provider.of<PopUpsViewModel>(context, listen: false)
                  .setHidePopUps();
            },
          );
        }, // 动画反向移动
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: double.infinity,
          color: Colors.black.withOpacity(0.5),
        ),
      ),
    );
  }

  /// 选择底部弹窗
  Widget _buildModalWidget() {
    return AnimatedBuilder(
      animation: _control!,
      builder: (BuildContext context, Widget? child) {
        return Positioned(
          bottom: _trasl!.value,
          left: 0,
          child: GestureDetector(
            onTap: () {}, // 动画反向移动
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom,
              ),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _updateAppBarBrightness() {
    Future<void>.delayed(const Duration(milliseconds: 0), () {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
      );
    });
  }

// 点击生日
  void birthdayClickEvent(BuildContext context) {
    final UserInfoViewModel viewModel =
        Provider.of<UserInfoViewModel>(context, listen: false);
    dialogs.showDoubleBtnModal<void>(
      context: context,
      enableDrag: true,
      title: '生日',
      confirmText: '确定',
      confirmCallback: () async {
        NetworkStatus.checkConnectionStatus(homePageKey.currentState!.context)
            .then((bool value) {
          if (!value) {
            return;
          }
          viewModel.updateUserBirthday(viewModel.tempDate);
        });
      },
      child: (BuildContext context) {
        return CupertinoTheme(
          data: CupertinoThemeData(
            textTheme: CupertinoTextThemeData(
              dateTimePickerTextStyle: TextStyle(
                fontSize: 16,
                color: AppSemanticColors.item.primary,
              ),
            ),
          ),
          child: SizedBox(
            height: 244,
            child: CustomCupertinoDatePicker(
              mode: CustomCupertinoDatePickerMode.date,
              maximumDate: DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
                DateTime.now().hour,
                DateTime.now().minute,
                DateTime.now().second,
                999,
                999,
              ),
              minimumYear: 1900,
              initialDateTime: DateTime(
                StringUtils.formatBirthday(viewModel.birthday)[0],
                StringUtils.formatBirthday(viewModel.birthday)[1],
                StringUtils.formatBirthday(viewModel.birthday)[2],
              ),
              onDateTimeChanged: (DateTime value) {
                final String month = StringUtils.formatMonthAndDay(value.month);
                final String day = StringUtils.formatMonthAndDay(value.day);
                viewModel.setTempDate('${value.year}-$month-$day');
              },
            ),
          ),
        );
      },
    );
  }

// 点击头像
  void avatarClickEvent(BuildContext context) {
    final UserInfoViewModel viewModel =
        Provider.of<UserInfoViewModel>(context, listen: false);
    dialogs.showSingleBtnModal<void>(
      context: context,
      enableDrag: true,
      child: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            GestureDetector(
              onTap: () {
                Vdn.goToPage("flutter://photocheck", params: <String, dynamic>{
                  "max": '1',
                  "clipPicParam": "0",
                }).then((Map<dynamic, dynamic> value) {
                  final PhotoCheckModel photoCheckModel =
                      PhotoCheckModel.fromJSON(value);
                  if (photoCheckModel.data.isNotEmpty) {
                    final String path = PersonalInfoUtils.convertType<String>(
                        photoCheckModel.data[0].path, '');
                    viewModel.uploadAvatar(path);
                  }
                  dialogs.closeSmartHomeModalBottomSheet();
                });
              },
              child: _buildButton(
                  text: '相册',
                  isRadius: true,
                  height: 54,
                  bgColor: AppSemanticColors.background.primary),
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () async {
                Vdn.goToPage('flutter://cameraentry', params: <String, dynamic>{
                  'clipPicParam': '0',
                }).then((Map<dynamic, dynamic> value) async {
                  final CameraEntryModel cameraEntryModel =
                      CameraEntryModel.fromJSON(value);
                  if (cameraEntryModel.retCode == '000000') {
                    final String filePath = cameraEntryModel.retData.data;
                    viewModel.uploadAvatar(filePath);
                  }
                  dialogs.closeSmartHomeModalBottomSheet();
                });
              },
              child: _buildButton(
                  text: '拍照',
                  isRadius: true,
                  height: 54,
                  bgColor: AppSemanticColors.background.primary),
            ),
            const SizedBox(height: 8),
          ],
        );
      },
      btnText: '取消',
      btnType: ButtonType.secondary,
      callback: () {},
    );
  }

  void genderClickEvent(BuildContext context) {
    final UserInfoViewModel viewModel =
        Provider.of<UserInfoViewModel>(context, listen: false);
    viewModel.resetTemGender();
    final int selectedGender = viewModel.tempGender;
    dialogs.showDoubleBtnModal<void>(
      context: context,
      enableDrag: true,
      title: '性别',
      confirmText: '确定',
      confirmCallback: () async {
        NetworkStatus.checkConnectionStatus(homePageKey.currentState!.context)
            .then((bool value) {
          if (!value) {
            return;
          }
          final int tempGender = viewModel.tempGender;
          viewModel.updateUserGender(tempGender == 0 ? '男' : '女');
        });
      },
      child: (BuildContext context) {
        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 12),
          height: 244,
          child: CupertinoPicker(
            itemExtent: 44,
            selectionOverlay: Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            backgroundColor: Colors.transparent,
            scrollController:
                FixedExtentScrollController(initialItem: selectedGender),
            onSelectedItemChanged: (int value) {
              viewModel.setTempGender(value);
            },
            children: <Widget>[
              _buildButton(text: '男', isRadius: true),
              _buildButton(text: '女', isRadius: true),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent,
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark),
      child: Stack(
        children: <Widget>[
          Scaffold(
              key: homePageKey,
              backgroundColor: AppSemanticColors.background.secondary,
              appBar:
                  MyAppBar(title: '个人资料', handleBackEvent: () => VDN.close()),
              body: DefaultTextStyle(
                style: TextStyle(
                  color: AppSemanticColors.item.primary,
                  fontFamilyFallback: fontFamilyFallback(),
                ),
                child: Container(
                  margin: const EdgeInsets.only(top: 8, left: 16, right: 16),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(16)),
                    color: AppSemanticColors.component.secondary.invert,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      _buildAvatarWidget(),
                      Consumer<UserInfoViewModel>(
                        builder: (BuildContext context,
                                UserInfoViewModel provider, Widget? child) =>
                            _buildProfileTextWidget(
                          leftText: '昵称',
                          rightText: provider.nickname.isEmpty
                              ? provider.mobile.isEmpty
                                  ? ''
                                  : provider.mobile.replaceFirst(
                                      new RegExp(r'\d{4}'), '****', 3)
                              : provider.nickname,
                        ),
                      ),
                      Consumer<UserInfoViewModel>(
                        builder: (BuildContext context,
                                UserInfoViewModel provider, Widget? child) =>
                            _buildProfileTextWidget(
                          leftText: '姓名',
                          rightText: provider.givenName.isEmpty
                              ? '未设置'
                              : provider.givenName,
                        ),
                      ),
                      Consumer<UserInfoViewModel>(
                        builder: (BuildContext context,
                                UserInfoViewModel provider, Widget? child) =>
                            _buildProfileTextWidget(
                          leftText: '性别',
                          rightText: StringUtils.convertGender(provider.gender),
                        ),
                      ),
                      Consumer<UserInfoViewModel>(
                        builder: (BuildContext context,
                                UserInfoViewModel provider, Widget? child) =>
                            _buildProfileTextWidget(
                          leftText: '生日',
                          rightText: provider.birthday.isEmpty
                              ? '未设置'
                              : provider.birthday,
                          showBottomBorder: false,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
          Consumer<PopUpsViewModel>(
            builder: (BuildContext context, PopUpsViewModel provider,
                    Widget? child) =>
                provider.showAvatar ||
                        provider.showBirthday ||
                        provider.showGender
                    ? _buildModalBgWidget()
                    : Container(),
          ),
          Consumer<PopUpsViewModel>(
            builder: (BuildContext context, PopUpsViewModel provider,
                    Widget? child) =>
                provider.showAvatar ||
                        provider.showBirthday ||
                        provider.showGender
                    ? _buildModalWidget()
                    : Container(),
          ),
        ],
      ),
    );
  }
}

class _DefaultAvatar extends StatelessWidget {
  const _DefaultAvatar({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/images/avatar.png',
      package: CONSTANT.packageName,
      fit: BoxFit.cover,
      width: _avatarSize,
      height: _avatarSize,
    );
  }
}
