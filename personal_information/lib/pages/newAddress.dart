import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_ui/flutter_common_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:personal_information/utils/StringUtils.dart';
import 'package:personal_information/utils/constant.dart';
import 'package:personal_information/utils/vdn.dart';
import 'package:personal_information/viewmodels/address_info_view_model.dart';
import 'package:personal_information/viewmodels/controller_input_view_model.dart';
import 'package:personal_information/viewmodels/show_input_view_model.dart';
import 'package:personal_information/viewmodels/user_info_view_model.dart';
import 'package:personal_information/widgets/appbar.dart';
import 'package:personal_information/widgets/city_picker.dart';
import 'package:personal_information/widgets/city_picker_result.dart';
import 'package:personal_information/widgets/custom_switch.dart';
import 'package:personal_information/widgets/textFiled.dart';
import 'package:provider/provider.dart';
import 'package:vdn/vdn.dart';

import '../utils/toast.dart';
import '../viewmodels/address_list_view_model.dart';

GlobalKey newAddressKey = GlobalKey(debugLabel: 'newAddress');

/// 新建&编辑地址页面
class NewAddressPage extends StatefulWidget {
  const NewAddressPage({
    Key? key,
    required this.title,
    required this.addressId,
    required this.isDefault,
    required this.receiverMobile,
    required this.receiverName,
    required this.provinceName,
    required this.provinceId,
    required this.cityName,
    required this.cityId,
    required this.countyName,
    required this.countyId,
    required this.line1,
    required this.line2,
  }) : super(key: key);

  /// 页面显示标题
  final String title;

  /// 地址ID
  final String addressId;

  /// 是否为默认地址
  final String isDefault;

  /// 联系人手机号
  final String receiverMobile;

  /// 联系人姓名
  final String receiverName;

  /// 省份名
  final String provinceName;

  /// 省份ID
  final String provinceId;

  /// 市名
  final String cityName;

  /// 市ID
  final String cityId;

  /// 区名
  final String countyName;

  /// 区ID
  final String countyId;

  /// 街道1
  final String line1;

  /// 街道2
  final String line2;

  @override
  _NewAddressPage createState() {
    return _NewAddressPage();
  }
}

class _NewAddressPage extends State<NewAddressPage> {
  @override
  void initState() {
    super.initState();
    CustomToast.init(context);
    // 联系人输入事件监听
    if (widget.title == '编辑地址') {
      _initEditData();
    }
    _initData();
  }

  String get pageName => 'NewAddressPage';

  void onPageShow() {
    // 页面显示时的逻辑
  }

  void onPageHide() {
    // 页面隐藏时的逻辑
  }

  void onPageDestroy() {
    // 页面销毁时的逻辑
    CustomToast.cancelToast();
  }

  void _initEditData() {
    // fix error: setState() or markNeedsBuild() called during build.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AddressInfoViewModel addressInfoViewModel =
          Provider.of<AddressInfoViewModel>(context, listen: false);
      addressInfoViewModel
          .setIsDefaultAddress(widget.isDefault == 'true' ? true : false);
      final CityResult result = new CityResult();
      result.province = widget.provinceName;
      result.provinceId = widget.provinceId;
      result.city = widget.cityName;
      result.cityId = widget.cityId;
      result.county = widget.countyName;
      result.countyId = widget.countyId;
      addressInfoViewModel.setAddressInfo(result);
    });
    // 编辑地址时需要设置默认值
    ControllerInputViewModel controllerInputViewModel =
        Provider.of<ControllerInputViewModel>(context, listen: false);
    controllerInputViewModel.initText(
        widget.receiverName,
        StringUtils.handleFormatPhoneNumber(widget.receiverMobile),
        widget.provinceName + ' ' + widget.cityName + ' ' + widget.countyName,
        widget.line1);
  }

  void _initData() {
    ControllerInputViewModel controllerInputViewModel =
        Provider.of<ControllerInputViewModel>(context, listen: false);
    controllerInputViewModel.initAddListener(context);
    UserInfoViewModel userInfoViewModel =
        Provider.of<UserInfoViewModel>(context, listen: false);
    userInfoViewModel.fetchUserInfo();
    AddressListViewModel addressListViewModel =
        Provider.of<AddressListViewModel>(context, listen: false);
    addressListViewModel.getAddressList();
  }

  /// [确认删除]对话框背景
  Widget _showDeleteConfirmDialogBg() {
    return Positioned(
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
      ),
    );
  }

  /// [确认删除]对话框
  Widget _showDeleteConfirmDialog() {
    return Positioned(
      top: (MediaQuery.of(context).size.height - 142.w) / 2,
      left: (MediaQuery.of(context).size.width - 270.w) / 2,
      child: Container(
        width: 270.w,
        height: 142.w,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        child: Column(
          children: <Widget>[
            Container(
              padding: EdgeInsets.all(16.w),
              height: 94.w,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Column(
                children: <Widget>[
                  Text(
                    '提示',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 17.w,
                      fontWeight: FontWeight.w400,
                      color: const Color.fromRGBO(0, 0, 0, 0.8),
                      decoration: TextDecoration.none,
                    ),
                  ),
                  Padding(padding: EdgeInsets.only(top: 12.w)),
                  Text(
                    '您确定要删除该地址吗？',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 12.w,
                      fontWeight: FontWeight.w400,
                      color: const Color.fromRGBO(0, 0, 0, 0.6),
                      decoration: TextDecoration.none,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.07),
                  ),
                ),
              ),
            ),
            Container(
              height: 46.w,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      Provider.of<AddressInfoViewModel>(context, listen: false)
                          .setIshowDeleteModal(false);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 12.w),
                      width: 135.w,
                      decoration: const BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            width: 0.5,
                            color: Color.fromRGBO(0, 0, 0, 0.07),
                          ),
                        ),
                      ),
                      child: Text(
                        '取消',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 17.w,
                          color: const Color.fromRGBO(0, 0, 0, 0.6),
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      Provider.of<AddressInfoViewModel>(context, listen: false)
                          .deleteMyAddress(context, widget.addressId);
                      // _deleteMyAddress();
                    },
                    child: Container(
                      width: 135.w,
                      padding: EdgeInsets.symmetric(vertical: 12.w),
                      child: Text(
                        '确定',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 17.w,
                          color: const Color.fromRGBO(34, 131, 226, 1),
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// [默认地址] widget
  Widget _buildDefaultAddressWidget() {
    return Container(
      height: 44,
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        top: 6,
        bottom: 6,
      ),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '设为默认地址',
            style: TextStyle(
              fontSize: 15.w,
              fontWeight: FontWeight.w400,
              color: const Color.fromRGBO(0, 0, 0, 0.8),
              height: 1.33,
            ),
          ),
          // 自定义滑动开关，适配UI规范
          CustomSwitch(
              isSwitch:
                  Provider.of<AddressInfoViewModel>(context).isDefaultAddress,
              activeColor: const Color.fromRGBO(34, 131, 226, 1),
              inactiveColor: Colors.grey.shade300,
              onChanged: widget.isDefault == 'false'
                  ? (bool value) {
                      Provider.of<AddressInfoViewModel>(context, listen: false)
                          .setIsDefaultAddress(value);
                    }
                  : null)
        ],
      ),
    );
  }

  /// [保存] 按钮
  Widget _buildSaveButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Debounce.run(() {
          Provider.of<AddressInfoViewModel>(context, listen: false)
              .createAddress(context, widget.title, widget.addressId);
        });
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 12.w),
        height: 46.w,
        width: 351.w,
        decoration: BoxDecoration(
          color: const Color.fromRGBO(24, 131, 226, 1),
          borderRadius: BorderRadius.all(Radius.circular(24.w)),
        ),
        child: Center(
          child: Text(
            '保存',
            style: TextStyle(
              fontSize: 17.w,
              color: Colors.white,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.none,
            ),
          ),
        ),
      ),
    );
  }

  /// [地址选择]弹窗
  Future<void> _handleChecCityPicker() async {
    AddressInfoViewModel addressInfoViewModel =
        Provider.of<AddressInfoViewModel>(context, listen: false);
    CityResult? result = await showCityPicker(context,
        initCity: CityResult()
          ..province = addressInfoViewModel.provinceName.isEmpty
              ? widget.provinceName
              : addressInfoViewModel.provinceName
          ..provinceId = addressInfoViewModel.provinceId.isEmpty
              ? widget.provinceId
              : addressInfoViewModel.provinceId
          ..city = addressInfoViewModel.cityName.isEmpty
              ? widget.cityName
              : addressInfoViewModel.cityName
          ..cityId = addressInfoViewModel.cityId.isEmpty
              ? widget.cityId
              : addressInfoViewModel.cityId
          ..county = addressInfoViewModel.countyName.isEmpty
              ? widget.countyName
              : addressInfoViewModel.countyName
          ..countyId = addressInfoViewModel.countyId.isEmpty
              ? widget.countyId
              : addressInfoViewModel.countyId);
    // 点击取消按钮
    // ignore: unnecessary_null_comparison
    if (result == null) {
      return;
    }
    addressInfoViewModel.setAddressInfo(result);

    if (mounted) {
      Provider.of<ControllerInputViewModel>(context, listen: false)
          .setAreaText("${result.province} ${result.city} ${result.county}");
    }
  }

  /// [我的地址] 输入表单
  Widget _buildAddressForm(BuildContext context) {
    ControllerInputViewModel controllerInputViewModel =
        Provider.of<ControllerInputViewModel>(context);
    return Container(
      height: widget.title == '编辑地址' ? 315.w : 270.w,
      margin: EdgeInsets.only(top: 8.w),
      child: ListView.builder(
        physics: const ClampingScrollPhysics(),
        itemCount: 1,
        itemBuilder: (BuildContext context, int index) {
          return Column(
            children: <Widget>[
              MyTextFiled(
                height: 44.w,
                text: '联系人',
                placeholder: '名字',
                maxLines: 1,
                keyboardType: TextInputType.text,
                textController: controllerInputViewModel.contactNameController,
                focusNode: controllerInputViewModel.contactNamefocusNode,
                showBorder: true,
                isShowSuffix: controllerInputViewModel
                        .contactNameController.text.isNotEmpty &&
                    controllerInputViewModel.contactNamefocusNode.hasFocus,
                onTap: () {
                  AddressInputViewModel addressInputViewModel =
                      Provider.of<AddressInputViewModel>(context,
                          listen: false);
                  addressInputViewModel.setAllInput(true, false, false);
                },
                onSubmitted: (String value) {
                  controllerInputViewModel.checkContactNameValid(context);
                },
              ),
              MyTextFiled(
                height: 44.w,
                text: '手机号',
                placeholder: '手机号',
                maxLines: 1,
                keyboardType: TextInputType.number,
                textController: controllerInputViewModel.phoneNumberController,
                focusNode: controllerInputViewModel.phoneNumberfocusNode,
                showBorder: true,
                inputFormatters: <TextInputFormatter>[
                  // ignore: deprecated_member_use
                  FilteringTextInputFormatter.digitsOnly,
                  controllerInputViewModel.mobileFormatter,
                  LengthLimitingTextInputFormatter(13),
                ],
                isShowSuffix: controllerInputViewModel
                        .phoneNumberController.text.isNotEmpty &&
                    controllerInputViewModel.phoneNumberfocusNode.hasFocus,
                onTap: () {
                  AddressInputViewModel addressInputViewModel =
                      Provider.of<AddressInputViewModel>(context,
                          listen: false);
                  addressInputViewModel.setAllInput(false, true, false);
                },
                onSubmitted: (String value) {
                  controllerInputViewModel.phoneNumberfocusNode.unfocus();
                  controllerInputViewModel.checkPhoneNumberValid(context);
                },
              ),
              MyTextFiled(
                height: 44.w,
                text: '所在地区',
                placeholder: '省/市/区',
                maxLines: 1,
                keyboardType: TextInputType.text,
                textController: controllerInputViewModel.areaController,
                focusNode: controllerInputViewModel.areafocusNode,
                showBorder: true,
                readOnly: true,
                isShowSuffix: false,
                onTap: () => _handleChecCityPicker(),
                onSubmitted: (String value) {
                  controllerInputViewModel.areafocusNode.unfocus();
                },
              ),
              MyTextFiled(
                height: 64.w,
                text: '详细地址',
                placeholder: '小区楼栋/乡村名称',
                maxLines: 2,
                keyboardType: TextInputType.multiline,
                textController:
                    controllerInputViewModel.detailAddressController,
                focusNode: controllerInputViewModel.detailAddressfocusNode,
                showBorder: widget.title == '编辑地址' ? true : false,
                isShowSuffix: controllerInputViewModel
                        .detailAddressController.text.isNotEmpty &&
                    controllerInputViewModel.detailAddressfocusNode.hasFocus,
                onTap: () {
                  AddressInputViewModel addressInputViewModel =
                      Provider.of<AddressInputViewModel>(context,
                          listen: false);
                  addressInputViewModel.setAllInput(false, false, true);
                },
                onSubmitted: (String value) {
                  controllerInputViewModel.detailAddressfocusNode.unfocus();
                  controllerInputViewModel.checkDetailAddressValid(context);
                },
              ),
              widget.title == '编辑地址'
                  ? _buildDefaultAddressWidget()
                  : Container(),
              Padding(padding: EdgeInsets.only(top: 20.w)),
              _buildSaveButton(context),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width,
        maxHeight: MediaQuery.of(context).size.height,
      ),
      designSize: const Size(375, 667),
      orientation: Orientation.portrait,
    );

    return AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarBrightness: Brightness.light,
          statusBarIconBrightness: Brightness.dark,
        ),
        child: Stack(
          children: <Widget>[
            Scaffold(
              key: newAddressKey,
              appBar: MyAppBar(
                  title: widget.title,
                  handleBackEvent: () =>
                      Vdn.close(result: <String, String>{'res': ''}),
                  rightActions: widget.title == '编辑地址'
                      ? <Widget>[
                          InkWell(
                            highlightColor: Colors.transparent,
                            splashColor: Colors.transparent,
                            onTap: () {
                              if (!Provider.of<AddressInfoViewModel>(context,
                                      listen: false)
                                  .showDeleteModal) {
                                Provider.of<AddressInfoViewModel>(context,
                                        listen: false)
                                    .setIshowDeleteModal(true);
                              }
                            },
                            child: Center(
                              child: Container(
                                padding: EdgeInsets.only(right: 12.w),
                                child: Text(
                                  '删除',
                                  style: TextStyle(
                                    fontFamily:
                                        CONSTANT.fontFamilyPingFangSCRegular,
                                    fontSize: 14.w,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(0, 0, 0, 0.93),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ]
                      : null),
              body: Stack(
                children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      FocusScopeNode currentFocus = FocusScope.of(context);
                      if (!currentFocus.hasPrimaryFocus &&
                          currentFocus.focusedChild != null) {
                        FocusManager.instance.primaryFocus!.unfocus();
                      }
                    },
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      color: Colors.transparent,
                    ),
                  ),
                  _buildAddressForm(context),
                ],
              ),
            ),
            Provider.of<AddressInfoViewModel>(context, listen: false)
                    .showDeleteModal
                ? _showDeleteConfirmDialogBg()
                : Container(),
            Provider.of<AddressInfoViewModel>(context, listen: false)
                    .showDeleteModal
                ? _showDeleteConfirmDialog()
                : Container(),
          ],
        ));
  }
}
