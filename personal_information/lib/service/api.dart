import 'dart:io';
import 'package:app_info/Appinfos.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:personal_information/service/httpService.dart';
import 'package:personal_information/utils/http.dart';
import 'package:user/user.dart';

class HTTP {
  /// 缓存文件路径
  static String _cacheFilePath = '';

  /// 获取区域列表json
  static Future getAddressList() async {
    final userinfo = await User.getUserInfo();
    final oauthdata = await User.getOauthData();
    final appinfo = await AppInfoPlugin.getAppInfo();
    final _timestamp = HttpUtils.getTimestamp();

    String sign = HttpUtils.getSHA256Sign(
      '/rcs/location/area-json',
      '',
      appinfo.appId,
      appinfo.appKey,
      _timestamp,
    );

    return await HttpManager.postData(
      'https://uws.haier.net/rcs/location/area-json?schemaVersion=v2',
      options: Options(
        contentType: Headers.jsonContentType,
        headers: {
          'timestamp': _timestamp,
          'sign': sign,
          'userId': userinfo.userId,
          'appId': appinfo.appId,
          'clientId': appinfo.clientId,
          'accessToken': oauthdata.uhome_access_token,
          'accountToken': oauthdata.user_center_access_token,
          'appVersion': appinfo.appVersion,
          'language': 'zh-cn',
          'timezone': 8,
          'sequenceId': _timestamp.toString(),
        },
      ),
      header: {},
    );
  }

  /// 下载区域地址json文件
  static Future downloadAddressFile(
      {required String url, required String savePath}) async {
    if (await _isFileExist(savePath)) {
      // 如果文件已经缓存，则不进行下载文件，使用缓存文件
      return;
    }
    return await HttpManager.downloadFile(url: url, savePath: savePath);
  }

  /// 获取存储文件路径
  static Future<String> getFilePath(String uniqueFileName) async {
    Directory appDir = await getApplicationSupportDirectory();
    String fullPath =
        appDir.path + '/cache_file_dir_appmine/' + '$uniqueFileName';

    return fullPath;
  }

  /// 查询文件是否存在
  static Future<bool> _isFileExist(String path) {
    File file = File(path);
    return file.exists();
  }

  /// 获取缓存文件路径
  static String getCacheFilePath() {
    return _cacheFilePath;
  }

  /// 设置缓存文件路径
  static void setCacheFilePath(String path) {
    _cacheFilePath = path;
  }
}
