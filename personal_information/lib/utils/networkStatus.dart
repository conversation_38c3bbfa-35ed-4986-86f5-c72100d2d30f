import 'package:network/isonline.dart';
import 'package:network/network.dart';
import 'package:flutter/material.dart';
import 'package:personal_information/utils/toast.dart';

class NetworkStatus {
  static Future<bool> checkConnectionStatus(BuildContext context) async {
    CustomToast.cancelToast();
    final IsOnline isOnline = await Network.isOnline();
    if (!isOnline.isOnline) {
      CustomToast.showToast(context, '网络不可用');
      return false;
    }
    return true;
  }
}
