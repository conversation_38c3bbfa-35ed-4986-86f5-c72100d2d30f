# 要求

    1. 所有回复都使用中文.
    2. 所有修改都尽量小，只修改必要的地方。
    3. 请扫描当前工程，根据以下修改规则进行修改。
    4. 不要增加额外注释，除非逻辑非常重要需要重点review；
    5. 不要有遗漏修改，全部修改完后需要重新编译工程，确保没有遗漏修改；
    6. 遇到需要执行flutter pub get的情况，无需我确认，直接执行即可。

# 目录扫描范围

只针对当前目录的以下13个目录进行扫描并在对应目录下修改，其他目录不处理：

```
./smart_home
./app_service
./app_mine
./library_widgets
./device_utils
./flutter_common_ui
./upservice
./wash_device_manager
./whole_house_music
./whole_house_air
./personal_information
./setting
./about_us
```

# 规则
根据以上目录扫描范围，逐个目录进行扫描并在对应目录下修改，其他目录不处理：

## 规则1：connectivity替换

1 当前目录下的pubspec.yaml中，若存在connectivity，则执行如下替换：

```
connectivity: x.x.x
```

改为：

```
  network:
    hosted:
      name: network
      url: http://**************:8083
  version: ">=0.0.1"
```

2 修改dart文件中引用路径：

```
import 'package:connectivity/connectivity.dart';
```

改为：

```
import 'package:network/isonline.dart';
import 'package:network/network.dart';
```

3 替换connectivity相关api使用：

3.1 若出现:

```
Connectivity().checkConnectivity().then((ConnectivityResult result) {
  // 有网络判断
  result != ConnectivityResult.none;
}
```

则替换为：

```
Network.isOnline().then((NetworkStatus networkStatus) {
  // 有网络判断
  networkStatus.isOnline;
});
```

3.2 若出现：

```
final ConnectivityResult connectivityResult = await Connectivity().checkConnectivity();
// 有网络判断
if (connectivityResult != ConnectivityResult.none) {

}
```

则替换为：

```
final NetworkStatus networkStatus = await Network.isOnline();
// 有网络判断
if (networkStatus.isOnline) {
  
} 
```

3.3 若出现：
```
_networkListener = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) async {
          // 有网判断
          result != ConnectivityResult.none;
        });
```

则上述订阅逻辑替换为：
```
void _networkStatusChangeCallback(NetworkStatus status) {
    bool isOnline = status.isOnline;
}

Network.addNetStateEventListener(_networkStatusChangeCallback);

```

解订阅逻辑:
```
_networkListener?.cancel();
```
替换为：
```
Network.removeNetStateEventListener(_networkStatusChangeCallback);
```


## 规则2：Appinfos替换

1. 当前目录下的pubspec.yaml中，若存在Appinfos引用，则执行如下替换：

```
Appinfos:
    hosted:
      name: Appinfos
      url: http://**************:8083
    version: ">=1.0.0"
```

改为：

```
app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: ">=1.0.0"
```

dart文件中修改Appinfos类路径：

```
import 'package:Appinfos/AppinfosModel.dart'; 
import 'package:Appinfos/Appinfos.dart'; 
```

改为：

```
import 'package:app_info/AppinfosModel.dart';
import 'package:app_info/Appinfos.dart';
```

## 规则3：flutter_picker替换

当前目录下的pubspec.yaml中，若存在flutter_picker，则执行如下替换：

```
flutter_picker: 2.0.3
```

改为：

```
flutter_picker_plus: 1.0.0
```

## 规则4：页面生命周期修改
针对该规则，只需扫描处理：以下两个目录即可，其他目录不处理：

```
./whole_house_air
./personal_infomation
```
检测：全局dart文件中是否存在didChangeAppLifecycleState：

```
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch(state) {
      case AppLifecycleState.resumed:
        onPageShow();
      case AppLifecycleState.hidden:
        onPageHide();
      case AppLifecycleState.detached:
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        break;
    }
  }
```

若存在didChangeAppLifecycleState使用，则进行以下逻辑扩展；若不存在则不处理：

1 当前class中 with UplusLifecycleMixin<当前类>
```
import 'package:flutter_common_ui/flutter_common_ui.dart';

class _AddressPage extends State<当前类> with UplusLifecycleMixin<当前类> {
  ...
}

```

2 当前class中重写UplusLifecycleMixin的3个方法：

```

  @override
  String get pageName => '当前类名';

  @override
  void onPageShow() {
    super.onPageShow();
    onPageShow();// 替换成：didChangeAppLifecycleState-AppLifecycleState.resumed的方法；
  }

  @override
  void onPageHide() {
    super.onPageHide();
    onPageHide();// 替换成：didChangeAppLifecycleState-AppLifecycleState.hidden的方法；
  }

  @override
  void onPageDestroy() {
    CustomToast.cancelToast(); // 替换成：didChangeAppLifecycleState.detached中的方法；
    super.onPageDestroy();
  }

```

3 删除didChangeAppLifecycleState相关代码

```
删除监听：WidgetsBindingObserver;
删除订阅调用：WidgetsBinding.instance.addObserver(this);
删除：didChangeAppLifecycleState方法：
删除解订阅调用：WidgetsBinding.instance.removeObserver(this);

```
