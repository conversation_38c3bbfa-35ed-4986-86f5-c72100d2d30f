#!/bin/bash

echo "开始修复Network.checkNetwork错误调用..."

# 定义需要修复的文件列表
files=(
    "./whole_house_air/lib/presentation/quick_manipulate_screen.dart"
    "./flutter_common_ui/lib/src/utils/util.dart"
    "./flutter_common_ui/lib/src/widgets/basic_widget/share_dialog.dart"
    "./flutter_common_ui/lib/src/widgets/carousel/carousel.dart"
    "./smart_home/lib/device/aggregation/utils/agg_utils.dart"
    "./smart_home/lib/device/aggregation/aggregation_card/view_model/aggregation_base_view_model.dart"
    "./about_us/lib/service/httpService.dart"
    "./setting/lib/pages/home.dart"
    "./library_widgets/lib/components/whole_house_card_scene_screen.dart"
    "./library_widgets/lib/components/whole_house_scene_screen.dart"
    "./library_widgets/lib/common/util.dart"
    "./app_mine/lib/app_mine.dart"
    "./app_mine/lib/utils/util.dart"
    "./app_mine/lib/list_menu/store/menu_middleware.dart"
    "./app_service/lib/app_service_presenter.dart"
    "./app_service/lib/app_service.dart"
    "./app_service/lib/my_devices/store/my_devices_middleware.dart"
    "./smart_home/lib/navigator/family/widget/family_pop_dialog.dart"
    "./smart_home/lib/device/aggregation/aggregation_detail/presenter/camera_presenter.dart"
    "./smart_home/lib/device/aggregation/aggregation_detail/widgets/aggregation_manage_list.dart"
    "./smart_home/lib/device/device_view_model/wash_device_view_model.dart"
    "./smart_home/lib/scene/store/scene_middleware.dart"
)

echo "发现需要修复的文件数量: ${#files[@]}"

# 使用sed批量替换
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "修复文件: $file"
        
        # 替换 Network.checkNetwork 为 Network.isOnline()
        sed -i '' 's/Network\.checkNetwork/Network.isOnline()/g' "$file"
        
        # 替换变量声明模式
        sed -i '' 's/final connectivityResult = await (Network\.isOnline());/final IsOnline isOnline = await Network.isOnline();/g' "$file"
        sed -i '' 's/final connectivityResult = await Network\.isOnline();/final IsOnline isOnline = await Network.isOnline();/g' "$file"
        sed -i '' 's/final IsOnline onValue = await Network\.isOnline();/final IsOnline isOnline = await Network.isOnline();/g' "$file"
        
        # 替换条件判断中的变量引用
        sed -i '' 's/connectivityResult\.isOnline/isOnline.isOnline/g' "$file"
        sed -i '' 's/onValue\.isOnline/isOnline.isOnline/g' "$file"
        
        echo "✓ 完成修复: $file"
    else
        echo "⚠ 文件不存在: $file"
    fi
done

echo ""
echo "批量修复完成！"
echo "注意: 某些文件可能仍需要手动添加import语句或处理类型定义问题"
