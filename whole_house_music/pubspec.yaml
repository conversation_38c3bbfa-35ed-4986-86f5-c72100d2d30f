name: whole_house_music
description: WebSocket ande Music flutter package project.
version: 0.0.1
author: <PERSON><PERSON><PERSON><PERSON>@haier.com
homepage: http://**************:8083
publish_to: http://**************:8083
flutterVersion: 3

environment:
  sdk: '>=3.2.3 <4.0.0'
  flutter: ">=3.16.5"

dependencies:
  flutter:
    sdk: flutter

  web_socket_channel: 2.2.0

  dio: 5.3.2

  app_info:
    hosted:
      name: app_info
      url: http://**************:8083
    version: '>=1.0.2'

  user:
    hosted:
      name: user
      url: http://**************:8083
    version: '>=0.1.1'

  message:
    hosted:
      name: message
      url: http://**************:8083
    version: '>=0.0.12'

  family:
    hosted:
      name: family
      url: http://**************:8083
    version: '>=0.2.0'

  plugin_device:
    hosted:
      name: plugin_device
      url: http://**************:8083
    version: ">=0.3.2"

  log:
    hosted:
      name: log
      url: http://**************:8083
    version: ">=0.0.2"

  trace:
    hosted:
      name: trace
      url: http://**************:8083
    version: '>=0.0.12'
  uplustrace:
    hosted:
      name: uplustrace
      url: http://**************:8083
    version: '>=1.0.0'

  device_utils:
    hosted:
      name: device_utils
      url: http://**************:8083
    version: ">=0.0.1"

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true
  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
