import 'dart:async';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:user/user.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:whole_house_music/utils/util_log.dart';
import 'package:whole_house_music/web_socket/websocket_protocol.dart';
import 'package:whole_house_music/web_socket/websocket_status.dart';

const int retryMaxCount = 3;

class WebSocketManager {
  WebSocketChannel? channel;
  WebSocketProtocol? _protocol;
  WebSocketConnectStatus _connectStatus = WebSocketConnectStatus.close;
  bool _needReconnect = false;
  int _retryCount = 0;
  Timer? _pingTimer;

  /// Reset try-connect count.
  set retryCount(int value) {
    _retryCount = value;
  }

  /// Current socket connect status.
  WebSocketConnectStatus get webSocketConnectStatus {
    return _connectStatus;
  }

  /// Async establish a socket connection.
  /// params：{socketPath} Socket server address.
  ///        {connectParams} Socket connection request parameters.If appInfo such as 'appId',
  ///        'appVersion' and 'clientId' are null, the request parameter will automatically add
  ///        appInfo. The same is true for 'timestamp' and 'accessToken'.
  ///        {protocol} Connection status and message receiving listener.
  /// return ：Future
  Future<void> connect(String socketPath, Map<String, String> connectParams,
      WebSocketProtocol protocol) async {
    if (_connectStatus == WebSocketConnectStatus.closing) {
      Future.delayed(Duration(milliseconds: 500), () {
        connect(socketPath, connectParams, protocol);
      });
      return;
    }
    if (_connectStatus == WebSocketConnectStatus.close) {
      _needReconnect = true;
      _connectStatus = WebSocketConnectStatus.connecting;
      final String paramsStr = await _getWebSocketUrlParams(connectParams);
      DevLogger.debug(
          tag: tagWholeHouseMusic, msg: 'Request params: $paramsStr');
      channel = IOWebSocketChannel.connect(Uri.parse(socketPath + paramsStr));
      _connectStatus = WebSocketConnectStatus.connect;
      _addListener(protocol);
      _ping();
    }
  }

  Future<String> _getWebSocketUrlParams(
      Map<String, String> connectParams) async {
    try {
      AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      if (connectParams['clientId'] == null) {
        connectParams['clientId'] = appInfoModel.clientId;
      }
      if (connectParams['appId'] == null) {
        connectParams['appId'] = appInfoModel.appId;
      }
      if (connectParams['appVersion'] == null) {
        connectParams['appVersion'] = appInfoModel.appVersion;
      }
      if (connectParams['timestamp'] == null) {
        connectParams['timestamp'] =
            DateTime.now().millisecondsSinceEpoch.toString();
      }
      if (connectParams['accessToken'] == null) {
        return _getAccessToken(connectParams);
      } else {
        return _mapToUrlParams(connectParams);
      }
    } catch (error) {
      DevLogger.debug(
          tag: tagWholeHouseMusic,
          msg: '_getWebSocketUrlParams exception: ' + error.toString());
      return '';
    }
  }

  Future<String> _getAccessToken(Map<String, String> paramsMap) async {
    bool _loginStatus = false;
    try {
      final loginStatus = await User.getLoginStatus();
      _loginStatus = loginStatus.isLogin;
    } catch (error) {
      DevLogger.debug(
          tag: tagWholeHouseMusic,
          msg: '_getAccessToken exception: ' + error.toString());
    }
    try {
      if (_loginStatus) {
        final oauthData = await User.getOauthData();
        paramsMap['accessToken'] = oauthData.uhome_access_token;
      }
    } catch (e) {
      DevLogger.debug(
          tag: tagWholeHouseMusic,
          msg: 'getUserInfo exception: ' + e.toString());
    }

    return _mapToUrlParams(paramsMap);
  }

  String _mapToUrlParams(Map<String, String> paramsMap) {
    List<String> arr = [];
    paramsMap.forEach((key, value) {
      arr.add('$key=$value');
    });
    return arr.join('&');
  }

  void _addListener(WebSocketProtocol protocol) {
    _protocol = protocol;
    channel?.stream.listen((message) {
      DevLogger.info(
          tag: tagWholeHouseMusic, msg: "WebSocket response message:$message");
      if (_connectStatus == WebSocketConnectStatus.connect) {
        if (message == 'pong') {
          _ping();
        } else {
          _protocol?.onReceiveMessage(message);
        }
      }
    }, onError: (error) {
      // connect err
      DevLogger.info(tag: tagWholeHouseMusic, msg: 'WebSocket onError: $error');
    }, onDone: () {
      // connect end
      DevLogger.info(tag: tagWholeHouseMusic, msg: 'WebSocket onDone');
      _connectStatus = WebSocketConnectStatus.close;
      _cancelTimer();
      if (_needReconnect == true &&
          _connectStatus == WebSocketConnectStatus.close &&
          _retryCount < retryMaxCount) {
        _retryCount += 1;
        Future.delayed(Duration(seconds: 1), () {
          _protocol?.onDisconnectWithError();
        });
      }
    });
  }

  void _ping() {
    _pingTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      sendMsg('ping');
    });
  }

  void _cancelTimer() {
    if (_pingTimer != null && _pingTimer!.isActive) {
      _pingTimer!.cancel();
      _pingTimer = null;
    }
  }

  /// Actively send socket request message to the server.
  /// param：{reqMsg} Message content which is String type.
  /// return：bool {true} The connection already exists and the message was sent successfully，
  ///             {false} Socket connection not established.
  bool sendMsg(String reqMsg) {
    if (_connectStatus == WebSocketConnectStatus.connect) {
      channel?.sink.add(reqMsg);
      DevLogger.info(
          tag: tagWholeHouseMusic, msg: "WebSocket send message: $reqMsg");
      return true;
    }
    return false;
  }

  /// Async disconnect webSocket.
  /// return：Future
  Future<void> disconnect() async {
    if (_connectStatus == WebSocketConnectStatus.connect) {
      _needReconnect = false;
      _connectStatus = WebSocketConnectStatus.closing;
      await channel?.sink.close(11000, 'Active shutdown');
      _retryCount = 0;
      channel = null;
      _protocol = null;
      _cancelTimer();
      DevLogger.info(tag: tagWholeHouseMusic, msg: 'webSocket disconnected');
    }
  }
}
