import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:whole_house_music/utils/util_log.dart';


import 'enum_type.dart';

class EnvUtil {
  static ServerEnv _serverEvn = ServerEnv.shengchan;
  static String _appVersion = '';

  static Future<void> requestServerEnv() async {
    try {
      // init _appInfoModel
      AppInfoModel appInfoModel = await AppInfoPlugin.getAppInfo();
      // init _serverEnv
      _serverEvn = _fromType(appInfoModel.env);
      _appVersion = appInfoModel.appVersion;
    } catch (error) {
      DevLogger.debug(
          tag: tagWholeHouseMusic, msg: 'getUserInfo exception: ' + error.toString());
    }
  }

  static ServerEnv getServerEnv() {
    return _serverEvn;
  }

  static String getAppVersion() {
    return _appVersion;
  }

  static ServerEnv _fromType(String env) {
    return env == "验收" ? ServerEnv.yanshou : ServerEnv.shengchan;
  }
}
