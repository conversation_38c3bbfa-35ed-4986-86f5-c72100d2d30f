
import 'dart:convert';

import 'package:app_info/Appinfos.dart';
import 'package:app_info/AppinfosModel.dart';
import 'package:user/modle/login_status_model.dart';
import 'package:user/user.dart';
import 'package:whole_house_music/music/server/http_response_model.dart';
import 'package:whole_house_music/music/server/request_env.dart';
import 'package:whole_house_music/music/server/server.dart';
import 'package:whole_house_music/music/configs/constant.dart';
import 'package:whole_house_music/utils/util_log.dart';
import 'http.dart';

class HttpAPI{
  /// 获取一些本地存储的请求信息
  static String _appId = "";
  static String _appKey = "";
  static String _clientId = "";
  static String _appVersion = "";
  static bool _loginStatus = false;
  static String _appEnvironment = "";
  static bool _grayMode = false;

  /// 初始化一些基本请求信息
  static Future<void> init() async {
    try {
      final LoginStatus loginStatus = await User.getLoginStatus();
      _loginStatus = loginStatus.isLogin;
    } catch (e) {
      _loginStatus = false;
    }
    if (_appId == '') {
      await getAppInfo();
    }
  }

  static Future<Map<String, dynamic>> _headers(
      [Map<String, dynamic>? otherHeaderInfo, int? timestamp]) async {
    String _accessToken;
    String _accountToken;
    String _userId;
    try {
      if (_loginStatus) {
        final _oauthdata = await User.getOauthData();
        final _userinfo = await User.getUserInfo();
        _accessToken = _oauthdata.uhome_access_token;
        _accountToken = _oauthdata.user_center_access_token;
        _userId = _userinfo.userId;
      } else {
        _accessToken = '';
        _accountToken = '';
        _userId = '';
      }

      Map<String, dynamic> headers = {
        'timestamp': timestamp.toString(),
        'appId': _appId,
        'clientId': _clientId,
        'userId': _userId,
        'accessToken': _accessToken,
        'accountToken': _accountToken,
        'appVersion': _appVersion,
      };

      if (otherHeaderInfo != null) {
        headers.addAll(otherHeaderInfo);
      }
      return headers;
    } catch (e) {
      DevLogger.debug(
          tag: tagWholeHouseMusic,
          msg: 'getUserInfo exception: ' + e.toString());
      return <String, dynamic>{};
    }
  }

  static Future<void> getAppInfo() async {
    try {
      final AppInfoModel appInfo = await AppInfoPlugin.getAppInfo();
      _appId = appInfo.appId;
      _appKey = appInfo.appKey;
      _clientId = appInfo.clientId;
      _appVersion = appInfo.appVersion;
      _appEnvironment = appInfo.env;
      _grayMode = appInfo.grayMode;
      if (_appVersion == null || _appVersion.isEmpty) {
        _appVersion = '7.0.0';
      }
    } catch (e) {
      DevLogger.debug(
          tag: tagWholeHouseMusic,
          msg: 'getAppInfo exception: ' + e.toString());
    }
  }


  // 获取支持音乐卡片的音箱设备
  static Future<HttpResponseModel> getSocketUrl() async {
    await init();
    final Map<String, String> params = {'encryType': '1'};
    final int timestamp = Http.getTimestamp();
    final String bodyJson = json.encode(params);
    final String sign = Http.getSign(
        APIConfigs.getSocketUrl, bodyJson, _appId, _appKey, timestamp);
    final Map<String, dynamic> headersInfo =
    await _headers({'sign': sign, 'timestamp': timestamp});

    final dynamic result = await httpManager.post(
        RequestUtil.getSerEnv() + APIConfigs.getSocketUrl,
        params: params,
        header: headersInfo);
    return HttpResponseModel.fromJson(
        result is Map ? result : <dynamic, dynamic>{});
  }
}